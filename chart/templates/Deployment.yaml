apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.podType }}
  namespace: {{ .Values.global.namespace }}
  labels:
    platform: cloudsop
  annotations:
    cloudsop.feature: ''
spec:
  replicas: {{ include "dvaicrossproxyservice.replicas" . }}
  selector:
    matchLabels:
      platform: cloudsop
      app: {{ .Values.podType }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.podType }}
        platform: cloudsop
      annotations:
        network.alpha.kubernetes.io/network: {{ .Values.global.network.defaultNetwork | squote }}
    spec:
      securityContext:
        runAsUser: 3001
        runAsGroup: 2000
      restartPolicy: Always
      affinity: {{- include "dvaicrossproxyservice.podAntiAffinity" . | nindent 8 }}
      nodeSelector:
        node.adn.huawei.com/group.{{ .Values.global.nodePool }}: ''
      containers:
      - name: dvaicrossproxyservice
        image: '{{ .Values.global.repo.address }}/{{ .Values.global.baseImage.name }}-{{ .Values.global.arch }}:{{ .Values.global.baseImage.version }}'
        command: [c-init]
        args: [-g, --, msctl, start, -t, normal, --, '$(PKG_ROOT)/dvaicrossproxyservice@{{ .Values.imageVersion.dvaicrossproxyserviceVersion }}/bin/start.sh']
        resources: {{- include "dvaicrossproxyservice.resources" (dict "resources" .Values.containers.dvaicrossproxyservice.resources "scale" .Values.global.scale.equivalentNE) | nindent 10 }}
        env:
        # 使用##是为了yaml加载时不报错，此处不可随意删除和使用，批量上车工具转换之后会去掉##，提交的Pod仓的代码不会有##显示。下同。
        - name: PKG_ROOT
          value: /opt/pkgs
        - name: RUN_USER
          value: ossadm
        - name: APP_ROOT
          value: $(PKG_ROOT)/dvpmservice@{{ .Values.imageVersion.dvpmserviceVersion }}
        - name: APP_NAME
          value: {{ .Values.app.name }}
        - name: APP_CONF_ROOT
          value: /opt/appconf
        - name: _APP_LOG_DIR
          value: /opt/log/textlog
        - name: _APP_DUMP_DIR
          value: /opt/log/dump/heapdump
        - name: LOG_LEVEL_PATH
          value: /etc/loglevel
        - name: _APP_SHARE_DIR
          value: /opt/share
        - name: _APP_TMP_DIR
          value: /opt/tmp
        - name: SIDECAR_ROOT
          value: /opt/sidecar
        - name: IR_SSL_ROOT
          value: /opt/ssl
        - name: SOP_CSI_MANIFEST_PATH
          value: /opt/pkg_version
        - name: OSS_LANG
          value: {{ .Values.global.lang }}
        - name: IP_VERSION
          value: {{ .Values.global.ipVersion | squote }}
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: COMPLETE_PROCESS_NAME
          value: $(POD_NAME)-$(APP_NAME)
        - name: PROCESS_INSTANCE_ID
          value: $(APP_NAME)_$(POD_NAME)
        # master节点IP
        - name: HOSTING_SERVER_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        # 性能增量订阅的环境变量
        - name: CONTAINER # 容器化标识
          value: "true"
        # 以下环境变量根据依赖的Rtsp动态生成
        - name: JAVA_HOME
          value: $(PKG_ROOT)/jre@{{ .Values.dependencies.jreVersion }}
        - name: TOMCAT_LOG_DIR
          value: $(_APP_LOG_DIR)/$(COMPLETE_PROCESS_NAME)/tomcatlog
        - name: TOMCAT_WORK_DIR
          value: $(_APP_SHARE_DIR)/$(COMPLETE_PROCESS_NAME)/tomcatwork
        - name: CATALINA_HOME
          value: $(PKG_ROOT)/tomcat@{{ .Values.dependencies.tomcatVersion }}
        - name: CATALINA_BASE
          value: $(APP_ROOT)
        - name: CATALINA_TMPDIR
          value: $(_APP_SHARE_DIR)/tomcattemp
        - name: CATALINA_OUT
          value: $(TOMCAT_LOG_DIR)/catalina.out
        - name: DV_CUSTOM_KEY # kmc加解密keyName
          value: {{ .Values.app.name }}.key
        - name: sandbox_escape
          value: "{{ .Values.global.sandbox_escape }}"
        - name: is_ms
          value: {{ .Values.global.is_ms |quote }}
        - name: CONTAINER_NAME
          value: {{ .Values.app.name }}
        - name: SSL_ROOT
          value: /opt/ssl
        volumeMounts:
        - name: tempdir
          mountPath: /opt/tmp
        - name: sidecar-{{ .Values.app.name }}
          mountPath: /opt/sidecar
        - name: log
          mountPath: /opt/log
        - name: dump
          mountPath: /opt/log/dump
        - name: loglevel
          mountPath: /etc/loglevel
        - name: share
          mountPath: /opt/share
        - name: hostname-volume
          mountPath: /etc/hostname-host
          readOnly: true
        - name: tmproot
          mountPath: /tmp
        # mrb微服务需要的挂载
        - name: pkg
          mountPath: /opt/pkgs
        - name: manifest-{{ .Values.app.name }}
          mountPath: /opt/pkg_version
        - mountPath: /etc/timezone
          name: timezone
        - name: digitalview-config
          mountPath: /opt/digitalview-config
        - name: ssl
          mountPath: /opt/ssl
      volumes:
      {{- include "dvaicrossproxyservice.volumes" (list "dvaicrossproxyservice") | nindent 6 }}
      - name: pkg
        csi:
          driver: sop-csi-driver
          volumeAttributes:
            type: pkgv1
      - name: manifest-{{ .Values.app.name }}
        csi:
          driver: sop-csi-driver
          volumeAttributes:
            type: manifest
            npkg.cxx3rdlib: {{ .Values.dependencies.cxx3rdlibVersion }}
            npkg.dv3rdlib: {{ .Values.dependencies.dv3rdlibVersion }}
            npkg.dvkernelcbb: {{ .Values.dependencies.dvkernelcbbVersion }}
            npkg.dvplatformcbb: {{ .Values.dependencies.dvplatformcbbVersion }}
            npkg.jre: {{ .Values.dependencies.jreVersion }}
            npkg.mqclient: {{ .Values.dependencies.mqclientVersion }}
            npkg.tomcat: {{ .Values.dependencies.tomcatVersion }}
            upkg.dvaicrossproxyservice: {{ .Values.imageVersion.dvaicrossproxyserviceVersion }}
            jars: '{{ .Values.jars.dvaicrossproxyservice }}'
      - configMap:
          defaultMode: 420
          name: timezone
        name: timezone
      - name: digitalview-config
        secret:
          defaultMode: 420
          secretName: digitalview-config
      - name: hostname-volume
        hostPath:
          path: /etc/hostname
          type: File
      - name: log
        csi:
          driver: sop-csi-driver
          volumeAttributes:
            type: log
            size: 1000Mi
            textLogPath: textlog
            binaryLogPath: binarylog
            identity: digitalview.beidou.dvaicrossproxyservice
      - name: dump
        csi:
          driver: sop-csi-driver
          volumeAttributes:
            type: dump
            coreDumpPath: coredump
            otherDumpPath: heapdump
            identity: digitalview.beidou.dvaicrossproxyservice
      - name: loglevel
        configMap:
          name: loglevel.beidou
          optional: true
      - name: tmproot
        emptyDir: {}
      - name: ssl
        emptyDir:
          medium: Memory
          sizeLimit: 5Mi
