<?xml version="1.0" encoding="UTF-8"?>

<param_validators>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/task"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/task"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="delete" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/task"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="taskDesc" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="650"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="taskName" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="200"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="situationName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="situationId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="taskType" required="true">
            <validator name="REGEXP_VALIDATOR" rule="1|2|3|5|6|7|10"/>
        </parameter>
        <parameter name="indicatorPredictScenario" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="aggregateMachineGroupList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="ticket" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="periodicType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="predictCron" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="trainCron" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="solutionName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="reportAlarm" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="alarmType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="4"/>
        </parameter>
        <parameter name="algorithmModelId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="algorithmModelName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="algorithmParam" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="20000"/>
        </parameter>
        <parameter name="datasourceId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="20"/>
        </parameter>
        <parameter name="indicatorSelectType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2"/>
        </parameter>
        <parameter name="indicatorList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z0-9\\.:=_\-/]*" caseSensitive="true"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.clusterName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="indicatorList.siteId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="indicatorList.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicatorList.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.abnormal" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.lastOutlierTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="indicatorList.unitedId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1000"/>
        </parameter>
        <parameter name="indicatorList.checkedNetId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="2048"/>
        </parameter>
        <parameter name="indicatorList.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="5"/>
        </parameter>
        <parameter name="indicatorList.indexId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.resourceTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.pql" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicatorList.subSite" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.groupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.aggregateMos" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.aggregateMos.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.aggregateMos.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.softDelete" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.softDeleteTimeStamp" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.deploymentMoType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.indicatorTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="indicatorList.msGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.indicatorGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z0-9\\.:=_\-/]*" caseSensitive="true"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.clusterName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.siteId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.abnormal" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.lastOutlierTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.unitedId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1000"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.checkedNetId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="2048"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="5"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.indexId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.resourceTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.pql" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.subSite" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.groupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.auxiliaryIndicator" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.aggregateMos" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.historyIndicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.historyIndicatorName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.softDelete" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.softDeleteTimeStamp" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.deploymentMoType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.msGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList.auxiliaryIndicator.indicatorGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="indicatorList.historyIndicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.historyIndicatorName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="prometheusData" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="prometheusData.pqls" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="4000"/>
        </parameter>
        <parameter name="prometheusData.step" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="20"/>
        </parameter>
        <parameter name="prometheusData.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="prometheusData.dataSourceId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="dataSourceType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="columnMapping" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="columnMapping.auxiliaryKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="columnMapping.dataSourceId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="columnMapping.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="columnMapping.sql" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="columnMapping.indicatorKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="columnMapping.originalValueKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="columnMapping.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="columnMapping.timeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="columnMapping.ipKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="columnMapping.dnKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="addition" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="beginTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="updateTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="lastExecutorUserId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="lastModifyUserId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="path" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="sourceName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="trainStatus" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="4"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="4"/>
        </parameter>
        <parameter name="taskDetail" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="300"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\]"/>
        </parameter>
        <parameter name="userId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="startStatus" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="5"/>
        </parameter>
        <parameter name="alarmTypeList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="alarmSelectType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="alarmSelectList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="alarmSelectGroupList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorPredictNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorTrainNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="alarmPredictTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="alarmTrainTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logPredictTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logTrainTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="canModify" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="intelligentRecommendation" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="REGEXP_VALIDATOR" rule="true|false"/>
        </parameter>
        <parameter name="solutionTypeForRecommendation" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="triggerTaskMessage" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="triggerTaskMessage.triggerType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1"/>
        </parameter>
        <parameter name="triggerTaskMessage.relationTree" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.delay" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8"/>
        </parameter>
        <parameter name="triggerTaskMessage.useTemplate" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="triggerTaskMessage.sendEmail" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="REGEXP_VALIDATOR" rule="true|false"/>
        </parameter>
        <parameter name="triggerTaskMessage.userGroupInfo" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="triggerTaskMessage.manualTriggerEmail" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="REGEXP_VALIDATOR" rule="true|false"/>
        </parameter>
        <parameter name="triggerTaskMessage.correlateType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2"/>
        </parameter>
        <parameter name="triggerTaskMessage.userId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorIds" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.displayFilter" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="REGEXP_VALIDATOR" rule="true|false"/>
        </parameter>
        <parameter name="triggerTaskMessage.filterRelevance" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList" maxArraySize="1000" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.taskName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="triggerTaskMessage.indicatorList.pql" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="importTaskStatus" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="groupLevel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="3"/>
        </parameter>
        <parameter name="reportAlarmName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="200"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="reportAlarmId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="capacityIfReverse" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="capacityIfRePredict" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="multipleTaskMessage" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="multipleTaskMessage.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="multipleTaskMessage.multipleTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="multipleTaskMessage.relationTree" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="multipleTaskMessage.userId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logSolutionType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logSolutionName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logIndexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\]"/>
        </parameter>
        <parameter name="logAnalysisField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logFilterColumns" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logAbnormalPattern" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logIndexTemplate" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\]"/>
        </parameter>
        <parameter name="logFieldMapping" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="logFieldMapping.analysisField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="300"/>
        </parameter>
        <parameter name="logFieldMapping.timeField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="300"/>
        </parameter>
        <parameter name="logFieldMapping.serialNoField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="300"/>
        </parameter>
        <parameter name="logFieldMapping.fileFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="300"/>
        </parameter>
        <parameter name="subSite" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="isAutoSchedule" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="associateProductPortraitId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="associateProductPortraitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleDependentIndicator" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="siteId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="strategyNames" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleTimeout" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="schedulePeriod" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleScaleInWaitTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="detectOutNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.hostPortraitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.hostType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.indicatorIdentity" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.indicatorIdentity.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.indicatorIdentity.returnValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.upperThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.lowerThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.forecastTaskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.forecastTaskName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="scheduleConfig.configId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="sendEvent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="REGEXP_VALIDATOR" rule="true|false"/>
        </parameter>
        <parameter name="sendEventLevel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="REGEXP_VALIDATOR" rule="1|2|3|4"/>
        </parameter>
        <parameter name="sendEventContent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="580"/>
            <validator name="BLACK_VALIDATOR" rule="[#]"/>
        </parameter>
        <parameter name="sendEventAlarmContent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="580"/>
            <validator name="BLACK_VALIDATOR" rule="[#]"/>
        </parameter>
        <parameter name="conceptDriftSwitch" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="conceptDriftRetrain" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="alarmNormalLevel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="alarmSeverityLevel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="alarmReportTimeRange" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="alarmTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="REGEXP_VALIDATOR" rule="[^#%+&amp;|&gt;&lt;&apos;;&quot;/\\\(\)]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="updateIndicatorAuto" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="indicatorDiscardTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="REGEXP_VALIDATOR" rule="1|2|3|4|5|6|7"/>
        </parameter>
        <parameter name="alarmNumberThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="eventDurationThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="useOriginalAlarmId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="indicatorTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="templateId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="msIndicatorGroupList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>

        <parameter name="msIndicatorGroupList.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>

        <parameter name="msIndicatorGroupList.msGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.measUnit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.moName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.collectTask" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.tableName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.customSQL" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="10000"/>
        </parameter>

        <parameter name="msIndicatorGroupList.timestampKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.measureIndicatorKeys" required="false" maxArraySize="200">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.measureObjectKeys" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="10000"/>
        </parameter>

        <wildcard>
            <parameter name="msIndicatorGroupList.measureObjectKeys\.[a-zA-Z0-9_]*" required="false">
                <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.EMPTY"/>
            </parameter>
        </wildcard>

        <parameter name="msIndicatorGroupList.indicatorGroupTag" required="false" maxArraySize="200">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="msIndicatorGroupList.measureIndicatorKeysJson" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="10000"/>
        </parameter>

        <parameter name="msIndicatorGroupList.measureObjectKeysJson" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="10000"/>
        </parameter>

        <parameter name="msIndicatorGroupList.indicatorGroupTagJson" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="10000"/>
        </parameter>

        <parameter name="msIndicatorGroupList.indicatorTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>

        <parameter name="indicatorGroupList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>

        <parameter name="indicatorGroupList.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>

        <parameter name="indicatorGroupList.indicatorGroupId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="1024"/>
        </parameter>

        <parameter name="indicatorGroupList.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.moName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.deployMoType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>

        <parameter name="indicatorGroupList.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_LENGTH_VALIDATOR" minLength="0" maxLength="256"/>
        </parameter>
        <parameter name="softDeleted" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="displayResult" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/starttask"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="isTraining" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/manualexecute"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/stoptask"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/trainmodel"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/tasklist"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="taskName" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="200"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="paging" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.pageSize" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.pageNumber" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.sortField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="20"/>
            <validator name="REGEXP_VALIDATOR" rule="^[A-Za-z0-9_]*$" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="paging.sortType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="4"/>
            <validator name="REGEXP_VALIDATOR" rule="asc|desc" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicatorTaskTypes" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR" />
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/customcolumns"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="dataSourceId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="sql" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="ticket" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="500"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/unassignedalarmid"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/taskstatus"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="taskValidId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z0-9\-]*" caseSensitive="true"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/indicatorinstances"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="50"/>
        </parameter>
        <parameter name="deploymentMoType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicators.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z0-9\\.:=_\-/]*" caseSensitive="true"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicators.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicators.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indicators.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="indicators.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicators.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicators.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicators.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicators.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicators.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="indicators.abnormal" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicators.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicators.lastOutlierTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="indicators.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
        </parameter>
        <parameter name="indicators.checkedNetId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="2048"/>
        </parameter>
        <parameter name="indicators.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="5"/>
        </parameter>
        <parameter name="indicators.indexId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicators.resourceTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="indicators.pql" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="indicators.softDelete" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicators.softDeleteTimeStamp" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/querymeasobjectsamount"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="512"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/partiallyresult"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="queryType" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="REGEXP_VALIDATOR" rule="train|predict"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/kpiforecastmatrix"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="portraitId" required="true">
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="sharedIndicator" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="siteId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/triggerautoschedule"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="isAutoSchedule" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="ticket" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="500"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/configdata/queryconfigitem"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="param" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z_,]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/configdata/modifyconfigurations"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="modifyItems" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.configItemName" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="ticket" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifiedItemName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="modifyItems.configItemMeaning" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.configId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="modifyItems.featureType" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="modifyItems.targetValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.dataType" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.defaultValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.maxValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.value" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.minValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.usedCapacity" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/configdata/premodifyconfigurations"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="modifiedItemName" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="modifyItems.configItemName" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.configItemMeaning" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.configId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="modifyItems.featureType" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="modifyItems.targetValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.dataType" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="modifyItems.defaultValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.maxValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.value" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.minValue" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="modifyItems.usedCapacity" required="true">
            <validator name="MIN_LENGTH_VALIDATOR" minLength="1" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[0-9]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/alarmtemplate"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="alarmTaskType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/validatepql"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="dataSourceId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="10"/>
        </parameter>
        <parameter name="step" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="pqls" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="4000"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/batch"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="false">
        <parameter name="type" required="true">
            <validator name="REGEXP_VALIDATOR" rule="start|stop|train|delete"/>
        </parameter>
        <parameter name="batchTaskInfos.taskId" required="true">
                <validator name="EMPTY_VALIDATOR" empty="false"/>
                <validator name="DIGITS_VALIDATOR"/>
                <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="batchTaskInfos.taskName" required="true">
                <validator name="EMPTY_VALIDATOR" empty="false"/>
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="200"/>
                <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="isTraining" required="false">
            <validator name="REGEXP_VALIDATOR" rule="0|1"/>
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/taskmanage/batchstatus"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
        <parameter name="batchId">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
    </param_validator>
</param_validators>
