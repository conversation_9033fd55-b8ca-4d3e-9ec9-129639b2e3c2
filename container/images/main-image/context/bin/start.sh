#!/bin/bash
#check user
INFO_LOG_FILE="${_APP_LOG_DIR}/DVAIAgentProxyService_start.log"
function log()
{
    local t=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$t [INFO] $*"
    echo "$t [INFO] $*" >>$INFO_LOG_FILE
}

log "****************************************** start ******************************************"

CUR_PATH=$(cd `dirname $0`;pwd)
SCRIPT_PATH=$0
IPMC_USER="`stat -c '%U' ${SCRIPT_PATH}`"
export IPMC_USER
CURRENT_USER="`/usr/bin/id -u -n`"
if [ "${IPMC_USER}" != "${CURRENT_USER}" ]
then
    log "only ${IPMC_USER} can execute this script."
    exit 1
fi

umask 027


## 新增平台一些连接默认参数
if [ -z "$HTTP_CONNECT_MAXTHREADS" ]
then
        log "There is no HTTP_CONNECT_MAXTHREADS"
        export HTTP_CONNECT_MAXTHREADS=20
fi
if [ -z "$HTTP_CONNECT_ACCEPTCOUNT" ]
then
        log "There is no HTTP_CONNECT_ACCEPTCOUNT"
        export HTTP_CONNECT_ACCEPTCOUNT=20
fi
if [ -z "$HTTP_CONNECT_MINSPARETHREADS" ]
then
        log "There is no HTTP_CONNECT_MINSPARETHREADS"
        export HTTP_CONNECT_MINSPARETHREADS=4
fi
if [ -z "$HTTP_CONNECT_MAXSPARETHREADS" ]
then
        log "There is no HTTP_CONNECT_MAXSPARETHREADS"
        export HTTP_CONNECT_MAXSPARETHREADS=10
fi
if [ -z "$HTTP_CONNECT_MAXIDLETIME" ]
then
        log "There is no HTTP_CONNECT_MAXIDLETIME"
        export HTTP_CONNECT_MAXIDLETIME=60000
fi


if [ -z "$JAVA_HOME" ]
then
    log "There is no JAVA_HOME"
    exit 1
fi

if [ -z "$CATALINA_HOME" ]
then
    log "There is no CATALINA_HOME"
    exit 1
fi

if [ -z "$APP_ROOT" ]
then
    log "There is no APP_ROOT"
    exit 1
fi

if [ ! -d "${_APP_SHARE_DIR}/temp" ]
then
  mkdir "${_APP_SHARE_DIR}/temp"
fi


export CATALINA_BASE=$APP_ROOT
export APP_NAME=DVAIAgentProxyService
export COMPLETE_PROCESS_NAME=$POD_NAME-$APP_NAME
LOG_PATH=$_APP_LOG_DIR/$COMPLETE_PROCESS_NAME

bash $APP_ROOT/upgrade/init.sh &

JAVA_OPTS="-Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Dlog.dir=$LOG_PATH"
JAVA_OPTS="$JAVA_OPTS -Dorg.apache.catalina.security.SecurityListener.UMASK=`umask`"
JAVA_OPTS="$JAVA_OPTS -Dorg.apache.catalina.connector.RECYCLE_FACADES=true"
JAVA_OPTS="$JAVA_OPTS -Dnet.sf.ehcache.skipUpdateCheck=true"
JAVA_OPTS="$JAVA_OPTS -Dorg.terracotta.quartz.skipUpdateCheck=true"
JAVA_OPTS="$JAVA_OPTS -Dzenith.connector=${ZENITH_CONNECTOR_JAVA_HOME}"
JAVA_OPTS="$JAVA_OPTS -XX:+CrashOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${_APP_SHARE_DIR}/DVAIAgentProxyService_dump.hprof"
JAVA_OPTS="$JAVA_OPTS -XX:OnOutOfMemoryError=\"rm ${_APP_SHARE_DIR}/DVAIAgentProxyService_dump_[0-9]*.hprof 2>/dev/null; mv ${_APP_SHARE_DIR}/DVAIAgentProxyService_dump.hprof ${_APP_SHARE_DIR}/DVAIAgentProxyService_dump_%p.hprof\""
export TOMCAT_LOG_DIR=$_APP_LOG_DIR/$COMPLETE_PROCESS_NAME/tomcatlog
mkdir -p $TOMCAT_LOG_DIR
export TOMCAT_WORK_DIR=$_APP_SHARE_DIR/$COMPLETE_PROCESS_NAME/tomcatwork
export CATALINA_OUT=$TOMCAT_LOG_DIR/catalina.out
JAVA_OPTS="$JAVA_OPTS -DTOMCAT_LOG_DIR=$TOMCAT_LOG_DIR  -DTOMCAT_WORK_DIR=$TOMCAT_WORK_DIR -DNFW=$COMPLETE_PROCESS_NAME -Dprocname=$COMPLETE_PROCESS_NAME "
export JAVA_OPTS="$JAVA_OPTS -server -Xms128m -Xmx512m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=128m -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=62 -XX:-UseLargePages -XX:+UseFastAccessorMethods -XX:+CMSClassUnloadingEnabled"
export LOGGING_CONFIG="-DNFW=$COMPLETE_PROCESS_NAME -Djava.util.logging.config.file=$CATALINA_BASE/conf/logging.properties"

## 安装后脚本
bash $APP_ROOT/init/post_install.sh

## 容器化需在启动脚本中主动调用数据库初始化脚本
while true
do
    echo ">>>Begin to init db..."
    bash ${APP_ROOT}/upgrade/init.sh
    if [ $? -ne 0 ];then
        sleep 10
        echo "error happened, need init db again." >> ${LOG_PATH}/start.log
        continue
    fi
    break
done

## 启动服务
echo " ******************************************Start Service ******************************************"
cmd=`msctl start -i --cmd-only -t java-framework-v1 -c "${JAVA_OPTS}"`
eval exec $cmd > ${CATALINA_OUT} 2>&1
log "Start the Service finished."
echo " ******************************************Start Service Finished ******************************************"
