import React, { useContext, useEffect, useState } from 'react';
import { Dialog, TextField } from 'eview-ui';
import { GlobalContext } from '../Context';
import { Store } from '../../utils/Common';
import i18n from '../../utils/locales';
import Http from '../../utils/Http';
import Message from '../message/Message';

function Node(props) {
  const [state, dispatch] = useContext(GlobalContext);
  const [message, setMessage] = useState({
    isOpen: false,
  });
  const cancelButtonText = i18n.translate('topo.link.cancel');
  const okButtonText = i18n.translate('topo.link.confirm');
  let linkNameValidation = true;
  const validator = value => {
    let validationReturnMap = {};
    linkNameValidation = true;
    validationReturnMap.result = true;
    if (value.length > 128) {
      linkNameValidation = false;
      validationReturnMap.result = false;
      validationReturnMap.message = i18n.translate(
        'topology.link.nameInputValidFail',
      );
    }

    // 链路名称白名单校验，匹配中文，英文字母大小写，数字，下滑线，横杠
    const rule = /^[\u4e00-\u9fa5_a-zA-Z0-9\-]+$/;
    if (value.trim() === '' || !rule.test(value)) {
      validationReturnMap.result = false;
      linkNameValidation = false;
      validationReturnMap.message = i18n.translate(
        'topology.link.inputValidFail',
      );
    }
    return validationReturnMap;
  };

  let nodePositionSelector;
  useEffect(() => {
    nodePositionSelector = new topoInterface.widgets.NodePositionSelector({
      domId: 'dvtopolocation',
      isEnable: true,
      choosePositionStartCallBack: choosePositionStartCallBack,
      choosePositionOverCallBack: choosePositionOverCallBack,
    });
    nodePositionSelector.getPosition();
  });

  function choosePositionStartCallBack() {
    window.topoInterface.util.changeCreateNeWinVisible(false);
  }

  function choosePositionOverCallBack(isChoosePosition, isPositionValid) {
    window.topoInterface.util.changeCreateNeWinVisible(true);
  }

  const closeSuccessPanel = () => {
    setMessage({
      isOpen: false,
    });
  };

  const successPanle = () => {
    setMessage({
      type: 'success',
      isOpen: true,
      content: i18n.translate('topology.create.node.success'),
    });
  };
  const errorPanle = () => {
    setMessage({
      type: 'error',
      isOpen: true,
      content: i18n.translate('topology.create.node.fail'),
    });
  };

  const createNode = topoId => {
    Http.post(true, {
      url: '/rest/dvtopowebsite/v1/topo/node',
      timeout: 1000 * 3,
      data: {
        label: textFieldValue.getValue(),
        parentId: topoId,
        xLocation: nodePositionSelector.getPosition().x,
        yLocation: nodePositionSelector.getPosition().y,
      },
      success: data => {
        if (
          data &&
          data.data &&
          data.resultMessage &&
          data.resultMessage === '200'
        ) {
          successPanle();
        } else {
          errorPanle();
        }
      },
      error: e => {
        errorPanle();
      },
    });
  };

  let style = { width: '430px' };
  let labelStyle = { display: 'block', paddingLeft: '0px' };
  let divStyle = { margin: '10px 20px', display: 'inline-block' };
  let textFieldValue;
  let locationStyle = { margin: '10px 0' };
  return (
    <>
      <Message {...message} closeSuccessPanel={closeSuccessPanel} />
      <Dialog
        title={i18n.translate('topo.node.title')}
        size={[640, 230]}
        style={{
          maxHeight: 'unset',
          minHeight: 'unset',
          maxWidth: '640px',
          minWidth: '640px',
        }}
        isOpen={state.isopen}
        onClose={() => {
          dispatch({ type: 'hidden' });
          Store.nodeMenuRefresh = false;
        }}
        modal={false}
        movable={false}
        children={
          <div style={divStyle} id="dvtoponame">
            <TextField
              label={`${i18n.translate('topo.link.content')}:`}
              labelStyle={labelStyle}
              required={true}
              hideRequiredMark={true}
              hintType="tip"
              validator={validator}
              autoComplete="off"
              inputStyle={style}
              ref={textField => (textFieldValue = textField)}
            />

            <div id="dvtopolocation" style={locationStyle} />
          </div>
        }
        resizable={false}
        buttons={[
          {
            text: cancelButtonText,
            onClick: () => {
              dispatch({ type: 'hidden' });
              Store.nodeMenuRefresh = false;
            },
          },
          {
            text: okButtonText,
            onClick: () => {
              if (
                textFieldValue.getValue().trim() === '' ||
                !linkNameValidation
              ) {
                textFieldValue.validate();
                textFieldValue.focus();
                return;
              }
              createNode(props.data);
              dispatch({ type: 'hidden' });
              Store.nodeMenuRefresh = false;
            },
          },
        ]}
      />
    </>
  );
}
export default Node;
