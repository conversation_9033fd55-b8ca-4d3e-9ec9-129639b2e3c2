import A3D from '@a3d/a3d';

const BOX = {
  max: new A3D.C.Vector3(),
  min: new A3D.C.Vector3(),
  size: new A3D.C.Vector3(),
};
const CENTER = new A3D.C.Vector3();

const CORNER_VEC3_START = new A3D.C.Vector3(0, 0, 0);
const CORNER_VEC3_END = new A3D.C.Vector3(0, 0, 0);

/**
 * 计算路径包围盒
 * @param {Object[]} path
 * @returns
 */
export const getBoundingBox = (path) => {
  BOX.max.x = -Infinity;
  BOX.max.y = -Infinity;
  BOX.max.z = -Infinity;
  BOX.min.x = Infinity;
  BOX.min.y = Infinity;
  BOX.min.z = Infinity;
  for (let i = 0, len = path.length; i < len; i++) {
    BOX.max.x = Math.max(BOX.max.x, path[i].x);
    BOX.max.y = Math.max(BOX.max.y, path[i].y);
    BOX.max.z = Math.max(BOX.max.z, path[i].z);
    BOX.min.x = Math.min(BOX.min.x, path[i].x);
    BOX.min.y = Math.min(BOX.min.y, path[i].y);
    BOX.min.z = Math.min(BOX.min.z, path[i].z);
  }
  BOX.size.copy(BOX.max).sub(BOX.min);
  return BOX;
};

/**
 * 计算路径中心点
 * @param {Object[]} path
 * @returns
 */
export const getCenter = (path) => {
  getBoundingBox(path);
  CENTER.copy(BOX.max).add(BOX.min).divideScalar(2);
  return CENTER;
};

/**
 * 处理路径，减去中心点偏移量
 * @param {Object[]} path
 * @param {Object} center
 */
export const fixPathToCenter = (path, center) => {
  for (let i = 0, len = path.length; i < len; i++) {
    path[i].x -= center.x;
    path[i].y -= center.y;
    path[i].z -= center.z;
  }
};

/**
 * 恢复路径，加上中心点偏移量
 * @param {Object[]} path
 * @param {Object} center
 */
export const recoverPathFromCenter = (path, center) => {
  for (let i = 0, len = path.length; i < len; i++) {
    path[i].x += center.x;
    path[i].y += center.y;
    path[i].z += center.z;
  }
};

/**
 * 判断路径是否逆时针
 * @param {Object[]} path
 */
export const isPathClockwise = (path) => {
  const n = path.length;
  let a = 0.0;
  for (let p = n - 1, q = 0; q < n; p = q ++) {
    a += path[ p ].x * path[ q ].z - path[ q ].x * path[ p ].z;
  }
  return a < 0;
};

/**
 * 获取第 index 个圆角的最终半径
 * @param {Number|Number[]} radius
 * @param {Number} index
 * @returns {Number}
 */
const getItemRadius = (radius, index) => {
  let number;
  if (Array.isArray(radius)) {
    number = Number(radius[index]);
    number = number > 0 ? number : 0;
  } else {
    number = Number(radius);
    number = number > 0 ? number : 0;
  }
  return number;
};

/**
 * 计算第 index 个圆角的起点、终点，记录在全局变量中
 * @param {Number} len
 * @param {Number} index
 * @param {Object[]} points
 * @param {Number|Number[]} radius 半径
 */
const setCornerStartAndEnd = (len, index, points, radius) => {
  const prev = (index - 1 + len) % len;
  const next = (index + 1) % len;
  CORNER_VEC3_START.copy(points[prev]).sub(points[index]);
  CORNER_VEC3_END.copy(points[next]).sub(points[index]);
  const prevLen = CORNER_VEC3_START.length();
  const nextLen = CORNER_VEC3_END.length();
  const cornerRadius = Math.min(getItemRadius(radius, index), prevLen / 2, nextLen / 2);
  CORNER_VEC3_START.normalize().multiplyScalar(cornerRadius).add(points[index]);
  CORNER_VEC3_END.normalize().multiplyScalar(cornerRadius).add(points[index]);
};

/**
 * 给 shape 设置带圆角的路径
 * @param {THREE.Shape} shape
 * @param {THREE.Vector3[]|Object[]} points
 * @param {Number|Number[]} radius
 * @param {Boolean} close
 * @returns
 */
export const setShapeWithCorners = function(shape, points, radius, close) {
  if (close) {
    const len = points.length;
    setCornerStartAndEnd(len, len - 1, points, radius);
    shape.moveTo(CORNER_VEC3_END.x, -CORNER_VEC3_END.z);
    for (let i = 0; i < len; i++) {
      setCornerStartAndEnd(len, i, points, radius);
      shape.lineTo(CORNER_VEC3_START.x, -CORNER_VEC3_START.z);
      if (!CORNER_VEC3_START.equals(CORNER_VEC3_END)) {
        shape.quadraticCurveTo(points[i].x, -points[i].z, CORNER_VEC3_END.x, -CORNER_VEC3_END.z);
      }
    }
  }
};

/**
 * 给 shape 设置不带圆角的路径
 * @param {THREE.Shape} shape
 * @param {THREE.Vector3[]|Object[]} points
 * @param {Boolean} close
 */
export const setShapeWithoutCorners = (shape, points, close) => {
  if (close) {
    const len = points.length;
    shape.moveTo(points[len - 1].x, -points[len - 1].z);
    for (let i = 0; i < len; i++) {
      shape.lineTo(points[i].x, -points[i].z);
    }
  }
};

export const getCurveWithCorners = function(points, radius) {
  const curvePath = new A3D.C.CurvePath();
  const len = points.length;
  setCornerStartAndEnd(len, len - 1, points, radius);
  let end = CORNER_VEC3_END.clone();
  let start;
  for (let i = 0; i < len; i++) {
    setCornerStartAndEnd(len, i, points, radius);
    start = CORNER_VEC3_START.clone();
    if (end) {
      const line = new A3D.C.LineCurve3(end, start);
      curvePath.add(line);
    }
    end = CORNER_VEC3_END.clone();
    const curve = new A3D.C.QuadraticBezierCurve3(
      start,
      points[i],
      end,
    );
    curvePath.add(curve);
  }
  return curvePath;
};
