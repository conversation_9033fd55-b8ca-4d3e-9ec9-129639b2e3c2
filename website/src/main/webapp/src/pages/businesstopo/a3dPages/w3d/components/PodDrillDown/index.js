import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {
  getGroundOptions, getTextPlane, getTextToTex, getTypeItem, getWallLine,
} from '../utils';
import ExtrudeShape from '../common/ExtrudeShape';
import {getObjectSize, getUrlParam} from '../common/utils';

const A3D = getA3D();

const nameColorNormal = '#F5F5F5';
const nameColorSelect = '#2E94FF';
const nameColorUnselect = '#BBBBBB';
const showNameCount = 8;
const maxBlockCount = 2;

export default class SitesDrillDown extends A3D.C.Object3D {
  #data;

  #size;

  #modelMap;

  #matMap;

  #w3d;

  #animation;

  #sizeSetting = {};

  #minRowCount = 5;

  #rowRatio = 8;

  #maxPodRowCount = 24;

  #appScaleRatio = 0.6;

  #podScaleRatio = 0.6;

  #vmScaleRatio = 0.6;

  #appDefaultScale = 1;

  #podDefaultScale = 1;

  #vmDefaultScale = 1;

  #meshMaxSize = 1;

  #appMap = {};

  #podMap = {};

  #vmMap = {};

  #cardMap = {};

  #selectedAppId;

  #selectedId;

  #relationId = [];

  #isGray = false;

  #isPiu = false;

  constructor(data, size, modelMap, matMap, w3d) {
    super();
    this.#data = data;
    this.#isPiu = data.isAgent ?? false;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#w3d = w3d;
    this.#animation = new A3D.Animation(this.#w3d.agent);

    const {width, depth} = this.#size;
    const offsetY = depth * 0.15;
    this.#sizeSetting.groundHeight = width * 0.008;
    this.#sizeSetting.appWidth = width;
    this.#sizeSetting.appDepth = depth;
    this.#sizeSetting.podWidth = width;
    this.#sizeSetting.podDepth = depth;
    this.#sizeSetting.vmWidth = width * 0.9;
    this.#sizeSetting.vmDepth = depth * 0.5;
    this.#sizeSetting.appPosY = offsetY;
    this.#sizeSetting.appPosZ = -(this.#sizeSetting.appDepth + this.#sizeSetting.podDepth) / 2;
    this.#sizeSetting.podPosY = 0;
    this.#sizeSetting.podPosZ = 0;
    this.#sizeSetting.vmPosY = -offsetY;
    this.#sizeSetting.vmPosZ = (this.#sizeSetting.vmDepth + this.#sizeSetting.podDepth) / 2;

    this.#meshMaxSize = width * 0.04;

    this.#sizeSetting.nameSize = depth * 0.07;
    this.#sizeSetting.space = depth * 0.025;
    this.#sizeSetting.spriteNameSize = width * 0.012;
    this.#sizeSetting.spriteNameSize2 = 0.012;

    this.#init();
    if (getUrlParam('isSearch') && getUrlParam('jumpId')) {
      let jumpId = parseInt(getUrlParam('jumpId'));
      let meshValue;
      if (this.#appMap[jumpId]) {
        meshValue = this.#appMap[jumpId].mesh;
      }
      if (this.#podMap[jumpId]) {
        meshValue = this.#podMap[jumpId].mesh;
      }
      if (this.#vmMap[jumpId]) {
        meshValue = this.#vmMap[jumpId].mesh;
      }
      setTimeout(() => {
        A3D.DOM.trigger(w3d.agent, 'click', {
          mesh: meshValue,
        });
      }, 2000);
    }
  }

  hoverIn(object3D, appCallback, podCallback, vmCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#appMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, isSelected);
      }
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedId === podId;
      const data = this.#podMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, isSelected);
      }
    }
    if (vmItem) {
      const vmId = vmItem.userData.id;
      const isSelected = this.#selectedId === vmId;
      const data = this.#vmMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, vmItem, isSelected);
      }
    }
  }

  hoverOut(object3D, appCallback, podCallback, vmCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#appMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, isSelected);
      }
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedId === podId;
      const data = this.#podMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, isSelected);
      }
    }
    if (vmItem) {
      const vmId = vmItem.userData.id;
      const isSelected = this.#selectedId === vmId;
      const data = this.#vmMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, vmItem, isSelected);
      }
    }
  }

  select(object3D, appCallback, podCallback, vmCallback, otherCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (appItem) {
      const appId = appItem.userData.id;
      this.#selectApp(appId);
      if (this.#data.applicationType === 3) {
        this.#selectModels(appId, []);
      } else {
        this.#selectModels('', []);
      }
      const data = this.#appMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, this.#isGray);
      }
    } else if (podItem) {
      const podId = podItem.userData.id;
      this.#selectApp('');
      this.#selectModels(podId, []);
      const data = this.#podMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, this.#isGray);
      }
    } else if (vmItem) {
      const vmId = vmItem.userData.id;
      this.#selectApp('');
      this.#selectModels(vmId, []);
      const data = this.#vmMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, vmItem, this.#isGray);
      }
    } else {
      this.#selectApp('');
      this.#selectModels('', []);
      if (otherCallback) {
        otherCallback(this.#isGray);
      }
    }
  }

  drillDown(object3D, callback) {
    const vmItem = getTypeItem(object3D, 'vm');
    if (vmItem) {
      const vmId = vmItem.userData.id;
      const data = this.#vmMap[vmId].data;
      if (callback) {
        callback(vmItem, data);
      }
    }
  }

  update(data) {
    this.#clear();
    this.#data = data;
    this.#init();
  }

  updateStatus(data) {
    this.#clear();
    this.#updateStatusData(data);

    this.#initContainer();

    this.#initApp();
    if (this.#data.applicationType === 4) {
      return;
    }
    if (this.#data.applicationType !== 3) {
      this.#initPods();
    }
    this.#initVm();
  }

  getDefaultSelected() {
    const selectAppId = this.#selectedAppId;
    if (selectAppId) {
      return this.#appMap[selectAppId].mesh?.children[0];
    }
    let defaultAppId = this.#data.moTypeList[0]?.dnId;
    if (this.#isPiu) {
      const alarmAppId = this.#data.moTypeList.find(appItem => (
        (appItem.alarmStatus === 1) ||
        (appItem.podList.findIndex(podItem => podItem.alarmStatus === 1) > -1) ||
        (appItem.vmList.findIndex(vmItem => vmItem.alarmStatus === 1) > -1)
      ))?.dnId;
      if (alarmAppId) {
        defaultAppId = alarmAppId;
      }
    }
    if (this.#isGray && defaultAppId) {
      return this.#appMap[defaultAppId].mesh?.children[0];
    }
    return null;
  }

  getClickSelected() {
    const selectedId = this.#selectedId;
    const pod = this.#podMap[selectedId]?.mesh?.children[0];
    const vm = this.#vmMap[selectedId]?.mesh?.children[0];
    return pod || vm || null;
  }

  getIsGray() {
    return this.#isGray;
  }

  updateSelected(data) {
    this.#selectModels(data.dnId, data.relation);
  }

  getCardMap() {
    return this.#cardMap;
  }

  #clear() {
    this.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material.dispose();
      }
    });
    this.remove(...this.children);
  }

  #updateStatusData(data) {
    this.#data = data;

    this.#initData();
    if (!this.#appMap[this.#selectedAppId]) {
      this.#selectedAppId = '';
    }
    if (!(this.#podMap[this.#selectedId] || this.#vmMap[this.#selectedId])) {
      this.#selectedId = '';
    }
  }

  #selectApp(appId) {
    if (!appId && this.#isGray) {
      return;
    }
    this.#selectedAppId = appId;
    Object.keys(this.#appMap).forEach((id) => {
      this.#setAppStatus(id);
    });
  }

  #selectModels(selectId, relationId = []) {
    this.#selectedId = selectId;
    this.#relationId = relationId;

    Object.keys(this.#appMap).forEach((id) => {
      const block = this.#appMap[id].blockMesh;
      if (block?.children[1]) {
        if (selectId) {
          block.children[1].children[0].material = this.#matMap.blockGroundSelect;
          block.children[2].children[0].material = this.#matMap.blockGroundLineSelect;
        } else {
          if (id === this.#selectedAppId) {
            block.children[1].children[0].material = this.#matMap.blockGroundSelect;
            block.children[2].children[0].material = this.#matMap.blockGroundLineSelect;
          } else {
            block.children[1].children[0].material = this.#matMap.blockGround;
            block.children[2].children[0].material = this.#matMap.blockGroundLine;
          }
        }
      }
      this.#setRelatedAppStatus(id);
    });
    Object.keys(this.#podMap).forEach((id) => {
      let mesh = this.#podMap[id].mesh;
      if (mesh) {
        if (selectId === id || relationId.includes(id)) {
          mesh.userData.isSelected = true;
        } else {
          mesh.userData.isSelected = false;
        }
        this.#setPodStatus(id);
      }
    });

    Object.keys(this.#vmMap).forEach((id) => {
      const mesh = this.#vmMap[id].mesh;
      if (selectId === id || relationId.includes(id)) {
        mesh.userData.isSelected = true;
      } else {
        mesh.userData.isSelected = false;
      }
      this.#setVmStatus(id);
    });
  }

  /** 初始化布局-start */
  #init() {
    this.#initData();
    this.#selectedAppId = '';
    this.#selectedId = '';
    this.#initContainer();

    this.#initApp();
    if (this.#data.applicationType === 4) {
      return;
    }
    if (this.#data.applicationType !== 3) {
      this.#initPods();
    }
    this.#initVm();
  }

  #initData() {
    const oldAppMap = this.#appMap;
    const oldPodMap = this.#podMap;
    const oldVmMap = this.#vmMap;

    this.#appMap = {};
    this.#podMap = {};
    this.#vmMap = {};

    this.#data.moTypeList.forEach((moType) => {
      this.#appMap[moType.dnId] = {data: moType};
      if (oldAppMap[moType.dnId]) {
        this.#appMap[moType.dnId].mesh = oldAppMap[moType.dnId].mesh;
      }
      moType.podList.forEach((pod) => {
        this.#podMap[pod.dnId] = {data: pod, appId: moType.dnId};
        if (oldPodMap[pod.dnId]) {
          this.#podMap[pod.dnId].mesh = oldPodMap[pod.dnId].mesh;
        }
      });
      moType.vmList.forEach((vm) => {
        if (this.#data.applicationType === 3) {
          this.#podMap[vm.dnId] = {data: vm, appId: moType.dnId};
          if (oldPodMap[vm.dnId]) {
            this.#podMap[vm.dnId].mesh = oldPodMap[vm.dnId].mesh;
          }
        }
      });
    });
    this.#data.vmList.forEach((vm) => {
      this.#vmMap[vm.dnId] = {data: vm};
      if (oldVmMap[vm.dnId]) {
        this.#vmMap[vm.dnId].mesh = oldVmMap[vm.dnId].mesh;
      }
    });
  }

  #initContainer() {
    const container = new A3D.C.Group();
    container.name = 'container';
    container.position.z = this.#size.offset;
    const lineGroup = new A3D.C.Group();
    lineGroup.name = 'lineGroup';
    this.add(container, lineGroup);
  }

  #initApp() {
    const appGroup = new A3D.C.Group();
    appGroup.name = 'appGroup';
    this.children[0].add(appGroup);

    const {
      appWidth, appDepth, appPosY, appPosZ,
    } = this.#sizeSetting;
    if (this.#data.applicationType === 3) {
      appGroup.position.set(0, appPosY, -10);
    } else {
      appGroup.position.set(0, appPosY, appPosZ);
    }
    this.#initGround(appGroup, appWidth, appDepth);
    this.#initAppList(appGroup);
    this.#initGroundName(appGroup, appWidth, 'app');
    this.#cardMap.app.position.x -= this.#size.width * 0.12;
  }

  #initPods() {
    const podGroup = new A3D.C.Group();
    podGroup.name = 'podGroup';
    this.children[0].add(podGroup);

    const {
      podWidth, podDepth, podPosY, podPosZ,
    } = this.#sizeSetting;
    podGroup.position.set(0, podPosY, podPosZ);
    this.#initGround(podGroup, podWidth, podDepth);
    this.#initPodList(podGroup);

    this.#initGroundName(podGroup, podWidth, 'pod');
  }

  #initVm() {
    const vmGroup = new A3D.C.Group();
    vmGroup.name = 'podGroup';
    this.children[0].add(vmGroup);

    let {
      vmWidth, vmDepth, vmPosY, vmPosZ,
    } = this.#sizeSetting;
    if (this.#data.applicationType !== 3) {
      vmGroup.position.set(0, vmPosY, vmPosZ);
    } else {
      vmGroup.position.set(0, vmPosY, vmPosZ - 8);
    }
    if (this.#data.applicationType === 3) {
      vmWidth = vmWidth * 1.2;
      vmDepth = vmDepth * 1.2;
    }
    this.#initGround(vmGroup, vmWidth, vmDepth);
    this.#initVmList(vmGroup);
    this.#initGroundName(vmGroup, vmWidth, 'vm');
  }

  /**
   * 创建站点地板
   * @returns
   */
  #initGround(parent, width, depth) {
    const {groundHeight} = this.#sizeSetting;
    const options = getGroundOptions(width, depth);
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = this.#matMap.ground;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'appGround';
    ground.position.y -= groundHeight;
    parent.add(ground);
  }

  #initGroundName(parent, width, type) {
    const detailGroup = new A3D.C.Group();
    detailGroup.position.x = -(width * 0.5 + this.#size.width * 0.06);
    parent.add(detailGroup);

    this.#cardMap[type] = detailGroup;
  }

  /** 创建app内容 */
  #initAppList(appGroup) {
    const data = this.#data.moTypeList;
    const {appWidth, appDepth} = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, appWidth, appDepth);

    const {perWidth, perDepth} = layoutData;
    this.#appDefaultScale = Math.min(Math.min(perDepth, perWidth) * this.#appScaleRatio, this.#meshMaxSize);

    const appListGroup = this.#drawAppList(layoutData, data);
    appGroup.add(appListGroup);
  }

  #drawAppList(layoutData, data) {
    const appListGroup = new A3D.C.Group();
    appListGroup.name = 'appListGroup';

    const posArr = this.#getLayoutItemPosition(layoutData);

    const isShowName = data.length <= showNameCount;
    data.forEach((itemData, index) => {
      this.#initAppItem(itemData, posArr[index], appListGroup, isShowName);
    });
    return appListGroup;
  }

  #initAppItem(itemData, position, container, isShowName) {
    let appItem;
    if (this.#appMap[itemData.dnId].mesh) {
      appItem = this.#appMap[itemData.dnId].mesh;
    } else {
      appItem = new A3D.C.Group();
      const appGeo = this.#modelMap.app.geometry;
      const appMat = this.#matMap.app.normal;
      const appMesh = new A3D.C.Mesh(appGeo, appMat);
      appItem.add(appMesh);
    }
    appItem.children[0].scale.set(this.#appDefaultScale, this.#appDefaultScale, this.#appDefaultScale);
    appItem.userData.isShowName = isShowName;
    appItem.userData.isSelected = itemData.dnId === this.#selectedAppId;
    const nameMesh = appItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getAppSprite(itemData, appItem.children[0]);
      appItem.add(sprite);
    }
    appItem.position.copy(position);
    container.add(appItem);

    this.#clickable(appItem, itemData.dnId, 'app');
    this.#setAppStatus(itemData.dnId);
  }

  #getAppSprite(itemData, mesh) {
    const cloneMesh = mesh.clone();
    cloneMesh.remove(...cloneMesh.children);
    const modelSize = getObjectSize(cloneMesh);
    const textArr = [itemData.name];
    if (this.#data.applicationType !== 4) {
      textArr.push(`${this.#data.i18n.version}: ${itemData.version}`);
    } else {
      textArr.push('');
    }
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    const nameSize = spriteNameSize * 2;
    sprite.position.z = modelSize.depth * 2 + nameSize * 0.5;
    sprite.position.y = nameSize * 0.4;

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setAppStatus(id) {
    const appSetting = this.#appMap[id];
    const isSelected = this.#selectedAppId === id;

    if (this.#selectedAppId) {
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = isSelected ? this.#matMap.app.alarm : this.#matMap.app.alarmUnselect;
      } else {
        appSetting.mesh.children[0].material = isSelected ? this.#matMap.app.normal : this.#matMap.app.normalUnselect;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = this.#matMap.app.alarm;
      } else {
        appSetting.mesh.children[0].material = this.#matMap.app.normal;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(nameColorNormal);
      }
    }

    this.#setBlockStatus(id, isSelected);
  }

  #setBlockStatus(id, isSelected) {
    const block = this.#appMap[id].blockMesh;
    if (!block) {
      return;
    }
    if (isSelected) {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = true;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGroundSelect;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLineSelect;
      }
    } else {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = false;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGround;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLine;
      }
    }
  }

  #setRelatedAppStatus(id) {
    if (this.#isGray || this.#selectedAppId) {
      return;
    }
    const appSetting = this.#appMap[id];
    if (this.#selectedId) {
      const podItem = this.#podMap[this.#selectedId];
      const isRelated = podItem && podItem.appId === id;
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = isRelated ? this.#matMap.app.alarm : this.#matMap.app.alarmUnselect;
      } else {
        appSetting.mesh.children[0].material = isRelated ? this.#matMap.app.normal : this.#matMap.app.normalUnselect;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isRelated ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = this.#matMap.app.alarm;
      } else {
        appSetting.mesh.children[0].material = this.#matMap.app.normal;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(nameColorNormal);
      }
    }
  }

  /** 创建vm内容 */
  #initVmList(vmGroup) {
    const data = this.#data.vmList;
    const {vmWidth, vmDepth} = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, vmWidth, vmDepth);

    const {perWidth, perDepth} = layoutData;
    this.#vmDefaultScale = Math.min(Math.min(perDepth, perWidth) * this.#vmScaleRatio, this.#meshMaxSize);

    const vmListGroup = this.#drawVmList(layoutData, data);
    vmGroup.add(vmListGroup);
  }

  #drawVmList(layoutData, data) {
    const vmListGroup = new A3D.C.Group();
    vmListGroup.name = 'vmListGroup';

    const posArr = this.#getLayoutItemPosition(layoutData);

    const isShowName = data.length <= showNameCount;
    data.forEach((itemData, index) => {
      this.#initVmItem(itemData, posArr[index], vmListGroup, isShowName);
    });
    return vmListGroup;
  }

  #initVmItem(itemData, position, container, isShowName) {
    let vmItem;
    if (this.#vmMap[itemData.dnId].mesh) {
      vmItem = this.#vmMap[itemData.dnId].mesh;
    } else {
      vmItem = new A3D.C.Group();
      const vmGeo = this.#modelMap.vm.geometry;
      const vmMat = this.#matMap.vm.normal;
      const vmMesh = new A3D.C.Mesh(vmGeo, vmMat);
      vmItem.add(vmMesh);
    }
    vmItem.children[0].scale.set(this.#vmDefaultScale, this.#vmDefaultScale, this.#vmDefaultScale);
    vmItem.userData.isShowName = isShowName;
    if (this.#selectedId === itemData.dnId || this.#relationId.includes(itemData.dnId)) {
      vmItem.userData.isSelected = true;
    } else {
      vmItem.userData.isSelected = false;
    }
    const nameMesh = vmItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getVmSprite(itemData, vmItem.children[0]);
      vmItem.add(sprite);
    }

    vmItem.position.copy(position);
    container.add(vmItem);

    this.#clickable(vmItem, itemData.dnId, 'vm');
    this.#setVmStatus(itemData.dnId);
  }

  #getVmSprite(itemData, mesh) {
    const cloneMesh = mesh.clone();
    cloneMesh.remove(...cloneMesh.children);
    const modelSize = getObjectSize(cloneMesh);
    const textArr = [itemData.name];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth * 1.2 + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setVmStatus(id) {
    const vmSetting = this.#vmMap[id];
    let isSelected = vmSetting.mesh.userData.isSelected;
    if (this.#data.applicationType === 3 && this.#selectedId) {
      let vm = this.#podMap[id];
      isSelected = (this.#selectedId === vm.appId || this.#selectedId === vm.data.dnId);
    }

    if (this.#selectedId) {
      if (vmSetting.data.alarmStatus === 1) {
        vmSetting.mesh.children[0].material = isSelected ? this.#matMap.vm.alarm : this.#matMap.vm.alarmUnselect;
      } else {
        vmSetting.mesh.children[0].material = isSelected ? this.#matMap.vm.normal : this.#matMap.vm.normalUnselect;
      }
      const nameMesh = vmSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (vmSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      const isNotRelated = !this.#isGray && this.#selectedAppId;
      if (vmSetting.data.alarmStatus === 1) {
        vmSetting.mesh.children[0].material = isNotRelated ? this.#matMap.vm.alarmUnselect : this.#matMap.vm.alarm;
      } else {
        vmSetting.mesh.children[0].material = isNotRelated ? this.#matMap.vm.normalUnselect : this.#matMap.vm.normal;
      }
      const nameMesh = vmSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (vmSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isNotRelated ? nameColorUnselect : nameColorNormal;
        nameMesh.material.color.set(nameColor);
      }
    }
  }

  /** 创建pod内容 */
  #initPodList(podGroup) {
    const listLength = this.#data.moTypeList.length;
    let layoutData;
    if (listLength <= maxBlockCount) {
      layoutData = this.#getPodLayoutData();
      this.#resetPodLayout(layoutData);
    } else {
      layoutData = this.#getPodLayoutData2();
      this.#resetPodLayout2(layoutData);
    }
    const {perWidth, perDepth} = layoutData;
    this.#podDefaultScale = Math.min(Math.min(perDepth, perWidth) * this.#podScaleRatio, this.#meshMaxSize);

    let podListGroup;
    if (this.#isGray) {
      podListGroup = this.#drawPodList(layoutData);
    } else {
      podListGroup = this.#drawPodList1(layoutData);
    }
    podGroup.add(podListGroup);
  }

  #getPodLayoutData() {
    let allCount = 0;
    this.#data.moTypeList.forEach((moType) => {
      moType.count = moType.podList.length;
      const minCount = Math.max(moType.count, 1);
      allCount += minCount;
    });
    let col = 0;
    let row = 1;
    if (allCount <= this.#maxPodRowCount) {
      col = allCount;
    } else {
      col = this.#maxPodRowCount;
      row = Math.ceil(allCount / col);
      if (col < row * 4) {
        col = Math.ceil(Math.sqrt(allCount / 4)) * 4;
        row = Math.ceil(allCount / col);
      }
    }
    col = 0;
    const layout = this.#data.moTypeList.map((moType) => {
      const curRow = row;
      const minCount = Math.max(moType.count, 1);
      let curCol = allCount > this.#maxPodRowCount ? Math.floor(minCount / allCount * this.#maxPodRowCount) : minCount;
      while (curCol === 0 || (curRow * curCol < moType.count)) {
        curCol += 1;
      }
      col += curCol;
      const result = {
        row: curRow,
        col: curCol,
        data: moType,
        level1: [],
      };
      let stayCount = minCount;
      while (stayCount > 0) {
        result.level1.push(Math.min(stayCount, curCol));
        stayCount -= curCol;
      }
      return result;
    });
    return {
      row, col, data: this.#data, children: layout,
    };
  }

  #resetPodLayout(layoutData) {
    const {
      podWidth, podDepth, nameSize, space,
    } = this.#sizeSetting;
    const blockCount = layoutData.children.length;
    const leftWidth = podWidth - (blockCount * 3 + 1) * space;
    const isGray = layoutData.data.moTypeList[0]?.isGray;
    this.#isGray = isGray === 0 || isGray === 1;
    const nameSpace = this.#isGray ? nameSize + space : 0;
    const leftDepth = podDepth - (nameSpace + 4 * space);
    const perWidth = leftWidth / layoutData.col;
    const perDepth = Math.min(leftDepth / layoutData.row, perWidth * 1.4);

    layoutData.perWidth = perWidth;
    layoutData.perDepth = perDepth;
    layoutData.position = {x: 0, y: 0, z: nameSpace / 2};

    let lastX = -podWidth / 2 + space;
    layoutData.children.forEach((child) => {
      child.size = {width: perWidth * child.col + space * 2, depth: leftDepth + space * 2};
      child.position = {x: lastX, y: 0, z: 0};
      lastX += child.size.width + space;
    });
    return layoutData;
  }

  #getPodLayoutData2() {
    let allCount = 0;
    this.#data.moTypeList.forEach((moType) => {
      moType.count = moType.podList.length;
      allCount += moType.count;
    });
    let col = 0;
    let row = 1;
    if (allCount <= this.#minRowCount) {
      col = allCount;
    } else {
      col = Math.ceil(Math.sqrt(allCount / this.#rowRatio)) * this.#rowRatio;
      row = Math.ceil(allCount / col);
    }

    const layout = this.#data.moTypeList.map((moType) => {
      const result = {
        data: moType,
        level1: [],
      };
      return result;
    });
    return {
      row, col, data: this.#data, children: layout,
    };
  }

  #resetPodLayout2(layoutData) {
    this.#isGray = false;
    const {podWidth, podDepth, space} = this.#sizeSetting;
    const leftWidth = podWidth - 2 * space;
    const leftDepth = podDepth - 2 * space;
    const perWidth = leftWidth / layoutData.col;
    const perDepth = Math.min(leftDepth / layoutData.row, perWidth * 1.4);

    layoutData.perWidth = perWidth;
    layoutData.perDepth = perDepth;
    layoutData.position = {x: 0, y: 0, z: 0};
    return layoutData;
  }

  #drawPodList(layoutData) {
    const podListGroup = new A3D.C.Group();
    podListGroup.name = 'podListGroup';
    podListGroup.position.copy(layoutData.position);
    const isShowName = layoutData.data.podCount <= showNameCount;
    layoutData.children.forEach((block) => {
      const blockGroup = new A3D.C.Group();
      blockGroup.name = 'blockGroup';
      blockGroup.position.copy(block.position);
      blockGroup.position.x += block.size.width / 2;
      podListGroup.add(blockGroup);
      const {width, depth} = block.size;
      this.#initBlockPodItems(block.data, layoutData, block, blockGroup, isShowName);

      this.#initBlockGround(blockGroup, width, depth, block);
      this.#initBlockLine(blockGroup, width, depth, block);
      this.#initBlockName(blockGroup, width, depth, block, this.#isGray);

      const appId = block.data.dnId;
      const app = this.#appMap[appId];
      app.blockMesh = blockGroup;
    });
    return podListGroup;
  }

  #drawPodList1(layoutData) {
    const podListGroup = new A3D.C.Group();
    podListGroup.name = 'podListGroup';
    podListGroup.position.copy(layoutData.position);
    const isShowName = layoutData.data.podCount <= showNameCount;
    const podsPosition = this.#getPodsPosition2(layoutData);
    const podIndex = {value: 0};
    layoutData.children.forEach((block) => {
      const blockGroup = new A3D.C.Group();
      blockGroup.name = 'blockGroup';
      podListGroup.add(blockGroup);

      this.#initBlockPodItems2(block.data, podsPosition, podIndex, blockGroup, isShowName);

      const appId = block.data.dnId;
      const app = this.#appMap[appId];
      app.blockMesh = blockGroup;
    });
    return podListGroup;
  }

  #initBlockGround(parent, width, depth, block) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    let groundMat;

    if (this.#selectedId) {
      groundMat = this.#matMap.blockGroundSelect;
    } else {
      if (block.data.dnId === this.#selectedAppId) {
        groundMat = this.#matMap.blockGroundSelect;
      } else {
        groundMat = this.#matMap.blockGround;
      }
    }

    const ground = new ExtrudeShape(options, groundMat);
    ground.children[0].renderOrder = 1;
    ground.name = 'blockGround';
    parent.add(ground);
  }

  #initBlockLine(parent, width, depth, block) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    options.close = true;
    let lineMat = this.#matMap.blockGroundLine;

    if (this.#selectedId) {
      lineMat = this.#matMap.blockGroundLineSelect;
    } else {
      if (block.data.dnId === this.#selectedAppId) {
        lineMat = this.#matMap.blockGroundLineSelect;
      } else {
        lineMat = this.#matMap.blockGroundLine;
      }
    }

    const line = getWallLine(lineMat, options);
    line.children[0].renderOrder = 1;
    line.name = 'blockLine';
    parent.add(line);
  }

  #initBlockName(parent, width, depth, blockData, isBlockName) {
    const {nameSize, space} = this.#sizeSetting;
    let name = '';
    if (isBlockName) {
      if (blockData.data.isGray === 1) {
        name = this.#data.i18n.grayArea;
      } else if (blockData.data.isGray === 0) {
        name = this.#data.i18n.productionArea;
      }
    }
    const nameCount = 20;
    const plane = getTextPlane({
      x: nameSize * nameCount,
      y: nameSize,
    }, name, {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
    });
    const x = -(width - nameSize * nameCount) * 0.5;
    const z = -(depth + nameSize) * 0.5 - space;
    plane.position.set(x, 0, z);
    plane.renderOrder = 1;
    parent.add(plane);
  }

  #initBlockPodItems(blockData, layoutData, blockLayout, blockGroup, isShowName) {
    const blockPodsGroup = new A3D.C.Group();
    blockGroup.add(blockPodsGroup);

    const podGeo = this.#modelMap.pod.geometry;
    const podMat = this.#matMap.pod.normal;
    const podMesh = new A3D.C.Mesh(podGeo, podMat);

    const podsPosition = this.#getPodsPosition(layoutData, blockLayout);
    blockData.podList.forEach((itemData, index) => {
      let podItem;
      if (this.#podMap[itemData.dnId].mesh) {
        podItem = this.#podMap[itemData.dnId].mesh;
      } else {
        podItem = new A3D.C.Group();
        podItem.add(podMesh.clone());
      }
      podItem.children[0].scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
      podItem.userData.isShowName = isShowName;
      if (this.#selectedId === itemData.dnId || this.#relationId.includes(itemData.dnId)) {
        podItem.userData.isSelected = true;
      } else {
        podItem.userData.isSelected = false;
      }
      podItem.userData.isBlockSelected = this.#selectedAppId === blockData.dnId;

      const nameMesh = podItem.getObjectByProperty('type', 'Sprite');
      if (nameMesh) {
        nameMesh.parent.remove(nameMesh);
      }
      if (isShowName) {
        const sprite = this.#getPodSprite(itemData, podMesh);
        podItem.add(sprite);
      }

      podItem.position.copy(podsPosition[index]);
      blockPodsGroup.add(podItem);

      this.#clickable(podItem, itemData.dnId, 'pod');
      this.#setPodStatus(itemData.dnId);
    });
  }

  #initBlockPodItems2(blockData, podsPosition, podIndex, blockGroup, isShowName) {
    const blockPodsGroup = new A3D.C.Group();
    blockGroup.add(blockPodsGroup);

    const podGeo = this.#modelMap.pod.geometry;
    const podMat = this.#matMap.pod.normal;
    const podMesh = new A3D.C.Mesh(podGeo, podMat);

    blockData.podList.forEach((itemData) => {
      let podItem;
      if (this.#podMap[itemData.dnId].mesh) {
        podItem = this.#podMap[itemData.dnId].mesh;
      } else {
        podItem = new A3D.C.Group();
        podItem.add(podMesh.clone());
      }
      podItem.children[0].scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
      podItem.userData.isShowName = isShowName;
      podItem.position.copy(podsPosition[podIndex.value++]);
      blockPodsGroup.add(podItem);

      if (this.#selectedId === itemData.dnId || this.#relationId.includes(itemData.dnId)) {
        podItem.userData.isSelected = true;
      } else {
        podItem.userData.isSelected = false;
      }
      podItem.userData.isBlockSelected = this.#selectedAppId === blockData.dnId;

      const nameMesh = podItem.getObjectByProperty('type', 'Sprite');
      if (nameMesh) {
        nameMesh.parent.remove(nameMesh);
      }
      if (isShowName) {
        const sprite = this.#getPodSprite(itemData, podMesh);
        podItem.add(sprite);
      }

      this.#clickable(podItem, itemData.dnId, 'pod');
      this.#setPodStatus(itemData.dnId);
    });
  }

  #getPodSprite(itemData, mesh) {
    const modelSize = getObjectSize(mesh);
    const textArr = [itemData.name];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth * 2 + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #getPodsPosition(layoutData, blockLayout) {
    const {perWidth, perDepth} = layoutData;
    const col = blockLayout.level1[0];
    const row = blockLayout.level1.length;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getPodsPosition2(layoutData) {
    const {
      perWidth, perDepth, row, col,
    } = layoutData;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #setPodStatus(id) {
    const podSetting = this.#podMap[id];
    const isBlockSelected = podSetting.mesh.userData.isBlockSelected || !this.#selectedAppId;
    const isSelected = podSetting.mesh.userData.isSelected;
    const mesh = podSetting.mesh.children[0];
    if (this.#selectedId) {
      if (podSetting.data.alarmStatus === 1) {
        mesh.material = isSelected ? this.#matMap.pod.alarm : this.#matMap.pod.alarmUnselect;
      } else if (podSetting.data.isGray === 1) {
        mesh.material = isSelected ? this.#matMap.pod.gray : this.#matMap.pod.grayUnselect;
      } else {
        mesh.material = isSelected ? this.#matMap.pod.normal : this.#matMap.pod.normalUnselect;
      }
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      if (podSetting.data.alarmStatus === 1) {
        mesh.material = isBlockSelected ? this.#matMap.pod.alarm : this.#matMap.pod.alarmUnselect;
      } else if (podSetting.data.isGray === 1) {
        mesh.material = isBlockSelected ? this.#matMap.pod.gray : this.#matMap.pod.grayUnselect;
      } else {
        mesh.material = isBlockSelected ? this.#matMap.pod.normal : this.#matMap.pod.normalUnselect;
      }
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = this.#isGray ? nameColorNormal : this.#getNoGrayPodNameColor(podSetting);
        nameMesh.material.color.set(nameColor);
      }
    }
  }

  #getNoGrayPodNameColor(podSetting) {
    const selectedColor = podSetting.mesh.userData.isBlockSelected ? nameColorSelect : nameColorUnselect;
    const noGrayNameColor = this.#selectedAppId ? selectedColor : nameColorNormal;
    return noGrayNameColor;
  }

  /** 创建内容通用方法 */
  #getLayoutData(data, width, depth) {
    const appCount = data.length;
    const {space} = this.#sizeSetting;
    const layout = {
      perWidth: 0, perDepth: depth, count: appCount, level1: [],
    };
    let col = 0;
    let row = 1;
    if (appCount <= this.#minRowCount) {
      col = this.#minRowCount;
      layout.perWidth = (width - space * 2) / col;
    } else {
      col = Math.ceil(Math.sqrt(appCount / this.#rowRatio)) * this.#rowRatio;
      row = Math.ceil(appCount / col);
      layout.perWidth = (width - space * 2) / col;
      layout.perDepth = (depth - space * 2) / row;
    }
    let stayCount = appCount;
    while (stayCount > 0) {
      layout.level1.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    return layout;
  }

  #getLayoutItemPosition(layoutData) {
    const {perWidth, perDepth} = layoutData;
    const col = layoutData.level1[0];
    const row = layoutData.level1.length;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getCanvasTex(text, nameCount = 12, extendSetting = {}) {
    const setting = {
      pixelRatio: 4,
      text,
      color: '#ffffff',
      'text-align': 'center',
      'font-size': 18,
      'font-weight': 'bold',
      'line-height': 22.5,
      maxWidth: 18 * nameCount,

      ...extendSetting,
    };
    return getTextToTex(setting);
  }

  #clickable(mesh, id, type) {
    mesh.userData[type] = true;
    mesh.userData.id = id;
    mesh.userData._type = type;
    if (type === 'app') {
      this.#appMap[id].mesh = mesh;
    } else if (type === 'pod') {
      this.#podMap[id].mesh = mesh;
    } else if (type === 'vm') {
      this.#vmMap[id].mesh = mesh;
    }
  }
}
