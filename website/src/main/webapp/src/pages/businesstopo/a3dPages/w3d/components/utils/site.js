import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import Wall from '../common/Wall';

const A3D = getA3D();

const defaultOptions = {
  depth: 0.01,
  radius: 0.2,
  radiusSegments: 32,
};

export const getGroundOptions = (width, depth) => {
  const halfWidth = width / 2;
  const halfDepth = depth / 2;
  const path = [
    { x: -halfWidth, y: 0, z: halfDepth },
    { x: halfWidth, y: 0, z: halfDepth },
    { x: halfWidth, y: 0, z: -halfDepth },
    { x: -halfWidth, y: 0, z: -halfDepth },
  ];
  const options = {
    path,
    ...defaultOptions,
  };
  return options;
};

export const getLineOptions = (path) => {
  const options = {
    path,
    close: false,
    ...defaultOptions,
  };
  return options;
};

export const getWallLine = (material, options) => {
  const line = new Wall(material, options);
  line.add({
    height: 0.002,
    width: 0.05,
    ...options,
  });
  return line;
};

const fittingString = (ctx, str, maxWidth) => {
  let width = ctx.measureText(str).width;
  const ellipsis = '…';
  const ellipsisWidth = ctx.measureText(ellipsis).width;
  if (width <= maxWidth || width <= ellipsisWidth) {
    return str;
  }
  let len = str.length;
  let finalStr = str;
  while (width >= maxWidth - ellipsisWidth && len-- > 0) {
    finalStr = finalStr.substring(0, len);
    width = ctx.measureText(finalStr).width;
  }
  return finalStr + ellipsis;
};

const setCtx = (ctx, setting) => {
  const pixelRatio = setting.pixelRatio || 1;
  const fontWeight = setting['font-weight'];
  const fontSize = setting['font-size'] * pixelRatio;
  ctx.textAlign = setting['text-align'];
  ctx.textBaseline = 'top';
  ctx.font = `${fontWeight} ${fontSize}px "Arial", "Microsoft YaHei"`;
  ctx.fillStyle = setting.color;
};

const getFinalText = (ctx, setting, _text) => {
  const pixelRatio = setting.pixelRatio || 1;
  let text = _text;
  const maxWidth = setting.maxWidth || setting.width;
  text = maxWidth ? fittingString(ctx, text, maxWidth * pixelRatio) : text;
  return text;
};

const setCanvasSize = (canvas, ctx, setting, textArr) => {
  const pixelRatio = setting.pixelRatio || 1;
  const lineHeight = setting['line-height'] * pixelRatio;
  let width = 0;
  let height;
  if (setting.width && setting.height) {
    width = setting.width * pixelRatio;
    height = setting.height * pixelRatio;
  } else {
    textArr.forEach((text) => {
      const size = ctx.measureText(text);
      width = Math.max(width, size.width);
    });
    height = lineHeight * textArr.length;
  }
  canvas.width = width;
  canvas.height = height;
};

export const getTextCanvas = (setting, canvasInput, resetSize = true, fillText = true) => {
  let canvas = canvasInput;
  if (!canvas) {
    canvas = document.createElement('canvas');
  }
  const ctx = canvas.getContext('2d');
  const pixelRatio = setting.pixelRatio || 1;
  const fontSize = setting['font-size'] * pixelRatio;

  setCtx(ctx, setting);
  const text = getFinalText(ctx, setting, setting.text);
  if (resetSize) {
    setCanvasSize(canvas, ctx, setting, [text]);
  }
  setCtx(ctx, setting);

  if (!fillText) {
    return canvas;
  }

  const HALF = 2;
  const FONT_SPACE = 0.1;
  const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
  if (ctx.textAlign === 'center') {
    ctx.fillText(text, canvas.width / HALF, top);
  } else {
    ctx.fillText(text, 0, top);
  }
  return canvas;
};

export const getTextToTex = (setting) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const pixelRatio = setting.pixelRatio || 1;
  const fontSize = setting['font-size'] * pixelRatio;
  const lineHeight = setting['line-height'] * pixelRatio;

  setCtx(ctx, setting);
  const textArr = Array.isArray(setting.text) ?
    setting.text.map((text) => getFinalText(ctx, setting, text)) : [getFinalText(ctx, setting, setting.text)];
  setCanvasSize(canvas, ctx, setting, textArr);
  setCtx(ctx, setting);

  textArr.forEach((text, index) => {
    const HALF = 2;
    const top = lineHeight * index + (lineHeight - fontSize) / HALF;
    if (ctx.textAlign === 'center') {
      ctx.fillText(text, canvas.width / 2, top);
    } else {
      ctx.fillText(text, 0, top);
    }
  });
  const texture = A3D.canvasToTex(canvas, true);
  texture.userData.detailName = setting.text;
  texture.userData.drawName = Array.isArray(setting.text) ? textArr : textArr[0];
  return texture;
};

export const getCanvasTex = (name, nameCount = 10) => {
  const setting = {
    pixelRatio: 4,
    text: name,
    color: '#ffffff',
    'text-align': 'center',
    'font-size': 18,
    'font-weight': 'bold',
    'line-height': 22.5,
    maxWidth: 18 * nameCount,
  };
  const texture = getTextToTex(setting);
  return texture;
};

export const getTextPlane = (size, name, _setting = {}) => {
  const width = size.x;
  const depth = size.y;
  const geo = new A3D.C.PlaneGeometry(width, depth);
  const mat = new A3D.C.MeshBasicMaterial({
    transparent: true,
    depthWrite: false,
    opacity: 0,
  });
  const plane = new A3D.C.Mesh(geo, mat);
  plane.rotation.x = Math.PI * -0.5;
  const setting = {
    pixelRatio: 4,
    text: name,
    width: width / depth * 20,
    height: 20,
    color: '#fff',
    'text-align': 'left',
    'font-size': 18,
    'font-weight': 'bold',
    'line-height': 22.5,
    ..._setting,
  };
  mat.map = getTextToTex(setting);
  plane.material.opacity = 1;
  return plane;
};
