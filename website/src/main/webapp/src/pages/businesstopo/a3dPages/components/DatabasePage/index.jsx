import '@pages/businesstopo/a3dPages/styles/split.less';
import React, {
  useEffect, useRef, useState, useReducer,
} from 'react';
import { ConfigProvider } from 'eview-ui';
import Tooltip from 'eview-ui/Tooltip';
import FloatPanel from '../common/FloatPanel';
import '@pages/businesstopo/css/sitedrilldown/index.css';
import '@pages/businesstopo/css/index.less';
import Loader from 'eview-ui/Loader';

import {
  destroyW3D, getInitData,
  initW3D,
  resizeW3D,
  updateDataToW3D, updateStatusToW3D,
} from './home3D';

import {BUSINESS_TOPO_CONTEXT, refeshTime} from '../../../const';
import {initState, reducer} from '../../../reducer';
import TimeLinePanel from '@pages/businesstopo/components/timeline/TimeLinePanel';
import {DATABASE_TYPE_HOME, OVER_VIEW} from '@pages/businesstopo/const/timeLine';
import RightPanel from '@pages/businesstopo/a3dPages/components/DatabasePage/RightPanel';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import {getSolutionData} from '@pages/businesstopo/api';
import {setSessionData} from '@pages/businesstopo/util';
import MessageDeatil from '@pages/businesstopo/components/tooltip/messageDeatil';
import {$t, setHelpId} from '@util';
import DivMessage from 'eview-ui/DivMessage';

function DatabasePage() {
  const [state, dispatch] = useReducer(reducer, initState);
  let {solutionId, selectedTime, siteGroup, isTimeTrack} = state;
  const w3dContainerRef = useRef();
  const shardMapRef = useRef();
  const [panelShow, setPanelShow] = useState(true);
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [loading, setLoading] = useState(false);
  const [groupShow, setGroupShow] = useState([]);
  let selecteGroupId = useRef(-1);
  let selecteNode = useRef(-1);
  const intervalId = useRef(0);
  const MAX_GROUP_LENGTH = 4;
  const [showMessage, setShowMessage] = useState(false);
  const initFlagRef = useRef(0);
  setHelpId('com.huawei.dvtopo.business');
  let style2 = {
    marginBottom: '20px',
    position: 'absolute',
    'z-index': '99999',
    right: '1%',
    top: '8%',
    width: '385px',
  };

  function areMapEqual(map1, map2) {
    if (map1.size !== map2.size) {
      return false;
    }

    for (const [key, value] of map1) {
      if (!map2.has(key) || map2.get(key) !== value) {
        return false;
      }
    }
    return true;
  }

  function createIdToShardIdMap(databaseData) {
    const idToShardIdMap = new Map();
    databaseData.data.forEach(item => idToShardIdMap.set(item.id, item.shardId));
    return idToShardIdMap;
  }

  async function changeSiteGroup(groupId) {
    if (selecteGroupId.current !== groupId) {
      selecteGroupId.current = groupId;
      setLoading(true);
      const databaseData = await getInitData({
        solutionId,
        timestamp: selectedTime,
        siteTeamName: groupId,
      },
      );
      const shardIdsSet = createIdToShardIdMap(databaseData);
      shardMapRef.current = shardIdsSet;
      updateDataToW3D(databaseData);
      setLoading(false);
      if (!databaseData.data || databaseData.data.length === 0) {
        setShowMessage(true);
      } else {
        setShowMessage(false);
      }
    }
  }

  const startRefresh = () => {
    setRefreshFlag(new Date().getTime());
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, refeshTime);
    intervalId.current = id;
  };
  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };
  const timeTrack = async time => {
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    if (time === topoSession.selectedTime) {
      return;
    }
    stopRefresh();
    setSessionData({
      selectedTime: time,
      isTimeTrack: true,
    });
    dispatch({selectedTime: time, sitePannelIsOpen: false, showSiteRightPanel: false, isTimeTrack: true});
    let param = {timestamp: time};
    let currentSelectSolutionIndex = 0;
    let solutionData = await getSolutionData(param, res => res);
    if (solutionData.data.length > 0) {
      if (solutionData.data[currentSelectSolutionIndex].displayDatabaseView === 'false') {
        location.replace('/eviewwebsite/index.html#path=/businesstopo');
      }
      dispatch({
        solutionId: solutionData.data[currentSelectSolutionIndex].solutionId,
      });
    } else {
      const nullData = {
        data: [],
        relationship: [],
        id: null,
        sites: [],
        i18n: {},
      };
      updateDataToW3D(nullData);
      setGroupShow([]);
      setShowMessage(true);
      return;
    }

    let initParam = {
      solutionId: solutionData.data[currentSelectSolutionIndex].solutionId,
      timestamp: 0,
    };
    if (selecteGroupId.current !== -1) {
      initParam.siteTeamName = selecteGroupId.current;
    }

    const databaseData = await getInitData(initParam,
    );
    if (!databaseData.data || databaseData.data.length === 0) {
      setGroupShow([]);
      setShowMessage(true);
    } else {
      setShowMessage(false);
    }
    updateDataToW3D(databaseData);
  };
  const backTimeTrack = async() => {
    let currentSelectSolutionIndex = 0;
    setShowMessage(false);
    let param = {timestamp: 0};
    let solutionData = await getSolutionData(param, res => res);
    if (solutionData.data.length > 0) {
      if (solutionData.data[currentSelectSolutionIndex].displayDatabaseView === 'false') {
        location.replace('/eviewwebsite/index.html#path=/businesstopo');
      }
      dispatch({
        solutionId: solutionData.data[currentSelectSolutionIndex].solutionId,
      });
    }
    startRefresh();
    setSessionData({
      selectedTime: 0,
    });
    dispatch({selectedTime: 0, sitePannelIsOpen: false, showSiteRightPanel: false, isTimeTrack: false});

    let initParam = {
      solutionId: solutionData.data[currentSelectSolutionIndex].solutionId,
      timestamp: 0,
    };
    if (selecteGroupId.current !== -1) {
      initParam.siteTeamName = selecteGroupId.current;
    }

    const databaseData = await getInitData(initParam
    );
    updateDataToW3D(databaseData);
    if (!databaseData.data || databaseData.data.length === 0) {
      setGroupShow([]);
      setShowMessage(true);
    } else {
      setShowMessage(false);
    }
  };

  useEffect(async() => {
    if (solutionId) {
      if (isTimeTrack) {
        return;
      }
      setSessionData({
        selectedTime: 0,
      });
      let param = {
        solutionId,
        timestamp: 0,
      };
      if (selecteGroupId.current !== -1) {
        param.siteTeamName = selecteGroupId.current;
      }

      let databaseData = await getInitData(
        param
      );
      const shardIdMap = createIdToShardIdMap(databaseData);
      if (!areMapEqual(shardIdMap, shardMapRef.current)) {
        shardMapRef.current = shardIdMap;
        updateDataToW3D(databaseData);
      } else {
        updateStatusToW3D(databaseData);
      }
    }
  }, [
    refreshFlag,
  ]);
  const siteGroupPosition = {
    1: [{top: 25, left: 35}],
    2: [{top: 25, left: 12}, {top: 25, left: 63}],
    3: [{top: 15, left: 12}, {top: 15, left: 63}, {top: 30, left: 38}],
    4: [{top: 15, left: 12}, {top: 15, left: 63}, {top: 40, left: 12}, {top: 40, left: 63}],
  };

  const events = [
    {
      eventName: 'from3d_showPanel',
      fn: option => {
        const {id, type} = option;
        if (type === 'pod') {
          dispatch({dataBasePannelIsOpen: true, showDataBaseRightPanel: true, podData: {id}});
          selecteNode.current = id;
        }
        if (type === 'site') {
          dispatch({dataBasePannelIsOpen: false, showDataBaseRightPanel: false});
        }
      },
    }, {
      eventName: 'switchDatabase',
      fn: option => {
        dispatch({
          dataBasePannelIsOpen: false, showDataBaseRightPanel: false,
        });
      },
    },
  ];
  useEffect(async() => {
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    let currentSelectSolutionIndex = 0;
    let param = {timestamp: topoSession.selectedTime ? topoSession.selectedTime : 0};
    let solutionData = await getSolutionData(param, res => res);
    if (solutionData.data.length > 0) {
      if (solutionData.data[currentSelectSolutionIndex].displayDatabaseView === 'false') {
        location.replace('/eviewwebsite/index.html#path=/businesstopo');
      }
      dispatch({
        solutionId: solutionData.data[currentSelectSolutionIndex].solutionId,
        selectedTime: topoSession.selectedTime,
        isTimeTrack: topoSession.isTimeTrack,
        selectSolutionIndex: currentSelectSolutionIndex,
      });
    }
    if (solutionData.data[currentSelectSolutionIndex].solutionId) {
      const fetchData = async() => {
        let initData = await initW3D(w3dContainerRef, solutionData.data[currentSelectSolutionIndex].solutionId, dispatch);
        const shardIdMap = createIdToShardIdMap(initData);
        shardMapRef.current = shardIdMap;
        if (initData && initData.hasFiltered && initData.hasFiltered === 1) {
          dispatch({
            hasFiltered: true,
          });
        }
        if (!initData.data || initData.data.length === 0) {
          setShowMessage(true);
        } else {
          setShowMessage(false);
        }
        initFlagRef.current = 1;
        if (!state.isTimeTrack) {
          setTimeout(() => {
            startRefresh();
          }, refeshTime);
        }
      };
      fetchData();
    }
  }, []);

  useEffect(() => {
    events.forEach(item => {
      eventBus.addListener(item.eventName, item.fn);
    });
    return () => {
      events.forEach(item => {
        eventBus.removeListener(item.eventName, item.fn);
      });
      destroyW3D();
    };
  }, []);

  useEffect(() => {
    resizeW3D();
  }, [panelShow]);

  useEffect(() => {
    if (siteGroup.length > 1) {
      setGroupShow(siteGroup);
      if (selecteGroupId.current === -1) {
        selecteGroupId.current = siteGroup[0].siteTeamId;
      }
    } else {
      setGroupShow([]);
    }
  }, [
    siteGroup,
  ]);

  return (
    <BUSINESS_TOPO_CONTEXT.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div className='rightPanelTest' width="25rem" setPanelShow={setPanelShow}>
          <div className='split_left'>
            <div className="siteGroups">
              {groupShow.map((group, groupIndex) => {
              // 根据 siteLength 找到对应的位置数组
                const positions = group.siteNum > MAX_GROUP_LENGTH ? siteGroupPosition[MAX_GROUP_LENGTH] : siteGroupPosition[group.siteNum] || [];
                return (
                  <Tooltip
                    key={group.siteTeamName}
                    placement="topLeft"
                    content={group.siteTeamName}
                    trigger={['hover', 'focus']}
                  >
                    <div
                      key={group.siteTeamName}
                      className="siteGroup"
                      onClick={() => changeSiteGroup(group.siteTeamName)}
                    >
                      {/* 使用位置数组设置子div的坐标 */}
                      {positions.map((position, index) => (
                        <div
                          key={index}
                          className="siteItem"
                          style={{
                            top: `${position.top}px`,
                            left: `${position.left}px`,
                          }}
                        />
                      ))}
                    </div>
                  </Tooltip>
                );
              })}
            </div>
            <div className="topo">
              <div ref={w3dContainerRef} className="w3d_container" />
              <MessageDeatil display={showMessage} main={false} />
            </div>
            <FloatPanel />
            <Toolbar isDatabase={true} />
            <TimeLinePanel
              pageType={DATABASE_TYPE_HOME}
              renderTopology={timeTrack}
              backTimeTrack={backTimeTrack}
            />
            {state.dataBasePannelIsOpen && <RightPanel refreshFlag={refreshFlag} />}
          </div>
          <DivMessage text={$t('filtered_info')} type="warn" style={style2} display={state.hasFiltered}
            disposeTimeOut={3000} onClose={() => {
              dispatch({
                hasFiltered: false,
              });
            }}
          />
          <Loader type="global" isOpen={loading} desc="Please wait..." maskElementId="evu_selector_example" />
        </div>
      </ConfigProvider>
    </BUSINESS_TOPO_CONTEXT.Provider>
  );
}

export default DatabasePage;
