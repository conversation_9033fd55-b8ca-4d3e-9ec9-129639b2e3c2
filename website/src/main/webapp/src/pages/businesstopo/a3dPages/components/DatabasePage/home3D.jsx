import eventBus from '@pages/businesstopo/a3dPages/bus';
import {getMessage, registerResource} from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';

import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/database';
import '../../styles/card.less';
import {eventBusHandler, getRouteQuery} from '../utils';
import {queryDataBaseGridData, queryDataBaseTipsData} from '@pages/businesstopo/api';
import {dateTimeFormat} from '@util/tools';

registerResource(i18n, 'w3d');
let home3D;
let dataBaseDispatch;
const i18nObj = {
  NEFault: getMessage('w3d.NEFault'),
  ARSwitchover: getMessage('w3d.ARSwitchover'),
  ASSwitchover: getMessage('w3d.ASSwitchover'),
};

const transformData = (input, dbName) => {
  input.dataList.forEach(item => {
    if (item.shardId === null || item.shardId === '') {
      item.shardId = `${item.siteId}_${item.id}`;
      input.relationShipList.push({
        shardId: item.shardId,
        podIds: [item.id],
      });
    }
  });

  const data = input.dataList.map(item => {
    let status;
    if (item.status === 'alarm') {
      status = 'alarm';
    } else if (item.status === 'unknown') {
      status = 'unknown';
    } else {
      status = 'normal';
    }

    return {
      shardId: item.shardId,
      id: item.id,
      status,
      designRole: item.designRole,
      currentRole: item.currentRole,
    };
  });

  const relationship = input.relationShipList.map(rel => ({
    shardId: rel.shardId,
    podIds: rel.podIds,
  }));

  const sites = input.siteList.map(site => {
    const siteData = {
      id: site.siteId,
      name: `${site.siteName}`,
      [dbName]: {
        business: [],
        podCount: site.dbLibrary.podCount,
        podFault: site.dbLibrary.podFault,
        arSwitchover: site.dbLibrary.arSwitchover,
        asSwitchover: site.dbLibrary.asSwitchover,
      },
    };

    // 如果 businessList 不为空，则处理业务数据
    if (site.dbLibrary.businessList.length > 0) {
      site.dbLibrary.businessList.map(business => {
        // 如果 dbsList 不为空，则处理 dbs 数据
        if (business.dbsList.length > 0) {
          if (business.dbsList.length > 0) {
            const transformedDbs = business.dbsList.map(dbsItem => ({
              name: dbsItem.name,
              podIds: dbsItem.podIds,
            }));
            siteData[dbName].business.push(
              {
                key: business.key,
                name: business.name,
                dbs: transformedDbs,
              },
            );
          }
        }
      });
    } else {
      siteData[dbName].business = [];
    }
    return siteData;
  });

  return {
    data,
    id: input.siteTeamName,
    relationship,
    sites,
  };
};

function merge(target, source) {
  // 合并 data 和 relationship 字段
  const mergedData = [...target.data, ...source.data];
  const mergedRelationship = [...target.relationship, ...source.relationship];

  // 用于存储合并后的 sites 对象，便于查找和合并
  const mergedObjects = {};

  // 遍历 target.sites 并将其添加到 mergedObjects
  target.sites.forEach(obj => {
    mergedObjects[obj.id] = {...obj};
  });

  // 遍历 source.sites 并合并到 mergedObjects
  source.sites.forEach(obj => {
    if (mergedObjects[obj.id]) {
      mergedObjects[obj.id] = {...mergedObjects[obj.id], ...obj};
    } else {
      mergedObjects[obj.id] = {...obj};
    }
  });

  // 将 mergedObjects 转换回数组格式
  const mergedSites = Object.values(mergedObjects);

  // 返回合并后的结果
  return {
    data: mergedData,
    relationship: mergedRelationship,
    id: target.id,
    sites: mergedSites,
  };
}

const ASList = ['A', 'S'];
const RList = ['R', 'Ra', 'Rs'];

export const isAS = (designRole, currentRole) => {
  const isAS1 = designRole === 'A' && currentRole === 'S';
  const isAS2 = designRole === 'S' && currentRole === 'A';
  const isAS3 = designRole === 'Ra' && currentRole === 'Rs';
  const isAS4 = designRole === 'Rs' && currentRole === 'Ra';
  return isAS1 || isAS2 || isAS3 || isAS4;
};

export const isAR = (designRole, currentRole) => {
  const isAR1 = ASList.includes(designRole) && RList.includes(currentRole);
  const isAR2 = RList.includes(designRole) && ASList.includes(currentRole);
  return isAR1 || isAR2;
};

export const getInitData = async param => {
  let physicParam = {...param};
  let memoryParam = {...param};
  physicParam.dbType = 1;
  memoryParam.dbType = 2;
  const physicData = await queryDataBaseGridData(physicParam);
  const memoryData = await queryDataBaseGridData(memoryParam);
  if (!physicData.data) {
    physicData.data = [];
    physicData.data.dataList = [];
    physicData.data.relationShipList = [];
    physicData.data.siteList = [];
  }
  if (!memoryData.data) {
    memoryData.data = [];
    memoryData.data.dataList = [];
    memoryData.data.relationShipList = [];
    memoryData.data.siteList = [];
  }
  let convertedMemoryData = transformData(memoryData.data, 'memory');
  let convertedPhysicData = transformData(physicData.data, 'physic');
  let data = merge(convertedMemoryData, convertedPhysicData);
  // Default values
  const defaultValues = {
    business: [],
    podCount: 0,
    podFault: 0,
    arSwitchover: 0,
    asSwitchover: 0,
  };
  // Update sites
  data.sites.forEach(site => {
    if (!site.physic) {
      site.physic = {...defaultValues};
    } else if (!site.physic.business) {
      site.physic.business = defaultValues.business;
    }

    if (!site.memory) {
      site.memory = {...defaultValues};
    } else if (!site.memory.business) {
      site.memory.business = defaultValues.business;
    }
  });
  let siteGroup = memoryData.data.siteTeamSimpleInfoList;
  data.i18n = i18nObj;
  data.hasFiltered = physicData.data.hasFiltered || memoryData.data.hasFiltered;

  const podIdToSiteNameMap = new Map();

  // Function to add podIds to the map for a given site
  function addPodIdsToMap(site) {
    const siteName = site.name;
    const addPodIds = (dbs) => {
      dbs.forEach(db => {
        db.podIds.forEach(podId => {
          podIdToSiteNameMap.set(podId, siteName);
        });
      });
    };

    // Add podIds from memory and physic
    site.memory.business.forEach(business => addPodIds(business.dbs));
    site.physic.business.forEach(business => addPodIds(business.dbs));
  }
  // Populate the map with all sites
  if (data && data.sites) {
    data.sites.forEach(site => addPodIdsToMap(site));
  }
  dataBaseDispatch({siteGroup, podIdToSiteNameMap});
  return data;
};

const initPodDom = async(dom, data, tipsData) => {
  let text = getMessage('w3d.normal');
  let status;
  if (isAS(data.designRole, data.currentRole)) {
    status = 'asSwitchover';
  }
  if (isAR(data.designRole, data.currentRole)) {
    status = 'arSwitchover';
  }
  if (status === 'asSwitchover') {
    text = getMessage('w3d.ASSwitchover');
  }
  if (status === 'arSwitchover') {
    text = getMessage('w3d.ARSwitchover');
  }
  dom.innerHTML = `
    <div class="status">
      <div class="podId">${tipsData.data.podName}</div>
      <div class="podStatus ${data.status}">${text}</div>
    </div>
    <div class="time">
      ${getMessage('w3d.createTime')}: ${dateTimeFormat(tipsData.data.podCreateTime)}
    </div>
    ${status === 'asSwitchover' ? `
        <div class="switchTime">
          ${getMessage('w3d.ASSwitchoverTime')}: ${dateTimeFormat(tipsData.data.asSwitchTime)}
        </div>
      ` : ''}
    ${status === 'arSwitchover' ? `
      <div class="switchTime">
        ${getMessage('w3d.ARSwitchoverTime')}:${dateTimeFormat(tipsData.data.arSwitchTime)}
      </div>
    ` : ''}
    ${tipsData.data.alarmCount > 0 ? `
      <div class="alarmCount">
        ${getMessage('w3d.numberOfAlarms')}: <span class="alarm">${tipsData.data.alarmCount}</span>
      </div>
    ` : ''}
  `;
};

const events = [
  {
    // 2D-3D对接，hover pod ，显示卡片
    eventName: 'from3d_showPodDataBaseTip',
    async fn(setting) {
      setting.dom.innerHTML = '';
      let param = {};
      let {selectedTime} = JSON.parse(sessionStorage.getItem('topoSession') || '{}');
      param.podId = setting.data.id;
      if (!selectedTime) {
        param.timestamp = 0;
      } else {
        param.timestamp = selectedTime;
      }
      let tipsData = await queryDataBaseTipsData(param);
      const dom = document.createElement('div');
      dom.classList.add('w3d-podDataBaseTip');
      initPodDom(dom, setting.data, tipsData);
      setting.dom.append(dom);
    },
  },
];

export const initW3D = async(container, solutionId, dispatch) => {
  dataBaseDispatch = dispatch;
  events.forEach(item => {
    eventBus.addListener(item.eventName, item.fn);
  });
  let topoSession = JSON.parse(
    sessionStorage.getItem('topoSession') || '{}',
  );
  // 2D-3D对接，初始化更新数据
  let param = {
    solutionId,
    timestamp: topoSession.selectedTime,
  };
  const initData = await getInitData(param, dispatch);
  home3D = new Home3D(eventBusHandler);
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, {sitesDatabase: initData});
  return initData;
};

export const destroyW3D = () => {
  events.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};

export const updateStatusToW3D = async databaseData => {
  eventBusHandler.emit('to3d_updateSitesDatabaseStatus', databaseData);
};

export const updateDataToW3D = data => {
  data.i18n = i18nObj;
  eventBusHandler.emit('to3d_changeSitesDatabaseData', data);
};
