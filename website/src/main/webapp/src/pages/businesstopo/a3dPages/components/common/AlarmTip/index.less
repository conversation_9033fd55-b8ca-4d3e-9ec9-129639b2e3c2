.alarmTipDom {
  background: #272727;
  box-shadow: 0 12px 48px 0 rgba(0, 0, 0, 0.5);
  padding: 1.5rem;
  border-radius: 0.25rem;
  transition: all.3s cubic-bezier(0.5, 0, 0.84, 0.25);
  position: relative;

  .alarmTip_center {
    display: flex;
    gap: 1rem;
  }

  .alarmTip_tabs {
    display: flex;
    font-size: 1rem;
    color: #bbb;
    font-weight: Medium;
    position: relative;

    .tip {
      display: inline-block;
      min-width: 2.5rem;
      height: 1.5rem;
      cursor: pointer;
      margin-right: 1.5rem;
      position: relative;

      &.active {
        color: #2e94ff;

        .line {
          background-color: #2e94ff;
        }
      }

      .line {
        display: inline-block;
        height: 0.125rem;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .close {
      display: inline-block;
      width: 1.5rem;
      height: 1.5rem;
      position: absolute;
      background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_close_lined.svg);
      right: 0;
      background-size: 1rem;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
    }
  }

  .alarmCard {
    background: #393939;
    backdrop-filter: blur(0px);
    width: 20.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
    border-radius: 0.25rem;
    display: flex;
    gap: 1.5rem;
    flex-direction: column;

    .title {
      color: #f5f5f5;
      font-weight: 1rem;
      display: flex;
      gap: 0.25rem;

      .icon {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        background-repeat: no-repeat;
        background-size: 100%;

        &.even {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/even.svg);
        }
      }
    }

    .tag {
      position: absolute;
      width: 3.5rem;
      height: 1.625rem;
      display: flex;
      background-size: 100% 100%;
      right: 0;
      top: 1.3125rem;
      font-size: 0.75rem;
      align-items: center;
      justify-content: center;
    }

    &.emergency {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_icon.svg);
        }
      }

      .tag {
        color: #e54545;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_tag.svg);
      }
    }

    &.general {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_icon.svg);
        }
      }

      .tag {
        color: #ffbb33;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_tag.svg);
      }
    }

    &.important {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_icon.svg);
        }
      }

      .tag {
        color: #ff8000;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_tag.svg);
      }
    }

    &.hints {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_icon.svg);
        }
      }

      .tag {
        color: #2e94ff;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_tag.svg);
      }
    }

    .center {
      display: flex;
      gap: 0.75rem;
      flex-direction: column;
      min-height: 7.125rem;

      .secondTitle {
        display: flex;
        gap: 1rem;
        padding-left: 0.25rem;
      }

      .gapDiv {
        display: flex;
        gap: 0.5rem;
        min-width: 6rem;
        color: #bbb;
        align-items: center;
        height: 1.5rem;

        .icon {
          width: 1rem;
          height: 1rem;
          display: flex;
          background-repeat: no-repeat;
          background-size: 100%;

          &.des {
            background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_des.svg);
          }

          &.address {
            background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_address_lined.svg);
          }

          &.time {
            background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_clock_lined.svg);
          }

          &.source {
            background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_source.svg);
          }
        }
      }
    }

    .button {
      background: #2070f3;
      height: 2rem;
      font-size: 0.875rem;
      color: #ffffff;
      line-height: 0.875rem;
      border-radius: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      span {
        width: 1rem;
        height: 1rem;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/play.svg);
        margin-right: 0.25rem;
      }
    }
  }

  .pagination {
    position: absolute;
    width: calc(100% - 1rem);
    height: 2rem;
    display: flex;
    justify-content: space-between;
    top: 50%;
    left: 0.5rem;

    .page {
      display: inline-block;
      width: 2rem;
      height: 2rem;
      background-size: 100%;
      z-index: 1;
      cursor: pointer;
    }

    .next {
      background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/next.svg);
    }

    .pre {
      background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/pre.svg);
    }
  }
}
