import { models } from '../../..';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { bindDblclickEvents } from './utils';

const A3D = getA3D();

const getSiteMap = (w3d) => ({
  site: {
    ground: w3d.themeManager.referStyle('site-ground'),
    businessGround: w3d.themeManager.referStyle('business-ground'),
    businessGroundFocus: w3d.themeManager.referStyle('business-ground-focus'),
  },
  app: {
    normal: w3d.themeManager.referStyle('app-normal'),
    grey: w3d.themeManager.referStyle('app-grey'),
    alarm: w3d.themeManager.referStyle('app-alarm'),
  },
  database: {
    normal: w3d.themeManager.referStyle('database-normal'),
    grey: w3d.themeManager.referStyle('database-grey'),
  },
  line: {
    normal: w3d.themeManager.referStyle('db-line-normal'),
    alarm: w3d.themeManager.referStyle('db-line-alarm'),
  },
  circle: {
    normal: w3d.themeManager.referStyle('circle-normal'),
    alarm: w3d.themeManager.referStyle('circle-alarm'),
  },
  dbName: {},
});

const getSiteTeamMap = (w3d) => ({
  siteTeam: {
    line: w3d.themeManager.referStyle('site-team-line'),
    ground: w3d.themeManager.referStyle('site-team-ground'),
    block: {
      normal: w3d.themeManager.referStyle('site-item-normal'),
      related: w3d.themeManager.referStyle('site-item-related'),
      alarm: w3d.themeManager.referStyle('site-item-alarm'),
      hover: w3d.themeManager.referStyle('site-item-hover'),
    },
  },
});

export const getSitesDrillDownSetting = (data, w3d) => {
  const modelMap = {
    app: models.app.children[0].clone(),
    database: models.database.children[0].clone(),
  };
  const matMap = {
    ...getSiteMap(w3d),
    ...getSiteTeamMap(w3d),
  };
  data.drGroupList.forEach((drGroup) => {
    drGroup.moTypeInfoList.forEach((moType) => {
      const moTypeName = moType.moTypeName;
      if (!matMap.dbName[moTypeName]) {
        matMap.dbName[moTypeName] = w3d.themeManager.referStyle(moTypeName);
      }
    });
  });
  return { modelMap, matMap };
};

const createHoverCard = (data, dbItem, w3d, componentManager) => {
  const id = `${data.moTypeName}_${data.moTypeId}`;
  const type = 'db';
  componentManager.cardManager.updateCard(
    type,
    { id },
    dbItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showDbTip({
    data,
    dom: domContainer,
  });
};

export const createSiteHoverCard = (data, siteItem, w3d, componentManager) => {
  const id = data.siteId;
  const type = 'site';
  componentManager.cardManager.updateCard(
    type,
    { id },
    siteItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showSiteTip({
    data,
    dom: domContainer,
  });
};

const createBusinessHoverCard = (data, businessItem, w3d, componentManager) => {
  const id = data.siteId;
  const type = 'site';
  componentManager.cardManager.updateCard(
    type,
    { id },
    businessItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showSiteTip({
    data,
    dom: domContainer,
  });
};

const createLineHoverCard = (data, lineItem, w3d, componentManager) => {
  const id = `${data.moTypeId}_${data.siteId}`;
  const type = 'dbLine';
  componentManager.cardManager.updateCard(
    type,
    { id, position: lineItem.children[2].position },
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showDbLineTip({
    data,
    dom: domContainer,
  });
};

const bindSiteHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(info.object, (data, dbItem, selected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!selected) {
        componentManager.events.hoverInObject(dbItem);
        createHoverCard(data, dbItem, w3d, componentManager);
      }
    }, (data, lineItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      createLineHoverCard(data, lineItem, w3d, componentManager);
    }, (data, siteItem) => {
      if (data.siteName.length > 6) {
        createSiteHoverCard(data, siteItem, w3d, componentManager);
      }
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    }, (data, groupItem) => {
      if (groupItem.material.map.userData.drawName !== groupItem.material.map.userData.detailName) {
        createBusinessHoverCard(data, groupItem, w3d, componentManager);
      }
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    },
    );
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(info.object, (data, dbItem, selected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!selected) {
        componentManager.events.hoverOutObject(dbItem);
        componentManager.cardManager.removeCard(
          { id: `${data.moTypeName}_${data.moTypeId}` },
          'db',
        );
      }
    }, (data, lineItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.cardManager.removeCard(
        { id: `${data.moTypeId}_${data.siteId}` },
        'dbLine',
      );
    }, (data, siteItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (data.siteName.length > 8) {
        componentManager.cardManager.removeCard(
          {id: data.siteId},
          'site',
        );
      }
    }, (data, groupItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (groupItem.material.map.userData.drawName !== groupItem.material.map.userData.detailName) {
        componentManager.cardManager.removeCard(
          {id: data.siteId},
          'site',
        );
      }
    },
    );
    w3d.agent.renderOnce();
    return true;
  });
};

const bindSiteClickEvents = (compIns, w3d, componentManager) => {
  bindDblclickEvents(compIns, (e, info) => {
    compIns.select(info.object, (data, dbItem) => {
      if (data) {
        componentManager.events.showPanel({
          type: 'moType',
          id: data.moTypeId,
          isActiveDv: data.isActiveDv,
          isHealthy : data.isHealthy,
          applicationType: data.applicationType,
        });
        componentManager.cardManager.removeCard(
          { id: `${data.moTypeName}_${data.moTypeId}` },
          'db',
        );
        componentManager.events.selectObject(dbItem);
      } else {
        componentManager.events.showPanel({
          type: '',
          id: '',
        });
        componentManager.events.selectObject(null);
      }
    }, (data) => {
      componentManager.events.changeSite(data);
    });
  }, (e, info) => {
    compIns.drillDown(info.object, (dataItem, moTypeItem, siteData) => {
      componentManager.events.drillDownMoType({
        siteId: siteData.id,
        siteName: siteData.name,
        moTypeId: dataItem.moTypeId,
        moTypeName: dataItem.moTypeName,
        isActiveDv: dataItem.isActiveDv,
        applicationType:dataItem.applicationType,
      });
    });
  });
};

export const bindSiteDrillDownEvents = (compIns, w3d, componentManager) => {
  bindSiteHoverEvents(compIns, w3d, componentManager);
  bindSiteClickEvents(compIns, w3d, componentManager);
};
