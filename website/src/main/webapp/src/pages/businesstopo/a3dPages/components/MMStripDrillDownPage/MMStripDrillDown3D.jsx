import React from 'react';
import ReactDOM from 'react-dom';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {registerResource} from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/siteDrillDown';
import {eventBusHandler, getQuery, getRouteQuery, toMMStripDrillDownPage} from '../utils';
import Pod from '@pages/businesstopo/components/tooltip/pod';
import SiteTips from '@pages/businesstopo/components/tooltip/siteTips';
import {querySiteGridData} from '@pages/businesstopo/api';
import {queryCurrentIds} from '../../../api';
import {sorter} from '@digitalview/fe-utils';
import {setSessionData} from '../../../util';

registerResource(i18n, 'w3d');
let home3D;
const moTypeMap = {};
const getDrGroupList = (data) => {
  const drGroupList = data.drGroupList.map((drGroup) => {
    const newDrGroup = {...drGroup};
    newDrGroup.moTypeInfoList = drGroup.moTypeInfoList.filter((moType) => moType.moTypeInstanceCount > 0);
    return newDrGroup;
  });
  return drGroupList;
};

const getRelationship = (data, drGroupList) => {
  const relationship = {};
  drGroupList.forEach(drGroup => {
    drGroup.moTypeInfoList.forEach(moType => {
      relationship[moType.moTypeId] = {
        moTypeLinks: new Set(),
        siteLinks: [],
      };
    });
  });
  data.moTypeLinks.forEach(relation => {
    const id1 = relation.fromMoTypeId;
    const id2 = relation.toMoTypeId;
    if (relationship[id1] && relationship[id2]) {
      relationship[id1].moTypeLinks.add({id: id2, protocol: relation.protocol, realation: 'to'});
      relationship[id2].moTypeLinks.add({id: id1, protocol: relation.protocol, realation: 'from'});
    }
  });

  Object.keys(relationship).forEach(moTypeId => {
    relationship[moTypeId].moTypeLinks = [...relationship[moTypeId].moTypeLinks].map(obj => ({
      to: obj.id,
      protocol: obj.protocol,
      realation:obj.realation,
    }));
  });

  data.moSiteLinks.forEach(link => {
    if (relationship[link.fromMoTypeId]) {
      relationship[link.fromMoTypeId].siteLinks.push({
        to: link.toSiteId,
        alarmCount: 0,
        protocol: link.protocol,
      });
    }
  });
  for (const key in relationship) {
    if (Object.prototype.hasOwnProperty.call(relationship, key)) {
      const {siteLinks} = relationship[key];
      if (siteLinks.length > 1) {
        const protocols = [...new Set(siteLinks.map(link => link.protocol))];
        siteLinks.forEach(link => {
          link.protocol = protocols.join('&');
        });
      }
    }
  }
  return relationship;
};

// 处理数据结构
const getData = (data) => {
  const query = getQuery();
  data.drGroupList.forEach(item => {
    item.moTypeInfoList.forEach(moType => {
      moTypeMap[moType.moTypeId] = moType.moTypeName;
    });
  });
  const drGroupList = getDrGroupList(data);
  drGroupList.forEach(v => {
    (v.moTypeInfoList || []).sort((a, b) => sorter(a.moTypeName, b.moTypeName));
  });
  const relationship = getRelationship(data, drGroupList);
  let positionSetting = [];
  if (!data.autoLayoutsFlag) {
    positionSetting = transformedData(data.drGroupList);
  }
  let stripUnit = query.stripUnit;
  if (!stripUnit) {
    stripUnit = data.stripeUnitList[0];
  }
  const result = {
    id: stripUnit,
    name: data.siteName,
    autoPosition: data.autoLayoutsFlag,
    positionSetting,
    drGroupList,
    relationship,
    siteInfoList: data.stripeUnitList.map(siteInfo1 => {
      const newSiteInfo = {
        siteName: siteInfo1,
        siteId: siteInfo1,
        siteTeamId: 'stripeUnit',
      };
      return newSiteInfo;
    }),
  };
  result.drGroupList.forEach(group => {
    group.moTypeInfoList.forEach(info => {
      if (Object.keys(info).includes('grayStatus')) {
        info.isSupportGray = info.grayStatus;
        delete info.grayStatus;
      }
    });
  });
  return result;
};

const getStripData = (data) => {
  let {
    stripId,
  } = JSON.parse(sessionStorage.getItem('topoSession'));
  const query = getQuery();
  let result = data.map(item => {
    return {
      businessId: item.siteId,
      businessName: item.siteName,
      businessType: 'north',
      mainBusiness: parseInt(query.stripId) === item.siteId || parseInt(stripId) === item.siteId,
      iconType: 2,
    };
  });

  result.sort((a, b) => sorter(a.businessName, b.businessName));
  const index = result.findIndex(v => v.mainBusiness);
  return [...result.slice(index), ...result.slice(0, index)];
};

const events = [
  {
    eventName: 'from3d_showPanel',
    // 2D-3D对接，点击事件，联动右侧面板
    fn(setting) {
      setting;
      // 打印 'showPanel', setting
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showDbTip',
    fn(setting) {
      ReactDOM.render(
        <Pod data={setting.data} suppotGray={false} />,
        setting.dom,
      );
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showSiteTip',
    fn(setting) {
      ReactDOM.render(
        <SiteTips data={setting.data} />,
        setting.dom,
      );
    },
  },
  {
    // 2D-3D对接，重置选中站点
    eventName: 'from3d_changeSite',
    async fn(setting) {
      this.changeSite(setting);
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_drillDownMoType',
    fn(setting) {
      const query = getQuery();
      let {
        selectedTime,
      } = JSON.parse(sessionStorage.getItem('topoSession'));
      if (selectedTime === 0) {
        destroyW3D();
        let url = setting.applicationType === 4 ? '/eviewwebsite/index.html#path=/businesstopo/hsmhome' : '/eviewwebsite/index.html#path=/businesstopo/mohome';
        location.replace(`${url}&siteId=${query.stripId}&siteName=${encodeURIComponent(query.stripName)}&moTypeId=${setting.moTypeId}&moTypeName=${encodeURIComponent(setting.moTypeName)}&solutionId=${query.solutionId}&applicationType=${setting.applicationType}&isMM=true${query.stripUnit ? `&stripUnit=${query.stripUnit}` : ''}`);
        return;
      }
      let param = {
        instanceIdList: [],
        timestamp: selectedTime,
        targetTimestamp: 0,
        solutionType: 3,
      };
      param.instanceIdList.push(setting.moTypeId);
      queryCurrentIds(param, ({data:idMap}) => {
        destroyW3D();
        let url = setting.applicationType === 4 ? '/eviewwebsite/index.html#path=/businesstopo/hsmhome' : '/eviewwebsite/index.html#path=/businesstopo/mohome';
        location.replace(`${url}&siteId=${query.stripId}&moTypeId=${idMap[setting.moTypeId]}&siteName=${query.stripName}&moTypeName=${setting.moTypeName}&solutionId=${query.solutionId}&applicationType=${setting.applicationType}&isMM=true${query.stripUnit ? `&stripUnit=${query.stripUnit}` : ''}`);
      });
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_switchBusiness',
    async fn(setting) {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      const query = getQuery();
      let _siteData;
      if (!topoSession.isTimeTrack) {
        let param = {
          siteId: setting.currentBusinessId,
          timestamp: 0,
          solutionId: query.solutionId,
          unitId: '',
        };
        _siteData = await querySiteGridData(param);
        toMMStripDrillDownPage(setting.currentBusinessId, _siteData?.data?.stripeUnitList?.[0] || '', query.solutionId, setting.currentBusinessName);
        eventBus.emit('resetSolutionIdAndStripId', {
          solutionId: query.solutionId,
          stripId: setting.currentBusinessId,
        });
        updateSiteData(_siteData.data);
      } else {
        setSessionData(
          {
            stripId: setting.currentBusinessId,
          });
        let param = {
          instanceIdList: [],
          timestamp: topoSession.selectedTime,
          targetTimestamp: 0,
          solutionType: 3,
        };
        param.instanceIdList.push(setting.currentBusinessId);
        queryCurrentIds(param, async({data: idMap}) => {
          let param2 = {
            siteId: setting.currentBusinessId,
            timestamp: topoSession.selectedTime,
            solutionId: topoSession.solutionId,
            unitId: '',
          };
          eventBus.emit('resetSolutionIdAndStripId', {
            solutionId: topoSession.solutionId,
            stripId: setting.currentBusinessId,
          });
          _siteData = await querySiteGridData(param2);
          toMMStripDrillDownPage(idMap[setting.currentBusinessId], _siteData?.data?.stripeUnitList?.[0] || '', query.solutionId, setting.currentBusinessName);
          updateSiteData(_siteData.data);
        });
      }
    },
  },
];

export const initW3D = async(container, setting) => {
  // setting下的变量：const setting = { siteData, positionSetting, groupId, changeSite }
  events.forEach((item) => {
    item.fn = item.fn.bind(setting);
    eventBus.addListener(item.eventName, item.fn);
  });
  // 将数据转化成3D需要的数据
  const data = getData(setting.stripData);
  data.isMM = true;
  let data2 = getStripData(setting.stripData.siteInfoList);
  if (!home3D) {
    home3D = new Home3D(eventBusHandler);
  }
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, {sitesDrillDown: data, mmnorth: data2});
  if (setting.groupId) {
    eventBus.emit('to3d_focusGroup', setting.groupId);
  }
  eventBus.emit('from3d_gridData', {
    ...data,
    stripInfoList: data2,
  });
};

export const updateSiteData = (data) => {
  // 2D-3D对接，切换站点更新站点数据
  const siteData = getData(data);
  siteData.isMM = true;
  eventBus.emit('to3d_updateDrillDownData', siteData);
};

export const updateSiteDataStatus = data => {
  // 2D-3D对接，切换站点更新站点数据
  const siteData = getData(data);
  eventBus.emit('to3d_updateDrillDownStatus', siteData);
};

export const destroyW3D = () => {
  events.forEach((item) => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};


const transformedData = data => data.map(item => ({
  groupKey: item.groupKey,
  drGroupName: item.drGroupName, // Replace with actual translation function
  xPos: item.position[0].xPos,
  zPos: item.position[0].zPos,
  xLen: item.position[0].xLen,
  zLen: item.position[0].zLen,
  row: item.position[0].row,
}));
