import { random } from '@pages/businesstopo/a3dPages/w3d/components/common/utils';

const siteCount = 3;
const memorydbs = {
  invgmdb: {
    belong: 'management',
    active: 2,
    redundancy: true,
  },
  cbpmdb: {
    belong: 'billing',
    active: 3,
    redundancy: true,
  },
  adaptermdb: {
    belong: 'billing',
    active: 2,
    redundancy: true,
  },
  cdfmdb: {
    belong: 'billing',
    active: 1,
    redundancy: true,
  },
};

const memory = {
  podCount: 0,
  podFault: 0,
  asSwitchover: 0,
  arSwitchover: 0,
  business: [
    {
      key: 'management',
      name: '管理及后付费业务',
      dbs: [],
    },
    {
      key: 'billing',
      name: '实时计费类业务',
      dbs: [],
    },
  ],
};

const physicdbs = {
  userdb: {
    belong: 'management',
    active: 3,
    redundancy: true,
  },
  billdb: {
    belong: 'management',
    active: 3,
    redundancy: true,
  },
  edrdb: {
    belong: 'management',
    active: 2,
    redundancy: true,
  },
  edrhisdb: {
    belong: 'management',
    active: 1,
    redundancy: true,
  },
  bmpdb: {
    belong: 'management',
    active: 1,
    redundancy: true,
  },
  billsharedb: {
    belong: 'management',
    active: 1,
    redundancy: true,
  },
  MEDdb: {
    belong: 'offlineMediation',
    active: 1,
    redundancy: true,
  },
  reportdb: {
    belong: 'BDI',
    active: 1,
    redundancy: true,
  },
  UVCdb: {
    belong: 'UVC',
    active: 2,
    redundancy: true,
  },
};

const physic = {
  podCount: 0,
  podFault: 0,
  asSwitchover: 0,
  arSwitchover: 0,
  business: [
    {
      key: 'management',
      name: '管理及后付费业务',
      dbs: [],
    },
    {
      key: 'offlineMediation',
      name: 'offline mediation',
      dbs: [],
    },
    {
      key: 'BDI',
      name: 'BDI',
      dbs: [],
    },
    {
      key: 'UVC',
      name: 'UVC',
      dbs: [],
    },
  ],
};

const getStatus = (status1) => {
  let status = 'normal';
  const number = random();
  if (!status1) {
    if (number < 0.4) {
      status = 'alarm';
    }
  } else if (status1 !== 'normal') {
    if (number < 0.2) {
      status = 'alarm';
    }
  }
  return status;
};

const getSiteId = (index) => `site${index + 1}`;
const getPodId = (siteId, typedbs, dbType, podType, index) =>
  `${siteId}-${typedbs}-${dbType}-${podType}${index + 1}`;

const getAllStatus = (db, alarm) => {
  let status1 = getStatus();
  let status2 = getStatus(status1);
  if (db === 'bmpdb') {
    status1 = alarm ? 'alarm' : 'normal';
    status2 = alarm ? 'alarm' : 'normal';
  }
  if (status1 !== 'normal' && status2 === 'normal') {
    status1 = random() < 0.5 ? status1 : 'asSwitchover';
    status2 = 'asSwitchover';
  } else if (status1 !== 'normal' && status2 !== 'normal') {
    status1 = 'arSwitchover';
    status2 = 'arSwitchover';
  }
  return { status1, status2 };
};

const getRelationshipAndData = (relationship, data, groupIndex, typedbs, type, alarm) => {
  for (let i = 0; i < siteCount; i++) {
    const siteId = getSiteId(i + groupIndex * 3);
    Object.keys(typedbs).forEach((db) => {
      if (db === 'bmpdb' && i > 0) {
        return;
      }
      for (let j = 0; j < typedbs[db].active; j++) {
        const relation = {
          active: getPodId(siteId, type, db, 'a', j),
          standby: getPodId(siteId, type, db, 's', j),
          redundancy: [],
        };
        const { status1, status2 } = getAllStatus(db, alarm);
        data.push({
          id: relation.active,
          activeId: relation.active,
          status: status1,
        }, {
          id: relation.standby,
          activeId: relation.active,
          status: status2,
        });
        if (typedbs[db].redundancy) {
          let status3 = 'normal';
          if (status1 === 'arSwitchover' || status2 === 'arSwitchover') {
            status3 = 'arSwitchover';
          }
          const siteIndex = (i + 1) % siteCount + groupIndex * 3;
          const redundancyId = getPodId(getSiteId(siteIndex), type, db, 'r', j);
          relation.redundancy.push(redundancyId);
          data.push({
            id: redundancyId,
            activeId: relation.active,
            status: status3,
          });
        }
        relationship.push(relation);
      }
    });
  }
};

const getSite = (i, groupIndex) => {
  const site = {
    id: `site${i + 1 + groupIndex * 3}`,
    name: `site${i + 1 + groupIndex * 3}`,
    memory: JSON.parse(JSON.stringify(memory)),
    physic: JSON.parse(JSON.stringify(physic)),
  };
  Object.keys(memorydbs).forEach((db) => {
    const belong = memorydbs[db].belong;
    const dbObj = {
      name: db,
      active: [],
      standby: [],
      redundancy: [],
    };
    site.memory.business.find(((item) => item.key === belong)).dbs.push(dbObj);
  });
  Object.keys(physicdbs).forEach((db) => {
    const belong = physicdbs[db].belong;
    const dbObj = {
      name: db,
      active: [],
      standby: [],
      redundancy: [],
    };
    site.physic.business.find(((item) => item.key === belong)).dbs.push(dbObj);
  });
  return site;
};

const getSites = (sites, data, groupIndex) => {
  for (let i = 0; i < siteCount; i++) {
    const site = getSite(i, groupIndex);
    sites.push(site);
  }
  data.forEach((item) => {
    const str = item.id.split('-');
    const site = sites.find((subItem) => subItem.id === str[0]);
    const typedbs = str[1];
    const db = str[2];
    const typedbsObj = typedbs === 'memory' ? memorydbs : physicdbs;
    const belong = typedbsObj[db].belong;
    const dbObj = site[typedbs].business
      .find((businessItem) => businessItem.key === belong).dbs
      .find((subItem) => subItem.name === db);
    if (str[3][0] === 'a') {
      dbObj.active.push(item.id);
      item.status === 'alarm' && (site[typedbs].podFault++);
      item.status === 'asSwitchover' && (site[typedbs].asSwitchover++);
      item.status === 'arSwitchover' && (site[typedbs].arSwitchover++);
    }
    if (str[3][0] === 's') {
      dbObj.standby.push(item.id);
      item.status === 'asSwitchover' && (site[typedbs].asSwitchover++);
      item.status === 'arSwitchover' && (site[typedbs].arSwitchover++);
    }
    if (str[3][0] === 'r') {
      dbObj.redundancy.push(item.id);
      item.status === 'arSwitchover' && (site[typedbs].arSwitchover++);
    }
    site[typedbs].podCount++;
  });
};

const getTestData = (groupIndex, alarm) => {
  const result = {
    id: `siteGroup${groupIndex + 1}`,
    data: [],
    relationship: [],
    sites: [],
  };
  getRelationshipAndData(result.relationship, result.data, groupIndex, memorydbs, 'memory', alarm);
  getRelationshipAndData(result.relationship, result.data, groupIndex, physicdbs, 'physic', alarm);
  getSites(result.sites, result.data, groupIndex);
  return result;
};

const updateDataToSwitch = (oldData) => {
  const tempData = JSON.parse(JSON.stringify(oldData));
  tempData.data.forEach((item) => {
    if (item.id.includes('bmpdb')) {
      const siteId = item.id.split('-')[0];
      const site = tempData.sites.find((_site) => _site.id === siteId);
      if (item.status === 'normal') {
        item.status = 'arSwitchover';
        site.physic.arSwitchover++;
      }
    }
  });
  return tempData;
};

const oldData = getTestData(0, false);
const newData = updateDataToSwitch(oldData);

