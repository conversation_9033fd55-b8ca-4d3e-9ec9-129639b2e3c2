import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { models, textures } from '../../../global';

const A3D = getA3D();

/**
 * 获取绘制场景所需要的模型和材质
 * @param w3d 3D绘制实例
 */
export const getIncidentViewSetting = (w3d) => {
  const modelMap = {
    site: models.site.children[0].clone(),
    app: models.app.children[0].clone(),
    pod: models.pod.children[0].clone(),
    vm: models.vm.children[0].clone(),
  };
  const matMap = {
    alarm: {
      selected: textures.alarmItemSelected,
      selectdBg: textures.alarmSelectedBg,
      unSelected: textures.alarmItemUnSelected,
      layerBg: textures.alarmLayerBg,
    },
    site: {
      normal: w3d.themeManager.referStyle('site-normal'),
      normalUnselect: w3d.themeManager.referStyle('site-normal-unselect'),
      alarm: w3d.themeManager.referStyle('site-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('site-alarm-unselect'),
      ground: w3d.themeManager.referStyle('site-ground'),
    },
    app: {
      normal: w3d.themeManager.referStyle('app-normal'),
      normalUnselect: w3d.themeManager.referStyle('app-normal-unselect'),
      alarm: w3d.themeManager.referStyle('app-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('app-alarm-unselect'),
    },
    pod: {
      normal: w3d.themeManager.referStyle('pod-normal'),
      normalUnselect: w3d.themeManager.referStyle('pod-normal-unselect'),
      alarm: w3d.themeManager.referStyle('pod-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('pod-alarm-unselect'),
      gray: w3d.themeManager.referStyle('pod-grey'),
      grayUnselect: w3d.themeManager.referStyle('pod-grey-unselect'),
    },
    vm: {
      normal: w3d.themeManager.referStyle('vm-normal'),
      normalUnselect: w3d.themeManager.referStyle('vm-normal-unselect'),
      alarm: w3d.themeManager.referStyle('vm-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('vm-alarm-unselect'),
    },
    ground: w3d.themeManager.referStyle('ground'),
    businessGround: w3d.themeManager.referStyle('business-ground'),
    blockGround: w3d.themeManager.referStyle('block-ground'),
    blockGroundSelect: w3d.themeManager.referStyle('block-ground-select'),
    blockGroundLine: w3d.themeManager.referStyle('block-ground-line'),
    blockGroundLineSelect: w3d.themeManager.referStyle('block-ground-line-select'),
  };
  return { modelMap, matMap };
};

const CARD_POSITION = {
  site: { x: -12, y: 0, z: 0 },
  app: { x: 6, y: 0, z: 0 },
  pod: { x: 0, y: 0, z: 0 },
  vm: { x: 0, y: 0, z: 0 },
};

const renderPanelLeftDesc = (compIns, componentManager, siteDatas) => {
  const cardContainerMap = compIns.getCardMap();
  Object.keys(cardContainerMap).forEach(type => {
    const container = cardContainerMap[type];
    const cardType = 'incidentPanelDetail';
    componentManager.cardManager.removeCard({ id: type }, cardType);
    componentManager.cardManager.updateCard(
      cardType,
      { id: type, position: CARD_POSITION[type] },
      container,
    );
    const card = componentManager.cardManager.cards[cardType][type];
    const d2Object = card.children[0];
    const domContainer = d2Object.element.children[0];
    componentManager.events.showIncidentPanelInfo({
      data: siteDatas,
      dom: domContainer,
      type,
    });
  });
};

/**
 * 设置场景的默认状态视图
 */
export const initDefaultView4Scene = (compIns, w3d, componentManager) => {
  const clickSelected = compIns.getClickSelected();
  const defaultSelected = compIns.getDefaultSelected();
  compIns.select(null);
  componentManager.events.selectObject(null);
  componentManager.events.selectSite(null);
  if (defaultSelected && !clickSelected) {
    compIns.select(
      defaultSelected,
      null,
      null,
      null,
      (data, item, dataWithAllLayer) => {
        componentManager.events.selectSite(item); // 作用是添加一个锥形光圈
        renderPanelLeftDesc(compIns, componentManager, dataWithAllLayer);
      },
    );
  }
  if (clickSelected) {
    compIns.select(
      clickSelected,
      (data, appItem) => {
        componentManager.events.showPanel({
          type: 'app',
          id: data.dn,
          name: data.name,
        });
        componentManager.events.selectObject(appItem);
      },
      (data, podItem) => {
        componentManager.events.showPanel({
          type: 'pod',
          id: data.dn,
          name: data.name,
        });
        componentManager.events.selectObject(podItem);
      },
      (data, vmItem) => {
        componentManager.events.showPanel({
          type: 'vm',
          id: data.dn,
          name: data.name,
        });
        componentManager.events.selectObject(vmItem);
      },
    );
  }
};

const HOVER_CARD_TYPE = 'groupDetail';

const createCard = (data, modelItem, w3d, componentManager, type) => {
  const id = data.dn;
  const cardType = HOVER_CARD_TYPE;
  componentManager.cardManager.updateCard(
    cardType,
    { id },
    modelItem,
  );
  const card = componentManager.cardManager.cards[cardType][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showCommonTip({
    data,
    dom: domContainer,
    type,
  });
};

const bindHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(
      info.object, // hover的对象
      (data, appItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        if (!isSelected) {
          componentManager.events.hoverInObject(appItem);
        }
        createCard(data, appItem, w3d, componentManager, 'app');
      },
      (data, podItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        if (!isSelected) {
          componentManager.events.hoverInObject(podItem);
          createCard(data, podItem, w3d, componentManager, 'pod');
        }
      },
      (data, vmItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        if (!isSelected) {
          componentManager.events.hoverInObject(vmItem);
          createCard(data, vmItem, w3d, componentManager, 'vm');
        }
      },
      (data, siteItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        if (!isSelected) {
          componentManager.events.hoverInObject(siteItem);
          createCard(data, siteItem, w3d, componentManager, 'site');
        }
      },
      (data, alarmItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        if (!isSelected) {
          componentManager.events.hoverInObject(alarmItem);
        }
      },
    );
    w3d.agent.renderOnce();
    return true;
  });

  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(
      info.object,
        (data, appItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        if (!isSelected) {
          componentManager.events.hoverOutObject(appItem);
          componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        }
      },
      (data, podItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        if (!isSelected) {
          componentManager.events.hoverOutObject(podItem);
          componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        }
      },
      (data, vmItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        if (!isSelected) {
          componentManager.events.hoverOutObject(vmItem);
          componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        }
      },
      (data, siteItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        if (!isSelected) {
          componentManager.events.hoverOutObject(siteItem);
          componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        }
      },
      (data, alarmItem, isSelected) => {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        if (!isSelected) {
          componentManager.events.hoverOutObject(alarmItem);
        }
      },
    );
    w3d.agent.renderOnce();
    return true;
  });
};

const bindClickEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'click', (e, info) => {
    compIns.select(
      info.object,
      (data, appItem) => {
        componentManager.events.showPanel({
          type: 'app',
          id: data.dn,
        });
        componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        componentManager.events.selectObject(appItem);
      },
      (data, podItem) => {
        componentManager.events.showPanel({
          type: 'pod',
          id: data.dn,
          name: data.name,
        });
        componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        componentManager.events.selectObject(podItem);
      },
      (data, vmItem) => {
        componentManager.events.showPanel({
          type: 'vm',
          id: data.dn,
          name: data.name,
        });
        componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        componentManager.events.selectObject(vmItem);
      },
      (data, siteItem, dataWithAllLayer) => {
        componentManager.events.showPanel({
          type: 'site',
          id: data.dn,
          name: data.name,
        });
        componentManager.cardManager.removeCard({ id: data.dn }, HOVER_CARD_TYPE);
        componentManager.events.selectSite(siteItem);
        renderPanelLeftDesc(compIns, componentManager, dataWithAllLayer);
      },
      () => {
        componentManager.events.selectNull();
      },
    );
    return true;
  });
};

/**
 * 给3D视图添加交互事件
 */
export const bindIncidentViewEvents = (compIns, w3d, componentManager) => {
  initDefaultView4Scene(compIns, w3d, componentManager);
  bindHoverEvents(compIns, w3d, componentManager);
  bindClickEvents(compIns, w3d, componentManager);
};
