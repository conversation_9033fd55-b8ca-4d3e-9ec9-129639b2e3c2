import { getObjectSize } from '@pages/businesstopo/a3dPages/w3d/components/common/utils';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

let clickTimer = null;
let clickObj = null;
export const bindDblclickEvents = (object3D, clickEv, dblclickEv) => {
  A3D.DOM.bind(object3D, 'click', (e, info) => {
    clearTimeout(clickTimer);
    if (clickTimer === null || clickObj !== object3D) {
      clickObj = object3D;
      // 第一次点击，启动单击定时器
      clickTimer = setTimeout(() => {
        // 如果没有进行第二次点击，这里执行单击逻辑
        if (clickEv) {
          clickEv(e, info);
        }
        clickTimer = null;
        clickObj = null;
      }, 300);
    } else {
      // 第二次点击
      if (dblclickEv) {
        dblclickEv(e, info);
      }
      clickTimer = null;
      clickObj = null;
    }
    return true;
  });
};

const bindHoverEvents = (object3D, w3d, componentManager) => {
  const name = object3D.material.map.userData.detailName;
  const id = `detail_${name}`;
  const type = 'nameDetailRight';

  A3D.DOM.bind(object3D, 'hoverIn', () => {
    componentManager.cardManager.updateCard(
      type,
      { id, name },
      object3D,
    );
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(object3D, 'hoverOut', () => {
    componentManager.cardManager.removeCard({ id, name }, type);
    w3d.agent.renderOnce();
    return true;
  });
};

export const initSystemName = (name, align, compIns, wrapperGroup, { w3d, componentManager }) => {
  let sprite;
  const material = w3d.themeManager.referStyle(name);
  const nameSize = 0.8;
  if (material) {
    sprite = new A3D.C.Sprite(material);

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.center.x = 1;
    sprite.renderOrder = 2;
  }

  if (sprite) {
    const size = getObjectSize(compIns);
    sprite.position.copy(size.max).add(size.min).multiplyScalar(0.5);
    if (align === 'bottom') {
      sprite.position.y = size.min.y + nameSize * 0.5;
    }
    sprite.position.x -= size.width * 0.5 + 1.5;
    bindHoverEvents(sprite, w3d, componentManager);
    wrapperGroup.add(sprite);
  }
};
