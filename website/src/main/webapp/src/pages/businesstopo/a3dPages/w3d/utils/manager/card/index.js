import Card from '../../../components/Card';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import './card.less';

const A3D = getA3D();

const _offset = { x: 0, y: 0, z: 0 };

const strategies = {
  pod: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-pod');
      return dom;
    },
    offset: {
      x: 0,
      y: 2,
      z: 0,
    },
    show: true,
  },
  tps: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-tps');
      return dom;
    },
    offset: {
      x: 0,
      y: 0,
      z: 0,
    },
    show: true,
  },
  groupDetail: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-groupDetail');
      return dom;
    },
    offset: {
      x: 0.5,
      y: 0.5,
      z: 0,
    },
    show: true,
  },
  businessDetail: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-groupDetail');
      return dom;
    },
    offset: {
      x: 0.5,
      y: 0.5,
      z: 0,
    },
    show: true,
  },
  db: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-db');
      return dom;
    },
    offset: {
      x: 0,
      y: 1.0,
      z: 0,
    },
    show: true,
  },
  site: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-db');
      return dom;
    },
    offset: {
      x: 0,
      y: 1.0,
      z: 0,
    },
    show: true,
  },
  dbLine: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-dbLine');
      return dom;
    },
    offset: {
      x: 0,
      y: 0,
      z: 0,
    },
    show: true,
  },

  podDrillDown: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-pod');
      return dom;
    },
    offset: {
      x: 0,
      y: 1.2,
      z: 0,
    },
    show: true,
  },
  podDrillDownDeital: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-podDetail');
      return dom;
    },
    offset: {
      x: 0,
      y: 0,
      z: 0,
    },
    show: true,
  },

  nameDetail: {
    getDom: (data) => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-nameDetail');
      dom.innerText = data.data.name;
      return dom;
    },
    offset: {
      x: 0,
      y: 1,
      z: 0,
    },
    show: true,
  },

  nameDetailRight: {
    getDom: (data) => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-nameDetail', 'w3d-nameDetailRight');
      dom.innerText = data.data.name;
      return dom;
    },
    offset: {
      x: 0,
      y: 1,
      z: 0,
    },
    show: true,
  },

  incidentPanelDetail: {
    getDom: () => {
      const dom = window.document.createElement('div');
      dom.classList.add('w3d-card', 'w3d-incidentPanelDetail');
      return dom;
    },
    offset: {
      x: 0,
      y: 0,
      z: 0,
    },
    show: true,
  },
};

const handler = {
  add: (type, dataSetting, cardWrapper, w3d, cardManager) => {
    const props = {
      getDom: (_dataSetting) => {
        const dom = strategies[type].getDom(_dataSetting, w3d, cardManager);
        return dom;
      },
      updateDom: (dom, _dataSetting) => {
        if (strategies[type].updateDom) {
          strategies[type].updateDom(dom, _dataSetting, w3d, cardManager);
        }
      },
      offset: strategies[type].offset || _offset,
    };
    let fnKeys = ['showDom', 'hideDom'];
    fnKeys.forEach((key) => {
      if (strategies[type][key]) {
        props[key] = (dom) => {
          strategies[type][key](dom);
        };
      }
    });
    const card = new Card(dataSetting, props);
    cardWrapper.add(card);
    return card;
  },
  destroy: (type, id, cardManager) => {
    if (cardManager.events.destroyCardDom) {
      cardManager.events.destroyCardDom({
        type,
        id,
      });
    }
    if (strategies[type] && strategies[type].destroyDom) {
      strategies[type].destroyDom(id, cardManager);
    }
  },
};

export default class CardManager {
  constructor(w3d, events, componentManager) {
    this.w3d = w3d;
    this.events = { ...events };
    this.componentManager = componentManager;

    this.cards = {};
    this.cardData = {};
    this.cardParent = {};

    this.cardWrapper = A3D.createGroup();
    this.cardWrapper.name = 'cardGroup';
    w3d.agent.add(this.cardWrapper);

    this.selectDeviceId = '';
    this.selectCardType = '';

    this.warnStatusMap = {};

    Object.keys(strategies).forEach((key) => {
      this.cards[key] = {};
      this.cardData[key] = {};
      this.cardParent[key] = {};
    });
  }

  #addCard(type, dataSetting, parent) {
    const id = dataSetting.id;
    if (this.#isShow(type, dataSetting) || this.#isSelected(type, dataSetting)) {
      const card = handler.add(type, dataSetting, parent, this.w3d, this);

      this.cards[type][id] = card;
    }
    this.cardData[type][id] = dataSetting;
    this.cardParent[type][id] = parent;
  }

  #updateCard(type, dataSetting) {
    const id = dataSetting.id;
    const card = this.cards[type][id];

    card.set(dataSetting);
    Object.assign(this.cardData[type][id].data, dataSetting.data);
    this.cardData[type][id].position = dataSetting.data.position;
    if (!(this.#isShow(type, dataSetting) || this.#isSelected(type, dataSetting))) {
      this.#hideCard(id, type);
    }
  }

  updateCard(type, data, parent = this.cardWrapper) {
    if (!strategies[type]) {
      return;
    }
    const id = data.uuid || data.id;
    const card = this.cards[type][id];
    const dataSetting = {
      id,
      position: data.position,
      data,
    };
    if (card) {
      this.#updateCard(type, dataSetting);
    } else {
      this.#addCard(type, dataSetting, parent);
    }
    this.w3d.agent.renderOnce();
  }

  #removeCard(id, type) {
    const data = this.cardData[type][id];
    if (data) {
      const card = this.cards[type][id];
      if (card) {
        card.destroy();
      }
      delete this.cards[type][id];
      delete this.cardData[type][id];
      delete this.cardParent[type][id];

      handler.destroy(type, id, this);
    }
  }

  #hideCard(id, type) {
    const data = this.cardData[type] && this.cardData[type][id];
    if (data) {
      const card = this.cards[type][id];
      if (card) {
        card.destroy();
      }
      delete this.cards[type][id];

      handler.destroy(type, id, this);
    }
  }

  removeCard(data, type) {
    const id = data.uuid || data.id;
    if (strategies[type]) {
      this.#removeCard(id, type);
    } else {
      Object.keys(strategies).forEach((key) => {
        this.#removeCard(id, key);
      });
    }
    this.w3d.agent.renderOnce();
  }

  setWarnStatus(statusMap) {
    this.warnStatusMap = statusMap;
  }

  select(id = '', type = '') {
    let _selectDeviceId = this.selectDeviceId;
    let _selectCardType = this.selectCardType;
    const selectedCard = this.cards[_selectCardType] && this.cards[_selectCardType][_selectDeviceId];
    let selectedData;
    if (selectedCard) {
      if (_selectDeviceId !== id || _selectCardType !== type) {
        selectedData = this.cardData[_selectCardType][_selectDeviceId];
        selectedCard.set({ id: _selectDeviceId, showCard: false });

        if (!this.#isShow(_selectCardType, selectedData)) {
          this.#hideCard(_selectDeviceId, _selectCardType);
        }
      }
    }

    selectedData = this.cardData[type] && this.cardData[type][id];
    if (selectedData) {
      let selectingCard = this.cards[type][id];
      this.selectDeviceId = id;
      this.selectCardType = type;
      if (!selectingCard) {
        this.#addCard(type, selectedData, this.cardParent[type][id]);
        selectingCard = this.cards[type][id];
      }
      selectingCard.set({ id, showCard: true });
    } else {
      this.selectDeviceId = '';
      this.selectCardType = '';
    }
    this.w3d.agent.renderOnce();
  }

  #isShow(type, dataSetting) {
    const isWarn = Object.keys(this.warnStatusMap).some((key) => {
      const isKeyWarn = this.warnStatusMap[key].includes(dataSetting.data[key]);
      return isKeyWarn;
    });
    return strategies[type].show || isWarn;
  }

  #isSelected(type, dataSetting) {
    return this.selectCardType === type && this.selectDeviceId === dataSetting.id;
  }

  hideAllCard(hide = false) {
    Object.keys(strategies).forEach((type) => {
      const cardDatas = this.cardData[type];
      Object.keys(cardDatas).forEach((id) => {
        const dataSetting = cardDatas[id];
        if (hide || !this.#isShow(type, dataSetting)) {
          this.#hideCard(id, type);
        } else {
          this.#addCard(type, dataSetting, this.cardParent[type][id]);
        }
      });
    });
    this.selectDeviceId = '';
    this.selectCardType = '';
  }

  clear() {
    Object.keys(this.cards).forEach((type) => {
      Object.keys(this.cards[type]).forEach((id) => {
        this.#removeCard(id, type);
      });
    });
  }
}
