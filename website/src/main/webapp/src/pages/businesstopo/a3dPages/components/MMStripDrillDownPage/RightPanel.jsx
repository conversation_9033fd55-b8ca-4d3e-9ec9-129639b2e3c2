/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useState} from 'react';

import {Panel} from '../../../components/mm/StripDrillDown/RightPanel';
import {BUSINESS_TOPO_CONTEXT} from '../../../const';

function RightPanel(props) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {showKPIPanel} = state;

  const {refreshFlag} = props;
  return (
    <div
      className="business-right-panel"
      style={{
        width: showKPIPanel ? '400px' : '0px',
        overflowX: 'hidden',
        zIndex: 9999,
      }}
    >
      <div
        className="dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-bottomPanel"
        style={{
          position: 'fixed', zIndex: 1000, opacity: 1,
        }}
        onClick={() => {
          dispatch({showKPIPanel: !showKPIPanel});
        }}
      >
        <div className={`topo-mm-common-panel-${showKPIPanel ? 'close' : 'expend'}-vertical-right`} />
      </div>

      <div style={{width: '400px'}}>
        <div
          className="right-panel-container"
          style={{visibility: showKPIPanel ? 'visible' : 'hidden'}}
        >
          <Panel refreshFlag={refreshFlag} />
        </div>
      </div>
    </div>
  );
}

export default RightPanel;
