import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {getObjectSize} from '../common/utils';
import {drawRoundedRect} from '../utils';

const A3D = getA3D();
const SIZE_TIME = 0.4;

export default class GroupItem extends A3D.C.Object3D {
  data;

  #modelMap;

  #matMap;

  constructor(data, modelMap, matMap) {
    super();

    this.data = data;
    this.#modelMap = modelMap;
    this.#matMap = matMap;

    this.#init();
  }

  hoverIn() {
    this.#updateName(true);
  }

  hoverOut() {
    this.#updateName(false);
  }

  update(data) {
    const oldData = this.data;
    this.data = data;
    if (data.alarmCount !== oldData.alarmCount || data.isActiveDv !== oldData.isActiveDv || data.isHealthy !== oldData.isHealthy) {
      this.#updateModel();
      this.#updateWarn();
    }
  }

  #init() {
    this.#initModel();
    this.#initName();

    const warnGroup = new A3D.C.Group();
    warnGroup.name = 'warnGroup';
    this.add(warnGroup);
    this.#initWarn(warnGroup);
  }

  #initModel() {
    const groupGeo = this.#modelMap.site.group.geometry;
    const groupMatNormal = this.#matMap.site.group.normal;
    const groupMatAlarm = this.#matMap.site.group.alarm;
    const groupMatGray = this.#matMap.site.group.gray;
    let mat;
    if (this.data.isActiveDv === 0) {
      mat = groupMatGray;
    } else {
      if (this.data.alarmCount > 0) {
        mat = groupMatAlarm;
      } else {
        if (this.data.isHealthy === 0) {
          mat = groupMatAlarm;
        } else {
          mat = groupMatNormal;
        }
      }
    }
    const businessGroupItem = new A3D.C.Mesh(groupGeo, mat);
    this.add(businessGroupItem);
  }

  #initName() {
    const modelSize = getObjectSize(this.#modelMap.site.group);

    const name = this.data.groupName;
    const material = this.#matMap.site.groupName[name];
    const sprite = new A3D.C.Sprite(material);

    const nameSize = modelSize.depth * SIZE_TIME;
    sprite.position.z = modelSize.depth + nameSize * 0.5;
    sprite.position.y = nameSize * 0.6;

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.renderOrder = 2;

    this.add(sprite);
  }

  #updateName(hover) {
    const modelSize = getObjectSize(this.#modelMap.site.group);

    const name = hover ? `${this.data.groupName}_full` : this.data.groupName;
    const material = this.#matMap.site.groupName[name];
    const sprite = this.children[1];
    sprite.material = material;

    const nameSize = modelSize.depth * SIZE_TIME;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
  }

  #initWarn(warnGroup) {
    const alarmCount = this.data.alarmCount;
    if (alarmCount <= 0) {
      return;
    }
    const modelSize = getObjectSize(this.#modelMap.site.group);
    const alarmSize = modelSize.depth * 0.32;

    const canvas = this.#initCanvas();
    const texture = A3D.canvasToTex(canvas, true);
    const material = new A3D.C.SpriteMaterial({
      map: texture,
      color: '#FFFFFF',
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    sprite.renderOrder = 2;
    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
    sprite.center.x = 0;
    sprite.position.y = modelSize.height * 1.15;
    sprite.position.x = modelSize.width * 0.15;

    warnGroup.add(sprite);
  }

  #initCanvas() {
    const alarmCount = this.data.alarmCount;
    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    return canvas;
  }

  #drawCanvas(canvas, content) {
    const pixelRatio = 4;
    const lineHeight = 24 * pixelRatio;
    const fontSize = 18 * pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    const size = ctx.measureText(content.slice(1));

    canvas.width = size.width + lineHeight + 2;
    canvas.height = lineHeight + 2;

    drawRoundedRect(ctx, {x: 1, y: 1}, canvas.width - 2, lineHeight, lineHeight * 0.5);
    // 填充路径
    ctx.fillStyle = '#F43146';
    ctx.fill();

    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = '#ffffff';

    const HALF = 2;
    const FONT_SPACE = 0.1;
    const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
    ctx.fillText(content, canvas.width / HALF, top);
  }

  #updateModel() {
    const businessGroupItem = this.children[0];
    const groupMatNormal = this.#matMap.site.group.normal;
    const groupMatAlarm = this.#matMap.site.group.alarm;
    const groupMatGray = this.#matMap.site.group.gray;
    let mat;
    if (this.data.isActiveDv === 0) {
      mat = groupMatGray;
    } else {
      if (this.data.alarmCount > 0) {
        mat = groupMatAlarm;
      } else {
        if (this.data.isHealthy === 0) {
          mat = groupMatAlarm;
        } else {
          mat = groupMatNormal;
        }
      }
    }
    businessGroupItem.material = mat;
  }

  #updateWarn() {
    const alarmCount = this.data.alarmCount;
    const warnGroup = this.children[2];
    const warn = warnGroup.children[0];
    if (alarmCount <= 0) {
      if (warn) {
        warn.geometry.dispose();
        warn.material.dispose();
        warnGroup.remove(warn);
      }
    } else {
      if (warn) {
        this.#updateWarnCanvas(warn, alarmCount);
      } else {
        this.#initWarn(warnGroup);
      }
    }
  }

  #updateWarnCanvas(warn, alarmCount) {
    const sprite = warn;
    const alarmSize = sprite.scale.y;

    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    sprite.material.map = A3D.canvasToTex(canvas, true);

    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
  }
}
