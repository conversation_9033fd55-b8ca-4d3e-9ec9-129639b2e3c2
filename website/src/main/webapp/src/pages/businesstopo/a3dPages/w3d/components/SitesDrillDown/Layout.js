export default class Layout {
  static initLayout(level1Data, aspectRatio) {
    const computedCount = level1Data.children.reduce((pre, cur) => {
      let result = 2;
      if (cur.count) {
        const MIN_COL = 4;
        result = Math.ceil(cur.count / MIN_COL) * MIN_COL;
      }
      return pre + result;
    }, 0);
    const maxRow = Math.ceil(Math.sqrt(computedCount / aspectRatio));
    const maxCol = maxRow * aspectRatio;

    return this.#getLayoutSetting(level1Data, maxRow, maxCol);
  }

  static #getLayoutSetting(level1Data, maxRow, maxCol) {
    const level1Layout = [{ row: maxRow, col: 0, group: [] }];
    this.#setLayout(level1Layout, level1Data, maxCol);
    const level1Setting = {
      data: level1Data.data || {},
      row: level1Layout.reduce((total, cur) => total + cur.row, 0),
      col: level1Layout.reduce((total, cur) => Math.max(total, cur.col), 0),
      level1: level1Layout,
    };
    return level1Setting;
  }

  static #setLayout(level1Layout, level1Data, maxCol) {
    let level1GroupIndex = 0;
    const maxIndex = level1Layout.length;
    for (let i = 0, len = level1Data.children.length; i < len; i++) {
      const level2Data = level1Data.children[i];
      let level2Setting = this.#getLevel1Setting(level2Data, level1Layout[level1GroupIndex].row);
      if (level1Layout[level1GroupIndex].col + level2Setting.col > maxCol) {
        if (level1GroupIndex < maxIndex - 1) {
          level1GroupIndex++;
          level2Setting = this.#getLevel1Setting(level2Data, level1Layout[level1GroupIndex].row);
        } else {
          // 超出最长列数，重置，重新布局
          level1Layout[0].row--;
          level1Layout.forEach((level1Group) => {
            level1Group.col = 0;
            level1Group.group.length = 0;
          });
          level1Layout.push({ row: 1, col: 0, group: [] });
          this.#setLayout(level1Layout, level1Data, maxCol);
          return;
        }
      }
      level1Layout[level1GroupIndex].col += level2Setting.col;
      level1Layout[level1GroupIndex].group.push(level2Setting);
    }
    for (let i = level1Layout.length - 1; i >= 0; i--) {
      if (level1Layout[i].group.length === 0) {
        level1Layout.pop();
      }
    }
  }

  static #getLevel1Setting(level2Data, curRow) {
    const curCount = level2Data.count || 1;
    const col = Math.ceil(curCount / curRow / 2) * 2;
    const level2Layout = [];
    let stayCount = level2Data.count;
    while (stayCount > 0) {
      level2Layout.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    const row = level2Layout.length;
    return {
      data: level2Data.data || {}, col, row, level2: level2Layout,
    };
  }
}
