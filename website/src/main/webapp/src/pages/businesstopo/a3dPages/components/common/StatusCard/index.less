.statusCard {
  width: 100%;
  min-height: 42rem;
  position: relative;
  margin-top: 1rem;

  .statusCard_tabs {
    display: flex;
    font-size: 1.25rem;
    color: #bbb;
    font-weight: Medium;
    margin-bottom: 1.5rem;

    .tip {
      display: inline-block;
      min-width: 2.5rem;
      height: 1.625rem;
      cursor: pointer;
      margin-right: 1.5rem;
      position: relative;

      .line {
        display: inline-block;
        height: 0.125rem;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }

      &.active {
        color: #2e94ff;

        .line {
          background-color: #2e94ff;
        }
      }
    }
  }

  .statusCard_tag {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #bbbbbb;
    margin-bottom: 1rem;

    .status_tag {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 1.5rem;
      padding: 0 0.5rem;
      cursor: pointer;

      &.active {
        background: rgba(46, 148, 255, 0.1);
        border-radius: 1rem;
        color: #2e94ff;
      }
    }
  }

  .statusCard_item {
    background: #313131;
    color: #eeeeee;
    border-radius: 0.5rem;
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
    margin: 0.5rem 0;
    position: relative;

    &:hover {
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.5);
    }

    .title {
      display: flex;
      font-size: 1rem;
      color: #f5f5f5;
      line-height: 1.5rem;
      font-weight: Medium;
      margin-bottom: 1rem;
      gap: 0.5rem;
      width: 18rem;

      .icon {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        background-size: 100% 100%;
      }

      .text {
        display: inline-block;
        width: 17rem;
      }
    }

    .tag {
      position: absolute;
      width: 3.5rem;
      height: 1.625rem;
      display: flex;
      background-size: 100% 100%;
      right: 0;
      top: 1.3125rem;
      font-size: 0.75rem;
      align-items: center;
      justify-content: center;
    }

    &.urgent {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_icon.svg);
        }
      }

      .tag {
        color: #e54545;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_tag.svg);
      }
    }

    &.minor {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_icon.svg);
        }
      }

      .tag {
        color: #ffbb33;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_tag.svg);
      }
    }

    &.important {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_icon.svg);
        }
      }

      .tag {
        color: #ff8000;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_tag.svg);
      }
    }

    &.hints {
      .title {
        .icon {
          background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_icon.svg);
        }
      }

      .tag {
        color: #2e94ff;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_tag.svg);
      }
    }

    .desc {
      font-size: 0.875rem;
      color: #bbbbbb;
      line-height: 1rem;
      height: 1rem;
      max-width: 20rem;
      overflow: hidden;
      margin-bottom: 1rem;
    }

    .time {
      font-size: 0.875rem;
      color: #f5f5f5;
      line-height: 1rem;
      display: flex;

      span {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/time_icon.svg);
        margin-right: 0.5rem;
        background-size: 100% 100%;
      }
    }
  }
}