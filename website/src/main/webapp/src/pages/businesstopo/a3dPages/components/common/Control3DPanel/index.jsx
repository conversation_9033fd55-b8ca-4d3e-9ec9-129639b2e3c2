import './index.less';
import resetCamera from '@pages/businesstopo/a3dPages/w3d/assets/images/resetCamera.svg';
import zoomIn from '@pages/businesstopo/a3dPages/w3d/assets/images/zoomIn.svg';
import zoomOut from '@pages/businesstopo/a3dPages/w3d/assets/images/zoomOut.svg';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import React from 'react';
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/control3DPanel';

registerResource(i18n, 'control3D');
const data = [
  [
    {
      name: getMessage('control3D.defaultView'),
      value: 'resetCamera',
      icon: resetCamera,
      fn: () => {
        eventBus.emit('to3d_resetCamera');
      },
    },
  ],
  [
    {
      name: getMessage('control3D.zoomIn'),
      value: 'zoomIn',
      icon: zoomIn,
      fn: () => {
        eventBus.emit('to3d_zoomIn');
      },
    },
    {
      name: getMessage('control3D.zoomOut'),
      value: 'zoomOut',
      icon: zoomOut,
      fn: () => {
        eventBus.emit('to3d_zoomOut');
      },
    },
  ],
];

function Control3DPanel() {
  return (
    <div className="control3DPanel">
      {data.map((item, index) => (
        <div className="control3DPanel_btnGroup" key={index}>
          {
            item.map((subItem) => (
              <div
                className="w3d-icon"
                onClick={() => subItem.fn()}
                data-title={subItem.name}
                key={index}
              >
                <img src={subItem.icon} />
              </div>
            ))
          }
        </div>
      ))}
    </div>
  );
}
export default Control3DPanel;
