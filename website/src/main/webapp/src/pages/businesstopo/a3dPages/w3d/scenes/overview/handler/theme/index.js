import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { getCanvasTex } from '../../../../components/utils';
import { initMap } from '../../../commonHandler/ThemeHandler';

const A3D = getA3D();

let _default = {};
let _dark = {};

let nameTextureMap = {};
const getTextureMap = (data) => {
  data.north.forEach((north) => {
    const businessId = north.businessId;
    const businessName = north.businessName;
    if (!nameTextureMap[businessId]) {
      nameTextureMap[businessId] = getCanvasTex(businessName, 12);
    }
  });
  data.south.forEach((south) => {
    const businessId = south.businessId;
    const businessName = south.businessName;
    if (!nameTextureMap[businessId]) {
      nameTextureMap[businessId] = getCanvasTex(businessName, 12);
    }
  });
  data.site.forEach((sites) => {
    sites.siteList.forEach((site) => {
      site.groupList.forEach((drGroup) => {
        const groupName = drGroup.groupName;
        if (!nameTextureMap[groupName]) {
          nameTextureMap[groupName] = getCanvasTex(groupName, 8);
          nameTextureMap[`${groupName}_full`] = getCanvasTex(groupName, Infinity);
        }
      });
    });
  });
  nameTextureMap.northServiceSystem = getCanvasTex(data.i18n.northServiceSystem, 12);
  nameTextureMap.southServiceSystem = getCanvasTex(data.i18n.southServiceSystem, 12);
  nameTextureMap.CBS = getCanvasTex(data.i18n.CBS, 12);
};

const addGroupName = async(data) => {
  getTextureMap(data);
  Object.keys(nameTextureMap).forEach((name) => {
    if (!_default[name]) {
      _default[name] = new A3D.C.SpriteMaterial({
        map: nameTextureMap[name],
        color: '#BBBBBB',
        depthWrite: false,
      });
      _dark[name] = new A3D.C.SpriteMaterial({
        map: nameTextureMap[name],
        color: '#BBBBBB',
        depthWrite: false,
      });
    }
  });
};

export const resetTheme = async(defaultTheme, darkTheme, data, w3d) => {
  _default = {};
  _dark = {};
  nameTextureMap = {};
  await initMap(defaultTheme, darkTheme, w3d);
  await addGroupName(data);
  Object.assign(defaultTheme, _default);
  Object.assign(darkTheme, _dark);
};
