import eventBus from '@pages/businesstopo/a3dPages/bus';

const addZero = function(value) {
  return value < 10 ? `0${value}` : value;
};

export const getTime = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = addZero(date.getMonth() + 1);
  const day = addZero(date.getDate());
  const hours = addZero(date.getHours());
  const minutes = addZero(date.getMinutes());
  const seconds = addZero(date.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export const eventBusHandler = {
  on: (eventName, fn) => {
    eventBus.addListener(eventName, fn);
  },
  off: (eventName, fn) => {
    eventBus.removeListener(eventName, fn);
  },
  emit: (eventName, ...rest) => {
    eventBus.emit(eventName, ...rest);
  },
};

export const getQuery = () => {
  const queryStr = window.location.href.match(/(?<=[?&])[^?&]+/g);
  const map = {};
  if (!queryStr) {
    return map;
  }
  queryStr.forEach((str) => {
    const keyVal = str.split('=');
    map[keyVal[0]] = keyVal[1];
  });
  return map;
};

export const getRouteQuery = () => {
  const query = getQuery();
  const result = {
    showGui: query.debug === '1',
    msaa: query.msaa !== '0',
    hdp: 0,
  };
  return result;
};

const isA3DDev = () => window.location.hash.includes('serviceTopology');

// 到总览视图
export const toOverviewPage = () => {
  let hash = '';
  if (isA3DDev()) {
    hash = '/serviceTopology/overview';
  } else {
    hash = `path=/businesstopo&_t=${Math.round(Date.now() / 1000)}`;
  }
  window.location.hash = hash;
};

// 到站点下钻
export const toSiteDrillDownPage = (siteId, groupId) => {
  let hash = '';
  const query = `${siteId}${groupId ? `&selectMold=${groupId}` : ''}`;
  if (isA3DDev()) {
    hash = `/serviceTopology/siteDrillDown?${query}`;
  } else {
    hash = `path=/businesstopo/sitehome&${query}&_t=${Math.round(Date.now() / 1000)}`;
  }
  window.location.hash = hash;
};

// 到条带下钻
export const toMMStripDrillDownPage = (stripId, stripUnit, solutionId, stripName) => {
  let hash = '';
  const query = `stripId=${stripId}&stripName=${stripName}&solutionId=${solutionId}${stripUnit ? `&stripUnit=${stripUnit}` : ''}`;
  const currentUrl = window.location.href;
  hash = `#path=/businesstopo/mmStripDrillDown&${query}&_t=${Math.round(Date.now() / 1000)}`;
  window.history.replaceState({}, '', currentUrl.split('#')[0] + hash);

  eventBus.emit('changeCrumbs', (stripId, stripUnit, solutionId, stripName));
};
// 到网元下钻
export const toPodDrillDownPage = ({
  siteId, siteName, moTypeId, moTypeName,
}) => {
  let hash = '';
  const query = `siteId=${siteId}&moTypeId=${moTypeId}&siteName=${siteName}&moTypeName=${moTypeName}`;
  hash = `path=/businesstopo/mohome&${query}&_t=${Math.round(Date.now() / 1000)}`;

  window.history.replaceState({}, '', oldUrl.split('#')[0] + hash);
};

// 到数据库视图
export const toDatabasePage = () => {
  let hash = '';
  if (isA3DDev()) {
    hash = '/serviceTopology/database';
  } else {
    hash = `path=/businesstopo/database&_t=${Math.round(Date.now() / 1000)}`;
  }
  window.location.hash = hash;
};


export const sortListByName = (list, key) =>{
  return list.sort((a, b) => {
    if (a[key] < b[key]) {return -1;}
    if (a[key] > b[key]) {return 1;}
    return 0;
  });
};
