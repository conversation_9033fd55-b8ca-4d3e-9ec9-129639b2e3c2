import '@pages/businesstopo/a3dPages/styles/split.less';

import React, {
  useEffect, useRef, useState, useReducer,
} from 'react';
import {BUSINESS_TOPO_CONTEXT as TopoContext} from '@pages/businesstopo/const';
import {initState, reducer} from '@pages/businesstopo/reducer';
import { ConfigProvider } from 'eview-ui';
import Crumbs from 'eview-ui/Crumbs';
import {
  destroyW3D,
  initW3D,
  updateSiteData,
  resizeW3D, updateSiteDataStatus,
} from './MMStripDrillDown3D';
import {getQuery, toMMStripDrillDownPage} from '../utils';
import Toolbar from '../../../components/toolbar/Toolbar';
import TimeLinePanel from '../../../components/timeline/TimeLinePanel';
import {STRIP_VIEW} from '../../../const/timeLine';
import i18n from '@pages/businesstopo/locales/sitedrilldown';
import {$t, registerResource} from '@util';
import {refeshTime} from '../../../const';
import {setSessionData} from '../../../util';
import {queryCurrentIds} from '../../../api';
import MessageDeatil from '../../../components/tooltip/messageDeatil';
import {querySiteGridData} from '@pages/businesstopo/api';

import RightPanel from './RightPanel';
import '../../../css/index.less';
import eventBus from '../../bus';

registerResource(i18n, 'tooltip');

async function changeStripe(data) {
  const query = getQuery();
  if (data.siteId === query.stripUnit) {
    return;
  }
  let {
    stripId,
    solutionId,
    selectedTime,
  } = JSON.parse(sessionStorage.getItem('topoSession'));
  let param;
  if (selectedTime > 0) {
    // 时间回溯
    param = {
      siteId: stripId,
      timestamp: selectedTime,
      solutionId,
      unitId:data.siteId,
    };
  } else {
    // 实时场景
    param = {
      siteId: query.stripId,
      timestamp: 0,
      solutionId:query.solutionId,
      unitId:data.siteId,
    };
  }
  const _stripData = await querySiteGridData(param);
  toMMStripDrillDownPage(query.stripId, data.siteId, query.solutionId, query.stripName);
  updateSiteData(_stripData.data);
}

const init = async(w3dContainerRef, stripId, solutionId, selectedTime, dispatch) => {
  let param = {
    siteId: stripId,
    timestamp: selectedTime || 0,
    solutionId,
    unitId: '',
  };
  const query2 = getQuery();

  if (query2.stripUnit) {
    param.unitId = query2.stripUnit;
  }
  const stripData = await querySiteGridData(param);

  toMMStripDrillDownPage(query2.stripId, query2.stripUnit ? query2.stripUnit : stripData.data.stripeUnitList[0], query2.
    solutionId, query2.stripName);
  dispatch({
    mmStripTimeLineFlag: new Date().getTime(),
  });
  await initW3D(w3dContainerRef, {
    stripData: stripData.data,
    changeSite: async(data) => {
      await changeStripe(data);
    },
  },
  );
};


const getStripAndUnitName = () => {
  const query = getQuery();
  if (query.stripUnit) {
    return `${decodeURIComponent(getQuery().stripName)}(${decodeURIComponent(getQuery().stripUnit)})`;
  }
  return getQuery().stripName || $t('stripName');
};

function MMStripDrillDown3D() {
  const [state, dispatch] = useReducer(reducer, initState);
  const [data, setData] = useState([]);
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const intervalId = useRef(0);
  const w3dContainerRef = useRef();
  const query = getQuery();

  const {showKPIPanel} = state;
  // 定时刷新
  useEffect(async() => {
    if (refreshFlag === 0) {
      return;
    }
    const refreshquery = getQuery();

    let param = {
      siteId: refreshquery.stripId,
      timestamp: 0,
      solutionId: refreshquery.solutionId,
      unitId: '',
    };
    if (refreshquery.stripUnit) {
      param.unitId = refreshquery.stripUnit;
    }
    const siteData = await querySiteGridData(param);
    siteData.isRefresh = true;
    updateSiteDataStatus(siteData.data);
  }, [refreshFlag]);

  useEffect(() => {
    resizeW3D();
  }, [showKPIPanel]);

  useEffect(() => {
    const handleChangeCrumbs = () => {
      setData([
        {
          title: $t('overViewName'),
          url: '/eviewwebsite/index.html#path=/businesstopo',
        }, {
          title: getStripAndUnitName(),
        },
      ]);
    };

    eventBus.addListener('changeCrumbs', handleChangeCrumbs);
    window.addEventListener('hashchange', handleChangeCrumbs);
    return () => {
      eventBus.removeListener('changeCrumbs', handleChangeCrumbs);
      window.removeEventListener('hashchange', handleChangeCrumbs);
    };
  }, []);

  useEffect(() => {
    setData([
      {
        title: $t('overViewName'),
        url: '/eviewwebsite/index.html#path=/businesstopo',
      }, {
        title: getStripAndUnitName(),
      },
    ]);
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    if (topoSession.selectedTime === 0) {
      setTimeout(()=>{
        let newTopoSession = JSON.parse(
          sessionStorage.getItem('topoSession') || '{}',
        );
        if (newTopoSession.selectedTime === 0) {
          startRefresh();
        }
      }, refeshTime);
    }
    eventBus.addListener('to3d_updateDrillDownData', () => {
      dispatch({
        mmStripTimeLineFlag: new Date().getTime(),
      });
      setData([
        {
          title: $t('overViewName'),
          url: '/eviewwebsite/index.html#path=/businesstopo',
        }, {
          title: getStripAndUnitName(),
        },
      ]);
    });
    if (topoSession.selectedTime > 0) {
      return ()=>{
        // 空实现
      };
    }
    init(w3dContainerRef, query.stripId, query.solutionId, topoSession.selectedTime, dispatch);
    return () => destroyW3D();
  }, []);

  const startRefresh = () => {
    setRefreshFlag(new Date().getTime());
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, refeshTime);
    // 如果直接有定时任务了，但是又开启了一个，需要先停止
    if (intervalId.current) {
      clearInterval(intervalId.current);
    }
    intervalId.current = id;
  };
  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };
  const changeUrl = () => {
    const query1 = getQuery();
    let hash = `path=/businesstopo/mmStripDrillDown&stripId=${query1.stripId}&stripName=${[query1.stripName]}&solutionId=${query1.solutionId}${query1.stripUnit ? `&stripUnit=${query1.stripUnit}` : ''}`;
    window.location.hash = hash;
  };
  // 进入回溯状态 ，stripId 可能会发生变化，需要修改url
  const timeTrack = async time => {
    const query1 = getQuery();
    stopRefresh();
    destroyW3D();
    dispatch({
      selectedTime: time,
      isTimeTrack: true,
      solutionId:'',
      stripId:'',
    });
    setSessionData({
      selectedTime: time,
      isTimeTrack: true,
      solutionId:'',
      stripId:'',
    });
    let param = {
      instanceIdList: [],
      timestamp: 0,
      targetTimestamp: time,
      solutionType: 3,
    };
    param.instanceIdList.push(query1.stripId);
    param.instanceIdList.push(query1.solutionId);
    queryCurrentIds(param, async({data:idMap}) => {
      let param2 = {
        siteId: idMap[query1.stripId],
        timestamp: time,
        solutionId: idMap[query1.solutionId],
        unitId: query1.stripUnit ? query1.stripUnit : '',
      };
      dispatch({
        solutionId: idMap[query1.solutionId],
        stripId: idMap[query1.stripId],
      });
      setSessionData({
        solutionId: idMap[query1.solutionId],
        stripId: idMap[query1.stripId],
      });

      const stripData = await querySiteGridData(param2);
      if (stripData.data.drGroupList.length === 0) {
        setShowMessage(true);
      } else {
        setShowMessage(false);
      }
      await initW3D(w3dContainerRef, {
        stripData: stripData.data,
        changeSite: async(data1) => {
          await changeStripe(data1); 
        },
      },
      );
      let stripUnit = query1.stripUnit ? query1.stripUnit : stripData.data.stripeUnitList[0];
      // 修改浏览器url
      toMMStripDrillDownPage(query1.stripId, stripUnit, query1.solutionId, query1.stripName);
      dispatch({
        mmStripTimeLineFlag: new Date().getTime(),
      });
    });
  };

  // 退出回溯状态
  const backTimeTrack = async() => {
    setShowMessage(false);
    dispatch({selectedTime: 0, isTimeTrack: false});
    setSessionData(
      {
        isTimeTrack: false,
        selectedTime: 0,
      });
    destroyW3D();
    changeUrl();
  };

  return (
    <TopoContext.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div id='business_topo' style={{height: '100%', width: '100%', 'backgroud-color': 'black'}}>
          <div
            id="topology3d_main_container"
            className={
              showKPIPanel ?
                'topology3d_main_container w3d_container' :
                'topology3d_main_container_full w3d_container'
            }
            ref={w3dContainerRef}
          />

          <Toolbar isMMSite={true} isMM={true} />
          <MessageDeatil display={showMessage} main={false} />

          <div
            style={{
              position: 'absolute',
              display: 'inline-block',
              top: 'calc(4rem + 10px)',
              left: '16px',
              zIndex: 9999,
            }}
          >
            <Crumbs data={data} seprator="/" />
          </div>

          <TimeLinePanel
            pageType={STRIP_VIEW}
            renderTopology={timeTrack}
            backTimeTrack={backTimeTrack}
          />

          <RightPanel
            refreshFlag={refreshFlag}
          />
        </div>
      </ConfigProvider>
    </TopoContext.Provider>
  );
}

export default MMStripDrillDown3D;
