
import React, { useEffect, useState, useRef } from 'react';
import Memory from '../common/Memory';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import PodDetails from '../common/PodDetails';

function RightSplit() {
  const splitRef = useRef(null);
  const [paths, setPath] = useState('memory');
  const [node, selectNode] = useState('site');
  const [podId, setPodId] = useState('site1');
  const events = [{
    eventName: 'from3d_showPanel',
    fn: (option) => {
      const { id, type } = option;
      selectNode(type);
      setPodId(id);
    },
  }, {
    eventName: 'from3d_hidePanel',
    fn: () => {
      splitRef?.current.setPanelSize(0);
    },
  }, {
    eventName: 'switchDatabase',
    fn: (option) => {
      setPath(option);
      selectNode('site');
    },
  },
  ];
  useEffect(() => {
    events.forEach((item) => {
      eventBus.addListener(item.eventName, item.fn);
    });
    return () => {
      events.forEach((item) => {
        eventBus.removeListener(item.eventName, item.fn);
      });
    };
  }, []);
  const renderRightDome = () => {
    if (paths === 'memory' || paths === 'physic') {
      if (node === 'site') {
        return <Memory siteId={podId} />;
      }
      return <PodDetails podId={podId} />;
    }
    return '物理库';
  };
  return (
    <div className='split_right'>
      {renderRightDome()}
    </div>
  );
}
export default RightSplit;
