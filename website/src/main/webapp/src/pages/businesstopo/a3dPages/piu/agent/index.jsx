import React, {useState, useEffect, useMemo} from 'react';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {refreshUerAuthenticate} from '@pages/businesstopo/api/timeLine';
import {getSolutionData} from '@pages/businesstopo/api';
import {SOLUTION_TYPE} from '@pages/businesstopo/a3dPages/piu/const';
import Switch from '@pages/businesstopo/a3dPages/piu/components/Switch';
import MMOverview from '@pages/businesstopo/a3dPages/components/MMOverviewPage';
import CBSOverView from '@pages/businesstopo/a3dPages/piu/agent/cbs/Overview';


const BusinessTopo = (props) => {
  const {
    scene = 'cbs', // cbs | mm | bes 三种选择，控制展示那种topo总览页面
    style = {
      background: 'transparent', // 整体背景色
    },
    cameraSetting = {},
    alarmSetting = {},
  } = props;

  const [solutionData, setSolutionData] = useState({
    dataList: [],
    selectIndex: 0,
  });

  useEffect(()=>{
    eventBus.removeAllListener();
    refreshUerAuthenticate(() => {
      let param = {
        timestamp:0,
      };
      getSolutionData(param, res => {
        setSolutionData({
          dataList: res.data || [],
          selectIndex: (res.data || []).findIndex(v => v.solutionType === SOLUTION_TYPE[scene]),
        });
      });
    });
  }, []);

  return (
    <>
      {
        [1, 2].includes(solutionData.dataList[solutionData.selectIndex]?.solutionType) &&
          <CBSOverView
            solutionData={solutionData.dataList[solutionData.selectIndex]}
            style={style}
            cameraSetting={cameraSetting}
            alarmSetting={alarmSetting}
          />
      }
      {
        [3].includes(solutionData.dataList[solutionData.selectIndex]?.solutionType) &&
          <MMOverview
            nowSolutionData={[solutionData.dataList[solutionData.selectIndex]]}
            style={style}
            cameraSetting={cameraSetting}
            alarmSetting={alarmSetting}
          />
      }
      {
        solutionData.dataList.length > 1 &&
        <Switch
          solutionDataList={solutionData.dataList}
          selectIndex={solutionData.selectIndex}
          onClick={(index) => {
            setSolutionData(prevState => {
              return {
                ...prevState,
                selectIndex: index,
              };
            });
          }}
        />
      }
    </>
  );
};

export default BusinessTopo;
