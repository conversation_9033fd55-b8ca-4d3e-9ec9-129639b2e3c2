export default class Layout2 {
  static initLayout(level1Data) {
    return this.#getLayoutSetting(level1Data);
  }

  static #getLayoutSetting(level1Data) {
    const level1Layout = [{ group: [] }];
    this.#setLayout(level1Layout, level1Data);
    const level1Setting = {
      data: level1Data.data || {},
      level1: level1Layout,
    };
    return level1Setting;
  }

  static #setLayout(level1Layout, level1Data) {
    let level1GroupIndex = 0;
    for (let i = 0, len = level1Data.children.length; i < len; i++) {
      const level2Data = level1Data.children[i];
      let level2Setting = this.#getLevel1Setting(level2Data);
      level1Layout[level1GroupIndex].group.push(level2Setting);
    }
  }

  static #getLevel1Setting(level2Data) {
    const curCount = level2Data.count || 1;
    const row = level2Data.data.row;
    const col = Math.ceil(curCount / row);
    const level2Layout = [];
    let stayCount = level2Data.count;
    while (stayCount > 0) {
      level2Layout.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    return {
      data: level2Data.data || {}, col, row, level2: level2Layout,
    };
  }
}
