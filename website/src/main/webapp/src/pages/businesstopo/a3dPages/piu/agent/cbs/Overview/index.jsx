import React, {useEffect, useState, useRef} from 'react';
import {registerResource} from '@util/index';
import i18n from '@pages/businesstopo/locales';
import {getAllBussinessData, getOverViewGrid, getSiteDistritionData} from '@pages/businesstopo/api';
import {getInitCBSW3DData} from '@pages/businesstopo/a3dPages/piu/agent/cbs/util/a3d';
import {initW3D4Piu, destroyW3D4Piu} from '@pages/businesstopo/components/OverviewPage/overview3D';
import '@pages/businesstopo/css/index.less';

const CBSOverView = (props) => {
  registerResource(i18n, 'businessTopo');
  const {style, solutionData = {}, cameraSetting, alarmSetting} = props;

  const w3dContainerRef = useRef(null);
  const w3dObjRef = useRef(null);
  const [overViewData, setOverViewData] = useState({});

  useEffect(() => {
    let param = {
      timestamp: 0,
      solutionId: solutionData.solutionId,
    };

    const initFetchData = async () => {
      const [businessRes, overviewRes] = await Promise.all([
        getAllBussinessData(param),
        getOverViewGrid(param),
      ]);
      let businessDataResult = businessRes.data;

      let currentBussiness = businessDataResult.find(
        item => item.businessType === 'south' && item.isMain,
      );
      let currentNorthBussiness = businessDataResult.find(
        item => item.businessType === 'north' && item.isMain,
      );

      // 请求连线
      const [northLineRsp, southLineRsp] = await Promise.all([
        getSiteDistritionData({instanceId: currentNorthBussiness.businessId}),
        getSiteDistritionData({instanceId: currentBussiness.businessId}),
      ]);

      const initData = getInitCBSW3DData(w3dContainerRef, {
        mainBusiness: {
          north: currentNorthBussiness,
          south: currentBussiness,
        },
        solutionData: solutionData,
        businessDataResult: businessRes.data,
        overviewData: overviewRes.data,
        northLineData: northLineRsp.data,
        southLineData: southLineRsp.data,
      });

      initData.i18n.CBS = solutionData.solutionName;

      // 将业务分组中的告警数据置换成智能体中的数据
      for (const siteGroup of initData.site) {
        for (const site of siteGroup.siteList) {
          for (const group of site.groupList) {
            if (alarmSetting[group.groupId] && alarmSetting[group.groupId].count > 0) {
              group.alarmCount = alarmSetting[group.groupId].count;
            } else {
              group.alarmCount = 0;
            }
            group.isHealthy = group.alarmCount > 0 ? 0 : null;
          }
        }
      }
      // 清空北向和南向的告警信息
      for (const southItem of initData.south) {
        southItem.alarmCsn = null;
      }
      for (const northItem of initData.north) {
        northItem.alarmCsn = null;
      }
      // 清空连线上的TPS数据
      for (const lineItem of initData.line) {
        lineItem.indicatorValue = null;
      }

      if (w3dContainerRef.current) {
        destroyW3D4Piu();
      }
      w3dObjRef.current = await initW3D4Piu(
        w3dContainerRef.current,
        {
          showGui: false,
          msaa: false,
          hdp: 0,
        },
        initData,
        {
          isPiu: true,
          piuCameraSetting: cameraSetting,
          piuData: {
            alarmSetting,
          },
        },
      );
      setOverViewData(initData);
    };

    initFetchData();

    return () => {
      destroyW3D4Piu();
    };
  }, []);

  return (
    <div id='business_topo' style={{height: '100%', width: '100%', background: style.background}}>
      <div
        id='topology3d_main_container'
        className='topology3d_main_container_full w3d_container'
        ref={w3dContainerRef}
      />
    </div>
  );
};


export default CBSOverView;
