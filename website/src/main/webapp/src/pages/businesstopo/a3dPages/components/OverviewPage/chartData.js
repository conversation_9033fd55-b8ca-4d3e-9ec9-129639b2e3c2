import { random } from '@pages/businesstopo/a3dPages/w3d/components/common/utils';

const chartTheme = 'dark';
const serviceData = {
  // 业务数据分布圆环图
  name: 'Pie<PERSON><PERSON>',
  option: {
    theme: chartTheme,
    type: 'circle',
    silent: false,
    padding: [48, 16, 48, 0],
    title: {
      text: '{a|3}\n{b|站点数}',
      textStyle: {
        rich: {
          a: {
            color: '#f5f5f5',
            fontSize: 40,
          },
          b: {
            fontSize: 10,
            color: '#bbb',
            padding: [10, 0, 0, 0],
          },
        },
      },
    },
    legend: { show: true },
    label: {
      show: true,
      type: 'percent',
      line: false,
    },
    data: [
      { value: 45, name: '站点1' },
      { value: 35, name: '站点2' },
      { value: 20, name: '站点3' },
    ],
  },
};

const tpsData = {
  // TPS面积图
  name: 'AreaChart',
  option: {
    theme: chartTheme,
    smooth: true,
    area: true,
    padding: [48, 16, 48, 0],
    legend: { show: false },
    data: [
      { time: '8:00', num: 25 },
      { time: '9:00', num: 30 },
      { time: '10:00', num: 26 },
      { time: '11:00', num: 34 },
      { time: '12:00', num: 28 },
      { time: '13:00', num: 36 },
      { time: '14:00', num: 32 },
      { time: '15:00', num: 38 },
      { time: '16:00', num: 45 },
    ],
    xAxis: {
      data: 'time',
      fullGrid: true,
      interval: 0,
    },
    yAxis: { name: '每秒事物数（TPS）' },
  },
};
const initialTimestamp = new Date().getTime();
const timestamps = [];
for (let i = 0; i < 25; i++) {
  const timestamp = initialTimestamp - i * 60 * 60 * 1000;
  timestamps.push(timestamp);
}

const trendingData = timestamps.map((timestamp) => {
  const percent = random();
  return {
    time: timestamp,
    name: '指标1',
    percent: percent.toFixed(2),
  };
});
const TimelineChartOption = {
  isMove: false,
  trendingData,
  customBackground: {
    emergency: '#E54545', important: '#EC6F1A', general: ' #FFBB33', processed: '#818181',
  },
  tooltip: {
    backgroundColor: '#393939',
    padding: 0,
    borderColor: '#393939',
    position: (point) => [point[0] - 93, point[1] - 100],
  },
  alarmData: [{
    time: new Date('2024-1-2 05:00:00').getTime(),
    type: 'processed',
    quantity: '0',
    alarm: [{
      name: '站点1DV分组容灾切换',
      alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
      alarmSource: 'oracle92131',
      alarmTime: '2024/09/28 12:00:00',
    }],
    event: [{
      name: '站点1DV分组容灾切换',
      alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
      alarmSource: 'oracle92131',
      alarmTime: '2024/09/28 12:00:00',
    }],
  }, {
    time: new Date('2024-12-24 22:00:00').getTime(),
    type: 'important',
    quantity: '3',
    alarm: [{
      name: '站点1DV分组容灾切换',
      alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
      alarmSource: 'oracle92131',
      alarmTime: '2024/09/28 12:00:00',
    },
    {
      name: '站点1DV分组容灾切换',
      alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
      alarmSource: 'oracle92131',
      alarmTime: '2024/09/28 12:00:00',
    },
    {
      name: '站点1DV分组容灾切换',
      alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
      alarmSource: 'oracle92131',
      alarmTime: '2024/09/28 12:00:00',
    }],
    event: [],
  }, {
    time: new Date('2024-1-2 00:00:00').getTime(),
    type: 'emergency',
    quantity: '5',
    alarm: [
      {
        name: '站点1DV分组容灾切换',
        alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
        alarmSource: 'oracle92131',
        alarmTime: '2024/09/28 12:00:00',
      },
      {
        name: '站点1DV分组容灾切换',
        alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
        alarmSource: 'oracle92131',
        alarmTime: '2024/09/28 12:00:00',
      },
      {
        name: '站点1DV分组容灾切换',
        alarmDes: '服务名=i2kdb, 主机IP=************, 数据库连…',
        alarmSource: 'oracle92131',
        alarmTime: '2024/09/28 12:00:00',
      }],
    event: [
      { name: '站点1DV分组容灾切换', locationInfo: '站点1的DV分组故障，站点2的DV分组升级为master', time: '2024/09/28 12:00:00' },
      { name: '站点1DV分组容灾切换', locationInfo: '站点1的DV分组故障，站点2的DV分组升级为master', time: '2024/09/28 12:00:00' }],
  }],
};
const tabsInfo = ['TPS', '成功率', '时延', '丢包', '抖动'];
const option = [
  { text: '5G service', value: 1 },
  { text: '管理业务', value: 2 },
];
export {
  serviceData, tpsData, TimelineChartOption, tabsInfo, option,
};
