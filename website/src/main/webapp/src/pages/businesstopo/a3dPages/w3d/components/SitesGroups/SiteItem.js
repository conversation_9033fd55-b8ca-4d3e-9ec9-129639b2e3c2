import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import ExtrudeShape from '../common/ExtrudeShape';
import { getObjectSize } from '../common/utils';
import { getGroundOptions, getTextPlane, getWallLine } from '../utils';
import GroupItem from './GroupItem';
import { grayUpgradeVert, grayUpgradeFrag } from './grayUpgrade';

const A3D = getA3D();
const point = new A3D.C.Object3D();
const tempColor = new A3D.C.Color();

export default class SiteItem extends A3D.C.Object3D {
  data;
  #sizeSetting;
  #modelMap;
  #matMap;
  constructor(data, sizeSetting, modelMap, matMap) {
    super();

    this.data = data;
    this.#sizeSetting = sizeSetting;
    this.#modelMap = modelMap;
    this.#matMap = matMap;

    this.#init();
  }

  getSize() {
    const position = new A3D.C.Vector3();
    this.getWorldPosition(position);

    this.add(point);
    point.position.z = -this.#sizeSetting.siteDepth / 2;
    const backPosition = new A3D.C.Vector3();
    point.getWorldPosition(backPosition);

    point.position.z = this.#sizeSetting.siteDepth / 2;
    const fontPosition = new A3D.C.Vector3();
    point.getWorldPosition(fontPosition);
    this.remove(point);

    return { position, backPosition, fontPosition };
  }

  hoverIn() {
    const siteGround = this.children[0];
    const mesh = siteGround.children[0];
    mesh.material = this.#matMap.site.ground.hover;
  }

  hoverOut() {
    const siteGround = this.children[0];
    const mesh = siteGround.children[0];
    mesh.material = this.#matMap.site.ground.normal;
  }

  select() {
    const groundSelect = this.getObjectByName('groundSelect');
    if (groundSelect) {
      return;
    }
    const { siteWidth, siteDepth } = this.#sizeSetting;
    const lineWidth = siteWidth * 0.004;
    const lineOptions = getGroundOptions(siteWidth - lineWidth, siteDepth - lineWidth);
    lineOptions.width = lineWidth;
    lineOptions.height = siteWidth * 0.001;
    lineOptions.close = true;
    const line = getWallLine(this.#matMap.site.ground.select, lineOptions);
    line.position.y = siteWidth * 0.03;
    line.name = 'groundSelect';
    this.add(line);
  }

  unselect() {
    const groundSelect = this.getObjectByName('groundSelect');
    if (groundSelect) {
      this.remove(groundSelect);
    }
  }

  update(data) {
    this.children[2].children.forEach((groupItem, index) => {
      if (data.groupList[index]) {
        groupItem.update(data.groupList[index]);
      }
    });
  }

  setGrayUpgrade(ids) {
    const grayUpgrade = this.getObjectByName('grayUpgrade');
    if (ids.includes(this.data.siteId)) {
      if (!grayUpgrade) {
        const color = tempColor.set(this.#matMap.site.ground.select.color);
        color.convertLinearToSRGB();
        const { siteWidth, siteDepth } = this.#sizeSetting;

        const planeOptions = getGroundOptions(siteWidth, siteDepth);
        planeOptions.depth = siteWidth * 0.001;

        const planeMat = new A3D.C.ShaderMaterial({
          uniforms: {
            uTime: { value: 0 },
            uColor: { value: color },
            uWidth: { value: siteWidth },
            uDepth: { value: siteDepth },
          },
          vertexShader: grayUpgradeVert,
          fragmentShader: grayUpgradeFrag,
          transparent: true,
        });
        const plane = new ExtrudeShape(planeOptions, planeMat);
        plane.children[0].onBeforeRender = () => {
          planeMat.uniforms.uTime.value = (Date.now() / 1000 * 4) % (Math.PI * 2);
        };
        plane.name = 'grayUpgrade';
        plane.position.y += siteWidth * 0.03;
        plane.pointerEvents = 'none';
        this.add(plane);
      }
    } else {
      if (grayUpgrade) {
        this.remove(grayUpgrade);
      }
    }
  }

  #init() {
    const { siteWidth, siteDepth } = this.#sizeSetting;
    const groundHeight = siteWidth * 0.03;
    const space = siteWidth * 0.02;
    const nameSize = siteWidth * 0.12;
    this.#initGround(siteWidth, siteDepth, groundHeight);
    this.#initSiteName(siteWidth, siteDepth, groundHeight, nameSize, space);
    const marginTop = space + nameSize;
    this.#initBusinessGroup(siteWidth, siteDepth, groundHeight, marginTop, space);
  }

  #initGround(width, depth, height) {
    const groundOptions = getGroundOptions(width, depth);
    groundOptions.depth = height * 0.99;
    const mat = this.#matMap.site.ground.normal;
    const ground = new ExtrudeShape(groundOptions, mat);
    ground.name = 'siteGround';
    ground.position.y -= height * 0.01;
    this.add(ground);
  }

  #initSiteName(width, depth, height, size, space) {
    const name = this.data.siteName;
    const nameCount = 10;
    const plane = getTextPlane({
      x: size * nameCount,
      y: size,
    }, name, {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
      maxWidth: width * nameCount * 1.5,
    });
    const x = -(width - size * 10) * 0.5 + space;
    const z = -(depth - size) * 0.5 + space;
    plane.position.set(x, height + 0.1, z);
    plane.userData._type = 'sitePlane';
    plane.userData._data = {
      siteId:this.data.siteId,
      siteName:this.data.siteName,
    };
    this.add(plane);
  }

  #initBusinessGroup(siteWidth, siteDepth, groundHeight, marginTop, space) {
    const businessGroup = new A3D.C.Group();
    businessGroup.position.y = groundHeight;
    this.add(businessGroup);

    const countAndSize = this.#getCountAndSize(siteWidth, siteDepth, marginTop, space);
    const scale = this.#getScale();

    this.data.groupList.forEach((businessItem, index) => {
      const businessGroupItem = this.#getBusinessGroupItem(businessItem, index, countAndSize, scale, marginTop);
      businessGroup.add(businessGroupItem);
    });
  }

  #getCountAndSize(siteWidth, siteDepth, marginTop, space) {
    const allWidth = siteWidth - space * 2;
    const allDepth = siteDepth - marginTop - space * 2;

    const allCount = this.data.groupList.length;
    const allColumn = Math.ceil(Math.sqrt(allCount));
    const allRow = Math.ceil(allCount / allColumn);

    const columnWidth = allWidth / allColumn;
    const rowDepth = allDepth / allRow;

    return {
      allColumn, allRow, columnWidth, rowDepth,
    };
  }

  #getScale() {
    const group = this.#modelMap.site.group;
    const modelSize = getObjectSize(group);
    const finalSize = this.#sizeSetting.siteModelSize;
    const scale = Math.min(finalSize / modelSize.width, finalSize / modelSize.depth);
    return scale;
  }

  #getBusinessGroupItem(businessItem, index, {
    allColumn, allRow, columnWidth, rowDepth,
  }, scale, marginTop) {
    const curColumn = index % allColumn;
    const curRow = Math.floor(index / allColumn);
    const x = (-(allColumn - 1) / 2 + curColumn) * columnWidth;
    const z = (-(allRow - 1) / 2 + curRow) * rowDepth + marginTop / 2;

    const businessGroupItem = new GroupItem(businessItem, this.#modelMap, this.#matMap);

    businessGroupItem.position.set(x, 0, z);
    businessGroupItem.scale.set(scale, scale, scale);

    return businessGroupItem;
  }
}
