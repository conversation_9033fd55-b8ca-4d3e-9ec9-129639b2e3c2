import { baseUrl } from '@pages/businesstopo/a3dPages/global';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export default {
  ':scene': {
    matType: 'Basic',
    setting: { color: '#171719' },
  },

  ground: {
    matType: 'Lambert',
    setting: {
      color: '#202123',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'block-ground': {
    matType: 'Basic',
    setting: {
      color: '#282b2e',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'block-ground-select': {
    matType: 'Basic',
    setting: {
      color: '#20293b',
      opacity: 0.4,
      transparent: true,
      depthWrite: false,
      blending: A3D.C.AdditiveBlending,
    },
  },
  'block-ground-line': {
    matType: 'Basic',
    setting: {
      color: '#4b7eb4',
      transparent: true,
      depthWrite: false,
      opacity: 0.4,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 3,
        repeatY: 1,
      },
    },
  },
  'block-ground-line-select': {
    matType: 'Basic',
    setting: {
      color: '#4b7eb4',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 3,
        repeatY: 1,
      },
    },
  },

  'app-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-emissiveMap.png`,
      },
    },
  },
  'app-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-emissiveMap.png`,
      },
    },
  },
  'app-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-emissiveMap.png`,
      },
    },
  },
  'app-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-emissiveMap.png`,
      },
    },
  },
  'database-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-emissiveMap.png`,
      },
    },
  },
  'database-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/alarm/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/alarm/database-emissiveMap.png`,
      },
    },
  },
  'database-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-emissiveMap.png`,
      },
    },
  },
  'database-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/alarm/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/alarm/database-emissiveMap.png`,
      },
    },
  },
  'pod-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/emissiveMap.png`,
      },
    },
  },
  'pod-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/emissiveMap.png`,
      },
    },
  },
  'pod-grey': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/grey/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/grey/emissiveMap.png`,
      },
    },
  },
  'pod-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/emissiveMap.png`,
      },
    },
  },
  'pod-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/emissiveMap.png`,
      },
    },
  },
  'pod-grey-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.2,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/grey/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/grey/emissiveMap.png`,
      },
    },
  },
  'vm-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/normal/vm-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/normal/vm-emissiveMap.png`,
      },
    },
  },
  'vm-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/alarm/vm-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/alarm/vm-emissiveMap.png`,
      },
    },
  },
  'vm-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/normal/vm-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/normal/vm-emissiveMap.png`,
      },
    },
  },
  'vm-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/alarm/vm-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/vm/alarm/vm-emissiveMap.png`,
      },
    },
  },
};
