import React, {useContext, useEffect, useState} from 'react';
import { Tooltip } from 'eview-ui';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
import * as api from '@pages/businesstopo/api';
import {$t} from '@util';
import PodPanelChart from '@pages/businesstopo/components/moTypeDrill/PodPanelChart';
import AlarmEventPanel from '@pages/businesstopo/components/AlarmEventPanel';
import {HEALTH_STATUS, STATUS, STATUS_NEW_TEXT} from '@pages/businesstopo/const/moTypeDrill';
import {dateTimeFormat} from '@util/tools';
import {getPodDeployData} from '@pages/businesstopo/api/moTypeDrill';
import errorIcon from '@images/error.png';

function RightPanel({refreshFlag}) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {dataBasePannelIsOpen, showDataBaseRightPanel, podData, podIdToSiteNameMap} = state;
  const [detailsInfoArr, setDetailsInfoArr] = useState([]);
  const [podName, setPodName] = useState('');
  const RIGHT_LENGTH = 400;

  const getColor = (index, value) => {
    if (value === STATUS.normal || value === HEALTH_STATUS.normal) {
      return '#00a874'; // red color
    } else if (value === STATUS.failed || value === HEALTH_STATUS.failed) {
      return '#F43146';
    } else {
      return '#f5f5f5';
    }
  };
  const queryPodDeployData = podId => new Promise((resolve, reject) => {
    let param = {instanceId: podId};
    if (state.isTimeTrack && state.selectedTime > 0) {
      param.endTime = state.selectedTime;
    }
    getPodDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        reject(new Error('error'));
        return;
      }
      resolve({
        respData: resp.data, dnId: resp.data.dnId, relation: resp.data.vmDataList.map(vm => vm.dnId),
      });
    }, () => {
      reject(new Error('error'));
    });
  });

  useEffect(async() => {
    if (podData.id === -1 || !podData.id) {
      return;
    }
    let podDeployData = await queryPodDeployData(podData.id);
    // 处理pod右侧面板初始化数据
    let vmData = {};
    if (podDeployData.respData.vmDataList && podDeployData.respData.vmDataList.length > 0) {
      vmData = podDeployData.respData.vmDataList[0];
    }
    if (podDeployData.respData) {
      setPodName(podDeployData.respData.podName);
    }
    let content = (
      <div style={{padding: '5px'}}>
        <p>
          {$t('moType.drill.vm.healthstatus')} :{' '}
          {$t(podDeployData.respData.csnState === 0 ? 'moType.drill.vm.normal' : 'moType.drill.vm.abnormal')}
        </p>
        <p>
          {$t('moType.drill.vm.status')} :{' '}
          {$t(podDeployData.respData.availableStatus === STATUS.normal ? 'moType.drill.pod.status.normal' : 'moType.drill.pod.status.abnormal')}
        </p>
      </div>
    );

    let infoArray = [
      {
        value:
          podDeployData.respData.csnState === 0 && podDeployData.respData.availableStatus === STATUS.normal ?
            STATUS.normal :
            STATUS.failed,
        title: $t('moType.drill.status'),
        content,
      },
      { value: vmData.vmName, title: $t('moType.drill.rightPanel.host') },
      {
        value: podIdToSiteNameMap.get(podData.id),
        title: $t('moType.drill.rightPanel.site'),
      },
      {
        value: podDeployData.respData.podIp ? podDeployData.respData.podIp : 'NA',
        title: $t('moType.drill.rightPanel.ip'),
      },
      {
        value: vmData.vmIp ? vmData.vmIp : 'NA',
        title: $t('moType.drill.rightPanel.hostIP'),
      },
      {
        value: dateTimeFormat(podDeployData.respData.createTime),
        title: $t('moType.drill.rightPanel.time'),
      },
      {
        value: podDeployData.respData.dockerName,
        title: $t('moType.drill.rightPanel.dockerName'),
      },
    ];

    setDetailsInfoArr(infoArray);

    let param = {
      instanceId: podData.id,
    };
    if (state.isTimeTrack && state.selectedTime > 0) {
      param.timestamp = state.selectedTime;
    }

    api.getAlarmData(param, res => {
      dispatch({
        alarmEventData: res.data.alarmDataList,
      });
    });
  }, [podData, state.isTimeTrack, state.selectedTime, refreshFlag]);
  return (
    <div style={{
      display: 'inline-block',
      right: 0,
      top: 0,
      position: 'absolute',
      zIndex: 999,
      width: showDataBaseRightPanel ? RIGHT_LENGTH : 0,
      height: 'calc(100vh - 4rem)',
    }}
    >
      <div
        className="dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-bottomPanel"
        style={{
          display: dataBasePannelIsOpen ? '' : 'none', position: 'fixed', zIndex: 1000, opacity: 1,
        }}
        onClick={() => {
          dispatch({dataBasePannelIsOpen: true, showDataBaseRightPanel: !showDataBaseRightPanel});
        }}
      >
        <div className={`topo-common-panel-${showDataBaseRightPanel ? 'close' : 'expend'}-vertical-right`} />
      </div>

      <div className="site-right-panel" style={{display: showDataBaseRightPanel ? 'block' : 'none'}}>
        <div className="moType_right_panel_title">
          <Tooltip content={podName} id='title_tooltip' color='#393939'
            overlayStyle={{ color: '#FFF' }}
          >
            <div className="moType_right_panel_title_text">{`${podName}`}</div>
          </Tooltip>
        </div>
        <div className="site-right-panel-text" style={{'margin-top': '32px'}}>
          <div className="pod-detail-right-panel-container">
            {detailsInfoArr.map((item, index) => (
              <div style={{ paddingBottom: '16px', width: '100px' }} key={index}>
                <Tooltip content={index === 0 ? item.content : item.value} placement='right' color='#393939'
                  overlayStyle={{ color: '#FFF' }}
                >
                  <div
                    className="pod-detail-title"
                    style={{ color: getColor(index, item.value) }}
                  >
                    <span
                      style={{ marginRight: '5px' }}
                    > {(index === 0) ? STATUS_NEW_TEXT[item.value] : item.value}
                    </span>
                    {(index === 0 && item.value === STATUS.failed) && (
                      <img
                        style={{ width: '15px', height: '15px' }}
                        src={errorIcon}
                      />
                    )}
                  </div>
                </Tooltip>
                <div className="pod-detail-value">{item.title}</div>
              </div>
            ))}
          </div>
        </div>

        <div style={{marginTop: '32px', height: '344px'}}>
          <PodPanelChart podData={podData} refresh={refreshFlag} />
        </div>
        <div>
          <AlarmEventPanel />
        </div>
      </div>
    </div>
  );
}

export default RightPanel;
