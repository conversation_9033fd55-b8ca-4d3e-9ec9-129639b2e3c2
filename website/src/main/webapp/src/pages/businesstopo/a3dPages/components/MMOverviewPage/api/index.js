import {request} from '@util';
const overviewgridMMUrl = '/rest/dvtopowebsite/v1/business/topo/queryoverviewgridformm';
const mmDistributionUrl = '/rest/dvtopowebsite/v1/business/topo/queryindicatordistributionformm';
const mmTopPieUrl = '/rest/dvtopowebsite/v1/business/topo/querytopindicator';
const mmHistoryLineUrl = '/rest/dvtopowebsite/v1/business/topo/queryindicatorhistoryformm';
// 设置请求配置
export const requestConfig = {
  isScheduledTask: true, // 定时任务的标记
};


export const getOverViewGridMM = (params, success, error) => request.post(overviewgridMMUrl, params, success, error, false, requestConfig);

export const queryMMOverViewGridLine = (params, success, error) => request.post(mmDistributionUrl, params, success, error, false, requestConfig);


export const queryMMTopPie = (params, success, error) => request.post(mmTopPieUrl, params, success, error, false, requestConfig);

export const queryMMHistoryLineUrl = (params, success, error) => request.post(mmHistoryLineUrl, params, success, error, false, requestConfig);
