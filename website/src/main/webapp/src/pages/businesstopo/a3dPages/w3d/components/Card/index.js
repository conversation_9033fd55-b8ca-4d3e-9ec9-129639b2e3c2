import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer';

const A3D = getA3D();

const getDom = (data) => {
  const dom = document.createElement('div');
  dom.innerText = data.text || 'text';
  return dom;
};

const updateDom = (dom, data) => {
  dom.innerText = data.text || 'text1';
};

export default class Card extends A3D.C.Group {
  constructor(data, props) {
    super();

    this.parameters = {
      getDom,
      updateDom,
      offset: {
        x: 0,
        y: 0,
        z: 0,
      },
      ...props,
    };
    if (data) {
      this.#add(data);
    }
  }

  copy(source) {
    super.copy(source, false);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    return this;
  }

  set(data) {
    const position = data.position;
    if (position) {
      this.position.copy(position).add(this.parameters.offset);
    }

    const dom = this.children[0].element.children[0];
    this.parameters.updateDom(dom, data);
  }

  #add(data) {
    let dom = this.parameters.getDom(data);
    let wrapper = document.createElement('div');
    wrapper.append(dom);

    const cardMesh = new CSS2DObject(wrapper);
    this.add(cardMesh);

    const position = data.position;
    if (position) {
      this.position.copy(position);
    }
    this.position.add(this.parameters.offset);
  }

  hide() {
    const dom = this.children[0].element.children[0];
    if (this.parameters.hideDom) {
      this.parameters.hideDom(dom);
    }
    this.children[0].visible = false;
  }

  show() {
    const dom = this.children[0].element.children[0];
    if (this.parameters.showDom) {
      this.parameters.showDom(dom);
    }
    this.children[0].visible = true;
  }

  isShow() {
    return this.children[0].visible;
  }

  destroy() {
    if (this.children[0]) {
      this.remove(this.children[0]);
    }
    if (this.parent) {
      this.parent.remove(this);
    }
  }
}
