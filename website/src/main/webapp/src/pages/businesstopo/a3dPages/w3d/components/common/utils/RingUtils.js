const DIVISIONS = 5;
const PI = Math.PI;
const PI2 = 2 * PI;

const getRadianByPosition = function(x, y) {
  let tan;
  let radian;
  if (x === 0) {
    if (y > 0) {
      radian = PI / 2;
    } else {
      radian = -PI / 2;
    }
  } else {
    tan = Math.abs(y / x);
    if (x > 0) {
      if (y >= 0) {
        radian = Math.atan(tan);
      } else {
        radian = 2 * PI - Math.atan(tan);
      }
    } else {
      if (y >= 0) {
        radian = PI - Math.atan(tan);
      } else {
        radian = PI + Math.atan(tan);
      }
    }
  }
  return radian;
};

export default class RingUtils {
  static getPointAt(percent, radius, output = {}, startRadian = 0) {
    let radian = PI2 * percent + (startRadian);
    radian %= PI2;
    output.x = Math.cos(radian) * radius;
    output.y = -Math.sin(radian) * radius;
    return output;
  }

  static getPercent(x, y, startRadian = 0) {
    let radian = getRadianByPosition(x, -y) - (startRadian % PI2);
    radian = (PI2 + radian) % PI2;
    return radian / PI2;
  }

  static getPoints(radius, startRadian = 0, divisions = DIVISIONS, output = []) {
    let percent = 0;
    const step = 1 / divisions;
    const target = {};
    for (let i = 0; i < divisions; i++) {
      this.getPointAt(percent, radius, target, startRadian);
      output.push({
        x: target.x,
        y: target.y,
      });
      percent += step;
    }
    return output;
  }
}
