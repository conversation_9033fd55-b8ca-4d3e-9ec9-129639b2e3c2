import '@pages/businesstopo/a3dPages/styles/split.less';
import React, {
  useEffect, useRef, useState, useReducer,
} from 'react';
import Control3DPanel from '../common/Control3DPanel';
import FloatPanel from '../common/FloatPanel';
import Split from '../common/Split';
import { destroyW3D, initW3D, resizeW3D } from './overview3D';

function OverviewPage() {
  const w3dContainerRef = useRef();
  const [panelShow, setPanelShow] = useState(true);
  useEffect(() => {
    initW3D(w3dContainerRef);
    return () => destroyW3D();
  }, []);
  useEffect(() => {
    resizeW3D();
  }, [panelShow]);
  return (
    <Split className='rightPanelTest' width='25rem' setPanelShow={setPanelShow}>
      <div className='split_left'>
        <div className="topo">
          <div ref={w3dContainerRef} className="w3d_container" />
          <Control3DPanel />
        </div>
        <FloatPanel />
        <div className="timeLine">
          时间轴
        </div>
      </div>
      <div className='split_right' />
    </Split>);
}

export default OverviewPage;
