import arSwitchover from '@pages/businesstopo/a3dPages/w3d/assets/images/arSwitchover.svg';
import asSwitchover from '@pages/businesstopo/a3dPages/w3d/assets/images/asSwitchover.svg';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { initMap } from '../../../commonHandler/ThemeHandler';

const A3D = getA3D();

const _default = {};
const _dark = {};

const podStatusMap = {};
const alarmSize = 200;
const imgs = { asSwitchover, arSwitchover };
const getImageCanvas = (imgUrl) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = alarmSize;
  canvas.height = alarmSize;
  const img = document.createElement('img');
  img.src = imgUrl;
  return new Promise((resolve) => {
    img.onload = function() {
      ctx.drawImage(img, 0, 0, alarmSize, alarmSize);
      resolve(canvas);
    };
  });
};
const initPodStatusMap = async() => {
  const keys = Object.keys(imgs);
  for (let i = 0, len = keys.length; i < len; i++) {
    const key = keys[i];
    const imgUrl = imgs[key];
    if (!podStatusMap[key]) {
      const canvas = await getImageCanvas(imgUrl);
      podStatusMap[key] = A3D.canvasToTex(canvas, true);
    }
  }
};
const addPodStatus = async() => {
  await initPodStatusMap();
  Object.keys(podStatusMap).forEach((name) => {
    if (!_default[name]) {
      _default[name] = new A3D.C.MeshBasicMaterial({
        transparent: true,
        depthWrite: false,
        opacity: 1,
        map: podStatusMap[name],
      });
      _dark[name] = new A3D.C.MeshBasicMaterial({
        transparent: true,
        depthWrite: false,
        opacity: 1,
        map: podStatusMap[name],
      });
    }
  });
};

const addPodRelationLine = () => {
  const matColor = '#5c85b4';
  _default.podRelationLine = new LineMaterial({
    color: matColor,
    linewidth: 0.05,
    worldUnits: true,
    // alphaToCoverage: true,
    transparent: true,
    // opacity: 0.5,
    depthTest: false,
  });
  _dark.podRelationLine = new LineMaterial({
    color: matColor,
    linewidth: 0.05,
    worldUnits: true,
    // alphaToCoverage: true,
    transparent: true,
    // opacity: 0.5,
    depthTest: false,
  });
};

export const resetTheme = async(defaultTheme, darkTheme, data, w3d) => {
  await initMap(defaultTheme, darkTheme, w3d);
  await addPodStatus();
  addPodRelationLine();
  Object.assign(defaultTheme, _default);
  Object.assign(darkTheme, _dark);
};
