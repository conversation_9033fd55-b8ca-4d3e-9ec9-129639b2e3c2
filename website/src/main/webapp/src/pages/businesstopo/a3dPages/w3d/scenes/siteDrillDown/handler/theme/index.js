import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { getCanvasTex } from '../../../../components/utils';
import { initMap } from '../../../commonHandler/ThemeHandler';

const A3D = getA3D();

const _default = {};
const _dark = {};

const nameTextureMap = {};
const getTextureMap = (data) => {
  if (data.mmnorth) {
    data.mmnorth.forEach((north) => {
      const businessId = north.businessId;
      const businessName = north.businessName;
      if (!nameTextureMap[businessId]) {
        nameTextureMap[businessId] = getCanvasTex(businessName, 12);
      }
    });
  }

  data.sitesDrillDown.drGroupList.forEach((drGroup) => {
    drGroup.moTypeInfoList.forEach((moType) => {
      const moTypeName = moType.moTypeName;
      if (!nameTextureMap[moTypeName]) {
        nameTextureMap[moTypeName] = getCanvasTex(moTypeName, 6);
      }
    });
  });
};

const addDbName = async(data) => {
  getTextureMap(data);
  Object.keys(nameTextureMap).forEach((name) => {
    if (!_default[name]) {
      _default[name] = new A3D.C.SpriteMaterial({
        map: nameTextureMap[name],
        color: '#BBBBBB',
        depthWrite: false,
      });
      _dark[name] = new A3D.C.SpriteMaterial({
        map: nameTextureMap[name],
        color: '#BBBBBB',
        depthWrite: false,
      });
    }
  });
};

const addDbRelationLine = () => {
  const matColor = '#d5d5d6';
  const alarmColor = '#ba4243';
  const linewidth = 0.0375;

  _default['db-line-normal'] = new LineMaterial({
    color: matColor,
    linewidth,
    worldUnits: true,
    transparent: true,
    depthWrite: false,
  });
  _dark['db-line-normal'] = new LineMaterial({
    color: matColor,
    linewidth,
    worldUnits: true,
    transparent: true,
    depthWrite: false,
  });
  _default['db-line-alarm'] = new LineMaterial({
    color: alarmColor,
    linewidth,
    worldUnits: true,
    transparent: true,
    depthWrite: false,
  });
  _dark['db-line-alarm'] = new LineMaterial({
    color: alarmColor,
    linewidth,
    worldUnits: true,
    transparent: true,
    depthWrite: false,
  });
};

export const resetTheme = async(defaultTheme, darkTheme, data, w3d) => {
  await initMap(defaultTheme, darkTheme, w3d);
  await addDbName(data);
  addDbRelationLine();
  Object.assign(defaultTheme, _default);
  Object.assign(darkTheme, _dark);
};
