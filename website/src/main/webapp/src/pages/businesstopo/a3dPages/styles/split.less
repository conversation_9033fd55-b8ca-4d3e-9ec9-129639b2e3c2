.split_left {
  width: 100%;
  height: 100%;
  position: relative;
  .backTo {
    position: absolute;
    top: 1.25rem;
    left: 2rem;
    font-size: 1.125rem;
    line-height: 1.5rem;
    color: #FFFFFF;
    z-index: 1;
    cursor: pointer;

    &::before {
      content: "";
      position: absolute;
      width: 0.5rem;
      height: 0.5rem;
      left: -1rem;
      border-left: 2px solid #ffffff;
      border-top: 2px solid #ffffff;
      transform: rotate(-45deg) translate(0, 8px);
    }
  }
  .siteGroups{
    display: flex;
    position: absolute;
    z-index: 999;
    height: 80px;
    width: 100%;
    top: 2%;
    border-color: #242424;
    display: flex;
    align-items: center;
    justify-content: center;
    .siteGroup{
      margin-left: 24px;
      height: 60px;
      width: 96px;
      border-radius : 5px;
      background : #242424 ;
      border : 1px solid #666666 ;
      position: relative;
      .siteItem{
        height: 12px;
        width: 24px;
        border-radius : 2px ;
        border-color : #333333 ;
        background : #333333 ;
        position:absolute;
      }
    }
  }
  .topo {
    width: 100%;
    height: calc(100% - 80px);
    background-size: 100% 100%;
    position: relative;

    .w3d_container {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .w3d_container_fix {
      position: absolute;
      width: 100%;
      height: 20%;
      overflow: hidden;
      background-image: linear-gradient(rgba(23, 23, 25, 1) 0%,
      rgba(23, 23, 25, 0.8) 90%,
      rgba(23, 23, 25, 0) 100%);

      .w3d_control_container {
        position: absolute;
        width: 100%;
        height: 500%;
      }
    }
  }

  .timeLine {
    height: 80px;
    background-image: linear-gradient(180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 50%);
    backdrop-filter: blur(4px);
    position: relative;

    .timeLine_exitPlayback {
      width: 6.75rem;
      height: 100%;
      background: #393939;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 1;
      border-right: 0.0625rem solid #636363;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      cursor: pointer;
      gap: 0.25rem;

      img {
        width: 1rem;
        height: 1rem;
      }

      span {
        display: inline-block;
        font-size: 0.875rem;
        color: #f5f5f5;
        line-height: 1.25rem;
      }
    }

    .container {
      background-color: transparent;
      margin-top: 0;
    }
  }
}

.split_right {
  padding: 1.5rem 1rem;
  width: 23rem;

  .KPI_top {
    width: 100%;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .KPI_title {
      display: inline-block;
      font-size: 1.25rem;

      .KPI_title_select {
        width: 7.5rem;
        margin-left: 1rem;
        font-size: 0.875rem;
        display: inline-block;
      }
    }

    .KPI_panel {
      display: inline-block;
      cursor: pointer;
      width: 1rem;
      height: 1rem;
      background: url(~@pages/businesstopo/a3dPages/assets/image/panel/panel_fold.svg);
      background-position: center center;
      background-size: 100% 100%;

      &:hover {
        background: url(~@pages/businesstopo/a3dPages/assets/image/panel/panel_fold_hover.svg);
        background-size: 100% 100%;
      }

      &.panelShow {
        transform: rotate(0);
      }

      &.panelHide {
        transform: rotate(180deg);
      }
    }
  }

  .KPI_content {
    margin: 1rem 0;
    width: 100%;
  }

  .KPI_chart {
    .title {
      font-size: 1rem;
      color: #f5f5f5;
      line-height: 1.25rem;
      margin-top: 0.5rem;
    }

    .chart {
      height: 17rem;
    }
  }

  .KPI_tabs {
    display: flex;
    font-size: 1rem;
    color: #bbb;
    font-weight: Medium;
    position: relative;

    .tip {
      display: inline-block;
      height: 1.5rem;
      cursor: pointer;
      margin-right: 1.5rem;
      position: relative;

      &.active {
        color: #2e94ff;

        .line {
          background-color: #2e94ff;
        }
      }

      .line {
        display: inline-block;
        height: 0.125rem;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .close {
      display: inline-block;
      width: 1.5rem;
      height: 1.5rem;
      position: absolute;
      background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/ic_close_lined.svg);
      right: 0;
      background-size: 1rem;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
    }
  }
}

.rightPanelTest {
  font-family: "Arial", "Microsoft YaHei";

  background-color: #000000;
  color: #ffff;

  display: flex;
  flex: 1 1 0%;
  height: calc(100vh - 4rem);
  outline: none;
  overflow: hidden;
  user-select: text;
  flex-direction: row;
  left: 0px;
  right: 0px;
  position: relative;

  .ev_split_vertical_cls {
    cursor: initial !important;
  }

  .split_content {
    background-color: #171719;
  }
}

.tipDom {
  color: #393939;
  box-sizing: border-box;
  position: relative;
  display: inline-block;
  box-shadow: 0 16px 48px 0 rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(0px);
  background: #393939;
  padding: 0.75rem 1rem;

  .tip {
    position: relative;
    box-sizing: border-box;
    min-width: 2.5rem;
    font-size: 0.875rem;
    color: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-items: left;
    gap: 0.75rem;

    div {
      display: flex;
      justify-content: start;
      align-items: center;

      span {
        display: inline-block;
      }

      .point {
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
      }

      .processed {
        background: #818181;
      }

      .general {
        background: #ffbb33;
      }

      .important {
        background: #ec6f1a;
      }

      .emergency {
        background: #e54545;
      }

      .tips {
        background: #2070f3;
      }
    }

    :nth-child(1) {
      gap: 0.25rem;
    }

    :nth-child(2) {
      gap: 0.5rem;
    }
  }

  &::after {
    position: absolute;
    z-index: 3;
    bottom: -0.875rem;
    left: calc(50% - 0.5rem);
    content: "";
    border: 0.5rem solid transparent;
    border-top-color: initial;
  }
}