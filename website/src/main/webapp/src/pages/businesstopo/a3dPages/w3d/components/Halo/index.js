import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
const A3D = getA3D();

const pos = new A3D.C.Vector3();

export default class Halo extends A3D.C.Group {
  #selectHalo;
  #selectObj;
  #subSelectHalo;
  #scaleY = 2;
  constructor(props) {
    super();

    if (props) {
      this.parameters = A3D.util.extend(true, { animate: true }, props);
    }
  }

  addSelectHalo(needSwitch, texture, baseTexture, parent, offsetY = 0) {
    if (needSwitch) {
      this.#changeSelect(parent);
    }
    let position = parent.getWorldPosition(pos);
    position.y += offsetY;
    if (this.#selectHalo) {
      this.#setPosition(this.#selectHalo, parent, offsetY);
      this.showSelectHalo();
      this.#selectObj = parent;
      return;
    }
    this.#selectHalo = A3D.createGroup();
    let halo = this.#createSprite(texture);
    let base = this.#createSprite(baseTexture);
    this.#selectHalo.add(halo, base);
    this.#setPosition(this.#selectHalo, parent, offsetY);
    this.showSelectHalo();
    this.#selectObj = parent;
  }

  addSubSelectHalo(texture, parent, offsetY = 0) {
    if (this.#subSelectHalo) {
      this.#setPosition(this.#subSelectHalo, parent, offsetY);
      this.showSubSelectHalo();
      return;
    }
    this.#subSelectHalo = this.#createSprite(texture);
    this.#setPosition(this.#subSelectHalo, parent, offsetY);
    this.showSubSelectHalo(this.#subSelectHalo);
  }

  showSelectHalo() {
    if (!this.#selectHalo) {
      return;
    }
    this.#show(this.#selectHalo);
  }

  hideSelectHalo() {
    if (!this.#selectHalo) {
      return;
    }
    this.#hide(this.#selectHalo);
  }

  showSubSelectHalo() {
    if (!this.#subSelectHalo) {
      return;
    }
    this.#show(this.#subSelectHalo);
  }

  hideSubSelectHalo() {
    if (!this.#subSelectHalo) {
      return;
    }
    this.#hide(this.#subSelectHalo);
  }

  enableAnimation(enabled) {
    this.parameters.animate = enabled;
  }

  getSelectObj() {
    return this.#selectObj;
  }

  #show(obj) {
    let parameters = this.parameters;
    let animation = parameters.animationOption.animation;
    let scale;
    if (animation && parameters.animate && !animation.isPlaying()) {
      animation.stop();
      animation.push({
        duration: 250,
        onUpdate: (e) => {
          scale = e.value * this.#scaleY;
          obj.children.forEach((item) => {
            item.scale.y = scale;
          });
        },
        onStop: () => {
          obj.children.forEach((item) => {
            item.scale.y = this.#scaleY;
          });
        },
      });
      animation.start();
    } else {
      obj.children.forEach((item) => {
        item.scale.y = this.#scaleY;
      });
    }
  }

  #hide(obj, callback) {
    let parameters = this.parameters;
    let animation = parameters.animationOption.animation;
    let scale;
    if (animation && parameters.animate && !animation.isPlaying()) {
      animation.stop();
      animation.push({
        duration: 250,
        onUpdate: (e) => {
          scale = (1 - e.value) * this.#scaleY;
          obj.children.forEach((item) => {
            item.scale.y = scale;
          });
        },
        onStop: () => {
          if (callback) {
            callback();
          }
        },
      });
      animation.start();
    } else {
      obj.children.forEach((item) => {
        item.scale.y = 0;
      });
    }
  }

  #createSprite(texture) {
    let sprite = new A3D.C.Sprite();
    A3D.fixTransparent(sprite);
    const material = new A3D.C.SpriteMaterial({
      transparent: true,
      depthWrite: false,
      map: texture,
    });
    sprite.material = material;
    let img = texture.image;
    let width = img.width;
    let height = img.height;
    sprite.center.set(0.5, 0);
    sprite.scale.set(this.#scaleY * width / height, this.#scaleY, 1);
    sprite.renderOrder = 6;
    return sprite;
  }

  #setPosition(obj, parent, offsetY) {
    if (!parent) {
      return;
    }
    parent.add(obj);
    obj.position.y = offsetY;
  }

  #changeSelect(curSelectObj) {
    if (!this.#selectHalo || !this.#subSelectHalo) {
      return;
    }
    if (curSelectObj !== this.#selectObj) {
      this.#selectHalo.parent.add(this.#subSelectHalo);
    }
  }
}
