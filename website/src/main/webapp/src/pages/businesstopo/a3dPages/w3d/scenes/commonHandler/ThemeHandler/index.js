import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

const textureMap = {};

const setPromiseMap = (url, promiseMap, w3d) => {
  if (url && !promiseMap[url]) {
    promiseMap[url] = new Promise((resolve) => {
      A3D.tex(url).then((res) => {
        textureMap[url] = res;
        resolve();
      });
    });
  }
};

const initThemeMap = (theme, w3d, promiseMap) => {
  const themeKeys = Object.keys(theme);
  for (let i = 0, len = themeKeys.length; i < len; i++) {
    const key = themeKeys[i];
    if (theme[key]?.setting) {
      const attrs = Object.keys(theme[key].setting);
      for (let j = 0, len1 = attrs.length; j < len1; j++) {
        const attr = attrs[j];
        if (attr === 'map' || attr.includes('Map')) {
          const url = theme[key].setting[attr].url;
          setPromiseMap(url, promiseMap, w3d);
        }
      }
    }
  }
};

const getMap = (url, setting, attr) => {
  const _texture = textureMap[url];
  if (!_texture) {
    return null;
  }
  const texture = _texture.clone();
  if (setting[attr].repeatX || setting[attr].repeatY) {
    texture.repeat.x = setting[attr].repeatX;
    texture.repeat.y = setting[attr].repeatY;
    texture.wrapS = A3D.C.RepeatWrapping;
    texture.wrapT = A3D.C.RepeatWrapping;
  }
  if (typeof setting[attr].flipY === 'boolean') {
    texture.flipY = setting[attr].flipY;
  }
  return texture;
};

const resetThemeMap = (theme, w3d) => {
  const themeKeys = Object.keys(theme);
  for (let i = 0, len = themeKeys.length; i < len; i++) {
    const key = themeKeys[i];
    if (theme[key]?.setting) {
      const attrs = Object.keys(theme[key].setting);
      for (let j = 0, len1 = attrs.length; j < len1; j++) {
        const attr = attrs[j];
        if (attr === 'map' || attr.includes('Map')) {
          const url = theme[key].setting[attr].url;
          url && (theme[key].setting[attr] = getMap(url, theme[key].setting, attr));
        }
      }
    }
  }
};

const resetColor = (theme) => {
  const regExp = /^#[\da-f]{6,8}$/;
  const color = new A3D.C.Color();
  Object.keys(theme).forEach((mat) => {
    if (mat === ':scene' || theme[mat].matType === 'Basic' || theme[mat].matType === 'Lambert') {
      return;
    }
    Object.keys(theme[mat].setting).forEach((key) => {
      if (regExp.test(theme[mat].setting[key])) {
        color.set(theme[mat].setting[key]).convertSRGBToLinear();
        theme[mat].setting[key] = `#${color.getHexString()}`;
      }
    });
  });
};

export const initMap = async(defaultTheme, darkTheme, w3d) => {
  const promiseMap = {};
  initThemeMap(defaultTheme, w3d, promiseMap);
  initThemeMap(darkTheme, w3d, promiseMap);
  await Promise.all(Object.values(promiseMap));
  resetThemeMap(defaultTheme, w3d);
  resetThemeMap(darkTheme, w3d);
};
