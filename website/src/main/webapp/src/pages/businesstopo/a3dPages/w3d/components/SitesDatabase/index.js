import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { getDottedLine, setStatus } from '../utils';
import { initPodLine, roleList } from '../utils/siteDatabase';
import SiteDatabase, { isNullOrUndefined } from './SiteDatabase';

const A3D = getA3D();

const getSiteItem = (object3D) => {
  let node3D = null;
  if (object3D instanceof A3D.C.Object3D) {
    node3D = object3D;
    while (node3D && !(node3D instanceof SiteDatabase)) {
      node3D = node3D.parent;
    }
  }
  return node3D;
};

const getPodItem = (object3D) => {
  let node3D = null;
  if (object3D instanceof A3D.C.Object3D) {
    node3D = object3D;
    while (node3D && !node3D.userData.pod) {
      node3D = node3D.parent;
    }
  }
  return node3D;
};

const getNameItem = (object3D) => object3D?.material.map?.userData.detailName ? object3D : null;

export default class SitesDatabase extends A3D.C.Object3D {
  #data;
  #size;
  #modelMap;
  #matMap;
  #w3d;
  #animation;
  #opacityAnimation;

  #podMap = {};
  #relationshipMap = {};
  #siteMap = {};

  #selectedSiteIndex = 0;
  #selectedPodId = '';
  #selectAble = true;
  #showType = 'memory';

  #matrixSetting = [];
  #pathMatrxSetting = [];

  constructor(data, size, modelMap, matMap, w3d) {
    super();
    this.#data = data;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#w3d = w3d;
    this.#animation = new A3D.Animation(w3d.agent);
    this.#opacityAnimation = new A3D.Animation(w3d.agent);

    Object.keys(this.#matMap.site).forEach((key) => {
      this.#matMap.site[key].userData.opacity = this.#matMap.site[key].opacity;
    });

    this.#init();
  }

  hoverIn(object3D, podCb, siteCb, nameCb) {
    if (!this.#selectAble) {
      return;
    }
    const siteItem = getSiteItem(object3D);
    const newSelectedIndex = this.children[0].children.indexOf(siteItem);
    if (newSelectedIndex === this.#selectedSiteIndex) {
      const podItem = getPodItem(object3D);
      if (podItem) {
        const podId = podItem.userData.id;
        const selected = podId === this.#selectedPodId;
        const data = this.#podMap[podId]?.data;
        const mesh = this.#podMap[podId]?.mesh;
        if (podCb) {
          podCb(data, mesh, selected);
        }
      } else {
        if (podCb) {
          podCb(null, null, false);
        }
      }
      const blockName = getNameItem(object3D);
      if (blockName) {
        const detailName = blockName.material.map.userData.detailName;
        const drawName = blockName.material.map.userData.drawName;
        const isShowName = detailName !== drawName;
        if (nameCb && isShowName) {
          nameCb(blockName, detailName);
        }
      }
    } else if (newSelectedIndex >= 0) {
      siteItem.hoverIn();
      const data = this.#siteMap[siteItem.name].data;
      if (siteCb) {
        siteCb(data, siteItem);
      }
    }
  }

  hoverOut(object3D, podCb, siteCb, nameCb) {
    if (!this.#selectAble) {
      return;
    }
    const siteItem = getSiteItem(object3D);
    const newSelectedIndex = this.children[0].children.indexOf(siteItem);
    if (newSelectedIndex === this.#selectedSiteIndex) {
      const podItem = getPodItem(object3D);
      if (podItem) {
        const podId = podItem.userData.id;
        const selected = podId === this.#selectedPodId;
        const data = this.#podMap[podId]?.data;
        const mesh = this.#podMap[podId]?.mesh;
        if (podCb) {
          podCb(data, mesh, selected);
        }
      } else {
        if (podCb) {
          podCb(null, null, false);
        }
      }
      const blockName = getNameItem(object3D);
      if (blockName) {
        const detailName = blockName.material.map.userData.detailName;
        const drawName = blockName.material.map.userData.drawName;
        const isShowName = detailName !== drawName;
        if (nameCb && isShowName) {
          nameCb(blockName, detailName);
        }
      }
    } else if (newSelectedIndex >= 0) {
      siteItem.hoverOut();
      const data = this.#siteMap[siteItem.name].data;
      if (siteCb) {
        siteCb(data, siteItem);
      }
    }
  }

  select(object3D, podCb, otherCb) {
    if (!this.#selectAble) {
      return;
    }
    const siteItem = getSiteItem(object3D);
    const newSelectedIndex = this.children[0].children.indexOf(siteItem);
    if (newSelectedIndex === this.#selectedSiteIndex) {
      const podId = object3D.parent.userData.id;
      if (podId !== this.#selectedPodId) {
        const finalPodId = this.#podMap[podId] ? podId : '';
        this.#selectPod(finalPodId, siteItem.name, podCb, otherCb);
        this.#selectedPodId = finalPodId;
      }
    } else {
      if (newSelectedIndex < 0) {
        const selectedSite = this.children[0].children[this.#selectedSiteIndex];
        if (!selectedSite) {
          return;
        }
        this.#selectPod('', selectedSite.name, podCb, otherCb);
        this.#selectedPodId = '';
        return;
      }
      this.hoverOut(siteItem);
      this.#selectPod('', siteItem.name, podCb, otherCb);
      this.#selectedPodId = '';
      this.#selectSite(newSelectedIndex, () => {
        this.#selectedSiteIndex = newSelectedIndex;
      });
    }
  }

  changeType(type = 'memory') {
    this.#showType = type;
    this.children[0].children.forEach((siteItem) => {
      siteItem.update(type);
    });
  }

  hide(duration, callback) {
    if (this.#opacityAnimation.isPlaying()) {
      this.#opacityAnimation.stop();
    } else {
      this.children[0].traverse((child) => {
        if (child.isMesh && child.material && !child.material.userData.unchange) {
          child.material.userData.opacity = child.material.opacity;
        }
      });
    }
    this.#opacityAnimation.push({
      duration: duration || 1000,
      onUpdate: (e) => {
        this.children[0].traverse((child) => {
          if (child.isMesh && child.material && !child.material.userData.unchange) {
            const initOpacity = child.material.userData.opacity;
            child.material.opacity = (1 - e.value) * initOpacity;
          }
        });
      },
      onComplete: () => {
        if (callback) {
          callback();
        }
      },
    });
    this.#opacityAnimation.start();
  }

  show(duration, callback) {
    if (this.#opacityAnimation.isPlaying()) {
      this.#opacityAnimation.stop();
    }
    this.children[0].traverse((child) => {
      if (child.isMesh && child.material && !child.material.userData.unchange) {
        child.material.opacity = 0;
      }
    });
    this.#opacityAnimation.push({
      duration: duration || 1000,
      onUpdate: (e) => {
        this.children[0].traverse((child) => {
          if (child.isMesh && child.material && !child.material.userData.unchange) {
            const initOpacity = child.material.userData.opacity || 1;
            child.material.opacity = e.value * initOpacity;
          }
        });
      },
      onComplete: () => {
        if (callback) {
          callback();
        }
      },
    });
    this.#opacityAnimation.start();
  }

  update(data) {
    this.children[0].remove(...this.children[0].children);
    this.children[1].remove(...this.children[1].children);
    this.#resetData();
    this.#data = data;
    this.#init();
  }

  updateStatus(siteGroupData) {
    this.#updatePodStatus(siteGroupData);
    this.#updateDetail(siteGroupData);
    if (this.#selectedPodId) {
      const pod = this.#podMap[this.#selectedPodId]?.mesh.children[0];
      this.select(null);
      this.select(pod);
    }
  }

  #updatePodStatus(siteGroupData) {
    const updateRelationship = new Set();
    siteGroupData.data.forEach((item) => {
      if (this.#podMap[item.id]) {
        let flag = false;
        if (this.#podMap[item.id].data.status !== item.status) {
          this.#podMap[item.id].data.status = item.status;
          flag = true;
        }
        if (this.#podMap[item.id].data.designRole !== item.designRole ||
          this.#podMap[item.id].data.currentRole !== item.currentRole) {
          this.#podMap[item.id].data.designRole = item.designRole;
          this.#podMap[item.id].data.currentRole = item.currentRole;
          flag = true;
          updateRelationship.add(this.#podMap[item.id].data.shardId);
        }
        if (flag && this.#podMap[item.id].mesh) {
          setStatus(this.#podMap[item.id], this.#matMap);
        }
      }
    });
    updateRelationship.forEach((shardId) => {
      const oldRelation = this.#relationshipMap[shardId];
      this.#relationshipMap[shardId] = {
        ...oldRelation,
        other: [],
        currentRole: {},
      };
      roleList.forEach((role) => {
        delete this.#relationshipMap[shardId][role];
      });
      this.#initRelationship(oldRelation.podIds, this.#relationshipMap[shardId]);
      this.#resetPodLine(shardId);
    });
  }

  #resetPodLine(shardId) {
    this.#clearPodLine(shardId);
    const podA = this.#relationshipMap[shardId].A;
    const podS = this.#relationshipMap[shardId].S;
    const podRa = this.#relationshipMap[shardId].Ra;
    const podRs = this.#relationshipMap[shardId].Rs;
    this.#addPodLine(podA, podS, shardId);
    this.#addPodLine(podRa, podRs, shardId);
  }

  #clearPodLine(shardId) {
    roleList.forEach((role) => {
      const rolePod = this.#relationshipMap[shardId][role];
      if (rolePod) {
        const lineWrapper = this.#podMap[rolePod]?.mesh?.parent;
        if (lineWrapper) {
          const lineMesh = lineWrapper.getObjectByName(shardId);
          if (lineMesh) {
            lineMesh.traverse((child) => {
              if (child.isMesh) {
                child.geometry.dispose();
                child.material.dispose();
              }
            });
            lineMesh.parent.remove(lineMesh);
          }
        }
      }
    });
  }

  #addPodLine(A, S, shardId) {
    if (A && S) {
      const podA = this.#podMap[A]?.mesh;
      const podS = this.#podMap[S]?.mesh;
      if (podA && podS) {
        const wrapperA = podA.parent;
        const wrapperS = podS.parent;
        if (wrapperA && wrapperA === wrapperS) {
          initPodLine(shardId, [podA.position, podS.position], 0, this.#matMap.site.podLine, wrapperA);
        }
      }
    }
  }

  #updateDetail(siteGroupData) {
    siteGroupData.sites.forEach((siteData, index) => {
      const siteItem = this.children[0].children[index];
      let flag = false;
      const oldData = this.#siteMap[siteItem.name].data;
      Object.keys(oldData.memory).forEach((key) => {
        const oldNum = oldData.memory[key];
        const newNum = siteData.memory[key];
        if (typeof oldNum === 'number' && typeof newNum === 'number' && oldNum !== newNum) {
          oldData.memory[key] = siteData.memory[key];
          flag = true;
        }
      });
      Object.keys(oldData.physic).forEach((key) => {
        const oldNum = oldData.physic[key];
        const newNum = siteData.physic[key];
        if (typeof oldNum === 'number' && typeof newNum === 'number' && oldNum !== newNum) {
          oldData.physic[key] = siteData.physic[key];
          flag = true;
        }
      });
      if (flag) {
        siteItem.updateSiteDetail();
      }
    });
  }

  #selectPod(podId, siteId, podCb, otherCb) {
    const oldSeleted = this.#selectedPodId;
    const newSelected = podId;
    const opacity = 0.3;

    if (oldSeleted) {
      this.#updateSelectedOpacity(oldSeleted, opacity);
      this.#recoverSelectedMat(oldSeleted);
      this.#updateUnselectedOpacity(1);
      this.#removeLine();
      this.#recoverEvents(oldSeleted);
    }
    if (this.#podMap[newSelected]) {
      this.#changeSelectedMat(newSelected);
      this.#updateSelectedOpacity(newSelected, 1);
      this.#updateUnselectedOpacity(opacity);
      this.#createLine(newSelected, siteId);
      this.#setNoneEvents(newSelected);

      if (podCb) {
        podCb(this.#podMap[newSelected].data, this.#podMap[newSelected].mesh);
      }
    } else {
      const isOldSelected = siteId === this.children[0].children[this.#selectedSiteIndex].name;
      if (otherCb) {
        otherCb(this.#siteMap[siteId].data, this.#siteMap[siteId].mesh, isOldSelected);
      }
    }
  }

  #recoverEvents(id) {
    const podMesh = this.#podMap[id]?.mesh;
    if (podMesh) {
      podMesh.traverse((child) => {
        if (child.isMesh) {
          child.pointerEvents = '';
        }
      });
    }
  }

  #setNoneEvents(id) {
    const podMesh = this.#podMap[id]?.mesh;
    if (podMesh) {
      podMesh.traverse((child) => {
        if (child.isMesh) {
          child.pointerEvents = 'none';
        }
      });
    }
  }

  #changeSelectedMat(podId) {
    const shardId = this.#podMap[podId].data.shardId;
    const relationship = this.#relationshipMap[shardId];

    this.#changePodItemMat(relationship.A);
    this.#changePodItemMat(relationship.S);
    this.#changePodLineMat(shardId, relationship.A, relationship.S);

    this.#changePodItemMat(relationship.R);

    this.#changePodItemMat(relationship.Ra);
    this.#changePodItemMat(relationship.Rs);
    this.#changePodLineMat(shardId, relationship.Ra, relationship.Rs);

    relationship.other.forEach((id) => {
      this.#changePodItemMat(id);
    });
  }

  #changePodItemMat(podId) {
    if (!isNullOrUndefined(podId)) {
      const pod = this.#podMap[podId];
      const podMesh = pod.mesh.children[0];
      podMesh.material = podMesh.material.clone();
    }
  }

  #changePodLineMat(shardId, podA, podS) {
    if (!isNullOrUndefined(podA) && !isNullOrUndefined(podS)) {
      const pod = this.#podMap[podA];
      const wrapper = pod.mesh.parent;
      const lineMesh = wrapper && wrapper.getObjectByName(shardId);
      if (lineMesh) {
        lineMesh.children[0].material = lineMesh.children[0].material.clone();
      }
    }
  }

  #recoverSelectedMat(podId) {
    const shardId = this.#podMap[podId].data.shardId;
    const relationship = this.#relationshipMap[shardId];

    this.#recoverPodItemMat(relationship.A);
    this.#recoverPodItemMat(relationship.S);
    this.#recoverPodLineMat(shardId, relationship.A, relationship.S);

    this.#recoverPodItemMat(relationship.R);

    this.#recoverPodItemMat(relationship.Ra);
    this.#recoverPodItemMat(relationship.Rs);
    this.#recoverPodLineMat(shardId, relationship.Ra, relationship.Rs);

    relationship.other.forEach((id) => {
      this.#recoverPodItemMat(id);
    });
  }

  #recoverPodItemMat(podId) {
    if (!isNullOrUndefined(podId)) {
      const pod = this.#podMap[podId];
      const podMesh = pod.mesh.children[0];
      podMesh.material.dispose();
      setStatus(pod, this.#matMap);
    }
  }

  #recoverPodLineMat(shardId, podA, podS) {
    if (!isNullOrUndefined(podA) && !isNullOrUndefined(podS)) {
      const pod = this.#podMap[podA];
      const wrapper = pod.mesh.parent;
      const lineMesh = wrapper && wrapper.getObjectByName(shardId);
      if (lineMesh) {
        lineMesh.children[0].material.dispose();
        lineMesh.children[0].material = this.#matMap.site.podLine;
      }
    }
  }

  #updateSelectedOpacity(podId, opacity) {
    const shardId = this.#podMap[podId].data.shardId;
    const relationship = this.#relationshipMap[shardId];

    this.#updatePodItemOpacity(relationship.A, opacity);
    this.#updatePodItemOpacity(relationship.S, opacity);
    this.#updatePodLineOpacity(shardId, relationship.A, relationship.S, opacity);

    this.#updatePodItemOpacity(relationship.R, opacity);

    this.#updatePodItemOpacity(relationship.Ra, opacity);
    this.#updatePodItemOpacity(relationship.Rs, opacity);
    this.#updatePodLineOpacity(shardId, relationship.Ra, relationship.Rs, opacity);

    relationship.other.forEach((id) => {
      this.#updatePodItemOpacity(id, opacity);
    });
  }

  #updatePodItemOpacity(podId, opacity) {
    if (!isNullOrUndefined(podId)) {
      const pod = this.#podMap[podId];
      const podMesh = pod.mesh.children[0];
      podMesh.material.opacity = opacity;
    }
  }

  #updatePodLineOpacity(shardId, podA, podS, opacity) {
    if (!isNullOrUndefined(podA) && !isNullOrUndefined(podS)) {
      const pod = this.#podMap[podA];
      const wrapper = pod.mesh.parent;
      const lineMesh = wrapper && wrapper.getObjectByName(shardId);
      if (lineMesh) {
        lineMesh.children[0].material.opacity = opacity;
      }
    }
  }

  #updateUnselectedOpacity(opacity) {
    Object.keys(this.#matMap.pod).forEach((type) => {
      Object.keys(this.#matMap.pod[type]).forEach((status) => {
        this.#matMap.pod[type][status].opacity = opacity;
      });
    });
    this.#matMap.site.podLine.opacity = opacity;
    this.#matMap.site.businessLine.opacity = opacity;
    this.#w3d.agent.renderOnce();
  }

  #removeLine() {
    this.children[1].remove(...this.children[1].children);
  }

  #createLine(podId, siteId) {
    const shardId = this.#podMap[podId].data.shardId;
    const relationship = this.#relationshipMap[shardId];
    const currentRole = relationship.currentRole;

    let activePod;
    let designArr;

    const isUseCurrentRole = this.#isUseCurrentRole(relationship);
    if (isUseCurrentRole) {
      if (isNullOrUndefined(currentRole.A)) {
        return;
      }
      activePod = this.#podMap[currentRole.A];
      designArr = [currentRole.R, currentRole.Ra];
    } else {
      if (isNullOrUndefined(relationship.A)) {
        return;
      }
      activePod = this.#podMap[relationship.A];
      designArr = [relationship.R, relationship.Ra];
    }
    const startPos = activePod.mesh.getWorldPosition(new A3D.C.Vector3());
    const modelSize = this.#siteMap[siteId].mesh.podDefaultScale;
    startPos.y += modelSize * 0.5;
    designArr.forEach((id) => {
      const pod = this.#podMap[id];
      if (!(pod && pod.mesh)) {
        return;
      }
      const endPos = pod.mesh.getWorldPosition(new A3D.C.Vector3());
      endPos.y += modelSize * 0.1;
      const line = this.#getRelationLine(startPos, endPos);
      this.children[1].add(line);
    });
  }

  #isUseCurrentRole(relationship) {
    let flag = true;
    relationship.podIds.forEach((id) => {
      if (!roleList.includes(this.#podMap[id].data.currentRole)) {
        flag = false;
      }
    });
    return flag;
  }

  #getRelationLine(start, end) {
    const control = new A3D.C.Vector3().copy(start).add(end).multiplyScalar(0.5);
    control.y += this.#size.width * 0.4;
    const bezierCurve = new A3D.C.QuadraticBezierCurve3(start, control, end);

    const line = getDottedLine(bezierCurve, 6, this.#matMap.site.podRelationLine);
    line.renderOrder = 3;
    return line;
  }

  #selectSite(newSelectedIndex, callback) {
    this.#selectAble = false;
    this.#setMatrix(this.#selectedSiteIndex, newSelectedIndex);
    if (this.#animation.isPlaying()) {
      this.#animation.stop();
    }
    this.#animation.push({
      duration: 1000,
      onUpdate: (e) => {
        this.#updateMatrix(e.value);
      },
      onComplete: () => {
        callback();
        this.#selectAble = true;
      },
    });
    this.#animation.start();
  }

  #setMatrix(index, oldIndex) {
    this.#pathMatrxSetting.length = 0;
    const length = this.#matrixSetting.length;
    const step1 = (oldIndex + length - index) % length;
    const step2 = (index + length - oldIndex) % length;
    let step = step1;
    let dir = 1;
    if (step1 > step2) {
      step = step2;
      dir = -1;
    }
    for (let i = 0; i < step + 1; i++) {
      const curIndex = (index + length + dir * i) % length;
      const curMatrixSetting = [
        ...this.#matrixSetting.slice(length - curIndex),
        ...this.#matrixSetting.slice(0, length - curIndex),
      ];
      this.#pathMatrxSetting.push(curMatrixSetting);
    }
  }

  #updateMatrix(alpha) {
    const step = this.#pathMatrxSetting.length - 1;
    const curStep = Math.floor(alpha * step);
    const curAlpha = (alpha * step) % 1;
    const startMatrxSetting = this.#pathMatrxSetting[curStep];
    const endMatrxSetting = this.#pathMatrxSetting[curStep + 1];
    this.children[0].children.forEach((item, index) => {
      const start = startMatrxSetting[index];
      if (alpha === 1) {
        item.position.copy(start.position);
        item.scale.copy(start.scale);
      } else {
        const end = endMatrxSetting[index];
        item.position.lerpVectors(start.position, end.position, curAlpha);
        item.scale.lerpVectors(start.scale, end.scale, curAlpha);
      }
    });
  }

  /** 初始化布局-start */
  #init() {
    this.#initContainer();
    this.#initData(this.#data);
    this.#initSetting();
    this.#initSites();
  }

  #initContainer() {
    const group = new A3D.C.Group();
    group.name = 'sitesDatabase';
    const lineGroup = new A3D.C.Group();
    lineGroup.name = 'lines';
    this.add(group, lineGroup);
  }

  #initData(data) {
    data.data.forEach((pod) => {
      this.#podMap[pod.id] = { data: pod };
    });
    data.relationship.forEach((relationship) => {
      const newRelationship = {
        ...relationship,
        other: [],
        currentRole: {},
      };
      this.#relationshipMap[relationship.shardId] = newRelationship;
      this.#initRelationship(relationship.podIds, newRelationship);
    });
    data.sites.forEach((site) => {
      this.#siteMap[site.id] = { data: site };
    });
    this.name = data.id;
  }

  #initRelationship(podIds, relationship) {
    podIds.forEach((id) => {
      if (roleList.includes(this.#podMap[id].data.designRole)) {
        relationship[this.#podMap[id].data.designRole] = id;
      } else {
        relationship.other.push(id);
      }
      if (roleList.includes(this.#podMap[id].data.currentRole)) {
        relationship.currentRole[this.#podMap[id].data.currentRole] = id;
      }
    });
  }

  #resetData() {
    this.#selectedSiteIndex = 0;
    this.#selectedPodId = '';
    Object.keys(this.#podMap).forEach((key) => {
      delete this.#podMap[key];
    });
    Object.keys(this.#relationshipMap).forEach((key) => {
      delete this.#relationshipMap[key];
    });
    Object.keys(this.#siteMap).forEach((key) => {
      delete this.#siteMap[key];
    });
  }

  /**
   * 计算最外框的布局数据
   */
  #initSetting() {
    const siteLength = this.#data.sites.length - 1;
    const spaceZ = 0.1;

    const scaleSpaceRatio = 15;
    const allLength = 1.3;
    const spaceX = allLength / (siteLength * (scaleSpaceRatio + 1) - 1);
    let scale = Math.min(spaceX * scaleSpaceRatio, 0.6);
    if (siteLength === 0) {
      scale = 0;
    }

    const xLen = (scale + spaceX) * siteLength - spaceX;

    const positionZ1 = (1 + scale + spaceZ) / 2 - 1 / 2;
    const positionZ2 = positionZ1 - (1 + scale) / 2 - spaceZ;

    const { width, depth, offset } = this.#size;
    this.#matrixSetting.length = 0;
    this.#matrixSetting.push({
      position: { x: 0, y: 0, z: depth * positionZ1 + offset },
      scale: { x: 1, y: 1, z: 1 },
    });

    for (let i = 0; i < siteLength; i++) {
      this.#matrixSetting.push({
        position: { x: width * (-xLen / 2 + i * (scale + spaceX) + 0.5 * scale), y: 0, z: depth * positionZ2 + offset },
        scale: { x: scale, y: scale, z: scale },
      });
    }
  }

  /**
   * 创建 3 个站点、站点的外框
   */
  #initSites() {
    Object.keys(this.#siteMap).forEach((id, index) => {
      const siteData = this.#siteMap[id].data;
      const siteDatabase = new SiteDatabase(siteData, this.#modelMap, this.#matMap, {
        size: this.#size,
        podMap: this.#podMap,
        relationshipMap: this.#relationshipMap,
        type: this.#showType,
        i18n: this.#data.i18n,
      }, this.#w3d);
      siteDatabase.name = siteData.id;
      siteDatabase.position.copy(this.#matrixSetting[index].position);
      siteDatabase.scale.copy(this.#matrixSetting[index].scale);
      siteDatabase.userData.id = id;
      this.#siteMap[id].mesh = siteDatabase;
      this.children[0].add(siteDatabase);
    });
  }

  /** 初始化布局-end */
}
