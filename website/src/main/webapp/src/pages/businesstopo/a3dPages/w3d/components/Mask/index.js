import A3D from '@a3d/a3d';
import { boxShader } from './shader';
export default class Mask extends A3D.C.Group {
  constructor({
    depth = 1, rowSpace = 2, cloumSpace = 2, opacity = 1,
  } = {}) {
    super();
    this.name = 'mask';
    this.depth = depth;
    this.opacity = opacity;
    this.rowSpace = rowSpace;
    this.cloumSpace = cloumSpace;
  }

  select(obj, color = '#51c06a') {
    this.#remove();
    let result = obj;
    while (obj && obj.isObject3D && !result.isMesh) {
      result = result.children[0];
    }
    if (result && result.isMesh) {
      this.#addMaskGeo(color, result);
    }
  }

  destroy() {
    this.#remove();
    if (this.parent) {
      this.parent.remove(this);
    }
  }

  #addMaskGeo(color, obj) {
    let box = new A3D.C.Box3();
    let position = new A3D.C.Vector3();

    box.setFromObject(obj);
    const matrixWorld = obj.matrixWorld;
    const scaleX = matrixWorld.elements[0];
    const scaleY = matrixWorld.elements[5];
    const scaleZ = matrixWorld.elements[10];

    obj.getWorldPosition(position);

    let w = (box.max.x - box.min.x) / scaleX * this.rowSpace;
    let h = (box.max.z - box.min.z) / scaleZ * this.cloumSpace;
    const radius = Math.max(w, h) / 2;
    let dep = (box.max.y - box.min.y) / scaleY + this.depth;

    let boxGeo = new A3D.C.CylinderGeometry(radius, radius * 0.8, dep, 32);
    let boxMat = this.#createBoxMat({ height: dep, radius: radius * 0.8, color });
    let boxMsh = new A3D.C.Mesh(boxGeo, boxMat);
    boxMsh.pointerEvents = 'none';
    boxMsh.renderOrder = 10;

    this.add(boxMsh);
    this.position.y = dep * 0.5;

    obj.add(this);
  }

  #createBoxMat(uniforms = {}) {
    let color = new A3D.C.Color(uniforms.color || '#F34262').convertLinearToSRGB();
    let shader = JSON.parse(JSON.stringify(boxShader));
    shader.uniforms.height.value = uniforms.height || 1;
    shader.uniforms.radius.value = uniforms.radius || 1;
    shader.uniforms.color.value = color;
    shader.uniforms.opacity.value = this.opacity;
    let material = new A3D.C.ShaderMaterial(shader);
    return material;
  }

  #remove() {
    let child = this.children;
    for (let i = 0; i < child.length; i++) {
      child[i].geometry.dispose();
      child[i].material.dispose();
    }
    this.remove(...this.children);
    if (this.parent) {
      this.parent.remove(this);
    }
  }
}
