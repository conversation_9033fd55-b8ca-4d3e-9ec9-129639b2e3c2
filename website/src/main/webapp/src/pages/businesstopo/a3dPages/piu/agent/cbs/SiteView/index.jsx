import React, {useEffect, useReducer, useState, useRef} from 'react';
import {destroyW3D, initW3D} from '@pages/businesstopo/components/sitedrilldown/SiteDrillDown3D';
import * as api from '@pages/businesstopo/api';
import i18n from '@pages/businesstopo/locales/sitedrilldown';
import {$t, registerResource} from '@util';
import '@pages/businesstopo/css/index.less';

const getSiteOverViewGrid = async params => {
  let data = await api.querySiteGridData(
    params,
    res => res,
  );

  const filteredDrGroupList = data.data.drGroupList.map(drGroup => {
    const filteredMoTypeInfoList = drGroup.moTypeInfoList.filter(moType => {
      // 过滤掉 hasInstance 等于 0 的项，其余不过滤
      return moType.hasInstance !== 0;
    });

    // 返回过滤后的drGroup对象，带上已经过滤后的moTypeInfoList
    return {
      ...drGroup,
      moTypeInfoList: filteredMoTypeInfoList,
    };
  });
  // 更新原数据对象
  data.data.drGroupList = filteredDrGroupList;
  let newEles = data.data.siteInfoList.sort((a, b) => a.siteName.localeCompare(b.siteName),
  );
  data.data.siteInfoList = newEles;
  return data;
};

const SiteView = (props) => {
  registerResource(i18n, 'tooltip');

  const w3dContainerRef = useRef(null);

  const {
    style = {background: 'transparent'},
    cameraSetting = {distance: 160},
    solutionId: oldSolutionId = '',
    siteId: oldSiteId = '',
    errorSiteIdList = [],
    alarmSetting = {}, // moTypeId: {count: 2, msgDiv: <></>}
  } = props;

  const [siteInfo, setSiteInfo] = useState({
    solutionId: oldSolutionId,
    siteId: oldSiteId,
  });

  const [siteDataVal, setSiteDataVal] = useState({});

  useEffect(() => {
    setSiteInfo(prevState => {
      return {
        ...prevState,
        siteId: oldSiteId,
        solutionId: oldSolutionId,
      };
    });
  }, [oldSolutionId, oldSiteId]);

  const changeSiteFunc = (siteId) => {
    setSiteInfo(prevState => {
      return {
        ...prevState,
        siteId,
      };
    });
  };

  const initData = async() => {
    const siteData = await getSiteOverViewGrid({
      solutionId: siteInfo.solutionId,
      siteId: siteInfo.siteId,
      timestamp: 0,
    });

    // 将告警数据置换成智能体中的数据
    siteData.data.drGroupList.forEach(drGroup => {
      drGroup.moTypeInfoList.forEach(motype => {
        if (alarmSetting[motype.moTypeId]) {
          motype.alarmCount = alarmSetting[motype.moTypeId].count;
        } else {
          motype.alarmCount = 0;
        }
        motype.isHealthy = motype.alarmCount > 0 ? 0 : null;
      });
    });

    siteData.data.siteName = `${siteData.data.siteName}`;
    siteData.data.siteInfoList.forEach(info => {
      info.siteName = `${info.siteName}`;
      if (errorSiteIdList.includes(info.siteId)) {
        info.alarmStatus = true;
      }
    });
    setSiteDataVal(siteData);
    await initW3D(w3dContainerRef, () => {
      // 空方法
    }, siteInfo.siteId, siteInfo.solutionId, {
      siteData: siteData.data,
      undefined,
      environmentType: siteData.data.environmentType,
      changeSite: async changeData => {
        changeSiteFunc(changeData.siteId);
      },
      isPiu: true,
      piuData: {
        errorSiteIdList,
        alarmSetting,
      },
      cameraSetting,
    });
  };

  useEffect(() => {
    destroyW3D();

    initData();
    return () => destroyW3D();
  }, [JSON.stringify(siteInfo)]);

  return (
    <div id='siteDrillTopo'>
      <div id='site_main_container' style={{height: '100%', width: '100%'}}>
        <div
          className='topology3d_main_container_full w3d_container'
          style={{
            height: '100%',
            background: style.background,
          }}
          ref={w3dContainerRef}
        />
      </div>
    </div>
  );
};

export default SiteView;
