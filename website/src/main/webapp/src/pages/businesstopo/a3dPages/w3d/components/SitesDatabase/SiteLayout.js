const fixNum = (num, pow = 2) => {
  const ratio = Math.pow(10, pow);
  return Math.round(num * ratio) / ratio;
};

export default class SiteLayout {
  static initLayout(layoutData, size, nameSize, space, position) {
    const podDepth = this.#getPodDepth(layoutData, size, nameSize, space);
    const podWidth = this.#getPodWidth(layoutData, size, space);
    const podSize = { podWidth, podDepth };
    this.#setSize(layoutData, size, nameSize, space, podSize);
    this.#fixSizeToFull(layoutData);
    this.#setPosition(layoutData, nameSize, space);
    layoutData.podWidth = podWidth;
    layoutData.podDepth = podDepth;
    layoutData.position = position;
  }

  static #getPodDepth(layoutData, size, nameSize, space) {
    const l1LayerZ = layoutData.level1.length;
    let leastDepth = size.depth - (nameSize + space * 3) * l1LayerZ - space * (l1LayerZ - 1);
    layoutData.level1.forEach((businessGroup) => {
      let l2LayerZ = 0;
      businessGroup.group.forEach((business, index) => {
        if (index) {
          if (businessGroup.col === business.col) {
            l2LayerZ = Math.max(l2LayerZ, business.level2.length);
          }
        } else {
          l2LayerZ = business.level2.length;
        }
      });
      leastDepth -= space * (l2LayerZ - 1) + (nameSize + space * 3) * l2LayerZ;
    });
    const podDepth = leastDepth / layoutData.row;
    return podDepth;
  }

  static #getPodWidth(layoutData, size, space) {
    let podWidth = size.width;
    layoutData.level1.forEach((businessGroup) => {
      const l1LayerX = businessGroup.group.length;
      let leastWidth = size.width - space * (l1LayerX - 1);
      businessGroup.group.forEach((business) => {
        let l2LayerX = 0;
        business.level2.forEach((level2Group, index) => {
          if (index) {
            if (level2Group.col === business.col) {
              l2LayerX = Math.max(l2LayerX, level2Group.group.length);
            }
          } else {
            l2LayerX = level2Group.group.length;
          }
        });
        leastWidth -= space * (l2LayerX + 1 + l2LayerX * 2);
      });
      podWidth = Math.min(leastWidth / businessGroup.col, podWidth);
    });
    return podWidth;
  }

  static #setSize(layoutData, size, nameSize, space, podSize) {
    const { podWidth, podDepth } = podSize;
    layoutData.size = size;

    layoutData.level1.forEach((businessGroup) => {
      let lastBusinessX = -space;
      let businessGroupDepth = 0;
      businessGroup.group.forEach((business) => {
        let businessWidth = 0;
        let businessDepth = space * 2 + nameSize;

        business.level2.forEach((dbGroup) => {
          let lastDbX = space;
          const row = dbGroup.row;
          const dbDepth = podDepth * row + space * 3 + nameSize;
          businessDepth += dbDepth + space;
          dbGroup.group.forEach((db) => {
            const col = db.col;
            const dbWidth = podWidth * col + space * 2;
            lastDbX += dbWidth + space;

            db.size = { width: dbWidth, depth: dbDepth };
          });
          dbGroup.size = { width: lastDbX, depth: dbDepth };

          businessWidth = Math.max(businessWidth, lastDbX);
        });
        business.size = { width: businessWidth, depth: businessDepth };

        businessGroupDepth = Math.max(businessGroupDepth, businessDepth);
        lastBusinessX += businessWidth + space;
      });

      businessGroup.size = { width: lastBusinessX, depth: businessGroupDepth };
    });
  }

  static #fixSizeToFull(layoutData) {
    layoutData.level1.forEach((businessGroup) => {
      const businessGroupMoreWidth = fixNum(layoutData.size.width - businessGroup.size.width, 6);
      const businessMoreWidth = businessGroupMoreWidth / businessGroup.group.length;
      businessGroup.size.width = layoutData.size.width;
      businessGroup.group.forEach((business) => {
        business.size.width += businessMoreWidth;
        business.level2.forEach((dbGroup) => {
          const dbGroupMoreWidth = fixNum(business.size.width - dbGroup.size.width, 6);
          const dbMoreWidth = dbGroupMoreWidth / dbGroup.group.length;
          dbGroup.size.width = business.size.width;
          dbGroup.group.forEach((db) => {
            db.size.width += dbMoreWidth;
          });
        });
      });
    });
  }

  static #setPosition(layoutData, nameSize, space) {
    let businessGroupPosZ = -layoutData.size.depth / 2;
    layoutData.level1.forEach((businessGroup) => {
      businessGroup.position = { x: 0, y: 0, z: businessGroupPosZ };
      businessGroupPosZ += businessGroup.size.depth + space;

      let businessPosX = -businessGroup.size.width / 2;
      businessGroup.group.forEach((business) => {
        business.position = { x: businessPosX, y: 0, z: 0 };
        businessPosX += business.size.width + space;

        let dbGroupPosZ = -business.size.depth / 2 + nameSize + space * 2;
        business.level2.forEach((dbGroup) => {
          dbGroup.position = { x: 0, y: 0, z: dbGroupPosZ };
          dbGroupPosZ += dbGroup.size.depth + space;

          let dbPosX = -dbGroup.size.width / 2 + space;
          dbGroup.group.forEach((db) => {
            db.position = { x: dbPosX, y: 0, z: 0 };
            dbPosX += db.size.width + space;
          });
        });
      });
    });
  }
}
