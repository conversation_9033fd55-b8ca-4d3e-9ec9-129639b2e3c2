import HuiCharts from '@hui/charts';
import { tpsData1, statusInfoData, tabsInfo } from './chartData';
import './index.less';
import React, { useState, useEffect, useRef } from 'react';
import StatusCard from '../StatusCard';
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/podDetails';
import PropTypes from 'prop-types';

registerResource(i18n, 'control2D');

function Memory(props) {
  const [tabIndex, setTabIndex] = useState(0);
  const chartRef = useRef(null);
  useEffect(() => {
    if (tabIndex === 0) {
      chartRef.current.innerHTML = '';
      chartRef.current.removeAttribute('_echarts_instance_');
      const chartIns1 = new HuiCharts();
      chartIns1.init(chartRef?.current);
      chartIns1.setSimpleOption('AreaChart', tpsData1.option);
      setTimeout(() => {
        chartIns1.render();
      }, 50);
    }
  }, [tabIndex]);
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div className="KPI_top">
        <span className="KPI_title">
          {getMessage('control2D.details')}
          {' '}
          {props.podId}
        </span>
      </div>
      <div className="statusInfo">
        {statusInfoData.map((item, index) => (
          <div className="statusInfo_item" key={index}>
            <span className={`${index === 0 || index === 1 ? 'green' : ''}`}>
              {item.info}
            </span>
            <span>
              {item.title}
            </span>
          </div>
        ))}
      </div>
      <div className="KPI_top">
        <span className="KPI_title">
          {getMessage('control2D.NEKPI')}
        </span>
      </div>
      <div className="KPI_content">
        <div className="KPI_tabs">
          {tabsInfo.map((item, index) => (
            <span
              onClick={() => setTabIndex(index)}
              className={tabIndex === index ? 'active tip' : 'tip'}
              key={index}
            >
              {item}
              <span className='line' />
            </span>
          ))}
        </div>
        <div className='KPI_chart'>
          <div ref={chartRef} className='chart' />
        </div>
        <StatusCard />
      </div>
    </div>
  );
}

Memory.propTypes = { podId: PropTypes.string };

export default Memory;
