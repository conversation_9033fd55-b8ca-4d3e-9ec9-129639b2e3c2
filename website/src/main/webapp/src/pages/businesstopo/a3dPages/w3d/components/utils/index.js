export { getDottedLine, getLine, getLine2} from './line';
export {
  getCanvasTex, getGroundOptions, getLineOptions, getTextPlane, getTextToTex, getWallLine,
} from './site';
export { setStatus } from './siteDatabase';
export { setDbStatus } from './sitesDrillDown';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

/**
 * canvas 画带圆角的矩形
 * @param {*} ctx
 * @param {*} param1
 * @param {*} width
 * @param {*} height
 * @param {*} radius
 */
export const drawRoundedRect = (ctx, { x, y }, width, height, radius) => {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.arcTo(x + width, y, x + width, y + radius, radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius);
  ctx.lineTo(x + radius, y + height);
  ctx.arcTo(x, y + height, x, y + height - radius, radius);
  ctx.lineTo(x, y + radius);
  ctx.arcTo(x, y, x + radius, y, radius);
  // 关闭路径
  ctx.closePath();
};

export const getTypeItem = (object3D, type) => {
  let node3D = null;
  if (object3D instanceof A3D.C.Object3D) {
    node3D = object3D;
    while (node3D && node3D.userData._type !== type) {
      node3D = node3D.parent;
    }
  }
  return node3D;
};
