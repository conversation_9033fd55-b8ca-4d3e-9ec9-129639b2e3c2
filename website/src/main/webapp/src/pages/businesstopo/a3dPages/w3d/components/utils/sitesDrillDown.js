import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {getObjectSize} from '../common/utils';
import {drawRoundedRect} from '.';
import {grayRoundFrag, grayRoundVert} from './grayRound';

const A3D = getA3D();

const drawCanvas = (canvas, content) => {
  const pixelRatio = 4;
  const lineHeight = 24 * pixelRatio;
  const fontSize = 18 * pixelRatio;
  const ctx = canvas.getContext('2d');
  ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
  const size = ctx.measureText(content.slice(1));

  canvas.width = size.width + lineHeight + 2;
  canvas.height = lineHeight + 2;

  drawRoundedRect(ctx, {x: 1, y: 1}, canvas.width - 2, lineHeight, lineHeight * 0.5);
  // 填充路径
  ctx.fillStyle = '#F43146';
  ctx.fill();

  ctx.textAlign = 'center';
  ctx.textBaseline = 'top';
  ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
  ctx.fillStyle = '#ffffff';

  const HALF = 2;
  const FONT_SPACE = 0.1;
  const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
  ctx.fillText(content, canvas.width / HALF, top);
};

const initCanvas = (count) => {
  const content = String(count > 99 ? '99+' : count);
  const canvas = document.createElement('canvas');
  drawCanvas(canvas, content);
  return canvas;
};

const getSprite = (count, mesh) => {
  const modelSize = getObjectSize(mesh);
  const alarmSize = modelSize.depth * 0.5;

  const canvas = initCanvas(count);
  const texture = A3D.canvasToTex(canvas, true);
  const material = new A3D.C.SpriteMaterial({
    map: texture,
    color: '#FFFFFF',
    transparent: true,
    depthWrite: false,
  });
  const sprite = new A3D.C.Sprite(material);
  sprite.renderOrder = 12;
  sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
  sprite.center.x = 0;
  sprite.position.y = modelSize.height * 1.4;
  sprite.position.x = modelSize.width * 0.3;

  return sprite;
};

const setError = (dbItem, modelMap) => {
  const group = dbItem.mesh;
  const mesh = modelMap[group.userData.type];
  const alarmCount = dbItem.data.alarmCount;
  let alarmCard = group.getObjectByName('alarmCard');
  if (alarmCard) {
    group.remove(alarmCard);
  }
  if (alarmCount > 0) {
    alarmCard = getSprite(alarmCount, mesh);
    alarmCard.name = 'alarmCard';
    group.add(alarmCard);
  }
};

const setMaterial = (dbItem, matMap) => {
  const type = dbItem.type;
  const group = dbItem.mesh;
  const mesh = group.children[0];

  const support = dbItem.data.isSupportGray;
  const isGray = dbItem.data.isGrayUpdating > 0;
  const isDvAlarm = dbItem.data.isHealthy === 0;
  const isNoActiveDv = dbItem.data.isActiveDv === 0;
  if (type === 'strip') {
    mesh.material = dbItem.data.alarmCount > 0 ? matMap[type].group.alarm : matMap[type].group.normal;
    return;
  }

  if (isNoActiveDv) {
    mesh.material = matMap[type].grey;
  } else if (isDvAlarm) {
    mesh.material = matMap[type].alarm;
  } else if (support && isGray) {
    mesh.material = matMap[type].grey;
  } else {
    mesh.material = matMap[type].normal;
  }
};

const setGray = (dbItem) => {
  const type = dbItem.type;
  const group = dbItem.mesh;
  const mesh = group.children[0];

  const support = dbItem.data.isSupportGray;
  const isGray = dbItem.data.isGrayUpdating > 0;
  const isDvNode = dbItem.data.isActiveDv === 0 || dbItem.data.isActiveDv === 1;
  let gray = group.getObjectByName('gray');
  if (gray) {
    group.remove(gray);
  }
  if (support && !isDvNode) {
    if (!mesh.geometry.boundingBox) {
      mesh.geometry.computeBoundingBox();
    }
    const size = mesh.geometry.boundingBox.getSize(new A3D.C.Vector3());
    const width = size.x * 2;

    const geometry = new A3D.C.PlaneGeometry(width, width);
    geometry.rotateX(-Math.PI * 0.5);

    const planeMat = new A3D.C.ShaderMaterial({
      uniforms: {
        uTime: {value: 0},
        uRadius: {value: size.x * 0.6},
        uThickness: {value: size.x * 0.06},
        uBRadius: {value: size.x * 0.2},
        uLightThickness: {value: size.x * 0.2},
        uColor: {value: new A3D.C.Color('#69b4ff').convertLinearToSRGB()},
        uIsRound: {value: type === 'database'},
        uIsGray: {value: isGray},
      },
      vertexShader: grayRoundVert,
      fragmentShader: grayRoundFrag,
      transparent: true,
      depthWrite: false,
    });
    gray = new A3D.C.Mesh(geometry, planeMat);
    gray.name = 'gray';
    gray.renderOrder = 2;
    gray.rotateY(Math.PI / 4);
    gray.onBeforeRender = () => {
      planeMat.uniforms.uTime.value = (Date.now() / 1000 * 4) % (Math.PI * 2);
    };
    group.add(gray);
  }
};

export const setDbStatus = (dbItem, matMap, modelMap) => {
  setError(dbItem, modelMap);
  setMaterial(dbItem, matMap);
  setGray(dbItem, matMap);
};

export const getPlanePoints = (width, depth) => {
  const halfWidth = width / 2;
  const halfDepth = depth / 2;
  const points = [
    {x: -halfWidth, y: halfDepth},
    {x: halfWidth, y: halfDepth},
    {x: halfWidth, y: -halfDepth},
    {x: -halfWidth, y: -halfDepth},
  ];
  return points;
};
