import '@pages/businesstopo/a3dPages/styles/split.less';
import React, {useEffect, useRef, useState, useContext} from 'react';
import {
  destroyW3D,
  initW3D,
  resizeW3D, updateOverViewGridMM,
} from './MMOverview3D';
import {OVER_VIEW} from '@pages/businesstopo/const/timeLine';
import {setSessionData} from '@pages/businesstopo/util';
import {getSolutionData} from '@pages/businesstopo/api';
import Toolbar from '../../../components/toolbar/Toolbar';
import RightPanel from './RightPanel';
import {registerResource, setHelpId, setTabTitle} from '../../../../../commonUtil';
import i18n from '../../../locales';
import {$t} from '@util';
import TimeLinePanel from '../../../components/timeline/TimeLinePanel';
import {BUSINESS_TOPO_CONTEXT} from '../../../const';
import eventBus from '@pages/businesstopo/a3dPages/bus';

function MMOverview({nowSolutionData}) {
  registerResource(i18n, 'businessTopo');
  setHelpId('com.huawei.dvtopo.business');
  setTabTitle($t('business_layer_title'));
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {showKPIPanel} = state;

  useEffect(() => {
    resizeW3D();
  }, [showKPIPanel]);

  const w3dContainerRef = useRef();
  const [refreshFlag, setRefreshFlag] = useState(0);
  const initFlag = useRef(0);
  const intervalId = useRef(0);

  useEffect(async() => {
    if (initFlag.current !== 0) {
      let param = {timestamp: 0};
      let realTimesolutionData = await getSolutionData(param, res => res);
      if (realTimesolutionData.data.length > 0) {
        const oldState = state.isImporting;
        const oldSlutionId = state.solutionId;
        dispatch({
          solutionId: realTimesolutionData.data[0].solutionId,
          dataShow: true,
          dataDaseShow: realTimesolutionData.data[0].displayDatabaseView !== 'false',
          isImporting: realTimesolutionData.data[0].isImporting === true ? true : false,
        });
        const solutionEqual = oldSlutionId !== realTimesolutionData.data[0].solutionId;
        const stateEqual = (oldState !== realTimesolutionData.data[0].isImporting && realTimesolutionData.data[0].isImporting === false);
        if (solutionEqual || stateEqual) {
          location.reload();
        }
        updateOverViewGridMM(realTimesolutionData.data[0].solutionId);
      } else {
        dispatch({
          isImporting: false,
          dataShow: false,
        });
      }
    }
  }, [refreshFlag]);

  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };

  useEffect(() => {
    eventBus.addListener('overview_manualRefresh', () => {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      if (topoSession.selectedTime > 0) {
        return;
      }
      setRefreshFlag(new Date().getTime());
    });
    destroyW3D();
    stopRefresh();
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    initFlag.current = 0;
    if (!topoSession.selectedTime) {
      setSessionData({
        selectedTime: 0,
        isTimeTrack: false,
      });
    }
    if (topoSession.selectedTime > 0) {
      return;
    }

    let param = {
      timestamp: topoSession.selectedTime ? topoSession.selectedTime : 0,
    };
    if (nowSolutionData.length > 0) {
      param.solutionId = nowSolutionData[0].solutionId;
      setSessionData({
        solutionId: nowSolutionData[0].solutionId,
        selectSiteId: -1,
      });
      initW3D(w3dContainerRef, nowSolutionData[0].solutionId, dispatch);
      dispatch({
        solutionId: nowSolutionData[0].solutionId,
        dataShow: true,
        dataDaseShow: nowSolutionData[0].displayDatabaseView !== 'false',
        selectedTime: topoSession.selectedTime,
        isTimeTrack: topoSession.isTimeTrack,
        solutionData: nowSolutionData,
      });
      initFlag.current = 1;
      startRefresh();
    } else {
      dispatch({
        showMessage: false,
      });
      return;
    }

    return () => {
      stopRefresh();
      destroyW3D();
    };
  }, []);

  const startRefresh = () => {
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, 30000);
    intervalId.current = id;
  };
  // 时间回溯
  const timeTrack = time => {
    let param = {timestamp: time};
    setSessionData({
      isTimeTrack: true,
      selectedTime: time,
    });
    stopRefresh();
    getSolutionData(param, res => {
      destroyW3D();
      if (res.data.length > 0) {
        param.solutionId = res.data[0].solutionId;
        setSessionData({
          solutionId: res.data[0].solutionId,
        });
        destroyW3D();
        initW3D(w3dContainerRef, res.data[0].solutionId, dispatch);
        dispatch({
          solutionId: res.data[0].solutionId,
          dataShow: true,
          solutionData: res.data,
          showMessage:false,
          selectedTime: time,
        });
      } else {
        dispatch({
          showMessage: true,
        });
      }
    });
    initFlag.current = 1;
  };

  // 退出时间回溯模式
  const backTimeTrack = () => {
    startRefresh();
    let param = {timestamp: 0};
    getSolutionData(param, res => {
      if (res.data.length > 0) {
        param.solutionId = res.data[0].solutionId;
        setSessionData({
          solutionId: res.data[0].solutionId,
        });
        destroyW3D();
        initW3D(w3dContainerRef, res.data[0].solutionId, dispatch);
        dispatch({
          solutionId: res.data[0].solutionId,
          dataShow: true,
          solutionData: res.data,
          showMessage:false,
        });
      } else {
        destroyW3D();
        dispatch({
          showMessage: true,
        });
      }
    });
  };
  return (
    <div id="business_topo" style={{height: '100%', width: '100%', 'backgroud-color': 'black'}}>
      <div
        id="topology3d_main_container"
        className={
          showKPIPanel ?
            'topology3d_main_container w3d_container' :
            'topology3d_main_container_full w3d_container'
        }
        ref={w3dContainerRef}
      />

      <Toolbar showRightPanel={showKPIPanel} isMM={true} />

      <TimeLinePanel
        pageType={OVER_VIEW}
        renderTopology={timeTrack}
        backTimeTrack={backTimeTrack}
      />

      <RightPanel
        refreshFlag={refreshFlag}
      />
    </div>
  );
}

export default MMOverview;
