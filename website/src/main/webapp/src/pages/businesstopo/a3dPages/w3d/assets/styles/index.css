/*适配*/
@media screen and (min-width: 1680px) {
  html {
    font-size: 16px !important;
    height: 100%;
  }
}
  
@media screen and (min-width: 1440px) and (max-width: 1679px) {
  html {
    font-size: 14px !important;    
    height: 100%;
  }
}
  
@media screen and (max-width: 1439px) {
  html {
    font-size: 12px !important;
    height: 100%;
  }
}
  
* {
  margin: 0 auto;
  padding: 0;
  font-weight: normal;
  outline: none;
  box-sizing: border-box;
}
  
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
  font-weight: normal;
}
  
body {
  height: 100%;
  font-weight: normal;
  overflow: hidden;
}
  
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

li {
  list-style: none;
}