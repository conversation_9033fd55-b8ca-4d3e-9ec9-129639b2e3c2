import A3D from '@a3d/a3d';
import * as THREE1 from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { BokehPass } from 'three/examples/jsm/postprocessing/BokehPass';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { CSS2DObject, CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer';
import { CSS3DRenderer } from 'three/examples/jsm/renderers/CSS3DRenderer';
import { BokehShader } from 'three/examples/jsm/shaders/BokehShader';
import { CopyShader } from 'three/examples/jsm/shaders/CopyShader';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';

const THREE = {
  ...THREE1,
  OrbitControls,
  TransformControls,
  CSS2DRenderer,
  CSS2DObject,
  CSS3DRenderer,
  GLTFLoader,
  OBJLoader,
  EffectComposer,
  RenderPass,
  BokehShader,
  CopyShader,
  FXAAShader,
  ShaderPass,
  OutlinePass,
  BokehPass,
  // OutputPass,
};
let init;
export const getA3D = function() {
  if (init) {
    return A3D;
  }
  init = true;
  A3D.attachCore(THREE);
  return A3D;
};
