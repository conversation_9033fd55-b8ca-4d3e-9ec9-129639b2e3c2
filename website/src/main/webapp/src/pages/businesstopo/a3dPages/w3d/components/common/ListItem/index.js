import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { ATTRS, MESH_ATTRS } from '../const';
import { getInitBoundingBox, getObjectSize, getRotationFromDirection } from '../utils';

const A3D = getA3D();

export default class ListItem extends A3D.C.Group {
  /**
   * ListItem 组件构造方法
   * @param {JSON} props
   * @example
   * const props = {
   *   model: Group|Mesh,        // 模型
   *   data: {                  // 组件数据，eg: [{id: 'id-1'}, {id: 'id-2'}, ...]
   *     position: Vector3,    // 位置，可选项，默认值{ x:0, y:0, z:0 }
   *     rotation: Euler,     // 旋转，可选项，默认值{ x:0, y:0, z:0 }
   *     direction: Vector3, // 朝向，可选项，默认值{ x:0, y:0, z:0 }，可以代替rotation，组件内自动转化为旋转量。适用于只在某一平面如 xz 平面旋转的情况。
   *     scale: Vector3,    // 缩放，可选项，默认值{ x:1, y:1, z:1 }
   *     id: String,       // 可选项
   *   },
   *   onChangeCallback: Function,  // 可选项，更新listItem时，会触发此回调函数，必须返回模型，eg: (modelIns, index) => { return modelIns }
   * }
   */
  #needsUpdate;
  constructor(props) {
    super();

    if (props) {
      this.parameters = A3D.util.extend(true, {
        index: props.index || 0,
        onChangeCallback: (modelIns) => modelIns,
      }, props);
      this.#initModel(props.model);
      this.#setAttrs(props.data, true);
      this.boundingBox = getInitBoundingBox();
      this.#needsUpdate = true;
    }
  }

  /**
   * 重写 Object3D 的 copy 方法
   * @param {ListItem} source
   */
  copy(source) {
    super.copy(source, true);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    return this;
  }

  /**
   * 删除 listItem 本身
   * @param  {...Object3D} rest
   */
  remove(obj) {
    if (obj?.isObject3D) {
      super.remove(obj);
    } else {
      if (this.parent) {
        this.parent.remove(this);
      }
      this.parameters = null;
    }
  }

  /**
   * 获取 listItem 数据
   *
   * @returns {JSON} data listItem 数据，位置，缩放，旋转，名称
   */
  get() {
    const output = {};
    MESH_ATTRS.forEach((key) => {
      output[key] = this[key];
    });
    output.name = this.name;
    return output;
  }

  /**
   * 设置 listItem 数据
   * @param {JSON} data listItem 数据，位置，缩放，旋转，名称
   */
  set(data) {
    this.#setAttrs(data);
    this.#needsUpdate = true;
  }

  /**
   * 绑定事件
   * @param {String} event 事件类型
   * @param {Function} handler 事件回调
   */
  bind(event, handler) {
    let data = this.get();
    A3D.DOM.bind(this, event, (e, info) => {
      if (handler) {
        return handler(data, e, info);
      }
      return false;
    });
  }

  getBoundingBox() {
    if (!this.#needsUpdate) {
      return this.boundingBox;
    }
    this.#needsUpdate = false;
    this.boundingBox = getObjectSize(this);
    return this.boundingBox;
  }

  /**
   * 创建模型
   * @param {Object3D} model 模型
   */
  #initModel(model) {
    if (model) {
      this.add(model);
    }
  }

  /**
   * 更新模型
   */
  #updateModel(data) {
    let oldModel = this.children[0];
    if (!oldModel) {
      return;
    }
    const newModel = this.parameters.onChangeCallback(oldModel, this.parameters.index, data);
    if (newModel && newModel !== oldModel) {
      this.remove(oldModel).add(newModel);
    }
  }

  #setAttrs(data, useDefaultValue) {
    let setting = { ...data };
    let rotation = getRotationFromDirection(data);
    if (rotation) {
      setting.rotation = rotation;
    }
    let attr;
    MESH_ATTRS.forEach((key) => {
      attr = ATTRS[key];
      let value = setting[key];
      if (value) {
        this[key].set(value.x, value.y, value.z);
      } else if (useDefaultValue) {
        let defaultValue = attr.defaultValue;
        this[key].set(defaultValue.x, defaultValue.y, defaultValue.z);
      }
    });
    let name = setting.id || setting.name;
    if (name) {
      this.name = name;
    }
    this.#updateModel(data);
  }
}
