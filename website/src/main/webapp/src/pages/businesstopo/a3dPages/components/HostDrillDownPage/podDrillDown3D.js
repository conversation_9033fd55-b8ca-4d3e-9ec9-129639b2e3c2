import eventBus from '@pages/businesstopo/a3dPages/bus';
import {getMessage, registerResource} from '@pages/businesstopo/a3dPages/commonUtil/intl';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/podDrillDown';
import {eventBusHandler, getRouteQuery} from '../utils';
import './podDetailCard.less';
import {STATUS} from '@pages/businesstopo/const/moTypeDrill';

registerResource(i18n, 'w3d');
let home3D;

// 处理数据结构
const getI18n = () => ({
  productionArea: getMessage('w3d.productionArea'),
  grayArea: getMessage('w3d.grayArea'),
  version: getMessage('w3d.version'),
});

const getData = (data, isAgent) => {
  const result = {
    podCount: 0,
    productionProportion: 0,
    grayProportion: 0,
    applicationType: data.applicationType,
    environmentType: data.environmentType,
    moTypeList: [{
      podList:data.podDataList.map(pod => ({
        dnId: pod.dnId,
        name: pod.podName,
        alarmCount: pod.podAlarmCount,
        alarmStatus: (pod.csnState === 0 && pod.availableStatus === STATUS.normal) ? 0 : 1,
        createTime: pod.createTime,
        status: pod.availableStatus,
      })),
      vmList: [],
    }],
    vmList:data.vmDataList.map(item=>{
      return {
        dnId: item.dnId,
        name: item.vmName,
        ip: item.vmIp,
        status: item.availableStatus,
        alarmStatus: (item.csnState === 0 && item.availableStatus === STATUS.normal) ? 0 : 1,
      };
    }),
    i18n: getI18n(),
    isAgent,
  };
  result.podCount = data.podDataList.length;
  return result;
};

export const initW3D = async(container, setting) => {
  const {initData, eventArr, isAgent} = setting;
  eventArr.forEach(item => {
    eventBus.addListener(item.eventName, item.fn);
  });

  // 2D-3D对接，将数据转化成3D需要的数据
  const data = getData(initData, isAgent);
  if (!home3D) {
    home3D = new Home3D(eventBusHandler);
  }
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, {hostDrillDown: data});
};

// 局部增量刷新
export const updatePodDrillData = pageData => {
  const data = getData(pageData);
  eventBusHandler.emit('to3d_updatePodDrillDownData', data);
};

export const destroyW3D = eventArr => {
  eventArr.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });
  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};
