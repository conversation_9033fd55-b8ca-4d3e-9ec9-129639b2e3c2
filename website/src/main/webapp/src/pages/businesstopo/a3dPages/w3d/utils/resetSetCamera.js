import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import * as THREE from 'three';

const A3D = getA3D();

const xyz2rpt = (position) => {
  const spherical = new THREE.Spherical().setFromVector3(position);
  return {
    distance: spherical.radius,
    lon: spherical.theta,
    lat: -(spherical.phi - Math.PI * 0.5),
  };
};

const rpt2xyz = ({ distance, lat, lon }) => {
  let theta = lon;
  let phi = Math.PI * 0.5 - lat;
  const spherical = new THREE.Spherical(distance, phi, theta);
  return new THREE.Vector3().setFromSpherical(spherical);
};

const fixToRotateLessPi = (startRpt, endRpt) => {
  let moreLon;
  let lessLon;
  if (startRpt.lon > endRpt.lon) {
    moreLon = startRpt;
    lessLon = endRpt;
  } else {
    moreLon = endRpt;
    lessLon = startRpt;
  }
  if (moreLon.lon - lessLon.lon > Math.PI) {
    lessLon.lon += Math.PI * 2;
  }
};

const lerp = (start, end, temp, lerpNum) => {
  Object.keys(temp).forEach((item) => {
    temp[item] = start[item] * (1 - lerpNum) + end[item] * lerpNum;
  });
};

const setCamera = (camera, orbit, position, lookat, up) => {
  if (position) {
    camera.position.copy(position);
  }
  if (orbit) {
    const flag = orbit.enabled;
    orbit.enabled = false;
    if (up) {
      camera.up.copy(up);
      camera.updateProjectionMatrix();
    }
    if (lookat) {
      orbit.target.copy(lookat);
      camera.lookAt(lookat);
    }
    orbit.update(true);
    orbit.enabled = flag;
  } else {
    if (up) {
      camera.up.copy(up);
    }
    if (lookat) {
      camera.lookAt(lookat);
    }
    camera.updateProjectionMatrix();
  }
};

const getEndSettings = (mesh, options) => {
  const endSettings = {
    distance: options.distance || 0.01,
    lon: options.lon || 0,
    lat: options.lat || 0,
  };
  const endTargetPos = new THREE.Vector3().copy(mesh.position);
  const endCameraPos = endTargetPos.clone().add(rpt2xyz(endSettings));
  return { endSettings, endTargetPos, endCameraPos };
};

const getSrcSettings = (agent) => {
  const srcTargetPos = agent.env.lookAt.position.clone();
  const srcCameraPos = agent.camera.position.clone();
  const srcSettings = { ...xyz2rpt(srcCameraPos.clone().sub(srcTargetPos)) };
  return { srcTargetPos, srcSettings };
};

export const resetSetCameraByMesh = (agent) => {
  agent.setCameraByMesh = function(mesh, options) {
    if (!mesh) {
      return null;
    }
    const { endSettings, endTargetPos, endCameraPos } = getEndSettings(mesh, options);

    const EPSILON = 0.00001;
    if (endCameraPos.distanceTo(this.camera.position) < EPSILON
    && endTargetPos.distanceTo(this.env.lookAt.position) < EPSILON) {
      return null;
    }
    const orbit = this.orbit || this.orbitCtrl;
    if (!options.animate) {
      setCamera(this.camera, orbit, endCameraPos, endTargetPos);
      this.renderOnce();
      return null;
    }

    const { srcTargetPos, srcSettings } = getSrcSettings(this);
    fixToRotateLessPi(srcSettings, endSettings);
    const tempTargetPos = new THREE.Vector3();
    const tempSettings = {
      distance: 1,
      lat: 0,
      lon: 0,
    };
    const animation = new A3D.Animation(this);
    animation.push({
      duration: options.duration || 1000,
      easing: options.easing,
      onStart: options.onStart,
      onUpdate: (e) => {
        lerp(srcSettings, endSettings, tempSettings, e.value);
        let pos = rpt2xyz(tempSettings);
        tempTargetPos.copy(srcTargetPos).lerp(endTargetPos, e.value);
        this.camera.position.copy(pos).add(tempTargetPos);
        this.camera.lookAt(tempTargetPos.x, tempTargetPos.y, tempTargetPos.z);
        if (orbit) {
          orbit.target.copy(tempTargetPos);
        }
        if (options.onUpdate) {
          options.onUpdate(e);
        }
        this.renderOnce();
      },
      onComplete: options.onComplete,
      onStop: options.onStop,
    }).start();
    return animation;
  };
};
