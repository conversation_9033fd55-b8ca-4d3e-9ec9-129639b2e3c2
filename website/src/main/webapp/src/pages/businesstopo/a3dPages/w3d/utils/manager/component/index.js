import InstancedView from '../../../components/common/InstancedView';
import ListView from '../../../components/common/ListView';
import {
  CardManager, images, models, textures,
} from '../..';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { handler } from './handler';

const A3D = getA3D();

class Store {
  constructor() {
    this.data = { removeInfo: [] };
    this.dataMap = {};
  }

  setData(data) {
    Object.assign(this.data, data);
  }

  getData() {
    return this.data;
  }

  removeData(type) {
    this.data[type] = undefined;
    delete this.data[type];
  }

  setDataToMap(id, data, type, index) {
    if (this.dataMap[id] === undefined) {
      this.dataMap[id] = data;
    } else {
      Object.assign(this.dataMap[id], data);
    }
    if (type) {
      this.dataMap[id]._type = type;
    }
    if (!isNaN(index)) {
      this.dataMap[id]._index = index;
    }
  }

  getDataFromMap(id) {
    return this.dataMap[id];
  }

  removeDataInMap(id) {
    this.dataMap[id] = undefined;
    delete this.dataMap[id];
  }
}

export default class ComponentManager {
  constructor(w3d, resource, events) {
    this.comps = {};
    this.compItemMap = {};

    this.w3d = w3d;
    this.store = new Store();
    this.resource = A3D.util.extend(
      false,
      { modelsUrl: {}, imagesUrl: {}, textureUrl: {} },
      resource,
    );
    this.events = A3D.util.extend(false, {}, events);

    this.cardManager = new CardManager(w3d, events, this);

    this.wrapperGroup = A3D.createGroup();
    this.wrapperGroup.name = 'wrapperGroup';
    w3d.agent.add(this.wrapperGroup);
    this.#addThemeSwitchedCallback();
  }

  async loadResource() {
    const promises = [];
    Object.keys(this.resource.modelsUrl).forEach((key) => {
      promises.push(this.#loadRes(models, this.resource.modelsUrl[key], key, 'model'));
    });
    Object.keys(this.resource.imagesUrl).forEach((key) => {
      promises.push(this.#loadRes(images, this.resource.imagesUrl[key], key, 'image'));
    });
    Object.keys(this.resource.textureUrl).forEach((key) => {
      promises.push(this.#loadRes(textures, this.resource.textureUrl[key], key, 'texture'));
    });
    return Promise.all(promises);
  }

  async #loadRes(resources, url, key, type) {
    const w3d = this.w3d;
    return new Promise((resolve) => {
      let apiName;
      if (type === 'model') {
        apiName = 'loadModel';
      }
      if (type === 'image') {
        apiName = 'loadImage';
      }
      if (type === 'texture') {
        apiName = 'loadTexture';
      }
      if (!apiName) {
        resolve();
        return;
      }
      if (resources[key]) {
        resolve();
        return;
      }
      if (typeof url === 'string') {
        w3d[apiName](url).then((res) => {
          resources[key] = res;
          resolve();
        });
      }
      if (typeof url === 'object') {
        resources[key] = {};
        const urlArr = Object.keys(url);
        let finishCount = 0;
        urlArr.forEach((subKey) => {
          w3d[apiName](url[subKey]).then((res) => {
            resources[key][subKey] = res;
            finishCount++;
            if (finishCount === urlArr.length) {
              resolve();
            }
          });
        });
      }
    });
  }

  #dealArrayData(data, type, index, init) {
    const id = data.id;
    if (!id) {
      return;
    }
    if (init) {
      this.store.setDataToMap(id, data, type, index);
    } else {
      if (this.store.dataMap[id] === undefined && !data.needsToRemove) {
        this.store.data[type].push(data);
      }
      this.store.setDataToMap(id, data);
    }
  }

  #updateCompAndDataInMap(comp, data, type, init) {
    if (comp instanceof ListView) {
      // 如果是ListView组件，组件item、数据item通过idMap存起来
      data.forEach((item, index) => {
        const id = item.id;
        if (id) {
          if (init) {
            this.compItemMap[id] = comp.getListItem(index);
          } else {
            if (this.store.dataMap[id] === undefined) {
              this.store.data[type].push(item);
            }
          }
          this.store.setDataToMap(id, item, type);
        }
      });
    } else if (comp instanceof InstancedView && Array.isArray(data)) {
      // 如果是InstancedView组件，数据item通过idMap存起来
      data.forEach((item, index) => {
        this.#dealArrayData(item, type, index, init);
      });
    } else {
      // 如果是单项的组件，组件、数据通过idMap存起来
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          this.#dealArrayData(item, type, index, init);
        });
      } else {
        const id = data.id;
        if (id) {
          if (init) {
            this.compItemMap[id] = comp;
          }
          this.store.setDataToMap(id, data, type);
        }
      }
    }
  }

  #removeCompAndDataInMap(comp, data) {
    if (comp instanceof ListView) {
      data.forEach((item) => {
        const id = item.id;
        if (id) {
          this.compItemMap[id] = undefined;
          delete this.compItemMap[id];
          this.store.removeDataInMap(id);
        }
      });
    } else if (comp instanceof InstancedView && Array.isArray(data)) {
      data.forEach((item) => {
        const id = item.id;
        if (id) {
          this.store.removeDataInMap(id);
        }
      });
    } else {
      const id = data.id;
      if (id) {
        this.compItemMap[id] = undefined;
        delete this.compItemMap[id];
        this.store.removeDataInMap(id);
      }
    }
  }

  #addComp(type, data) {
    const fn = handler.add[type];
    if (fn) {
      let _data = Array.isArray(data) ? data.filter((item) => item.needsToRemove !== true) : data;
      this.store.setData({ [type]: _data });
      // 要求 handler.add 必须返回组件实例
      const comp = fn(_data, this.wrapperGroup, this.w3d, this);

      this.comps[type] = comp;
      this.#updateCompAndDataInMap(comp, _data, type, true);
      this.w3d.agent.renderOnce();
    }
  }

  updateComp(type, data) {
    const comp = this.comps[type];
    if (comp) {
      const fn = handler.update[type];
      if (fn) {
        this.#updateCompAndDataInMap(comp, data, type, false);
        fn(data, this.wrapperGroup, this.w3d, this, this.cardManager);
        this.w3d.agent.renderOnce();
      }
    } else {
      this.#addComp(type, data);
    }
  }

  removeComp(type) {
    const comp = this.comps[type];
    if (comp) {
      const data = this.store.getData()[type];
      const fn = handler.remove[type];
      if (fn) {
        fn(data, this.wrapperGroup, this.w3d, this, this.cardManager);
      }
      if (comp.parent) {
        comp.parent.remove(comp);
      }

      this.store.removeData(type);
      this.comps[type] = undefined;
      delete this.comps[type];
      this.#removeCompAndDataInMap(comp, data);

      this.w3d.agent.renderOnce();
    }
  }

  hideComp(type) {
    const comp = this.comps[type];
    if (comp) {
      comp.visible = !comp.visible;
      this.cardManager.hideAllCard(!comp.visible, type);
      this.w3d.agent.renderOnce();
    }
  }

  clear() {
    Object.keys(this.comps).forEach((type) => {
      this.removeComp(type);
    });
    this.cardManager.clear();
  }

  #addThemeSwitchedCallback() {
    this.w3d.addThemeSwitchedCallback('env', (option) => {
      if (images.env && option.dst === 'default') {
        this.w3d.agent.scene.background = images.env;
      }
    });
  }
}
