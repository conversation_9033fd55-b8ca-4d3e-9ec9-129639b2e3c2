import eventBus from '@pages/businesstopo/a3dPages/bus';
import { getMessage, registerResource } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import {
  getOverviewData, getOverviewUpdateData, getSiteIndicatorData,
  getSiteIndicatorData1,
  getSiteIndicatorData2,
  getSiteIndicatorUpdateData,
  getSiteIndicatorUpdateData1, queryGrayBasicInfo,
} from '@pages/businesstopo/a3dPages/network/request';
import Overview3D from '@pages/businesstopo/a3dPages/w3d/scenes/overview';
import { cancelPolling, createPolling } from '@pages/businesstopo/a3dPages/w3d/utils/polling';
import { eventBusHandler, getRouteQuery, toSiteDrillDownPage } from '../utils';

let businessIdInfo = { south: null, north: null };

registerResource(i18n, 'w3d');
let overview3D;

const getLineData = async function(businessId, getSiteIndicatorDataFn) {
  let lineData = await getSiteIndicatorDataFn(businessId);
  return lineData.data.siteDataList;
};

const dealLineData = function(lineData, businessId) {
  lineData.forEach((item) => {
    item.businessId = businessId;
  });
};

const getInitData = async(getOverviewDataFn, getSiteIndicatorDataFn, getSiteIndicatorDataFn1) => {
  let data = await getOverviewDataFn();
  data = data.data;
  let business = data.businessList;
  let north = [];
  let south = [];
  business.forEach((item) => {
    if (item.businessType === 'north') {
      north.push(item);
    } else {
      south.push(item);
    }
  });
  let northId = businessIdInfo.north || north[0].businessId;
  let southId = businessIdInfo.south || south[0].businessId;
  let northLineData = await getLineData(northId, getSiteIndicatorDataFn);
  dealLineData(northLineData, northId);
  let southLineData = await getLineData(southId, getSiteIndicatorDataFn1);
  dealLineData(southLineData, southId);
  let site = data.siteTeamList.filter((item) => item.siteList.length);
  const i18nMap = {
    northServiceSystem: getMessage('w3d.northServiceSystem'),
    southServiceSystem: getMessage('w3d.southServiceSystem'),
    CBS: getMessage('w3d.CBS'),
  };
  return {
    north, south, site, i18n: i18nMap, line: [...northLineData, ...southLineData],
  };
};

const events = [
  {
    eventName: 'from3d_showPanel',
    // 2D-3D对接，点击事件，联动右侧面板
    fn(setting) {
      // 打印：('showPanel', setting);
    },
  },
  {
    eventName: 'from3d_switchBusiness',
    // 2D-3D对接，点击切换当前business
    async fn(setting) {
      businessIdInfo[setting.type] = setting.currentBusinessId;
      let lineData = await getLineData(setting.currentBusinessId, getSiteIndicatorData2);
      dealLineData(lineData, setting.currentBusinessId);
      lineData.switchBusiness = true;
      overview3D.update({ line: lineData });
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_drillDownSite',
    fn(setting) {
      setTimeout(() => {
        toSiteDrillDownPage(setting.currentSiteId, setting.currentGroupId);
      }, 0);
    },
  },
  {
    // 2D-3D对接，hover业务分组，显示卡片
    eventName: 'from3d_showBusinessGroupTip',
    fn(setting) {
      // 打印：('showBusinessGroupTip', setting)
      const dom = document.createElement('div');
      dom.style.background = '#333333';
      dom.style.width = '100px';
      dom.style.height = '100px';
      setting.dom.append(dom);
    },
  },
  {
    // 2D-3D对接，hover tps连线，显示卡片
    eventName: 'from3d_showTPSCard',
    fn(setting) {
      // 打印：('showTPSCard', setting);
      const dom = document.createElement('div');
      dom.style.background = '#393939';
      dom.style.width = '300px';
      dom.style.height = '200px';
      setting.dom.append(dom);
    },
  },
];

export const initW3D = async(container) => {
  events.forEach((item) => {
    eventBus.addListener(item.eventName, item.fn);
  });

  // 2D-3D对接，初始化更新数据
  const initData = await getInitData(getOverviewData, getSiteIndicatorData, getSiteIndicatorData1);
  const number = 2;
  overview3D = new Overview3D(eventBusHandler);
  const routeQuery = getRouteQuery();
  await overview3D.init(container.current, routeQuery, initData);
  let _index = 0;
  // 2D-3D对接，循环更新数据
  createPolling({
    intervaltime: 1000 * 5,
    onUpdate: async() => {
      let data = [
        [getOverviewData, getSiteIndicatorData, getSiteIndicatorData1],
        [getOverviewUpdateData, getSiteIndicatorUpdateData, getSiteIndicatorUpdateData1],
      ];
      let fn = data[(++_index % number)];
      const updateData = await getInitData(...fn);
      overview3D.update(updateData);
    },
  });

  // 2D-3D对接，灰度升级，站点闪烁
  const grayBasicInfo = await queryGrayBasicInfo();
  eventBus.emit('to3d_updateGrayUpgrade', grayBasicInfo.data.updatingSiteIds);

  // 2D-3D对接，切换选中南北向：eventBus.emit('to3d_updateBusiness', 7);
};

export const destroyW3D = () => {
  events.forEach((item) => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  cancelPolling();

  overview3D?.destroy();
  overview3D = null;
};

export const resizeW3D = () => {
  if (overview3D?.resize) {
    overview3D.resize();
  }
};
