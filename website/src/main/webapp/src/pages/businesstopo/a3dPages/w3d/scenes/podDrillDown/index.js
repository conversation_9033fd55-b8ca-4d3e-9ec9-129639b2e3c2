import {baseUrl} from '@pages/businesstopo/a3dPages/global';
import defaultTheme from '@pages/businesstopo/a3dPages/network/theme/podDrillDown/default';
import {
  ComponentManager, createGui, offEvent, onEvent, W3D,
} from '@pages/businesstopo/a3dPages/w3d/utils';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import HoverHandler from '../commonHandler/HoverHandler';
import SelectHandler from '../commonHandler/SelectHandler';
import {resetTheme} from './handler/theme';
import {initDefault} from '../../utils/manager/component/helper/podDrillDown';

const A3D = getA3D();

const cameraSetting = {
  fov: 30,
  lat: Math.PI * 0.2,
  lon: 0,
  distance: 60,
  minDistance: 10,
  maxDistance: 100,
  near: 0.1,
  far: 500,

  // 相机焦点
  target: {
    x: 0,
    y: 0,
    z: 0,
  },
  animate: false,
  resetSetCameraByMesh: true,
};

const resource = {
  modelsUrl: {
    app: `${baseUrl}w3d/models/app.obj`,
    database: `${baseUrl}w3d/models/database.obj`,
    vm: `${baseUrl}w3d/models/vm.obj`,
    pod: `${baseUrl}w3d/models/pod.obj`,
  },
  textureUrl: {},
};

export default class Home3D {
  resize = null;

  constructor(eventBusHandler) {
    this.eventBusHandler = eventBusHandler;
    this.w3d = null;
    this.componentManager = null;
    this.#initEventHandlers();
    this.#initEvents();
  }

  // 初始化监听事件
  #initEventHandlers() {
    const events1 = this.#getEventsHandlers1();
    this.eventHandlers = [
      {name: 'to3d_zoomIn', callback: () => this.w3d.zoomIn()},
      {name: 'to3d_zoomOut', callback: () => this.w3d.zoomOut()},
      {name: 'to3d_resetCamera', callback: () => this.w3d.resetCamera()},
      {
        name: 'to3d_switchTheme',
        callback: (e) => {
          this.w3d.themeManager.switch(e);
        },
      },
      ...events1,
    ];
  }

  #getEventsHandlers1() {
    return [
      {
        name: 'to3d_updatePodDrillDownData',
        callback: (podData) => {
          const podDrillDown = this.componentManager.comps.podDrillDown || this.componentManager.comps.hostDrillDown;
          if (podDrillDown) {
            podDrillDown.updateStatus(podData);
            initDefault(podDrillDown, this.w3d, this.componentManager);
            this.w3d.agent.renderOnce();
          }
        },
      },
      {
        name: 'to3d_updateSelectedRelation',
        callback: (selectedRelation) => {
          const podDrillDown = this.componentManager.comps.podDrillDown;
          if (podDrillDown) {
            podDrillDown.updateSelected(selectedRelation);
          }
        },
      },
    ];
  }

  // 初始化模型、卡片的点击交互事件
  #initEvents() {
    this.events = {
      selectNull: () => {
        const podDrillDown = this.componentManager.comps.podDrillDown || this.componentManager.comps.hostDrillDown;
        if (podDrillDown) {
          podDrillDown.select(null, null, null, null, () => {
            this.componentManager.events.showPanel({
              type: '',
              id: '',
            });
            this.componentManager.events.selectObject(null);
          });
          this.w3d.agent.renderOnce();
        }
      },
      showPanel: (setting) => {
        this.eventBusHandler.emit('from3d_showPanel', setting);
      },
      showCommonTip: (setting) => {
        this.eventBusHandler.emit('from3d_showCommonTip', setting);
      },
      showPodDrillDownDeital: (setting) => {
        this.eventBusHandler.emit('from3d_showPodDrillDownDetail', setting);
      },
      ...this.#getHoverEvents(),
      drillDownHostType: (setting) => {
        if (this.isPiu) {
          return;
        }
        this.eventBusHandler.emit('from3d_drillDownHostType', setting);
      },
    };
  }

  #getHoverEvents() {
    const events = {
      hoverInObject: (object) => {
        this.hoverHandler.hoverIn(object);
      },
      hoverOutObject: (object) => {
        this.hoverHandler.hoverOut(object);
      },
      selectObject: (object) => {
        this.selectHandler1.select(object);
      },
      selectApp: (object) => {
        this.selectHandler2.select(object);
      },
    };
    return events;
  }

  async init(wrapper, routeQuery, data) {
    if (this.w3d) {
      this.w3d.destroy();
    }
    // 初始化w3d
    this.w3d = this.#initW3d(wrapper, routeQuery, data.podDrillDown?.isAgent || data.hostDrillDown?.isAgent );
    this.w3d.agent.env.raycaster.params.Line2 = {threshold: 0.2};
    // 监听事件
    onEvent(this.eventBusHandler, this.eventHandlers);

    this.selectHandler1 = new SelectHandler(this.w3d.agent);
    this.selectHandler2 = new SelectHandler(this.w3d.agent);
    this.hoverHandler = new HoverHandler(this.w3d.agent, this.selectHandler1, this.selectHandler2);

    // 创建组件管理器
    this.componentManager = new ComponentManager(this.w3d, resource, this.events);
    // 设置卡片告警态。无配置show情况下，卡片告警态显示，非告警态隐藏
    this.componentManager.cardManager.setWarnStatus({status: ['alarm']});
    // 加载资源
    await this.componentManager.loadResource();
    // resize
    this.resize = () => {
      this.w3d.resize();
    };
    // 初始化灯光
    this.#initLights();
    // 初始化材质管理器
    await this.#initTheme(data);
    // 根据静态数据，创建组件
    this.update(data);
    this.resize();
    // 显示GUI
    if (routeQuery.showGui) {
      createGui(this.w3d.gui, this.w3d);
    }

    window.addEventListener('resize', this.resize);
    this.#bindAgentEventListener();
  }

  update(updateData) {
    Object.keys(updateData).forEach((key) => {
      this.componentManager.updateComp(key, updateData[key]);
    });
  }

  destroy() {
    window.removeEventListener('resize', this.resize);
    offEvent(this.eventBusHandler, this.eventHandlers);
    this.w3d.destroy();
  }

  #initW3d(wrapper, routeQuery, isAgent) {
    if (routeQuery.isMM) {
      cameraSetting.distance = 70;
      cameraSetting.target.x = 3;
    }
    if (isAgent) {
      cameraSetting.distance = 65;
      cameraSetting.target.z = 10;
    }
    const w3d = new W3D(
      wrapper, routeQuery,
      {...cameraSetting,
      }, {
        customize: false,
        maxPolarAngle: Math.PI * 0.99,
        enableRotate: false,
        screenSpacePanning: false,
      }, {
        logarithmicDepthBuffer: true,
        // 若不使用outline，关闭后期处理可减少锯齿影响
        enableOutline: false,
        loopRender: false,
      },
    );
    const {MOUSE, TOUCH} = A3D.C;
    w3d.orbit.mouseButtons = {LEFT: MOUSE.PAN, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.ROTATE};
    w3d.orbit.touches = {ONE: TOUCH.PAN, TWO: TOUCH.DOLLY_ROTATE};
    return w3d;
  }

  #initLights() {
    let lightGroup = A3D.createGroup();
    lightGroup.name = 'lightGroup';
    this.w3d.agent.add(lightGroup);
    let dis = 50;
    this.w3d.agent.addLight('ambient', {parent: lightGroup, color: '#fff', intensity: 0.3});
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 1.2,
      x: 0,
      y: 0,
      z: 30,
    });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 1,
      x: 0,
      y: 50,
      z: 30,
      mapSize: 1024,
      near: 0.5,
      far: 500,
      left: -dis / 10,
      right: dis / 10,
      top: dis / 10,
      bottom: -dis / 10,
    });
  }

  async #initTheme(data) {
    if (data.podDrillDown?.isAgent || data.hostDrillDown?.isAgent) {
      delete defaultTheme[':scene'];
    }
    const darkTheme = JSON.parse(JSON.stringify(defaultTheme));

    // 初始化材质管理器
    await resetTheme(defaultTheme, darkTheme, data, this.w3d);
    this.w3d.initThemeManager(defaultTheme, darkTheme);
    this.w3d.themeManager.switch('default');
  }

  #bindAgentEventListener = () => {
    this.w3d.agent.on('click', () => {
      this.events.selectNull();
    });
  };
}
