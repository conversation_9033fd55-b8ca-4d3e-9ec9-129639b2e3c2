import {sorter} from '@digitalview/fe-utils';
import {getMessage} from '@pages/businesstopo/a3dPages/commonUtil/intl';

const processOverviewData = (overviewData) => {
  // 处理数据
  overviewData?.siteTeamList.forEach(siteTeam => {
    siteTeam.siteList.forEach(site => {
      site.groupList = site.groupList.filter(group => {
        // 移除 hasInstance 为 0 的条目
        if (group.hasInstance === 0) {
          return false;
        }
        // 修改 isActiveDv 为 0 的条目
        if (group.isActiveDv === 0) {
          group.alarmCount = 0;
          group.alarmCsn = '';
        }
        return true;
      });
    });
  });

  overviewData?.siteTeamList.forEach(siteTeam => {
    siteTeam.siteList.forEach(site => {
      // 添加前缀'site'
      site.siteName = `${site.siteName}`;
    });
  });
};

function getRelation(overviewData) {
  let relation = [];
  overviewData.siteTeamList.forEach(team => {
    team.siteList.forEach(site => {
      site.groupList.forEach(gorup => {
        relation[gorup.groupId] = site.siteId;
      });
    });
  });
  return relation;
}

const dealLineData = function(lineData, businessId, type) {
  lineData.forEach(item => {
    item.type = type;
    item.businessId = businessId;
  });
};

const sortByIsMain = (a, b) => {
  if (a.isMain && !b.isMain) {
    return -1;
  } else if (!a.isMain && b.isMain) {
    return 1;
  } else {
    return 0;
  }
};

function getRenderData(overviewData) {
  let business = overviewData.businessList;
  let north = [];
  let south = [];
  business.forEach(item => {
    if (item.businessType === 'north') {
      north.push(item);
    } else {
      south.push(item);
    }
  });
  south.sort(sortByIsMain);
  north.sort(sortByIsMain);

  let site = overviewData.siteTeamList.filter(item => item.siteList.length);
  site = site.sort((a, b) => sorter(a.siteTeamId, b.siteTeamId));
  site = site.map(item => {
    return {
      ...item,
      siteList: item.siteList.map(item2 => ({
        ...item2,
        groupList: item2.groupList.sort((a, b) => sorter(a.groupId, b.groupId)),
      })),
    };
  });
  return {north, south, site};
}

// 根据数据进行初始化
export const getInitCBSW3DData = (container, data) => {
  const {
    mainBusiness = {},
    businessDataResult = [],
    solutionData = {},
    overviewData = {},
    northLineData = {},
    southLineData = {},
  } = data;

  // 预处理数据
  processOverviewData(overviewData);
  getRelation(overviewData);

  // 处理连线数据
  dealLineData(northLineData.siteDataList, mainBusiness?.north?.businessId, 'north');
  dealLineData(southLineData.siteDataList, mainBusiness?.south?.businessId, 'south');
  let lineData = [...northLineData.siteDataList, ...southLineData.siteDataList];
  lineData.switchBusiness = true;
  let lineMap = {};
  lineData.forEach(item => {
    lineMap[item.siteId] = item.indicatorValue;
  });

  // 获取总览布局数据
  let {north, south, site} = getRenderData(overviewData);

  return {
    north,
    south,
    site,
    i18n: {
      northServiceSystem: getMessage('w3d.northServiceSystem'),
      southServiceSystem: getMessage('w3d.southServiceSystem'),
      CBS: getMessage('w3d.CBS'),
    },
    line: lineData,
    hasFiltered: overviewData.hasFiltered,
  };
};

