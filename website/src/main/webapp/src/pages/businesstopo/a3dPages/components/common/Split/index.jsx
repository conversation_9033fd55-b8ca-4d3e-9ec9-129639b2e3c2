import './index.less';
import React, { useState } from 'react';
import PropTypes from 'prop-types';

function Split(props) {
  const [show, setShow] = useState(true);
  const {
    children, width, className, setPanelShow,
  } = props;
  const paneRightStyle = { width: show ? width : 0 };
  const onClick = () => {
    setShow(!show);
    setPanelShow(!show);
  };
  return (
    <div className={`Split ${className}`}>
      <div className='PaneLeft'>
        {' '}
        {children && children[0]}
      </div>
      <div className='PaneRight' style={paneRightStyle}>
        <div className={`SplitResize ${show ? 'expansion_wb_cls' : 'expansion_eb_cls'}`}
          onClick={() => {
            onClick();
          }}
        />
        <div className='PaneRightContents'>
          {children && children[1]}
        </div>
      </div>
    </div>
  );
}

Split.propTypes = {
  children: PropTypes.array,
  width: PropTypes.number,
  className: PropTypes.string,
  setPanelShow: PropTypes.func,
};

export default Split;
