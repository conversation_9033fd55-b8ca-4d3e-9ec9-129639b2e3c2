.floatPanel {
    position: absolute;
    bottom: 7rem;
    left: 1rem;
    display: flex;
    color: #BBBBBB;
    align-items: end;
    z-index: 2;

    .floatPanel_card {
        position: relative;
        background: #393939;
        box-shadow: 0 0.25rem .75rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.25rem;
        backdrop-filter: blur(0.25rem);
        width: 4.25rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        align-items: center;
        font-size: .875rem;
    }

    .floatPanel_left {
        height: 11rem;
        padding: 1rem 0;

        .floatPanel_item {
            display: inherit;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            cursor: pointer;

            .icon {
                width: 2.5rem;
                height: 2.5rem;
                margin-top: 0;
                display: inherit;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
            }

            img {
                width: 1.5rem;
                height: 1.5rem;
            }

            span {
                display: inline-block;
            }

            &:hover {
                .icon {
                    background-color: rgba(103,103,103,0.2);
                    border-radius: 0.125rem;
                }
            }

            &.active {
                color: #5CACFF;

                .icon {
                    background: #272727;
                    border: 1px solid #5CACFF;
                    border-radius: 0.25rem;
                }
            }

        }
    }

    .floatPanel_right {
        margin-left: 0.5rem;
        height: 5.5rem;
        padding: 0.5rem;
        width: fit-content;
        min-width: 4.25rem;

        &::after {
            position: absolute;
            top: calc(30% - 0.5rem);
            left: -0.875rem;
            content: "";
            border: 0.5rem solid transparent;
            border-right-color: #393939;
        }

        span {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 2.75rem;
            width: 100%;
            padding: 0 0.5rem;
            border: 1px solid transparent;
            height: 2rem;
            box-sizing: border-box;
            cursor: pointer;

            &:hover {
                background-color: rgba(103,103,103,0.2);
                border-radius: 0.125rem;
            }

            &.active {
                background: #272727;
                border: 1px solid #5CACFF;
                border-radius: 0.25rem;

            }
        }
    }
}