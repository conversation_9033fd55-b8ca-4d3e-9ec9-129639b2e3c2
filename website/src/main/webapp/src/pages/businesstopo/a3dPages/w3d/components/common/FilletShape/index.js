import A3D from '@a3d/a3d';

const FILLET_VEC2_START = new A3D.C.Vector2(0, 0);
const FILLET_VEC2_END = new A3D.C.Vector2(0, 0);

/**
 * 获取大于等于0的数字
 * @param {number} number
 * @returns number
 */
const getNumber = (number) => {
  let result = Number(number);
  result = result > 0 ? result : 0;
  return result;
};

/**
 * 获取第 index 个圆角的最终半径
 * @param {number|array} radius
 * @param {number} index
 * @returns {number}
 */
const getItemRadius = (radius, index) => {
  const radiusItem = Array.isArray(radius) ? radius[index] : radius;
  return getNumber(radiusItem);
};

/**
 * 计算第 index 个圆角的起点、终点，记录在全局变量中
 * @param {number} len
 * @param {number} index
 * @param {array} points
 * @param {number|array} radius 半径
 */
const setFilletStartAndEnd = (len, index, points, radius) => {
  const prev = (index - 1 + len) % len;
  const next = (index + 1) % len;
  FILLET_VEC2_START.copy(points[prev]).sub(points[index]);
  FILLET_VEC2_END.copy(points[next]).sub(points[index]);
  const prevLen = FILLET_VEC2_START.length();
  const nextLen = FILLET_VEC2_END.length();
  const cornerRadius = Math.min(getItemRadius(radius, index), prevLen / 2, nextLen / 2);
  FILLET_VEC2_START.normalize().multiplyScalar(cornerRadius).add(points[index]);
  FILLET_VEC2_END.normalize().multiplyScalar(cornerRadius).add(points[index]);
};

/**
 * @config {array} [points] - 形状的点集
 * @config {number|array} [radius] - 拐点圆角半径
 */
export default class FilletShape extends A3D.C.Shape {
  constructor(points = [], radius = 0) {
    super();
    this.name = 'FilletShape';

    this.#set(points, radius);
  }

  /**
   * 给 shape 设置带圆角的路径
   * @param {array} points
   * @param {number|array} radius
   */
  #set(points, radius) {
    const len = points.length;
    for (let i = 0; i < len; i++) {
      setFilletStartAndEnd(len, i, points, radius);
      if (i === 0) {
        this.moveTo(FILLET_VEC2_START.x, FILLET_VEC2_START.y);
      } else {
        this.lineTo(FILLET_VEC2_START.x, FILLET_VEC2_START.y);
      }
      if (!FILLET_VEC2_START.equals(FILLET_VEC2_END)) {
        this.quadraticCurveTo(points[i].x, points[i].y, FILLET_VEC2_END.x, FILLET_VEC2_END.y);
      }
    }
    setFilletStartAndEnd(len, 0, points, radius);
    this.lineTo(FILLET_VEC2_START.x, FILLET_VEC2_START.y);
  }
}
