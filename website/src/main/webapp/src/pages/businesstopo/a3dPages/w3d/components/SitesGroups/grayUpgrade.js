export const grayUpgradeVert = `
#include <common>
#include <logdepthbuf_pars_vertex>
varying vec3 vpos;
void main() {
  vpos = position;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  #include <logdepthbuf_vertex>
}
`;

export const grayUpgradeFrag = `
uniform vec3 uColor;
varying vec3 vpos;
uniform float uTime;
uniform float uWidth;
uniform float uDepth;
#include <logdepthbuf_pars_fragment>

float getRatio(float pos, float width) {
  float ratio = 0.85;
  float percent = abs(pos / (width / 2.0));
  if (percent < ratio) {
    return 0.0;
  } else {
    return (percent - ratio) * (1.0 / (1.0 - ratio));
  }
}
void main() {
  #include <logdepthbuf_fragment>
  float ratio1 = getRatio(vpos.x, uWidth);
  float ratio2 = getRatio(vpos.z, uDepth);
  float ratio = max(ratio1, ratio2);
  float opacity = (cos(uTime) + 1.0) * 0.3;
  gl_FragColor = vec4(uColor, opacity * ratio);
}
`;
