.control3DPanel {
    position: absolute;
    right: 1rem;
    bottom: 1rem;

    .control3DPanel_btnGroup {
        background: #393939;
        box-shadow: 0 4px 12px 0 rgba(0,0,0,0.15);
        border-radius: 0.25rem;
        padding: 0.5rem;
        &:not(:first-child) {
            margin-top: 0.5rem;
        }

        .w3d-icon {
            font-size: 0;
            position: relative;
            cursor: pointer;

            &:not(:first-child) {
                margin-top: 0.5rem;
            }

            img {
                width: 1.5rem;
                height: 1.5rem;
            }

            &:hover {
                background-color: rgba(103,103,103,0.2);
                border-radius: 0.125rem;

                &:after {
                    position: absolute;
                    top: -0.25rem;
                    right: 2.5rem;
                    min-width: 3.25rem;
                    width: max-content;
                    padding: 0 0.5rem;
                    height: 2rem;
                    content: attr(data-title);

                    border-radius: 0.25rem;
                    background-color: #393939;
                    box-shadow : 0 4px 12px 0 rgba(0,0,0,0.15);
                    font-size: 0.875rem;
                    color: #FFFFFF;
                    text-align: center;
                    line-height: 2rem;
                }

                &:before {
                    position: absolute;
                    content: '';
                    top: 0.5rem;
                    right: 2.25rem;
                    border-left: 0.26rem solid #393939;
                    border-top: 0.25rem solid transparent;
                    border-bottom: 0.25rem solid transparent;
                    border-right: 0;
                }
            }
        }
    }
}