import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { getLineOptions, getWallLine } from './site';

const A3D = getA3D();

const geoRatio = 0.6;
const geoOffset = 0.4;
const alarmGeo = new A3D.C.PlaneGeometry(geoRatio, geoRatio);
alarmGeo.rotateX(-Math.PI / 2);

export const roleList = ['A', 'S', 'R', 'Ra', 'Rs'];
const ASList = ['A', 'S'];
const RList = ['R', 'Ra', 'Rs'];
export const statusList = ['normal', 'alarm'];

const getAlarmCard = (mat) => {
  const card = new A3D.C.Mesh(alarmGeo, mat);
  card.position.set(geoOffset, geoOffset, -geoOffset);
  card.renderOrder = 5;
  return card;
};

export const isAS = (designRole, currentRole) => {
  const isAS1 = designRole === 'A' && currentRole === 'S';
  const isAS2 = designRole === 'S' && currentRole === 'A';
  const isAS3 = designRole === 'Ra' && currentRole === 'Rs';
  const isAS4 = designRole === 'Rs' && currentRole === 'Ra';
  return isAS1 || isAS2 || isAS3 || isAS4;
};
export const isAR = (designRole, currentRole) => {
  const isAR1 = ASList.includes(designRole) && RList.includes(currentRole);
  const isAR2 = RList.includes(designRole) && ASList.includes(currentRole);
  return isAR1 || isAR2;
};

export const setStatus = (podItem, materialMap) => {
  const designRole = podItem.data.designRole;
  const currentRole = podItem.data.currentRole;
  const status = podItem.data.status;
  const group = podItem.mesh;
  const mesh = group.children[0];
  group.remove(group.children[1]);
  if (roleList.includes(currentRole)) {
    mesh.material = materialMap.pod[currentRole][status] || materialMap.pod[currentRole].normal;
    if (isAS(designRole, currentRole)) {
      group.add(getAlarmCard(materialMap.podWarn.asSwitchover));
    } else if (isAR(designRole, currentRole)) {
      group.add(getAlarmCard(materialMap.podWarn.arSwitchover));
    }
  } else {
    mesh.material = materialMap.pod.default[status] || materialMap.pod.default.normal;
  }
};

export const initPodLine = (podGroupingId, posArr, posIndex, mat, wrapper) => {
  const lineOptions = getLineOptions([posArr[posIndex], posArr[posIndex + 1]]);
  const line = getWallLine(mat, lineOptions);
  line.children[0].renderOrder = 2;
  line.name = podGroupingId;
  wrapper.add(line);
};
