import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

const scale = 1.2;
const linewidth = 2;

const defaultStartScale = new A3D.C.Vector3(1, 1, 1);
const duration = 500;

const defaultStartWidth = 1;
let startHoverInLine = 0;
let endHoverInLine = 0;
let startHoverOutLine = 0;
let endHoverOutLine = 0;

// hoverIn 永远只有一个，hoverOut可能有多个，所以hoverIn只负责增加动画；
// hoverOut 之前必须有hoverIn，所以hoverOut时停掉上一次hoverIn
// hoverOut 可以多个执行，这里当发现重复 in-out 时，会直接上上一次 hoverOut 回到初始状态，再执行当前一次 hoverOut
export default class HoverHandler {
  #startHoverInVec = new A3D.C.Vector3();
  #endHoverInVec = new A3D.C.Vector3();
  #startHoverOutVec = new A3D.C.Vector3();
  #endHoverOutVec = new A3D.C.Vector3();
  constructor(agent, ...rest) {
    this.hoverInObject = null;
    this.hoverOutObject = null;
    this.hoverInLineObj = null;
    this.hoverOutLineObj = null;

    this.selectHandlerArr = rest;

    this.hoverInAnimation = new A3D.Animation(agent);
    this.hoverOutAnimation = new A3D.Animation(agent);
  }

  hoverIn(object) {
    if (this.selectHandlerArr.some((selectHandler) => selectHandler.selectObject === this.hoverInObject)) {
      this.hoverInObject = null;
    }
    if (this.selectHandlerArr.some((selectHandler) => selectHandler.selectObject === this.hoverOutObject)) {
      this.hoverOutObject = null;
    }
    if (this.selectHandlerArr.some((selectHandler) => selectHandler.selectObject === object)) {
      return;
    }
    if (object) {
      if (this.hoverOutObject === object) {
        this.hoverOutAnimation.stop();
      }
      if (this.hoverInObject && this.hoverInObject !== object) {
        this.hoverOut(this.hoverInObject);
      }
      this.hoverInObject = object;
      if (!object.userData.startScale) {
        object.userData.startScale = object.scale.clone();
      }
      this.#startHoverInVec.copy(object.scale);
      this.#endHoverInVec.copy(object.userData.startScale).multiplyScalar(scale);
      this.hoverInAnimation.push({
        duration,
        onUpdate: (e) => {
          object.scale.lerpVectors(this.#startHoverInVec, this.#endHoverInVec, e.value);
        },
      }).start();
    } else {
      if (this.hoverInObject) {
        this.hoverOut(this.hoverInObject);
      }
    }
  }

  hoverOut(object) {
    this.#stopHoverIn(object);
    this.#stopHoverOut(object);
    this.#startHoverOut(object);
  }

  #stopHoverIn(object) {
    if (this.hoverInObject) {
      this.hoverInAnimation.stop();
      if (this.hoverInObject !== object) {
        const startScale = this.hoverInObject.userData.startScale || defaultStartScale;
        this.hoverInObject.scale.copy(startScale).multiplyScalar(scale);
      }
      this.hoverInObject = null;
    }
  }

  #stopHoverOut(object) {
    if (this.hoverOutObject) {
      this.hoverOutAnimation.stop();
      if (this.hoverOutObject !== object) {
        const startScale = this.hoverOutObject.userData.startScale || defaultStartScale;
        this.hoverOutObject.scale.copy(startScale);
      }
      this.hoverOutObject = null;
    }
  }

  #startHoverOut(object) {
    if (object) {
      this.hoverOutObject = object;
      this.#startHoverOutVec.copy(object.scale);
      this.#endHoverOutVec.copy(object.userData.startScale || defaultStartScale);
      if (this.#startHoverOutVec.equals(this.#endHoverOutVec)) {
        this.hoverOutObject = null;
        return;
      }
      this.hoverOutAnimation.push({
        duration,
        onUpdate: (e) => {
          object.scale.lerpVectors(this.#startHoverOutVec, this.#endHoverOutVec, e.value);
        },
        onComplete: () => {
          this.hoverOutObject = null;
        },
      }).start();
    }
  }

  hoverInLine(line) {
    if (line) {
      if (this.hoverOutLineObj === line) {
        this.hoverOutAnimation.stop();
      }
      this.hoverInLineObj = line;
      if (!line.userData.startWidth) {
        line.userData.startWidth = line.material.linewidth;
      }
      startHoverInLine = line.material.linewidth;
      endHoverInLine = line.userData.startWidth * linewidth;
      this.hoverInAnimation.push({
        duration,
        onUpdate: (e) => {
          line.material.linewidth = startHoverInLine + (endHoverInLine - startHoverInLine) * e.value;
        },
      }).start();
    }
  }

  hoverOutLine(line) {
    this.#stopHoverInLine();
    this.#stopHoverOutLine(line);
    this.#startHoverOutLine(line);
  }

  #stopHoverInLine() {
    if (this.hoverInLineObj) {
      this.hoverInAnimation.stop();
    }
  }

  #stopHoverOutLine(line) {
    if (this.hoverOutLineObj) {
      this.hoverOutAnimation.stop();
      if (this.hoverOutLineObj !== line) {
        const startWidth = this.hoverOutLineObj.userData.startWidth || defaultStartWidth;
        this.hoverOutLineObj.material.linewidth = startWidth;
      }

      delete this.hoverOutLineObj;
    }
  }

  #startHoverOutLine(line) {
    if (line) {
      this.hoverOutLineObj = line;
      startHoverOutLine = line.material.linewidth;
      endHoverOutLine = line.userData.startWidth || defaultStartWidth;
      this.hoverOutAnimation.push({
        duration,
        onUpdate: (e) => {
          line.material.linewidth = startHoverOutLine + (endHoverOutLine - startHoverOutLine) * e.value;
        },
        onComplete: () => {
          delete this.hoverOutLineObj;
        },
      }).start();
    }
  }
}
