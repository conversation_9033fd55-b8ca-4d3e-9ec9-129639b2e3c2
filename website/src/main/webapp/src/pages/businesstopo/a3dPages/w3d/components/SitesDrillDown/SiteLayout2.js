export default class SiteLayout {
  static initLayout(layoutData, size, nameSize, space, position) {
    const podDepth = this.#getPodDepth(layoutData, size, nameSize, space);
    const podWidth = this.#getPodWidth(layoutData, size, space);
    const podSize = { podWidth, podDepth };
    this.#setSize(layoutData, size, nameSize, space, podSize);
    this.#setPosition(layoutData, nameSize, space, size);
    layoutData.podWidth = podWidth;
    layoutData.podDepth = podDepth;
    layoutData.position = position;
  }

  static #getPodDepth(layoutData, size, nameSize, space) {
    let podDepth = Infinity;
    layoutData.level1[0].group.forEach((groupItem) => {
      const depth = (groupItem.data.zLen || 1) - (nameSize + space * 3);
      podDepth = Math.max(Math.min(podDepth, depth / groupItem.row), 0.1);
    });
    return podDepth;
  }

  static #getPodWidth(layoutData, size, space) {
    let podWidth = Infinity;
    layoutData.level1[0].group.forEach((groupItem) => {
      const width = (groupItem.data.xLen || 1) - space * 2;
      podWidth = Math.max(Math.min(podWidth, width / groupItem.col), 0.1);
    });
    return podWidth;
  }

  static #setSize(layoutData, size) {
    layoutData.size = size;
    layoutData.level1.forEach((businessGroup) => {
      businessGroup.group.forEach((business) => {
        business.size = { width: business.data.xLen, depth: business.data.zLen };
      });
    });
  }

  static #setPosition(layoutData, nameSize, space, size) {
    layoutData.level1.forEach((businessGroup) => {
      businessGroup.group.forEach((business) => {
        business.position = {
          x: business.data.xPos - size.width / 2,
          y: 0,
          z: business.data.zPos - size.depth / 2,
        };
      });
    });
  }
}
