import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {
  ATTRS, getParameters, INSTANCEDMESH_ATTRS, LAYOUT_MODES,
} from '../const';
import {
  arrayLayout,
  getInitBoundingBox, getLayoutData, getMaterial, getObjectSize, getRotationFromDirection,
  getStartLayout,
  onChange,
  runAnimation,
  toFixed,
} from '../utils';

const A3D = getA3D();

const whiteColor = new A3D.C.Color('#fff');
const _euler = new A3D.C.Euler();

export default class InstancedView extends A3D.C.Group {
  #geoSize = [];
  #needsUpdate;
  #color;
  #mesh;
  constructor(geometry, material, props) {
    super();
    if (props) {
      this.parameters = getParameters(props, { [LAYOUT_MODES.arrayMode]: this.#arrayLayout });
      this.#needsUpdate = true;
      this.count = 0;
      this.boundingBox = getInitBoundingBox();
      let instancedMesh = new A3D.Geo.InstancedMesh(geometry, getMaterial(material));
      this.add(instancedMesh);
      this.#color = material.color;
      material.color = whiteColor;
      this.#mesh = A3D.mesh(geometry);
    }
  }

  copy(source) {
    super.copy(source, true);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    this.#needsUpdate = true;
    this.count = source.count;
    this.boundingBox = getInitBoundingBox();
    return this;
  }

  add(data = {}, ...rest) {
    this.#needsUpdate = true;
    if (data.isObject3D) {
      super.add(data, ...rest);
      return this;
    }
    let instancedMesh = this.children[0];
    rest.unshift(data);
    for (let i = 0; i < rest.length; i++) {
      let setting = this.#getAttrs(rest[i], true);
      instancedMesh.add(setting);
      this.parameters.onChangeCallback(this.count, rest[i]);
      this.count = instancedMesh.count;
    }
    this.#onChange();
    return this;
  }

  remove(index, ...rest) {
    if (index.isObject3D) {
      super.remove(index, ...rest);
      return this;
    }
    let instancedMesh = this.children[0];
    rest.unshift(index);
    rest.sort((a, b) => b - a);
    rest.forEach((item) => {
      instancedMesh.remove(item);
      this.#geoSize.splice(item, 1);
    });
    this.count = instancedMesh.count;
    this.#onChange();
    return this;
  }

  set(fn, start = 0, count = 0) {
    const N = count === undefined ? this.count : Math.min(this.count || 0, count);
    for (let i = start; i < N; i++) {
      const ret = fn ? fn(i) : undefined;
      this.setIndex(i, ret);
    }
  }

  setIndex(index, data) {
    let setting = this.#getAttrs(data);
    this.children[0].setIndex(index, setting);
    this.parameters.onChangeCallback(index, data);
    return this;
  }

  getIndex(index, output) {
    return this.children[0].getIndex(index, output);
  }

  get(start = 0, count = 0) {
    const N = count === undefined ? this.count : Math.min(this.count || 0, count);
    const res = [];
    for (let i = start; i < N; i++) {
      res.push(this.getIndex(i));
    }
    return res;
  }

  select(index) {
    this.children[0].select(index);
  }

  query(name) {
    return this.children[0].query(name);
  }

  /**
   * 更新
   */
  update() {
    this.#onChange();
  }

  getBoundingBox() {
    let minx = Infinity;
    let miny = Infinity;
    let minz = Infinity;
    let maxx = -Infinity;
    let maxy = -Infinity;
    let maxz = -Infinity;
    let output = { position: {} };
    let position;
    let instancedMesh = this.children[0];
    for (let i = 0, len = instancedMesh.count; i < len; i++) {
      instancedMesh.getIndex(i, output);
      position = output.position;
      minx = Math.min(minx, position.x);
      maxx = Math.max(maxx, position.x);
      miny = Math.min(miny, position.y);
      maxy = Math.max(maxy, position.y);
      minz = Math.min(minz, position.z);
      maxz = Math.max(maxz, position.z);
    }
    let boundingBox = this.boundingBox;
    boundingBox.min.x = toFixed(minx);
    boundingBox.min.y = toFixed(miny);
    boundingBox.min.z = toFixed(minz);
    boundingBox.max.x = toFixed(maxx);
    boundingBox.max.y = toFixed(maxy);
    boundingBox.max.z = toFixed(maxz);
    let size = this.#getGeoSize()[0];
    boundingBox.width = toFixed(maxx - minx + size.width, 2);
    boundingBox.height = toFixed(maxy - miny + size.height, 2);
    boundingBox.depth = toFixed(maxz - minz + size.depth, 2);
    this.#needsUpdate = false;
    return this.boundingBox;
  }

  /**
   * 是否开启动画
   * @param {Boolean} enabled
   */
  enableAnimation(enabled) {
    this.parameters.animate = enabled;
  }

  #bind(event, handler) {
    A3D.DOM.bind(this.children[0], event, (e, info) => {
      if (handler) {
        return handler(info.instanceId, e, info);
      }
      return false;
    });
  }

  #onChange() {
    this.#needsUpdate = true;
    onChange(this, () => this.#runAnimation(), (data, percent) => this.#onLayout(data, percent));
  }

  #unbind(event, handler) {
    A3D.DOM.unbind(this.children[0], event, handler);
  }

  #getAnimationState() {
    let parameters = this.parameters;
    let endLayout = parameters.layoutGroup[parameters.layoutMode](this.count, this);
    if (Array.isArray(endLayout[0])) {
      return endLayout;
    }
    let startLayout = getStartLayout(endLayout, this.count, this.children[0], INSTANCEDMESH_ATTRS.flat(2));
    return [startLayout, endLayout];
  }

  #onLayout(states, percent) {
    let layoutData = getLayoutData(states, percent, this);
    if (layoutData) {
      this.set((i) => layoutData[i], 0);
    }
  }

  #arrayLayout(length, self) {
    let geoSize = self.#getGeoSize();
    return arrayLayout(length, self.parameters, geoSize);
  }

  /**
   * 当布局发生变化时自动计算布局并触发动画
   */
  #runAnimation() {
    let states = this.#getAnimationState();
    runAnimation(this, this.parameters.animationOption, states, (percent) => this.#onLayout(states, percent));
  }

  #getAttrs(data, useDefaultValue) {
    let setting = { color: this.#color, ...data };
    let rotation = getRotationFromDirection(data);
    if (rotation) {
      setting.rotation = rotation;
    }
    let attr;
    INSTANCEDMESH_ATTRS[0].forEach((key) => {
      attr = ATTRS[key];
      let value = setting[key];
      if (value) {
        if (key === 'color') {
          setting[key] = new A3D.C.Color(value);
        } else {
          setting[key] = attr.value.set(value.x, value.y, value.z);
        }
      } else if (useDefaultValue) {
        setting[key] = attr.defaultValue;
      }
    });
    INSTANCEDMESH_ATTRS[1].forEach((key) => {
      attr = ATTRS[key];
      let value = setting[key];
      if (value !== undefined) {
        setting[attr.newKey || key] = value;
      } else if (useDefaultValue) {
        setting[attr.newKey || key] = attr.defaultValue;
      }
    });
    return setting;
  }

  /**
   * 计算每个 item 的包围盒
   */
  #getGeoSize() {
    if (!this.#geoSize) {
      this.#geoSize = [];
    }
    let output = { rotation: _euler, scale: { x: 0, y: 0, z: 0 } };
    for (let i = 0; i < this.count; i++) {
      this.getIndex(i, output);
      this.#mesh.rotation.set(output.rotation.x, output.rotation.y, output.rotation.z);
      this.#mesh.scale.set(output.scale.x, output.scale.y, output.scale.z);
      this.#geoSize[i] = getObjectSize(this.#mesh);
    }
    return this.#geoSize;
  }
}
