import Disc from '@pages/businesstopo/a3dPages/w3d/components/Disc';
import {models, textures} from '../../../global';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export const getBusinessModelType = function(iconType) {
  if (iconType === 1) {
    return 'smpp';
  }
  if (iconType === 2) {
    return 'universal';
  }
  if (iconType === 3) {
    return '4G';
  }
  return '5G';
};

export const setBusinessOpacity = function(item, start, end, alpha) {
  let nameText = item.getObjectByName('nameText');
  if (isNaN(start)) {
    nameText.material.opacity = end;
    item.material.opacity = end;
    return;
  }
  nameText.material.opacity = start + (end - start) * alpha;
  item.material.opacity = start + (end - start) * alpha;
};

export const updateBusinessCard = function(data, index, disc, model, matMap) {
  if (model.children.length) {
    return;
  }
  let id = data[index].businessId;
  let material = matMap[id];
  const sprite = new A3D.C.Sprite(material);
  const canvas = material.map.source.data;
  sprite.scale.set(canvas.width / canvas.height * 0.5, 0.5);
  sprite.renderOrder = 6;
  sprite.position.y -= 0.85;
  sprite.name = 'nameText';
  if (index === disc.selectIndex) {
    material.color.set('#2E94FF');
  } else {
    material.color.set('#bbbbbb');
  }
  model.add(sprite);
};

export const bindBusinessEvents = function(disc, componentManager, w3d) {
  A3D.DOM.bind(disc, 'click', (e, info) => {
    if (disc.parameters.animationOption.animation.isPlaying()) {
      return true;
    }
    componentManager.store.data.selectBusiness = disc.name;
    componentManager.store.data.canUpdateLine = false;
    disc.select(info.object, (item) => {
      let business = componentManager.store.data[`${disc.name}Business`];
      business && (business.businessId = item.name || -1);
      componentManager.store.data.selectBusiness = disc.name;
      componentManager.store.data.canUpdateLine = false;
    }, (item, index, percent, selectIndex) => {
      if (index === disc.selectIndex) {
        setBusinessOpacity(item, 1, 0.5, percent);
      } else if (index === selectIndex) {
        setBusinessOpacity(item, 0.5, 1, percent);
      } else {
        setBusinessOpacity(item, undefined, 0.5, percent);
      }
    });
    return true;
  });
  A3D.DOM.bind(disc, 'hoverIn', (e, info) => {
    disc.hoverIn(info.object, (item) => {
      componentManager.events.hoverInObject(item);
      createHoverCard(item, w3d, componentManager);
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(disc, 'hoverOut', (e, info) => {
    disc.hoverIn(info.object, (item) => {
      componentManager.events.hoverOutObject(item);
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.cardManager.removeCard(
        {id: item.userData.data.businessId},
        'businessDetail',
      );
    });
    w3d.agent.renderOnce();
    return true;
  });
};

const toggleLine = function(data, index, line, show) {
  if (!line) {
    return;
  }
  let type = data[index].businessType;
  let lines = line.children.filter((item) => item.name.includes(type));
  if (show) {
    lines.forEach((item) => {
      line.show(item);
    });
  } else {
    lines.forEach((item) => {
      line.remove(item);
    });
  }
};

export const addBusiness = function(data, w3d, componentManager, matMap, level) {
  let halo = componentManager.comps.halo;
  let material;
  if (w3d.isMM) {
    material = new A3D.C.MeshPhysicalMaterial({
      color: 0x303030, // 基本颜色
      transparent: true, // 启用透明度
      opacity: 0.8,
      transmission: 0.1,
      onBeforeCompile: (shader) => {
        const vertexShader = shader.vertexShader.slice(0, -2);
        const fragmentShader = shader.fragmentShader.slice(0, -2);
        // 修改片元着色器
        shader.vertexShader = `varying vec3 vCenter; 
        ${vertexShader}
        #ifdef USE_TRANSMISSION
  vWorldPosition = worldPosition.xyz;
#endif
  vCenter = modelMatrix[3].xyz;}`;
        shader.fragmentShader = ` varying vec3 vCenter;
        ${fragmentShader} 
        float gradient = distance(vWorldPosition.z, vCenter.z) / 30.0;
  if (vWorldPosition.z - vCenter.z > 0.0) {
    gl_FragColor.a *= gradient;
  } else {
    gl_FragColor.a *= 0.001;
  }
}`;
      },
    });
  } else {
    material = w3d.themeManager.referStyle('ring');
  }
  let disc = new Disc({
    radius: 12,
    startRadian: -Math.PI / 2,
    scale: {x: 1.5, y: 1, z: 1},
    texture: matMap,
    material,
    order: level === 'top' ? 3 : 2,
    onChangeCallback: (model, index) => {
      updateBusinessCard(data, index, disc, model, matMap);
    },
    onSelectCallback: (model, index) => {
      componentManager.cardManager.removeCard(
        {id: model.name || -1},
        'businessDetail',
      );
      let prevSelect = halo.getSelectObj();
      if (model === prevSelect) {
        return;
      }
      halo.addSelectHalo(
        model.parent !== prevSelect?.parent,
        textures.select.halo, textures.select.select, model, -0.6,
      );
      updateBusinessCard(data, index, disc, model, matMap);
      let line = componentManager.comps.line;
      toggleLine(data, index, line, true);
      componentManager.store.data.canUpdateLine = true;
      if (!disc.parameters.animate) {
        return;
      }
      componentManager.events.switchBusiness({currentBusinessId: model.name || -1, type: disc.name, currentBusinessName: model.userData?.data?.businessName || ''});
    },
    onUnselectCallback: (model, index) => {
      halo.hideSelectHalo();
      let id = data[index].businessId;
      material = matMap[id];
      material.color.set('#bbbbbb');
      let line = componentManager.comps.line;
      toggleLine(data, index, line, false);
    },
    animationOption: {animation: new A3D.Animation(w3d.agent)},
    w3d,
  });
  let modelType;
  data.forEach((item) => {
    modelType = getBusinessModelType(item.iconType);
    let model = models[modelType].children[0].clone();
    model.name = item.businessId;
    if (item.alarmCsn && item.alarmCsn.split(',').length > 0) {
      model.material = w3d.themeManager.cloneStyle(`${modelType}-alarm`);
    } else {
      model.material = w3d.themeManager.cloneStyle(`${modelType}-normal`);
    }
    disc.add({...item, model});
  });
  disc.children[0].scale.x = 1.6;
  return disc;
};

export const getPosititons = function(componentManager) {
  let north = componentManager.comps.north.getSelectItem();
  let northPos = new A3D.C.Vector3();
  north.getWorldPosition(northPos);
  let south = componentManager.comps.south.getSelectItem();
  let southPos = new A3D.C.Vector3();
  south.getWorldPosition(southPos);
  let site = componentManager.comps.site;
  let sitePositions = site.getSitePosition();
  return {northPos, sitePositions, southPos};
};


const createHoverCard = (data, w3d, componentManager) => {
  let dataItem = data.userData.data;
  const id = dataItem.businessId;
  const type = 'businessDetail';
  componentManager.cardManager.updateCard(
    type,
    {id},
    data,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showBusinessTip({
    data: dataItem,
    dom: domContainer,
  });
};
