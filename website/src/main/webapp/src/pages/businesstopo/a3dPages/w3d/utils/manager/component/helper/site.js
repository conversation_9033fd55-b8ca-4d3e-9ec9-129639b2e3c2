import { models } from '../../..';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { bindDblclickEvents } from './utils';

const A3D = getA3D();

export const getSiteSetting = (data, w3d) => {
  const modelMap = {
    sites: {},
    site: { group: models.group.children[0].clone() },
  };
  const matMap = {
    sites: {
      ground: w3d.themeManager.referStyle('sites-ground'),
      line: w3d.themeManager.referStyle('sites-ground-line'),
    },
    site: {
      ground: {
        normal: w3d.themeManager.referStyle('site-ground'),
        hover: w3d.themeManager.referStyle('site-ground-hover'),
        select: w3d.themeManager.referStyle('site-ground-select'),
      },
      group: {
        normal: w3d.themeManager.referStyle('site-group-normal'),
        alarm: w3d.themeManager.referStyle('site-group-alarm'),
        gray: w3d.themeManager.referStyle('site-group-gray'),
      },
      groupName: {},
    },
  };
  data.forEach((sites) => {
    sites.siteList.forEach((site) => {
      site.groupList.forEach((drGroup) => {
        const groupName = drGroup.groupName;
        if (!matMap.site.groupName[groupName]) {
          matMap.site.groupName[groupName] = w3d.themeManager.referStyle(groupName);
          matMap.site.groupName[`${groupName}_full`] = w3d.themeManager.referStyle(`${groupName}_full`);
        }
      });
    });
  });
  return { modelMap, matMap };
};

const createHoverCard = (dataItem, groupItem, w3d, componentManager) => {
  const id = `${dataItem.siteId}_${dataItem.groupName}`;
  const type = 'groupDetail';
  componentManager.cardManager.updateCard(
    type,
    { id },
    groupItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showBusinessGroupTip({
    data: dataItem,
    dom: domContainer,
  });
};

const createSitePanelHoverCard = (dataItem, groupItem, w3d, componentManager) => {
  const id = `${dataItem.siteId}`;
  const type = 'groupDetail';
  componentManager.cardManager.updateCard(
    type,
    { id },
    groupItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showSiteNameCard({
    data: dataItem,
    dom: domContainer,
  });
};

const bindHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(info.object, (dataItem, groupItem) => {
      componentManager.events.hoverInObject(groupItem);
      createHoverCard(dataItem, groupItem, w3d, componentManager);
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    }, (dataItem, siteItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    }, (dataItem, sitePanelItem)=>{
      if (sitePanelItem.material.map.userData.drawName !== sitePanelItem.material.map.userData.detailName) {
        createSitePanelHoverCard(dataItem, sitePanelItem, w3d, componentManager);
      }
    },
    );
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(info.object, (dataItem, groupItem) => {
      componentManager.events.hoverOutObject(groupItem);
      componentManager.cardManager.removeCard(
        { id: `${dataItem.siteId}_${dataItem.groupName}` },
        'groupDetail',
      );
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
    }, (dataItem, siteItem) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
    }, (dataItem, sitePanelItem)=>{
      if (sitePanelItem.material.map.userData.drawName !== sitePanelItem.material.map.userData.detailName) {
        componentManager.cardManager.removeCard(
          { id: `${dataItem.siteId}` },
          'groupDetail',
        );
      }
    });
    w3d.agent.renderOnce();
    return true;
  });
};

const bindClickEvents = (compIns, w3d, componentManager) => {
  bindDblclickEvents(compIns, (e, info) => {
    compIns.select(info.object, null, (dataItem, siteItem) => {
      componentManager.events.showPanel({
        currentSiteName: dataItem?.siteName || '',
        currentSiteId: dataItem?.siteId || -1,
      });
    });
    w3d.agent.renderOnce();
  }, (e, info) => {
    compIns.drillDown(info.object, (dataItem, groupItem) => {
      componentManager.events.drillDownSite({
        currentSiteName: dataItem.siteName,
        currentSiteId: dataItem.siteId,
        currentGroupName: dataItem.groupName,
        currentGroupId: dataItem.groupId,
      });
    }, (dataItem, siteItem) => {
      componentManager.events.drillDownSite({
        currentSiteName: dataItem.siteName,
        currentSiteId: dataItem.siteId,
      });
    });
  });
};

export const bindSiteGroupsEvents = (compIns, w3d, componentManager) => {
  bindHoverEvents(compIns, w3d, componentManager);
  bindClickEvents(compIns, w3d, componentManager);
};
