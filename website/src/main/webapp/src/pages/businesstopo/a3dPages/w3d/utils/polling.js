let pollingData = {
  animationId: 0,
  startTime: new Date().getTime(),
  endTime: new Date().getTime(),
  intervalTime: 5 * 1000,
};

const dynamicUpdate = function() {
  pollingData.startTime = new Date().getTime();
  if ((pollingData.startTime - pollingData.endTime) > pollingData.intervalTime) {
    pollingData.endTime = pollingData.startTime;
    pollingData.onUpdate();
  }
  pollingData.animationId = requestAnimationFrame(dynamicUpdate);
};

export const createPolling = function({ intervaltime = 5 * 1000, onUpdate }) {
  pollingData.intervalTime = intervaltime;
  pollingData.onUpdate = onUpdate;
  pollingData.startTime = new Date().getTime();
  pollingData.endTime = new Date().getTime();
  dynamicUpdate();
};

export const cancelPolling = () => {
  cancelAnimationFrame(pollingData.animationId);
};

