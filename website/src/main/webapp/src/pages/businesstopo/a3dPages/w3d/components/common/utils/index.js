import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { ATTRS, DIRECTION_ATTRS, LAYOUT_MODES } from '../const';

const A3D = getA3D();

/**
 * 保留digits位小数
 * @param {Number} num
 * @param {Number} digits
 * @returns
 */
export const toFixed = function(num, digits = 2) {
  let pow = Math.pow(10, digits);
  return Math.round(num * pow) / pow;
};

const getDecimalPointLen = function(num) {
  let len;
  try {
    len = num.toString().split('.')[1].length;
  } catch (e) {
    len = 0;
  }
  return len;
};

export const getUrlParam = paramKey => {
  let urlParam = location.href.match(/&(\S*)/g)[0];
  let arr = urlParam.split('&');
  let allParam = {};
  arr.map(param => {
    let index = param.indexOf('=');
    if (index > 0) {
      let key = param.slice(0, index);
      allParam[key] = param.slice(index + 1, param.length);
    }
  });
  return allParam[paramKey] || '';
};

/**
 * 精确的浮点数相加，结果保留两位小数，规避精度丢失问题
 * @param {Number} num1
 * @param {Number} num2
 * @returns
 */
const accAdd = function(num1, num2) {
  let m; let c;
  let r1 = getDecimalPointLen(num1);
  let r2 = getDecimalPointLen(num2);
  c = Math.abs(r1 - r2);
  m = Math.pow(10, Math.max(r1, r2));
  let num1Inner;
  let num2Inner;
  if (c > 0) {
    let cm = Math.pow(10, c);
    if (r1 > r2) {
      num1Inner = Number(num1.toString().replace('.', ''));
      num2Inner = Number(num2.toString().replace('.', '')) * cm;
    } else {
      num1Inner = Number(num1.toString().replace('.', '')) * cm;
      num2Inner = Number(num2.toString().replace('.', ''));
    }
  } else {
    num1Inner = Number(num1.toString().replace('.', ''));
    num2Inner = Number(num2.toString().replace('.', ''));
  }
  return (num1Inner + num2Inner) / m;
};

/**
 * 精确的浮点数相减，结果保留两位小数，规避精度丢失问题
 * @param {Number} num1
 * @param {Number} num2
 * @returns
 */
const accSub = function(num1, num2) {
  let m; let n;
  let r1 = getDecimalPointLen(num1);
  let r2 = getDecimalPointLen(num2);
  // last modify by deeka 动态控制精度长度
  m = Math.pow(10, Math.max(r1, r2));
  n = r1 >= r2 ? r1 : r2;
  let result = ((num1 * m - num2 * m) / m).toFixed(n);
  return Number(result);
};

/**
 * 精确的浮点数相乘，结果保留两位小数，规避精度丢失问题
 * @param {Number} num1
 * @param {Number} num2
 * @returns
 */
const accMul = function(num1, num2) {
  let m = 0;
  let s1 = num1.toString();
  let s2 = num2.toString();
  try {
    m += s1.split('.')[1]?.length || '';
  } catch (e) {
    m = 0;
  }
  try {
    m += s2.split('.')[1]?.length || '';
  } catch (e) {
    m = 0;
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
};

/**
 * 精确的浮点数相除，结果保留两位小数，规避精度丢失问题
 * @param {Number} num1
 * @param {Number} num2
 * @returns
 */
const accDiv = function(num1, num2) {
  let t1 = getDecimalPointLen(num1);
  let t2 = getDecimalPointLen(num2);
  let r1 = Number(num1.toString().replace('.', ''));
  let r2 = Number(num2.toString().replace('.', ''));
  return (r1 / r2) * Math.pow(10, t2 - t1);
};

export const floatCalc = {
  add(...rest) {
    return rest.reduce((total, num) => accAdd(total, num));
  },
  sub(...rest) {
    return rest.reduce((total, num) => accSub(total, num));
  },
  mul(...rest) {
    return rest.reduce((total, num) => accMul(total, num));
  },
  div(...rest) {
    return rest.reduce((total, num) => accDiv(total, num));
  },
};

const safeRandom = () => {
  const crypto = window.crypto || window.webkitCrypto || window.mozCrypto || window.oCrypto || window.msCrypto;
  return crypto.getRandomValues(new Uint32Array(1))[0] / (0xffffffff + 1);
};

/**
 * 取min到max之间的随机数
 * @param {Number} max
 * @param {Number} min
 * @returns
 */
export const random = function(min = 0, max = 1) {
  return safeRandom() * (max - min) + min;
};

const scaleBoundingBox = function(boundingBox, scale) {
  if (!scale) {
    return boundingBox;
  }
  let scaleX = scale.x || scale;
  let scaleY = scale.y || scale;
  let scaleZ = scale.z || scale;
  if (isNaN(scaleX) || isNaN(scaleY) || isNaN(scaleZ)) {
    return boundingBox;
  }
  boundingBox.width *= scaleX;
  boundingBox.height *= scaleY;
  boundingBox.depth *= scaleZ;
  boundingBox.min.x *= scaleX;
  boundingBox.min.y *= scaleY;
  boundingBox.min.z *= scaleZ;
  boundingBox.max.x *= scaleX;
  boundingBox.max.y *= scaleY;
  boundingBox.max.z *= scaleZ;
  return boundingBox;
};

/**
 * 获取Geometry的尺寸
 * @param {Geometry} geo
 * @returns
 */
export const getGeometrySize = function(geo, scale) {
  if (!geo.boundingBox) {
    geo.computeBoundingBox();
  }
  let boundingBox = geo.boundingBox;
  let max = boundingBox.max;
  let min = boundingBox.min;
  boundingBox = {
    max: { x: toFixed(max.x), y: toFixed(max.y), z: toFixed(max.z) },
    min: { x: toFixed(min.x), y: toFixed(min.y), z: toFixed(min.z) },
    width: toFixed(boundingBox.max.x - boundingBox.min.x, 2),
    height: toFixed(boundingBox.max.y - boundingBox.min.y, 2),
    depth: toFixed(boundingBox.max.z - boundingBox.min.z, 2),
  };
  return scaleBoundingBox(boundingBox, scale);
};

let objectBox = new A3D.C.Box3();

/**
 * 获取Object3D的尺寸
 * @param {Object3D} obj
 * @returns
 */
export const getObjectSize = function(obj, scale) {
  objectBox.setFromObject(obj);
  let max = objectBox.max;
  let min = objectBox.min;
  let boundingBox = {
    max: { x: toFixed(max.x), y: toFixed(max.y), z: toFixed(max.z) },
    min: { x: toFixed(min.x), y: toFixed(min.y), z: toFixed(min.z) },
    width: toFixed(objectBox.max.x - objectBox.min.x, 2),
    height: toFixed(objectBox.max.y - objectBox.min.y, 2),
    depth: toFixed(objectBox.max.z - objectBox.min.z, 2),
  };
  return scaleBoundingBox(boundingBox, scale);
};

/**
 * 获取材质
 * @param {Function|Material} material
 * @returns
 */
export const getMaterial = function(material) {
  if (typeof material === 'function') {
    return material();
  }
  return material;
};

/**
 * 获取点的包围盒数据
 * @param {Array<{x,y,z}>} points
 * @returns
 */
export const getBoundingBox = function(points) {
  let minx = Infinity;
  let miny = Infinity;
  let minz = Infinity;
  let maxx = -Infinity;
  let maxy = -Infinity;
  let maxz = -Infinity;
  let position;
  for (let i = 0, len = points.length; i < len; i++) {
    position = points[i];
    minx = Math.min(minx, position.x);
    maxx = Math.max(maxx, position.x);
    miny = Math.min(miny, position.y);
    maxy = Math.max(maxy, position.y);
    minz = Math.min(minz, position.z);
    maxz = Math.max(maxz, position.z);
  }
  return {
    max: { x: maxx, y: maxy, z: maxz },
    min: { x: minx, y: miny, z: minz },
    width: maxx - minx,
    height: maxy - miny,
    depth: maxz - minz,
    center: {
      x: toFixed((minx + maxx) / 2),
      y: toFixed((miny + maxy) / 2),
      z: toFixed((minz + maxz) / 2),
    },
  };
};

/**
 * 获取初始化包围盒
 * @returns
 */
export const getInitBoundingBox = function() {
  return {
    max: { x: 0, y: 0, z: 0 },
    min: { x: 0, y: 0, z: 0 },
    width: 0,
    height: 0,
    depth: 0,
  };
};

/**
 * 通过direction获取rotation
 * @param {JSON} data
 * @returns
 */
export const getRotationFromDirection = function(data, defaultDir) {
  let rotation;
  if (data && !data.rotation && data.direction) {
    let direction = data.direction;
    let curDir = DIRECTION_ATTRS.curDir;
    let quaternion = DIRECTION_ATTRS.quaternion;
    let _defaultDir = defaultDir || DIRECTION_ATTRS.defaultDir;
    curDir.set(direction.x, direction.y, direction.z);
    quaternion.setFromUnitVectors(_defaultDir, curDir);
    rotation = DIRECTION_ATTRS.rotation.setFromQuaternion(quaternion);
  }
  return rotation;
};

const getContainerSizeRow = (len, geoSize, parameters) => {
  let count = parameters.count;
  let space = parameters.space;
  let i = 0; let containerWidth = 0; let rowHeight = []; let layerHeight = []; let containerDepth = 0;
  for (let y = 0; y < len; y += count.x * count.z) {
    let maxDepth = 0; let depthCount = 0;
    for (let z = 0; z < count.z && geoSize[i]; z++) {
      let widthCount = 0; let rowMaxheight = 0;
      for (let x = 0; x < count.x && geoSize[i]; x++) {
        widthCount += geoSize[i].width + space.x;
        rowMaxheight = Math.max(rowMaxheight, geoSize[i].depth);
        maxDepth = Math.max(maxDepth, geoSize[i].height);
        i++;
      }
      containerWidth = Math.max(containerWidth, widthCount - space.x);
      depthCount += rowMaxheight + space.z;
      rowHeight.push(rowMaxheight);
    }
    containerDepth = Math.max(containerDepth, depthCount - space.z);
    layerHeight.push(maxDepth);
  }
  if (parameters.container) {
    containerWidth = parameters.container.width;
    containerDepth = parameters.container.depth;
  }
  return {
    containerWidth, rowHeight, layerHeight, containerDepth,
  };
};

const getContainerSizeColumn = (len, geoSize, parameters) => {
  let count = parameters.count;
  let space = parameters.space;
  let i = 0; let containerWidth = 0; let rowHeight = []; let layerHeight = []; let containerDepth = 0;
  for (let y = 0; y < len; y += count.x * count.z) {
    let maxDepth = 0; let depthCount = 0;
    for (let x = 0; x < count.x && geoSize[i]; x++) {
      let widthCount = 0; let rowMaxheight = 0;
      for (let z = 0; z < count.z && geoSize[i]; z++) {
        widthCount += geoSize[i].depth + space.z;
        rowMaxheight = Math.max(rowMaxheight, geoSize[i].width);
        maxDepth = Math.max(maxDepth, geoSize[i].height);
        i++;
      }
      containerWidth = Math.max(containerWidth, widthCount - space.z);
      depthCount += rowMaxheight + space.x;
      rowHeight.push(rowMaxheight);
    }
    containerDepth = Math.max(containerDepth, depthCount - space.x);
    layerHeight.push(maxDepth);
  }
  if (parameters.container) {
    containerWidth = parameters.container.depth;
    containerDepth = parameters.container.width;
  }
  return {
    containerWidth, rowHeight, layerHeight, containerDepth,
  };
};

const getInitVal = (dir, containerWidth, width, count, isOdd) => {
  let val;
  let val1 = containerWidth - (width / 2 + count);
  let val2 = width / 2 + count;
  if (dir === 'row' || dir === 'column') {
    val = isOdd ? val1 : val2;
  }
  if (dir === 'row-reverse' || dir === 'column-reverse') {
    val = isOdd ? val2 : val1;
  }
  return val;
};

// 物体在行内的高度偏移量
const getAlignItemsOffset = (alignItems, rowHeight, geoHeight) => {
  let heightOffset = 0;
  if (alignItems === 'center') {
    heightOffset = (rowHeight - geoHeight) / 2;
  }
  if (alignItems === 'end') {
    heightOffset = rowHeight - geoHeight;
  }
  return heightOffset;
};

const getJustifyContentOffset = (justifyContent, containerWidth, count, spaceVal) => {
  let widthOffset = 0;
  if (justifyContent === 'center') {
    widthOffset = (containerWidth - (count - spaceVal)) / 2;
  }
  if (justifyContent === 'end') {
    widthOffset = containerWidth - (count - spaceVal);
  }
  return widthOffset;
};

// 奇数行沿着x 或 z轴忘justContent方向偏移
const setWrap = (dir, arr2, containerWidth, count, spaceVal) => {
  let offsetVal = containerWidth - (count - spaceVal);
  if (dir === 'row') {
    arr2.forEach((p) => {
      p.position.x -= offsetVal;
    });
  }
  if (dir === 'row-reverse') {
    arr2.forEach((p) => {
      p.position.x += offsetVal;
    });
  }
  if (dir === 'column') {
    arr2.forEach((p) => {
      p.position.z -= offsetVal;
    });
  }
  if (dir === 'column-reverse') {
    arr2.forEach((p) => {
      p.position.z += offsetVal;
    });
  }
  return arr2;
};

const offsetToCenter = (dir, arr2, widthOffset, containerWidth) => {
  if (dir === 'row') {
    arr2.forEach((p) => {
      p.position.x += widthOffset - containerWidth / 2;
    });
  }
  if (dir === 'row-reverse') {
    arr2.forEach((p) => {
      p.position.x -= widthOffset + containerWidth / 2;
    });
  }
  if (dir === 'column') {
    arr2.forEach((p) => {
      p.position.z += widthOffset - containerWidth / 2;
    });
  }
  if (dir === 'column-reverse') {
    arr2.forEach((p) => {
      p.position.z -= widthOffset + containerWidth / 2;
    });
  }
  return arr2;
};

const arrayLayoutRow = (len, parameters, geoSize) => {
  const {
    containerWidth, rowHeight, layerHeight, containerDepth,
  } = getContainerSizeRow(len, geoSize, parameters);
  let layoutData = []; let count = parameters.count; let space = parameters.space;
  let dir = parameters.dir; let wrap = parameters.wrap; let justifyContent = parameters.justifyContent;
  let alignItems = parameters.alignItems; let i = 0; let hCount = 0; let dCount = 0;
  for (let y = 0; y < len; y += count.x * count.z) {
    let arr1 = []; let thisFloorDepth = 0;
    for (let m = 0; m < count.z; m ++) {
      let rowIndex = y / count.x + m;
      if (!rowHeight[rowIndex]) {
        break;
      }
      thisFloorDepth += rowHeight[rowIndex] + space.z;
    }
    dCount = (containerDepth - thisFloorDepth) / 2;
    for (let z = 0; z < count.z; z++) {
      let arr2 = []; let wCount = 0;
      let rowHeightIndex = y / count.x + z;
      // 行数为奇数，且wrap为‘reverse’时
      let isOdd = wrap === 'reverse' && (z & 1) === 1;
      for (let x = 0; x < count.x && geoSize[i]; x++) {
        arr2.push({
          position: {
            x: getInitVal(dir, containerWidth, geoSize[i].width, wCount, isOdd),
            y: hCount,
            z: geoSize[i].depth / 2 + dCount,
          },
        });
        wCount += geoSize[i].width + space.x;
        let heightOffset = getAlignItemsOffset(alignItems, rowHeight[rowHeightIndex], geoSize[i].depth);
        // 所有3D对象沿着z轴向中心偏移
        arr2[arr2.length - 1].position.z += heightOffset - containerDepth / 2;
        i++;
      }
      if (isOdd) {
        arr2 = setWrap(dir, arr2, containerWidth, wCount, space.x);
      }
      dCount += rowHeight[rowHeightIndex] + space.z;
      let widthOffset = getJustifyContentOffset(justifyContent, containerWidth, wCount, space.x);
      arr2 = offsetToCenter(dir, arr2, widthOffset, containerWidth);
      arr1.push(arr2);
    }
    hCount += layerHeight[y / (count.x * count.z)] / 2 + (layerHeight[y / (count.x * count.z) + 1] || 0) / 2 + space.y;
    layoutData.push(arr1);
  }
  return layoutData.flat(3);
};

const arrayLayoutColumn = (len, parameters, geoSize) => {
  const {
    containerWidth, rowHeight, layerHeight, containerDepth,
  } = getContainerSizeColumn(len, geoSize, parameters);
  let layoutData = []; let count = parameters.count; let space = parameters.space; let dir = parameters.dir;
  let wrap = parameters.wrap; let justifyContent = parameters.justifyContent; let alignItems = parameters.alignItems;
  let i = 0; let hCount = 0; let dCount = 0;
  for (let y = 0; y < len; y += count.x * count.z) {
    let arr1 = [];
    let thisFloorDepth = 0;
    for (let m = 0; m < count.x; m ++) {
      let rowIndex = y / count.z + m;
      if (!rowHeight[rowIndex]) {
        break;
      }
      thisFloorDepth += rowHeight[rowIndex] + space.x;
    }
    dCount = (containerDepth - thisFloorDepth) / 2;
    for (let x = 0; x < count.x; x++) {
      let arr2 = []; let wCount = 0; let rowHeightIndex = y / count.z + x;
      // 行数为奇数，且wrap为‘reverse’时
      let isOdd = wrap === 'reverse' && (x & 1) === 1;
      for (let z = 0; z < count.z && geoSize[i]; z++) {
        arr2.push({
          position: {
            x: geoSize[i].width / 2 + dCount,
            y: hCount,
            z: getInitVal(dir, containerWidth, geoSize[i].depth, wCount, isOdd),
          },
        });
        wCount += geoSize[i].depth + space.z;
        let heightOffset = getAlignItemsOffset(alignItems, rowHeight[rowHeightIndex], geoSize[i].width);
        // 所有3D对象沿着x轴向中心偏移
        arr2[arr2.length - 1].position.x += heightOffset - containerDepth / 2;
        i++;
      }
      if (isOdd) {
        arr2 = setWrap(dir, arr2, containerWidth, wCount, space.z);
      }
      dCount += rowHeight[rowHeightIndex] + space.x;
      let widthOffset = getJustifyContentOffset(justifyContent, containerWidth, wCount, space.z);
      arr2 = offsetToCenter(dir, arr2, widthOffset, containerWidth);
      arr1.push(arr2);
    }
    hCount += layerHeight[y / (count.x * count.z)] / 2 + (layerHeight[y / (count.x * count.z) + 1] || 0) / 2 + space.y;
    layoutData.push(arr1);
  }
  return layoutData.flat(3);
};

/**
 * 阵列布局
 * @param {Number} len 阵列的个数
 * @param {JSON} parameters 配置参数
 * @param {JSON} geoSize 阵列模型的尺寸
 * @returns
 */
export const arrayLayout = function(len, parameters, geoSize) {
  if (parameters.dir.includes('row')) {
    return arrayLayoutRow(len, parameters, geoSize);
  }
  if (parameters.dir.includes('column')) {
    return arrayLayoutColumn(len, parameters, geoSize);
  }
  return [];
};

/**
 * 多层布局
 * @param { Number } len 数组长度
 * @param { JSON }  parameters 配置参数
 * @param { JSON }  geoSize 阵列模型的尺寸
 * @returns
 */
export const layerLayout = function(len, parameters, geoSize, selectIndex) {
  let sumHeight = 0;
  let sumDepth = 0;
  let layoutData = [];
  let height = 0;
  let depth = 0;
  for (let i = 0; i < len; i++) {
    height = geoSize[i].height;
    depth = geoSize[i].depth;
    layoutData.push({
      position: { x: 0, y: sumHeight + height / 2, z: -(sumDepth + depth / 2) },
      rotation: selectIndex === i ? { x: Math.PI / 2, y: 0, z: 0 } : ATTRS.rotation.defaultValue,
    });
    sumHeight += (isNaN(selectIndex) ? 1 : 5) * height + parameters.space.y;
    sumDepth += depth + parameters.space.z;
  }
  sumHeight -= parameters.space.y;
  sumDepth -= parameters.space.z;
  let setting;
  for (let i = 0; i < len; i++) {
    setting = layoutData[i];
    setting.position.y -= sumHeight / 2;
    setting.position.z += sumDepth / 2;
  }
  return layoutData;
};

/**
 * 多层布局
 * @param { Number } len 数组长度
 * @param { JSON }  parameters 配置参数
 * @param { JSON }  geoSize 阵列模型的尺寸
 * @returns
 */
export const layerSelectLayout = function(len, parameters, geoSize, selectIndex) {
  return layerLayout(len, parameters, geoSize, selectIndex);
};

/**
 * 轮播布局初始化
 *
 * @param {Number} length listItem个数
 * @param {JSON} parameters 参数
 * @param {Number} selectIndex 选中Index
 * @param {Object3D} listItemGroup listView.children[0]
 * @returns {Array<JSON>}
 */
export const carouselInitLayout = function(length, parameters, selectIndex, listItemGroup) {
  let geoSize = [];
  let index;
  for (let i = 0; i < length; i++) {
    index = (i + selectIndex + 1) % length;
    let ratio = 1 / listItemGroup.children[index].scale.x;
    geoSize.push(getObjectSize(
      listItemGroup.children[index],
      index === selectIndex ? ratio * parameters.selectScale : ratio * parameters.unselectScale,
    ));
  }
  let layoutData = arrayLayout(length, {
    dir: 'row-reverse',
    wrap: 'positive',
    justifyContent: 'center',
    alignItems: 'center',
    count: { x: length - 1 || 1, z: 2 },
    space: parameters.space,
  }, geoSize);
  let res = [];
  for (let i = 0; i < length; i++) {
    index = (i + selectIndex + 1) % length;
    res[index] = layoutData[i];
    res[index].scale = index === selectIndex ? {
      x: parameters.selectScale,
      y: parameters.selectScale,
      z: parameters.selectScale,
    } : {
      x: parameters.unselectScale,
      y: parameters.unselectScale,
      z: parameters.unselectScale,
    };
  }
  layoutData = null;
  return res;
};

/**
 * 轮播选中布局
 *
 * @param {Number} length listItem个数
 * @param {JSON} parameters 参数
 * @param {Number} selectIndex 选中Index
 * @param {Number} prevSelectIndex 上一次选中Index
 * @param {Object3D} listItemGroup listView.children[0]
 * @returns {Array<JSON>}
 */
export const carouselSelectLayout = function(length, parameters, selectIndex, prevSelectIndex, listItemGroup) {
  let states = selectIndex - prevSelectIndex;
  if (prevSelectIndex > selectIndex) {
    states = length + states;
  }
  let isClockwise = false;
  if (states >= length / 2) {
    states = length - states;
    isClockwise = true;
  }
  let res = [];
  let index = prevSelectIndex + length;
  for (let i = 0; i <= states; i++) {
    if (isClockwise) {
      res.push(carouselInitLayout(length, parameters, (index - i) % length, listItemGroup));
    } else {
      res.push(carouselInitLayout(length, parameters, (index + i) % length, listItemGroup));
    }
  }
  return res;
};

export const onChange = function(self, runAnimationFn, onLayoutFn) {
  let parameters = self.parameters;
  if (parameters.layoutMode === LAYOUT_MODES.dataDrivenMode) {
    return;
  }
  if (parameters.animate && parameters.animationOption?.animation) {
    runAnimationFn();
  } else {
    let endLayout = parameters.layoutGroup[parameters.layoutMode](self.count, self);
    onLayoutFn([null, endLayout], 1);
  }
};

/**
 * 如果有动画，获取动画开始前的状态数据
 *
 * @param {Array<JSON>} endLayout
 * @returns {Array<JSON>}
 */
export const getStartLayout = function(endLayout, count, mesh, attrs) {
  if (!endLayout) {
    return null;
  }
  let startLayout = [];
  for (let i = 0; i < count; i++) {
    let output = mesh.getIndex(i);
    let start = {};
    attrs.forEach((key) => {
      if (endLayout[0] && endLayout[0][key]) {
        start[key] = output[key];
      }
    });
    startLayout.push(start);
  }
  return startLayout;
};

/**
 * 根据布局类型，获取布局状态
 */
export const getAnimationState = function(self) {
  let parameters = self.parameters;
  let endLayout = parameters.layoutGroup[parameters.layoutMode](self.count, self);
  if (Array.isArray(endLayout[0])) {
    return endLayout;
  }
  let startLayout = getStartLayout(endLayout);
  return [startLayout, endLayout];
};

/**
 * 当布局发生变化时自动计算布局并触发动画
 */
export const runAnimation = function(self, animationOption, states, onLayoutFn) {
  let animation = animationOption?.animation;
  if (!animation) {
    return;
  }
  if (animation.isPlaying()) {
    animation.stop();
  }
  let animationSetting = {
    easing: animationOption.easing || A3D.Animation.Easing.Linear.None,
    delay: animationOption.delay || 0,
    duration: animationOption.duration || 400,
    onUpdate: (e) => {
      onLayoutFn(e.value);
    },
  };
  let endLayout = states[states.length - 1];
  if (animationOption.onStart) {
    animationSetting.onStart = () => animationOption.onStart(self, endLayout);
  }
  if (animationOption.onComplete) {
    animationSetting.onComplete = () => animationOption.onComplete(self, endLayout);
  }
  animation.push(animationSetting);
  animation.start();
};

export const lerp = (start, end, percent, output) => {
  let _output = output || {};
  _output.x = start.x + (end.x - start.x) * percent;
  _output.y = start.y + (end.y - start.y) * percent;
  _output.z = start.z + (end.z - start.z) * percent;
  return _output;
};

/**
 * 获取布局数据
 * @param {Array} states 状态数据
 * @param {Number} percent 动画百分比
 * @returns
 */
export const getLayoutData = function(states, percent) {
  if (percent === 0) {
    return null;
  }
  let state;
  if (percent === 1) {
    state = states[states.length - 1];
  } else {
    let statesCount = states.length - 1;
    let time = percent * statesCount;
    let stateIndex = Math.floor(time);
    let statePercent = time - stateIndex;
    let stateStart = states[stateIndex];
    let stateEnd = states[stateIndex + 1] || stateStart;
    state = stateStart.map((item, index) => {
      let lerpVal = {};
      Object.keys(item).forEach((key) => {
        lerpVal[key] = lerp(item[key], stateEnd[index][key], statePercent);
      });
      return lerpVal;
    });
  }
  return state;
};

const getStartCurve = function(pathSetting, index) {
  const next = index + 1;
  const curSetting = pathSetting[index];
  const nextSetting = pathSetting[next];

  const dir2 = curSetting.curNormalize;
  const p2 = curSetting.point.clone();

  const p4 = nextSetting.point.clone();
  p4.add(dir2.clone().multiplyScalar(-nextSetting.radius));
  const line = new A3D.C.LineCurve3(p2, p4);
  return [line];
};

const getCenterCurve = function(pathSetting, index, cutRadius, len) {
  const prev = index === 0 ? len - 1 : index - 1;
  const prevSetting = pathSetting[prev];
  const curSetting = pathSetting[index];
  const next = index + 1 < len ? index + 1 : 0;
  const nextSetting = pathSetting[next];

  // 计算三个点构成的两条线的方向
  const dir1 = prevSetting.curNormalize.clone().negate();
  const dir2 = curSetting.curNormalize;
  const p1 = dir1.clone().multiplyScalar(curSetting.radius).add(curSetting.point);
  const p2 = curSetting.point.clone();
  const p3 = dir2.clone().multiplyScalar(curSetting.radius).add(curSetting.point);

  const result = [];
  if (curSetting.radius > 0) {
    const beziercurve = new A3D.C.QuadraticBezierCurve3(p1, p2, p3);
    result.push(beziercurve);
  }

  const p4 = nextSetting.point.clone();
  if (cutRadius) {
    p4.add(dir2.clone().multiplyScalar(-nextSetting.radius));
  }
  const line = new A3D.C.LineCurve3(p3, p4);
  result.push(line);
  return result;
};

/**
 * 对每个拐角半径做插值，会根据每个拐角两边的边长和半径来计算合理的半径
 * @param {THREE.Vector3[]|Object[]} points
 * @param {Number[]} radius
 * @param {Number} radiusSegments
 * @param {Boolean} close
 * @returns
 */
export const getCurveInCorner = function(points, radius, radiusSegments, close) {
  const curve = new A3D.C.CurvePath();
  const len = points.length;
  const pathSetting = points.map((point, index) => {
    const vec = new A3D.C.Vector3().copy(point);
    const dir = vec.clone().negate().add(points[(index + 1) % len]);
    return {
      point: vec,
      curNormalize: dir.clone().normalize(),
      curLength: dir.length(),
      radius: radius[index],
    };
  });
  pathSetting.forEach((setting, index) => {
    const prev = index === 0 ? len - 1 : index - 1;
    const prevSetting = pathSetting[prev];
    let length1 = prevSetting.curLength / 2;
    let length2 = setting.curLength / 2;
    if (!close) {
      if (index === 1) {
        length1 = prevSetting.curLength;
      }
      if (index === len - 2) {
        length2 = setting.curLength;
      }
    }
    setting.radius = Math.min(length1, length2, setting.radius);
  });

  if (close) {
    for (let i = 0; i < len; i++) {
      const curves = getCenterCurve(pathSetting, i, true, len);
      curve.curves.push(...curves);
    }
  } else {
    for (let i = 0; i < len - 1; i++) {
      let curves;
      if (i === len - 2) {
        curves = getCenterCurve(pathSetting, i, false, len);
      } else if (i === 0) {
        curves = getStartCurve(pathSetting, i);
      } else {
        curves = getCenterCurve(pathSetting, i, true, len);
      }
      curve.curves.push(...curves);
    }
  }
  return curve.getPoints(radiusSegments);
};

export const isPointsEqual = function(point1, point2) {
  return point1.x === point2.x && point1.y === point2.y && point1.z === point2.z;
};
