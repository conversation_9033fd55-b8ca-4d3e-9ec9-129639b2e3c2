* {
  font-family: "Arial", "Microsoft YaHei";
}

html {
  overflow: hidden;
  background-color: #050505;

  body {
    margin: 0;
    padding: 0;
  }
}

.root-contaniner {
  height: 100%;

  .header {
    min-width: 920px;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    padding: 0 2rem;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
    width: 100%;
    background-color: #050505;
    color: #fff;
    z-index: 2021;
    box-shadow: 0 1px 2px 0 #272727;

    *:focus {
      outline-offset: 0;
      outline: 0 !important;
    }

    .header-logo-link {
      cursor: pointer;
      display: inline-block;
      height: 28px;

      img {
        height: 24px;
        vertical-align: middle;
        user-select: none;
      }

      span {
        margin-left: 0.5rem;
        vertical-align: middle;
        font-size: 1rem;
        font-weight: bold;
        color: #fff;
      }
    }

    .header-nav {
      position: relative;
      display: inline-flex;
      height: 4rem;
      line-height: 4rem;

      .header-nav-item {
        position: relative;
        margin-right: 2.5rem;
        font-size: 0.875rem;
        color: #bbb;
        width: auto;
        cursor: pointer;

        &.active {
          color: #fff;
          font-weight: bold;
        }

        span {
          margin-left: 0;
        }

        .nav-item-zh {
          margin-right: 0.5rem;
        }

        &:hover {
          .nav-red-line {
            width: 100%;
            transition: 0.3s;
          }
        }

        .nav-red-line {
          position: absolute;
          bottom: 0px;
          background-color: #fff;
          height: 0.1875rem;
          width: 0;
          left: 0;
          transition: 0.3s;

          &.visit {
            width: 100%;
          }
        }
      }
    }

    .header-operate {
      position: relative;
      display: inline-flex;
      align-items: center;

      .header_icon {
        margin-left: 1rem;
        padding: 0.625rem 0;
      }

      .header_user {
        display: inline-flex;
        align-items: center;

        .header_userName {
          display: inline-block;
          font-size: 0.875rem;
          color: #bbbbbb;
          text-align: right;
          margin-right: 0.5rem;
        }

        .header_userIcon {
          display: inline-block;
          width: 1.5rem;
          height: 1.5rem;
          border-radius: 50%;
          background-color: #fff;

          img {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
    }
  }

  .main_container {
    min-width: 920px;
    position: absolute;
    top: 4rem;
    left: 0;
    height: calc(100vh - 4rem);
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

//404
.content-notFound {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #000;

  img {
    width: 400px;
  }
}

/*适配*/
@media screen and (min-width: 1680px) {
  html {
    font-size: 16px !important;
    height: 100%;
  }
}

@media screen and (min-width: 1440px) and (max-width: 1679px) {
  html {
    font-size: 14px !important;
    height: 100%;
  }
}

@media screen and (max-width: 1439px) {
  html {
    font-size: 12px !important;
    height: 100%;
  }
}