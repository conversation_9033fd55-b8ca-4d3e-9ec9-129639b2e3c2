import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { LineSegments2 } from 'three/examples/jsm/lines/LineSegments2.js';
import { LineSegmentsGeometry } from 'three/examples/jsm/lines/LineSegmentsGeometry.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import {getBezier1, getBezier2, updateLine, updateMMLine} from '../../utils/manager/component/helper/line';
import Line from '../Line';

const A3D = getA3D();

export const getDottedLine = (curve, dashScale, dottedMat) => {
  const count = Math.ceil(curve.getLength() * (dashScale / 2)) * 2 - 1;
  const points = curve.getSpacedPoints(count);
  const positions = [];
  points.forEach((point) => {
    positions.push(point.x, point.y, point.z);
  });
  const segmentsGeometry = new LineSegmentsGeometry();
  segmentsGeometry.setPositions(positions);
  const segments = new LineSegments2(segmentsGeometry, dottedMat);
  segments.computeLineDistances();
  return segments;
};

export const getLine = (curve, mat) => {
  const count = Math.max(Math.ceil(curve.getLength()) * 2 - 1, 32);
  const points = curve.getSpacedPoints(count);
  const positions = [];
  points.forEach((point) => {
    positions.push(point.x, point.y, point.z);
  });
  const lineGeometry = new LineGeometry();
  lineGeometry.setPositions(positions);
  const line = new Line2(lineGeometry, mat);

  return line;
};

export const getLine2 = (curve) => {
  const count = Math.max(Math.ceil(curve.getLength()) * 14 - 1, 32);
  const points = curve.getSpacedPoints(count);
  const positions = [];
  points.forEach((point) => {
    positions.push(point.x, point.y, point.z);
  });
  const lineGeometry = new LineGeometry();
  lineGeometry.setPositions(positions);
  const line = new Line({
    linewidth: 1,
    onChangeCallback: (_data, lineMesh, flowMesh) => {
      updateMMLine(lineMesh, flowMesh);
    },
    onShowCallback: (_line) => {
      _line.children[1]?.traverse((item) => {
        item.visible = true;
      });
    },
    lineGeometry,
    points,
  });
  return line;
};