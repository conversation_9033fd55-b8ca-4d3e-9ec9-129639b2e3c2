import { random } from '@pages/businesstopo/a3dPages/w3d/components/common/utils';

const siteCount = 3;
const memorydbs = {
  invgmdb: {
    belong: 'management',
    active: 12,
    redundancy: false,
  },
  cbpmdb: {
    belong: 'billing',
    active: 40,
    redundancy: true,
  },
  abentermdb: {
    belong: 'billing',
    active: 6,
    redundancy: false,
  },
  cdfmdb: {
    belong: 'billing',
    active: 6,
    redundancy: false,
  },
};

const memory = {
  podCount: 0,
  podFault: 0,
  exceptionChange: 0,
  management: {
    name: '管理及后付费业务',
    dbs: [],
  },
  billing: {
    name: '实时计费类业务',
    dbs: [],
  },
};

const physicdbs = {
  userdb: {
    belong: 'management',
    active: 16,
    redundancy: false,
  },
  billdb: {
    belong: 'management',
    active: 23,
    redundancy: false,
  },
  edrdb: {
    belong: 'management',
    active: 17,
    redundancy: false,
  },
  edrhisdb: {
    belong: 'management',
    active: 10,
    redundancy: false,
  },
  bmpdb: {
    belong: 'management',
    active: 1,
    redundancy: false,
  },
  billsharedb: {
    belong: 'management',
    active: 1,
    redundancy: false,
  },
  MEDdb: {
    belong: 'offlineMediation',
    active: 2,
    redundancy: false,
  },
  reportdb: {
    belong: 'BDI',
    active: 2,
    redundancy: false,
  },
  UVCdb: {
    belong: 'UVC',
    active: 3,
    redundancy: false,
  },
};

const physic = {
  podCount: 0,
  podFault: 0,
  exceptionChange: 0,
  management: {
    name: '管理及后付费业务',
    dbs: [],
  },
  offlineMediation: {
    name: 'offline mediation',
    dbs: [],
  },
  BDI: {
    name: 'BDI',
    dbs: [],
  },
  UVC: {
    name: 'UVC',
    dbs: [],
  },
};

const getStatus = (status1) => {
  const number = random();
  const time = (status1 && status1 !== 'normal') ? 5 : 1;
  let status = 'normal';
  if (number < 0.02 * time) {
    status = 'warning';
  } else if (number < 0.04 * time) {
    status = 'alarm';
  }
  return status;
};

const getSiteId = (index) => `site${index + 1}`;
const getPodId = (siteId, typedbs, dbType, podType, index) =>
  `${siteId}-${typedbs}-${dbType}-${podType}${index + 1}`;

const getRelationshipAndData = (relationship, data, groupIndex, typedbs, type) => {
  for (let i = 0; i < siteCount; i++) {
    const siteId = getSiteId(i + groupIndex * 3);
    Object.keys(typedbs).forEach((db) => {
      for (let j = 0; j < typedbs[db].active; j++) {
        const relation = {
          active: getPodId(siteId, type, db, 'a', j),
          standby: getPodId(siteId, type, db, 's', j),
          redundancy: [],
        };
        const status1 = getStatus();
        const status2 = getStatus(status1);
        data.push({
          id: relation.active,
          activeId: relation.active,
          status: status1,
        }, {
          id: relation.standby,
          activeId: relation.active,
          status: status2,
        });
        if (typedbs[db].redundancy) {
          let status3 = 'normal';
          if (status1 !== 'normal' && status2 !== 'normal') {
            status3 = 'alarm';
          }
          const siteIndex = (i + 1) % siteCount + groupIndex * 3;
          const redundancyId = getPodId(getSiteId(siteIndex), type, db, 'r', j);
          relation.redundancy.push(redundancyId);
          data.push({
            id: redundancyId,
            activeId: relation.active,
            status: status3,
          });
        }
        relationship.push(relation);
      }
    });
  }
};

const getSite = (i, groupIndex) => {
  const site = {
    id: `site${i + 1 + groupIndex * 3}`,
    name: `site${i + 1 + groupIndex * 3}`,
    memory: JSON.parse(JSON.stringify(memory)),
    physic: JSON.parse(JSON.stringify(physic)),
  };
  Object.keys(memorydbs).forEach((db) => {
    const belong = memorydbs[db].belong;
    const dbObj = {
      name: db,
      active: [],
      standby: [],
      redundancy: [],
    };
    site.memory[belong].dbs.push(dbObj);
  });
  Object.keys(physicdbs).forEach((db) => {
    const belong = physicdbs[db].belong;
    const dbObj = {
      name: db,
      active: [],
      standby: [],
      redundancy: [],
    };
    site.physic[belong].dbs.push(dbObj);
  });
  return site;
};

const getSites = (sites, data, groupIndex) => {
  for (let i = 0; i < siteCount; i++) {
    const site = getSite(i, groupIndex);
    sites.push(site);
  }
  data.forEach((item) => {
    const str = item.id.split('-');
    const site = sites.find((subItem) => subItem.id === str[0]);
    const typedbs = str[1];
    const db = str[2];
    const typedbsObj = typedbs === 'memory' ? memorydbs : physicdbs;
    const belong = typedbsObj[db].belong;
    const dbObj = site[typedbs][belong].dbs.find((subItem) => subItem.name === db);
    if (str[3][0] === 'a') {
      dbObj.active.push(item.id);
      item.status !== 'normal' && (site[typedbs].podFault++);
    }
    if (str[3][0] === 's') {
      dbObj.standby.push(item.id);
      item.status !== 'normal' && (site[typedbs].podFault++);
    }
    if (str[3][0] === 'r') {
      dbObj.redundancy.push(item.id);
      item.status !== 'normal' && (site[typedbs].exceptionChange++);
    }
    site[typedbs].podCount++;
  });
};

const getTestData = (groupIndex = 0) => {
  const result = {
    id: `siteGroup${groupIndex + 1}`,
    data: [],
    relationship: [],
    sites: [],
  };
  getRelationshipAndData(result.relationship, result.data, groupIndex, memorydbs, 'memory');
  getRelationshipAndData(result.relationship, result.data, groupIndex, physicdbs, 'physic');
  getSites(result.sites, result.data, groupIndex);
  return result;
};

