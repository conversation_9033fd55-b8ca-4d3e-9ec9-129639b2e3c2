import { models, textures } from '../../..';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { bindDblclickEvents } from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/utils.js';

const A3D = getA3D();

export const getMMOverviewSetting = (data, w3d) => {
  const modelMap = {
    thirdParty: models.thirdParty.children[0].clone(),
    channel: models.channel.children[0].clone(),
    strip: models.strip.children[0].clone(),
  };
  const matMap = {
    thirdParty: {
      normal: w3d.themeManager.referStyle('universal-normal'),
      normalUnselect: w3d.themeManager.referStyle('universal-normal-unselect'),
      alarm: w3d.themeManager.referStyle('universal-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('universal-alarm-unselect'),
    },
    channel: {
      normal: w3d.themeManager.referStyle('app-normal'),
      normalUnselect: w3d.themeManager.referStyle('app-normal-unselect'),
      alarm: w3d.themeManager.referStyle('app-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('app-alarm-unselect'),
    },
    strip: {
      ground: {
        normal: w3d.themeManager.referStyle('site-ground'),
        hover: w3d.themeManager.referStyle('site-ground-hover'),
        select: w3d.themeManager.referStyle('site-ground-select'),
      },
      group: {
        normal: w3d.themeManager.referStyle('site-group-normal'),
        alarm: w3d.themeManager.referStyle('site-group-alarm'),
      },
    },
    ground: w3d.themeManager.referStyle('ground'),
    blockGround: w3d.themeManager.referStyle('block-ground'),
    blockGroundSelect: w3d.themeManager.referStyle('block-ground-select'),
    blockGroundLine: w3d.themeManager.referStyle('block-ground-line'),
    blockGroundLineSelect: w3d.themeManager.referStyle('block-ground-line-select'),
    businessGround: w3d.themeManager.referStyle('business-ground'),
    businessGroundSelect: w3d.themeManager.referStyle('business-ground-select'),

  };
  return { modelMap, matMap };
};

const createCard = (data, modelItem, w3d, componentManager, type) => {
  const id = data.id;
  const cardType = 'podDrillDown';
  componentManager.cardManager.updateCard(
    cardType,
    { id },
    modelItem,
  );
  const card = componentManager.cardManager.cards[cardType][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showCommonTip({
    data,
    dom: domContainer,
    type,
  });
};

const createStripCard = (id, name, modelItem, w3d, componentManager) => {
  let type = 'strip';
  const cardType = 'podDrillDown';
  componentManager.cardManager.updateCard(
    cardType,
    { id },
    modelItem,
  );
  const card = componentManager.cardManager.cards[cardType][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  let data = {};
  data.name = name;
  componentManager.events.showCommonTip({
    data,
    dom: domContainer,
    type,
  });
};


const createAlarmHoverCard = (dataItem, groupItem, w3d, componentManager, event) => {
  const id = dataItem.id;
  const type = 'groupDetail';
  componentManager.cardManager.updateCard(
    type,
    { id },
    groupItem,
  );
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showStripGroupTip({
    data: dataItem,
    dom: domContainer,
    event,
  });
};

function mmHoverEvent(componentManager, podItem, data, w3d, e) {
  componentManager.events.hoverInObject(podItem);
  if (data.alarmCsn && data.alarmCsn.length > 0) {
    createAlarmHoverCard(data, podItem, w3d, componentManager, e);
  } else {
    createCard(data, podItem, w3d, componentManager, 'channel');
  }
}

const bindPodHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(info.object, (data, appItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!isSelected) {
        componentManager.events.hoverInObject(appItem);
      }
      if (data.alarmCsn && data.alarmCsn.length > 0) {
        createAlarmHoverCard(data, appItem, w3d, componentManager, e);
      } else {
        createCard(data, appItem, w3d, componentManager, 'app');
      }
    }, (data, podItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!isSelected) {
        mmHoverEvent(componentManager, podItem, data, w3d, e);
      }
    }, (data, vmItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      componentManager.events.hoverInObject(vmItem);
      if (data.alarmCount > 0) {
        data.isManager = true;
        createAlarmHoverCard(data, vmItem, w3d, componentManager, e);
      } else {
        createCard(data, vmItem, w3d, componentManager, 'strip');
      }
    }, (id, stripNameItem)=>{
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if ( stripNameItem.material.map.userData.detailName !== stripNameItem.material.map.userData.drawName) {
        createStripCard(id, stripNameItem.material.map.userData.detailName, stripNameItem, w3d, componentManager);
      }
    },
    );
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(info.object, (data, appItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!isSelected) {
        componentManager.events.hoverOutObject(appItem);
      }
      if (data.alarmCsn && data.alarmCsn.length > 0) {
        componentManager.cardManager.removeCard({ id: data.id }, 'groupDetail');
      } else {
        componentManager.cardManager.removeCard({ id: data.id }, 'podDrillDown');
      }
    }, (data, podItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!isSelected) {
        componentManager.events.hoverOutObject(podItem);
      }
      if (data.alarmCsn && data.alarmCsn.length > 0) {
        componentManager.cardManager.removeCard({ id: data.id }, 'groupDetail');
      } else {
        componentManager.cardManager.removeCard({ id: data.id }, 'podDrillDown');
      }
    }, (data, vmItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.events.hoverOutObject(vmItem);
      if (data.alarmCount > 0) {
        componentManager.cardManager.removeCard({ id: data.id }, 'groupDetail');
      } else {
        componentManager.cardManager.removeCard({ id: data.id }, 'strip');
      }
    }, (id, stripNameItem)=>{
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if ( stripNameItem.material.map.userData.detailName !== stripNameItem.material.map.userData.drawName) {
        componentManager.cardManager.removeCard({ id }, 'strip');
      }
    },
    );
    w3d.agent.renderOnce();
    return true;
  });
};

const bindPodClickEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'click', (e, info) => {
    compIns.select(info.object, (data, appItem, isSelectApp) => {
      componentManager.events.showPanel({
        type: 'app',
        id: data.id,
        name: data.name,
        indicatorList: data.indicatorList || [],
      });
      componentManager.cardManager.removeCard({ id: data.id }, 'podDrillDown');
      componentManager.events.selectObject(appItem);
    }, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'channel',
        id: data.id,
        name: data.name,
        indicatorList: data.indicatorList || [],
      });
      componentManager.cardManager.removeCard({ id: data.id }, 'podDrillDown');
      componentManager.events.selectObject(podItem);
    }, (data, stripItem) => {
      componentManager.events.showPanel({
        type: 'strip',
        id: data.id,
        name: data.name,
        indicatorList: data.indicatorList || [],
      });
      componentManager.events.clickStrip(data);
      componentManager.events.selectObject(null);
    }, () => {
      componentManager.events.selectNull();
    });
    return true;
  });
};

const bindStripClickEvents = (compIns, w3d, componentManager) => {
  bindDblclickEvents(compIns, (e, info) => {
    //  bindDblclickEvents
  }, (e, info) => {
    const strip = compIns.isStripGroup(info.object);
    if (!strip) {
      return;
    }
    componentManager.events.dbClickStrip(strip.userData.data);
  });
};

export const initDefault = (compIns, w3d, componentManager) => {
  const isGray = compIns.getIsGray();
  const clickSelected = compIns.getClickSelected();
  const defaultSelected = compIns.getDefaultSelected();
  compIns.select(null);
  componentManager.events.selectObject(null);
  componentManager.events.selectApp(null);
  if (defaultSelected && (isGray || !clickSelected)) {
    compIns.select(defaultSelected, (data, appItem, isSelectApp) => {
      if (isSelectApp) {
        componentManager.events.selectApp(appItem);
      } else {
        componentManager.events.selectObject(appItem);
      }
    });
  }
  if (clickSelected) {
    compIns.select(clickSelected, null, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'channel',
        id: data.dnId,
        name: data.name,
      });
      componentManager.events.selectObject(podItem);
    }, (data, vmItem) => {
      componentManager.events.showPanel({
        type: 'vm',
        id: data.dnId,
        name: data.name,
      });
      componentManager.events.selectObject(vmItem);
    });
  }

  const cardContainerMap = compIns.getCardMap();
  Object.keys(cardContainerMap).forEach((type) => {
    const container = cardContainerMap[type];
    const id = type;
    const cardType = 'podDrillDownDeital';
    componentManager.cardManager.removeCard({ id }, cardType);
    componentManager.cardManager.updateCard(
      cardType,
      { id },
      container,
    );
    const card = componentManager.cardManager.cards[cardType][id];
    const d2Object = card.children[0];
    const domContainer = d2Object.element.children[0];
    componentManager.events.showPodDrillDownDeital({
      data: {},
      dom: domContainer,
      type,
    });
  });
};

export const bindMMOverviewEvents = (compIns, w3d, componentManager) => {
  initDefault(compIns, w3d, componentManager);
  bindPodHoverEvents(compIns, w3d, componentManager);
  bindPodClickEvents(compIns, w3d, componentManager);
  bindStripClickEvents(compIns, w3d, componentManager);
};
