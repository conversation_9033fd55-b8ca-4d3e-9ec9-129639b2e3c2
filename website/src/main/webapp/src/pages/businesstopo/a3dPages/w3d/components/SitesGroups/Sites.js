import ExtrudeShape from '@pages/businesstopo/a3dPages/w3d/components/common/ExtrudeShape';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import SiteItem from './SiteItem';
import { getGroundOptions, getWallLine } from '../utils';

const A3D = getA3D();

export default class Sites extends A3D.C.Object3D {
  #data;
  #sizeSetting;
  #modelMap;
  #matMap;
  constructor(data, sizeSetting, modelMap, matMap) {
    super();

    this.#data = data;
    this.#sizeSetting = sizeSetting;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    const { layout, sitesDepth } = this.#computedSiteItemLayout();
    this.#initGround(sizeSetting.sitesWidth, sitesDepth);
    this.#initSiteItems(layout);
  }

  update(data) {
    this.#data = data;
    this.children[1].children.forEach((siteItem, index) => {
      if (data.siteList[index]) {
        siteItem.update(data.siteList[index]);
      }
    });
  }

  setGrayUpgrade(ids) {
    this.children[1].children.forEach((siteItem) => {
      siteItem.setGrayUpgrade(ids);
    });
  }

  /**
   * 根据站点组的固定宽度，自动算出等宽深的站点节点的布局
   * 并算出该布局下当前站点组的深度
   * @returns
   */
  #computedSiteItemLayout() {
    const allCount = this.#data.siteList.length;
    const curSiteColumn = Math.ceil(Math.sqrt(allCount));
    const {
      siteWidth, sitesWidth, siteWidthSpaceRatio, siteDepth,
    } = this.#sizeSetting;
    const space = curSiteColumn > 1 ?
      (sitesWidth - siteWidth * (curSiteColumn + siteWidthSpaceRatio * 2)) / (curSiteColumn - 1) : 0;
    const layout = [];
    const len = Math.ceil(allCount / curSiteColumn);

    const depthSpace = siteDepth * 0.1;
    const sitesDepth = (depthSpace + siteDepth) * len + depthSpace;
    for (let i = 0; i < len; i++) {
      const curRowCount = i === len - 1 ? allCount - curSiteColumn * i : curSiteColumn;
      for (let j = 0; j < curRowCount; j++) {
        layout.push({
          x: (space + siteWidth) * (-(curRowCount - 1) / 2 + j),
          y: 0,
          z: (depthSpace + siteDepth) * (-(len - 1) / 2 + i),
        });
      }
    }
    return { layout, sitesDepth };
  }

  /**
   * 根据长宽初始化站点组的 地板、虚线
   * @param {*} width
   * @param {*} depth
   */
  #initGround(width, depth) {
    const groundGroup = new A3D.C.Group();
    groundGroup.name = 'siteGround';
    this.add(groundGroup);

    const ground = this.#getGround(width, depth);
    const groundLine = this.#getGroundLine(width, depth);
    groundGroup.add(ground, groundLine);
  }

  #getGround(width, depth) {
    const groundOptions = getGroundOptions(width, depth);
    groundOptions.depth = 0.01;
    const mat = this.#matMap.sites.ground;

    const ground = new ExtrudeShape(groundOptions, mat);
    return ground;
  }

  #getGroundLine(width, depth) {
    const groundOptions = getGroundOptions(width, depth);
    groundOptions.close = true;

    const material = this.#matMap.sites.line;
    const groundLine = getWallLine(material, groundOptions);
    groundLine.children[0].renderOrder = 1;
    return groundLine;
  }

  /**
   * 初始化站点组内的所有站点
   * @param {*} layout
   */
  #initSiteItems(layout) {
    const siteItemsGroup = new A3D.C.Group();
    siteItemsGroup.name = 'siteItemsGroup';
    this.add(siteItemsGroup);
    this.#data.siteList.forEach((siteItemData, index) => {
      const siteItem = new SiteItem(siteItemData, this.#sizeSetting, this.#modelMap, this.#matMap);

      siteItemsGroup.add(siteItem);

      siteItem.position.copy(layout[index]);
    });
  }
}
