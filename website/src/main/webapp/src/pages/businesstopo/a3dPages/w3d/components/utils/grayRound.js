export const grayRoundVert = `
#include <common>
#include <logdepthbuf_pars_vertex>
varying vec3 vpos;
void main() {
  vpos = position;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  #include <logdepthbuf_vertex>
}
`;

export const grayRoundFrag = `
varying vec3 vpos;
uniform vec3 uColor;
uniform float uTime;
uniform float uRadius;
uniform float uThickness;
uniform float uBRadius;
uniform float uLightThickness;
uniform bool uIsRound;
uniform bool uIsGray;
#include <logdepthbuf_pars_fragment>

bool isBlockBorder() {
  float x = abs(vpos.x);
  float z = abs(vpos.z);
  float nRadius = uRadius / sqrt(2.0) * 1.2;
  if (x > nRadius && x < nRadius + uThickness && z < nRadius - uBRadius) {
    return true;
  }
  if (z > nRadius && z < nRadius + uThickness && x < nRadius - uBRadius) {
    return true;
  }
  if (z > nRadius-uBRadius  && x > nRadius-uBRadius) {
    vec2 dir = vec2(x - (nRadius-uBRadius), z - (nRadius-uBRadius));
    if (length(dir) > uBRadius && length(dir) < uBRadius + uThickness) {
      return true;
    }
  }
  return false;
}

bool isRoundBorder() {
  float x = abs(vpos.x);
  float z = abs(vpos.z);
  vec2 dir = vec2(x, z);
  if (length(dir) > uRadius && length(dir) < uRadius + uThickness) {
    return true;
  }
  return false;
}

float getBlockLight() {
  float x = abs(vpos.x);
  float z = abs(vpos.z);
  float nRadius = uRadius / sqrt(2.0) * 1.2;
  float startDis = nRadius + uThickness;
  if (x > startDis && x < startDis + uLightThickness && z < nRadius - uBRadius) {
    return 1.0 - (x - startDis) / uLightThickness;
  }
  if (z > startDis && z < startDis + uLightThickness && x < nRadius - uBRadius) {
    return 1.0 - (z - startDis) / uLightThickness;
  }
  if (z > nRadius-uBRadius && x > nRadius-uBRadius) {
    vec2 dir = vec2(x - (nRadius-uBRadius), z - (nRadius-uBRadius));
    if (length(dir) > uBRadius + uThickness && length(dir) < uBRadius + uThickness + uLightThickness) {
      return 1.0 - (length(dir) - uBRadius - uThickness) / uLightThickness;
    }
  }
  return 0.0;
}

float getRoundLight() {
  float x = abs(vpos.x);
  float z = abs(vpos.z);
  vec2 dir = vec2(x, z);
  if (length(dir) > uRadius + uThickness && length(dir) < uRadius + uThickness + uLightThickness) {
    return 1.0 - (length(dir) - uRadius - uThickness) / uLightThickness;
  }
  return 0.0;
}

void main() {
  #include <logdepthbuf_fragment>

  if (uIsRound) {
    if (isRoundBorder()) {
      gl_FragColor = vec4(uColor, 1.0);
    } else if (uIsGray) {
      float ratio = (sin(uTime) + 1.0) * 0.5;
      float opacity = getRoundLight() * 0.5 * ratio;
      gl_FragColor = vec4(uColor, opacity);
    }
  } else {
    if (isBlockBorder()) {
      gl_FragColor = vec4(uColor, 1.0);
    } else if (uIsGray) {
      float ratio = (sin(uTime) + 1.0) * 0.5;
      float opacity = getBlockLight() * 0.5 * ratio;
      gl_FragColor = vec4(uColor, opacity);
    }
  }
}
`;
