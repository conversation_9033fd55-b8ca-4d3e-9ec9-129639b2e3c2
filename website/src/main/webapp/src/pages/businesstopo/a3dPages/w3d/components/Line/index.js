import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { Line2 } from 'three/examples/jsm/lines/Line2';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
const A3D = getA3D();

const positions = [];

export default class Line extends A3D.C.Group {
  #lineGeometry;

  #points;

  constructor(props) {
    super();
    if (props) {
      this.#lineGeometry = props.lineGeometry;
      this. #points = props.points;
      this.parameters = A3D.util.extend(true, {
        linewidth: 1,
        onChangeCallback: (modelIns) => modelIns,
        onShowCallback: (modelIns) => modelIns,
      }, props);
    }
  }

  add(data = {}, ...rest) {
    if (data.isObject3D) {
      super.add(data, ...rest);
      return this;
    }
    this.#addLine(data);
    return this;
  }

  set(line, data) {
    if (line?.parent === this) {
      this.parameters.onChangeCallback(data, line, line.userData.flow);
    }
  }

  hide(line, callback) {
    let geo = line.geometry;
    geo.instanceCount = -Infinity;
    if (callback) {
      callback(line);
    }
  }

  destoryOneLine(childName) {
    const object = this.getObjectByName(childName);
    if (!object) {
      return;
    }
    object.traverse((child) => {
      if (child.isMesh) {
        child?.geometry.dispose();
        child?.material.dispose();
      }
    });
    this.remove(object);
  }

  show(line) {
    let geo = line.geometry;
    let instanceCount = geo.instanceCount;
    if (instanceCount !== -Infinity) {
      return;
    }
    geo.instanceCount = 0;
    line.userData.pointIndex = 0;
  }

  getPointAt(line, percent = 0.5) {
    let points = line.userData.points;
    let index = Math.floor(points.length * percent);
    return points[index];
  }

  destroy() {
    this.children[0].children.forEach((item) => {
      if (item.geometry) {
        item.geometry.dispose();
      }
    });
    this.remove(...this.children);
    this.parameters = null;
  }

  hoverIn(line, callback) {
    let item = this.#getItem(line);
    if (item && callback) {
      callback(item);
    }
  }

  hoverOut(line, callback) {
    let item = this.#getItem(line);
    if (item && callback) {
      callback(item);
    }
  }

  #getItem(object3D) {
    let node3D = null;
    if (object3D instanceof A3D.C.Object3D) {
      node3D = object3D;
      while (node3D && node3D.parent !== this) {
        node3D = node3D.parent;
      }
      node3D = node3D || null;
    }
    return node3D;
  }

  #addLine(data) {
    let parameters = this.parameters;
    let points;
    let geo;

    if (!this.#lineGeometry) {
      points = this.parameters.getPoints[data.lineType](data.start, data.end);
      geo = new LineGeometry();
    } else {
      geo = this.#lineGeometry;
      points = this.#points;
    }
    geo.instanceCount = 0;
    this.#getPositions(points);
    geo.setPositions(positions);
    const material = new LineMaterial({
      transparent: true,
      depthWrite: false,
      color: '#fff',
      linewidth: parameters.linewidth,
      opacity: 0.8,
    });
    material.resolution.set(window.innerWidth, window.innerHeight);
    let line = new Line2(geo, material);
    line.userData.points = points;
    line.userData.pointIndex = 0;
    line.userData.data = data;
    this.#addFlow(line, data.end);
    line.name = data.id;
    line.renderOrder = 2;
    if (data.type !== 'north') {
      line.renderOrder = 6;
    }
    line.onBeforeRender = () => {
      if (geo.instanceCount === -Infinity) {
        return;
      }
      let pointIndex = line.userData.pointIndex;
      let curIndex = (pointIndex + 1) % points.length;
      let nextIndex = (curIndex + 1) % points.length;
      let curPos = points[curIndex];
      let nextPos = points[nextIndex];
      let lastNumber = 20;
      if (data.channelData) {
        lastNumber = Math.max(30, points.length * 0.4);
      }
      if (nextIndex > 4 && nextIndex < points.length - lastNumber) {
        !line.userData.flow.visible && (line.userData.flow.visible = true);
      } else {
        line.userData.flow.visible = false;
      }
      if (nextIndex === 20) {
        if (this.parameters.onShowCallback) {
          this.parameters.onShowCallback(line);
        }
      }
      line.userData.flow.position.copy(curPos);
      line.userData.flow.lookAt(nextPos);
      line.userData.pointIndex = curIndex;
      geo.instanceCount += points.length / 20;
    };
    this.add(line);
    this.parameters.onChangeCallback(data, line, line.userData.flow);
  }

  #addFlow(line) {
    let geo = new A3D.C.CapsuleGeometry(0.03, 0.8, 4, 8);
    geo.translate(0, 0.4, 0);
    geo.rotateX(Math.PI / 2);
    let mat = this.#getMat();
    let mesh = A3D.mesh(geo, mat);
    line.add(mesh);
    mesh.renderOrder = 7;
    line.userData.flow = mesh;
  }

  #getMat() {
    let options = {
      side: A3D.C.DoubleSide,
      transparent: true,
      depthWrite: false,
      uniformsExt: { start: new A3D.C.Vector3(0, 0, 0) },
      glsl: {
        vert: {
          defines:
          `
          varying vec2 vUv; 
          varying vec3 vPos;
          `,
          logic: `
          vUv = uv;
          vPos = position;
          `,
        },
        frag: {
          defines: `
          varying vec2 vUv;
          varying vec3 vPos;
          uniform vec3 start;
          `,
          replaces: {
            'vec4 diffuseColor = vec4( diffuse, opacity );':
            'vec4 diffuseColor = vec4( diffuse, distance(vPos, start) / 0.8 );',
          },
        },
      },
    };
    let mat = A3D.mat('AgentMaterial:Basic', options);
    return mat;
  }

  #getPositions(points) {
    positions.length = 0;
    points.forEach((point) => {
      positions.push(point.x, point.y, point.z);
    });
    return positions;
  }
}
