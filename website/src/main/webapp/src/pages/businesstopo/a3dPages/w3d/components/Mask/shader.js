import A3D from '@a3d/a3d';
export const boxShader = {
  uniforms: {
    color: { value: new A3D.C.Color('red') },
    height: { value: 10 },
    radius: { value: 1 },
    opacity: { value: 1 },
  },
  side: 2,
  depthWrite: false,
  transparent: true,
  vertexShader: `
    #include <common>
    #include <logdepthbuf_pars_vertex>
    varying vec2 vuv;
    varying vec3 vpos;
    void main(){
      vuv = uv;
      vpos = position;
      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
      #include <logdepthbuf_vertex>
    }
  `,
  fragmentShader: `
    uniform float height;
    uniform float radius;
    uniform vec3 color;
    uniform float opacity;

    varying vec2 vuv;
    varying vec3 vpos;
    #include <logdepthbuf_pars_fragment>
    void main(){
        #include <logdepthbuf_fragment>
        if(vpos.y == height * 0.5) {
          discard;
        } else if (vpos.y == height * -0.5) {
          vec2 pos = vec2(vpos.x, vpos.z);
          float curRadius = pow(length(pos) / radius, 2.0);
          gl_FragColor = vec4(color, curRadius);
        } else {
          float startOpacity = 0.8;
          float h = vpos.y + height * 0.5;
          gl_FragColor = vec4(color, pow(startOpacity - (h / height) * startOpacity, 2.0)*opacity);
        }
    }
    `,
};
