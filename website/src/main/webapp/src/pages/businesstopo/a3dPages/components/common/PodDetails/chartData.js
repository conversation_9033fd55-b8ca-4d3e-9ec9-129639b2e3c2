import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/podDetails';
registerResource(i18n, 'control2D');
import dateJSON from './date.json';
import { getChartsData } from './utils';
const chartTheme = 'dark';
const tpsData1 = {
  title: 'CAPS',
  name: 'AreaChart',
  option: {
    theme: chartTheme,
    smooth: true,
    area: true,
    legend: { show: true },
    padding: [48, 16, 48, 0],
    data: getChartsData(dateJSON),
    xAxis: {
      data: 'time',
      fullGrid: true,
      interval: 100,
    },
    yAxis: {
      name: getMessage('control2D.charts.yName'),
      nameTextStyle: { padding: [20, -20] },
    },
  },
};

const tpsData2 = {
  // TPS面积图
  title: 'CAPS',
  name: 'Area<PERSON>hart',
  option: {
    theme: chartTheme,
    smooth: true,
    area: true,
    legend: { show: true },
    padding: [48, 16, 48, 0],
    data: [
      {
        time: '8:00', 今天: 25, 前一天: 12, 前一周: 8,
      },
      {
        time: '9:00', 今天: 35, 前一天: 25, 前一周: 20,
      },
      {
        time: '10:00', 今天: 30, 前一天: 20, 前一周: 15,
      },
      {
        time: '11:00', 今天: 28, 前一天: 10, 前一周: 5,
      },
      {
        time: '12:00', 今天: 17, 前一天: 12, 前一周: 7,
      },
      {
        time: '13:00', 今天: 28, 前一天: 15, 前一周: 10,
      },
      {
        time: '14:00', 今天: 35, 前一天: 22, 前一周: 17,
      },
      {
        time: '15:00', 今天: 33, 前一天: 25, 前一周: 20,
      },
      {
        time: '16:00', 今天: 28, 前一天: 21, 前一周: 16,
      },
    ],
    xAxis: {
      data: 'time',
      fullGrid: true,
    },
    yAxis: { name: '每秒事务数（CAPS）' },
  },
};
const statusInfoData = [
  { title: getMessage('control2D.info.state'), info: getMessage('control2D.info.value') },
  { title: getMessage('control2D.tabs.successRate'), info: '75%' },
  { title: getMessage('control2D.info.total'), info: '1.2 (GB)' },
  { title: getMessage('control2D.info.podType'), info: 'cbpgmdb' },
  { title: getMessage('control2D.info.owning'), info: 'kwephis12345' },
  { title: getMessage('control2D.info.time'), info: '2023-10-10' },
];
const tabsInfo = ['TPS', 'CAPS', getMessage('control2D.tabs.successRate'), getMessage('control2D.tabs.latency')];
export {
  tpsData1, tpsData2, statusInfoData, tabsInfo,
};
