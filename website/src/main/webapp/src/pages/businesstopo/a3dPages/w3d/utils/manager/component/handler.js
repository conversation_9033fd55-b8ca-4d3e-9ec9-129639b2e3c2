import Halo from '@pages/businesstopo/a3dPages/w3d/components/Halo';
import Line from '@pages/businesstopo/a3dPages/w3d/components/Line';
import SitesDatabase from '@pages/businesstopo/a3dPages/w3d/components/SitesDatabase';
import SitesDrillDown from '@pages/businesstopo/a3dPages/w3d/components/SitesDrillDown';
import PodDrillDown from '@pages/businesstopo/a3dPages/w3d/components/PodDrillDown';
import SitesGroups from '@pages/businesstopo/a3dPages/w3d/components/SitesGroups';
import IncidentPerception from '@src/pages/businesstopo/a3dPages/w3d/components/IncidentView/Perception';
import IncidentAnalysis from '@src/pages/businesstopo/a3dPages/w3d/components/IncidentView/Analysis';
import {textures} from '@pages/businesstopo/a3dPages/w3d/utils';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {
  addBusiness,
  bindBusinessEvents,
} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/business';
import {
  bindLineEvents, createLine, getBezier1, getBezier2, getLineData, updateLine,
} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/line';
import {bindSiteGroupsEvents, getSiteSetting} from './helper/site';
import {bindSiteEvents, getSitesDatabaseSetting} from './helper/sitesDatabase';
import {bindSiteDrillDownEvents, getSitesDrillDownSetting} from './helper/sitesDrillDown';
import {bindPodDrillDownEvents, getPodDrillDownSetting} from './helper/podDrillDown';
import {bindIncidentViewEvents, getIncidentViewSetting} from './helper/incidentView';
import {initSystemName} from './helper/utils';
import {
  bindMMOverviewEvents,
  getMMOverviewSetting,
} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/mmOverview';
import MMOverview from '@pages/businesstopo/a3dPages/w3d/components/MMOverview';
import HostDrillDown from '@pages/businesstopo/a3dPages/w3d/components/HostDrillDown';

const A3D = getA3D();

export const handler = {
  add: {
    // 总览视图
    north: (data, wrapperGroup, w3d, componentManager) => {
      const matMap = {};
      data.forEach((item) => {
        const businessId = item.businessId;
        if (!matMap[businessId]) {
          matMap[businessId] = w3d.themeManager.referStyle(businessId);
        }
      });
      let disc = addBusiness(data, w3d, componentManager, matMap, 'top');
      wrapperGroup.add(disc);
      disc.children[0].position.y = -1.3;
      disc.position.y = 16;
      disc.rotateX(0.3);
      disc.name = 'north';
      bindBusinessEvents(disc, componentManager, w3d);
      let firstApp = disc.children[1].children[0];
      componentManager.comps.halo.addSubSelectHalo(textures.select.subSelect, firstApp, -0.6);
      A3D.DOM.trigger(w3d.agent, 'click', {mesh: firstApp});
      disc.enableAnimation(true);
      componentManager.store.data.northBusiness = data[0];

      initSystemName('northServiceSystem', 'center', disc, wrapperGroup, {w3d, componentManager});
      return disc;
    },
    south: (data, wrapperGroup, w3d, componentManager) => {
      const matMap = {};
      data.forEach((item) => {
        const businessId = item.businessId;
        if (!matMap[businessId]) {
          matMap[businessId] = w3d.themeManager.referStyle(businessId);
        }
      });
      let disc = addBusiness(data, w3d, componentManager, matMap, 'bottom');
      wrapperGroup.add(disc);
      disc.children[0].position.y = -1.5;
      disc.position.y = -11.5;
      disc.rotateX(-0.06);
      disc.name = 'south';
      bindBusinessEvents(disc, componentManager, w3d);
      A3D.DOM.trigger(w3d.agent, 'click', {mesh: disc.children[1].children[0]});
      disc.enableAnimation(true);
      componentManager.store.data.southBusiness = data[0];

      initSystemName('southServiceSystem', 'bottom', disc, wrapperGroup, {w3d, componentManager});
      return disc;
    },
    site: (data, wrapperGroup, w3d, componentManager) => {
      const compSize = {
        width: 48,
        depth: 24,
        siteDepthRatio: 1,
        siteModelRatio: 0.36,
      };
      const {modelMap, matMap} = getSiteSetting(data, w3d);
      const compIns = new SitesGroups(data, compSize, modelMap, matMap, w3d);
      compIns.rotateX(Math.PI * 0.2);
      bindSiteGroupsEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);

      initSystemName('CBS', 'center', compIns, wrapperGroup, {w3d, componentManager});
      return compIns;
    },
    halo: (data, wrapperGroup, w3d, componentManager) => {
      let halo = new Halo({animationOption: {animation: new A3D.Animation(w3d.agent)}});
      wrapperGroup.add(halo);
      return halo;
    },
    line: (data, wrapperGroup, w3d, componentManager, isInit = true) => {
      let dataNew = getLineData(componentManager, data, data.switchBusiness, componentManager.store.data.selectBusiness);
      let line = componentManager.comps.line || new Line({
        linewidth: 1,
        onChangeCallback: (_data, lineMesh, flowMesh) => {
          updateLine(_data, lineMesh, flowMesh, w3d);
        },
        onShowCallback: (_line) => {
          _line.children[1]?.traverse((item) => {
            item.visible = true;
          });
        },
        getPoints: {
          1: (start, end) => getBezier1(start, end),
          2: (start, end) => getBezier2(start, end),
        },
      });
      if (componentManager.store.data.canUpdateLine) {
        createLine(componentManager, dataNew, line, w3d);
      }
      if (isInit) {
        wrapperGroup.add(line);
        bindLineEvents(line, componentManager, w3d);
      }
      const totalSiteListsLength = componentManager.store.data.site.reduce((total, siteTeam) => {
        return total + siteTeam.siteList.length;
      }, 0);
      const MAX_SITE_LENGTH = 4;
      if (totalSiteListsLength > MAX_SITE_LENGTH) {
        let newChildren = line.children.filter(item => item.userData.data.type === componentManager.store.data.selectBusiness);
        line.children = newChildren;
      }
      return line;
    },

    // 数据库视图
    sitesDatabase: (data, wrapperGroup, w3d, componentManager) => {
      const compSize = {
        width: 60,
        depth: 28,
        offset: -6,
      };
      const {modelMap, matMap} = getSitesDatabaseSetting(data, w3d);
      const compIns = new SitesDatabase(data, compSize, modelMap, matMap, w3d);

      bindSiteEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },
    // mm条带
    mmnorth: (data, wrapperGroup, w3d, componentManager) => {
      w3d.isMM = true;
      const matMap = {};
      data.forEach((item) => {
        const businessId = item.businessId;
        if (!matMap[businessId]) {
          matMap[businessId] = w3d.themeManager.referStyle(businessId);
        }
      });
      let disc = addBusiness(data, w3d, componentManager, matMap, 'top');
      wrapperGroup.add(disc);
      disc.children[0].position.y = -1.3;
      disc.position.y = 16;
      disc.position.z = 3;
      disc.rotateX(-0.42);
      disc.name = 'north';
      bindBusinessEvents(disc, componentManager, w3d);
      let firstApp = disc.children[1].children[0];
      A3D.DOM.trigger(w3d.agent, 'click', {mesh: firstApp});
      disc.enableAnimation(true);
      componentManager.store.data.northBusiness = data[0];
      return disc;
    },
    // 站点下钻视图
    sitesDrillDown: (data, wrapperGroup, w3d, componentManager) => {
      let compSize = {
        width: 48,
        depth: 30,
        offset: -2,
      };
      if (data.isMM) {
        compSize = {
          width: 48 * 0.9,
          depth: 30 * 0.8,
          offset: 4,
        };
      }
      const {modelMap, matMap} = getSitesDrillDownSetting(data, w3d);
      const compIns = new SitesDrillDown(data, compSize, modelMap, matMap, w3d);

      bindSiteDrillDownEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },

    // 网元下钻视图
    podDrillDown: (data, wrapperGroup, w3d, componentManager) => {
      const compSize = {
        width: 50,
        depth: 16,
        offset: 0,
      };
      const {modelMap, matMap} = getPodDrillDownSetting(data, w3d);
      const compIns = new PodDrillDown(data, compSize, modelMap, matMap, w3d);
      bindPodDrillDownEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },
    // 主机下钻视图
    hostDrillDown: (data, wrapperGroup, w3d, componentManager) => {
      const compSize = {
        width: 50,
        depth: 16,
        offset: 0,
      };
      const {modelMap, matMap} = getPodDrillDownSetting(data, w3d);
      const compIns = new HostDrillDown(data, compSize, modelMap, matMap, w3d);
      bindPodDrillDownEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },
    mmOverview: (data, wrapperGroup, w3d, componentManager) => {
      const compSize = {
        width: 50,
        depth: 20,
        offset: -5,
      };
      const {modelMap, matMap} = getMMOverviewSetting(data, w3d);
      const compIns = new MMOverview(data, compSize, modelMap, matMap, componentManager, w3d);
      compIns.children[0].children[2].rotateX(0.1);
      bindMMOverviewEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },
    // 智能Incident四层视图
    incidentView: (data, wrapperGroup, w3d, componentManager) => {
      const panelSize = {
        width: 90,
        depth: 20,
        offset: 0,
      };
      const {modelMap, matMap} = getIncidentViewSetting(w3d);
      let compIns;
      if (data.stage === 1) {
        compIns = new IncidentPerception(data, panelSize, modelMap, matMap, w3d);
      } else {
        compIns = new IncidentAnalysis(data, panelSize, modelMap, matMap, w3d);
      }
      bindIncidentViewEvents(compIns, w3d, componentManager);
      wrapperGroup.add(compIns);
      return compIns;
    },
  },
  update: {
    // 总览视图
    site: (data, wrapperGroup, w3d, componentManager) => {
      let site = componentManager.comps.site;
      if (site) {
        site.update(data);
      }
    },
    line: (data, wrapperGroup, w3d, componentManager) => {
      let dataNew = getLineData(componentManager, data, data.switchBusiness);
      const totalSiteListsLength = componentManager.store.data.site.reduce((total, siteTeam) => {
        return total + siteTeam.siteList.length;
      }, 0);
      const MAX_SITE_LENGTH = 4;
      if (totalSiteListsLength > MAX_SITE_LENGTH) {
        dataNew = dataNew.filter(item=>item.type === componentManager.store.data.selectBusiness);
      }
      let line = componentManager.comps.line;
      let newLine = [];
      dataNew.forEach((item) => {
        let _line = line.getObjectByName(`${item.siteId}-${item.businessId}-${item.type}`);
        line.set(_line, item);
        if (!_line) {
          newLine.push(item);
        }
      });
      newLine.switchBusiness = data.switchBusiness;
      if (newLine.length) {
        handler.add.line(newLine, wrapperGroup, w3d, componentManager, false);
      }
    },
    lineThird2Channel: (data, wrapperGroup, w3d, componentManager) => {
      const compIns = componentManager.comps.mmOverview;
      if (!compIns) {
        return;
      }

      compIns.updateLineThird2Channel(data);
    },
    north: (data, wrapperGroup, w3d, componentManager) => {
      let north = componentManager.comps.north;
      if (north) {
        north.update(data);
      }
    },
    south: (data, wrapperGroup, w3d, componentManager) => {
      let south = componentManager.comps.south;
      if (south) {
        south.update(data);
      }
    },
  },
  remove: {},
};
