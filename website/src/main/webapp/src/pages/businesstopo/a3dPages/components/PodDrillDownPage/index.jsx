import React, { useEffect, useRef } from 'react';
import { initW3D, updatePodDrillData, destroyW3D } from './podDrillDown3D';
import './podDetailCard.less';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import PropTypes from 'prop-types';

function PodDrillDown({ pageParams }) {
  const w3dContainerRef = useRef();
  const eventRef = useRef();
  useEffect(() => {
    if (pageParams.data) {
      if (pageParams.isInit) {
        eventRef.current = pageParams.events;
        initW3D(w3dContainerRef, { initData: pageParams.data, eventArr: pageParams.events, isMM:pageParams.isMM });
      } else {
        updatePodDrillData(pageParams.data);
      }
    }
  }, [pageParams]);

  useEffect(() => () => destroyW3D(eventRef.current), []);

  return (
    <div className="podDrillDownTopology">
      <div ref={w3dContainerRef} className="w3d_container" />
    </div>
  );
}

PodDrillDown.propTypes = {
  pageParams: PropTypes.shape({
    events: PropTypes.array,
    isInit:PropTypes.bool,
    data: PropTypes.shape({
      businessClusterList:PropTypes.array,
    }),
  }).isRequired,
};

export default PodDrillDown;
