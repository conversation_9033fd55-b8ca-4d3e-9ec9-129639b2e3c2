import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import ExtrudeShape from '../common/ExtrudeShape';
import FilletShape from '../common/FilletShape';
import {getObjectSize} from '../common/utils';
import {
  drawRoundedRect, getCanvasTex,
  getGroundOptions, getLine, getLine2, getTextPlane,
  getTypeItem,
  getWallLine, setDbStatus,
} from '../utils';
import {getPlanePoints} from '../utils/sitesDrillDown';
import Layout from './Layout';
import Layout2 from './Layout2';
import SiteLayout from './SiteLayout';
import SiteLayout2 from './SiteLayout2';

const A3D = getA3D();

const nameColorNormal = '#737375';
const nameColorSelect = '#4785e0';
const nameColorRelated = '#f5f5f5';

export default class SitesDrillDown extends A3D.C.Object3D {
  #data;

  #size;

  #modelMap;

  #matMap;

  #w3d;

  #animation;

  #sizeSetting = {};

  #sizeSetting2 = {};

  #podScaleRatio = 0.4;

  #podDefaultScale = 1;

  #podMaxSpace = 3.2;

  #podMinSpace = 1.8;

  #dbMap;

  #businessMap;

  #siteTeamMap;

  #siteItemMap;

  #selectedDbId;

  #positionSetting;

  constructor(data, size, modelMap, matMap, w3d) {
    super();
    this.#data = data;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#w3d = w3d;
    this.#animation = new A3D.Animation(this.#w3d.agent);

    const {width} = this.#size;
    this.#sizeSetting.groundHeight = width * 0.008;
    this.#sizeSetting.nameSize1 = width * 0.02;
    this.#sizeSetting.space1 = width * 0.015;
    this.#sizeSetting.nameSize2 = width * 0.015;
    this.#sizeSetting.space2 = width * 0.01;

    this.#sizeSetting2.spaceX = width * 0.012;
    this.#sizeSetting2.spaceZ = width * 0.018;
    this.#sizeSetting2.spaceY = width * 0.005;
    this.#sizeSetting2.nameSize = width * 0.02;
    this.#sizeSetting2.siteWidth = width * 0.092;
    this.#sizeSetting2.siteDepth = width * 0.08;
    this.#sizeSetting2.siteRadius = width * 0.004;

    this.#init();
  }

  hoverIn(object3D, callback, lineCallBack, siteCallback, businessCallback) {
    const dbItem = getTypeItem(object3D, 'db');
    if (dbItem) {
      const dbId = dbItem.userData.id;
      const selected = dbId === this.#selectedDbId;
      const data = this.#dbMap[dbId].data;
      if (callback) {
        callback(data, dbItem, selected);
      }
      return;
    }
    const lineItem = getTypeItem(object3D, 'line');
    if (lineItem) {
      const dbId = lineItem.userData.dbId;
      const siteId = lineItem.userData.siteId;
      if (lineCallBack) {
        lineCallBack({moTypeId: dbId, siteId}, lineItem);
      }
      return;
    }
    const siteItem = getTypeItem(object3D, 'site');
    if (siteItem) {
      const siteId = siteItem.userData.siteId;
      this.#setSiteItemStatus(siteId, 'hover');
      const data = this.#siteItemMap[siteId].data;
      if (siteCallback) {
        siteCallback(data, siteItem);
      }
    }
    const businessItem = getTypeItem(object3D, 'businessGround');
    if (businessItem) {
      const data = businessItem.userData._data;
      if (businessCallback) {
        businessCallback(data, businessItem);
      }
    }
  }

  hoverOut(object3D, callback, lineCallBack, siteCallback, businessCallback) {
    const dbItem = getTypeItem(object3D, 'db');
    if (dbItem) {
      const dbId = dbItem.userData.id;
      const selected = dbId === this.#selectedDbId;
      const data = this.#dbMap[dbId].data;
      if (callback) {
        callback(data, dbItem, selected);
      }
    }
    const lineItem = getTypeItem(object3D, 'line');
    if (lineItem) {
      const dbId = lineItem.userData.dbId;
      const siteId = lineItem.userData.siteId;
      if (lineCallBack) {
        lineCallBack({moTypeId: dbId, siteId}, lineItem);
      }
    }
    const siteItem = getTypeItem(object3D, 'site');
    if (siteItem) {
      const siteId = siteItem.userData.siteId;
      const status = siteItem.userData.status;
      this.#setSiteItemStatus(siteId, status);
      const data = this.#siteItemMap[siteId].data;
      if (siteCallback) {
        siteCallback(data, siteItem);
      }
    }

    const businessItem = getTypeItem(object3D, 'businessGround');
    if (businessItem) {
      const data = businessItem.userData._data;
      if (businessCallback) {
        businessCallback(data, businessItem);
      }
    }
  }

  select(object3D, callback, siteCallback) {
    const dbItem = getTypeItem(object3D, 'db');
    const lineItem = getTypeItem(object3D, 'line');
    const siteItem = getTypeItem(object3D, 'site');
    if (dbItem) {
      const dbId = dbItem.userData.id;
      if (dbId !== this.#selectedDbId) {
        const data = this.#dbMap[dbId].data;
        this.#selectDb(dbId || '');
        if (callback) {
          callback(data, dbItem);
        }
        this.#selectedDbId = dbId || '';
      }
    } else if (siteItem) {
      const siteId = siteItem.userData.siteId;
      const data = this.#siteItemMap[siteId].data;
      if (siteCallback) {
        siteCallback(data, siteItem);
      }
    } else {
      if (!lineItem) {
        this.#selectDb('');
        if (callback) {
          callback(null, null);
        }
        this.#selectedDbId = '';
      }
    }
  }

  drillDown(object3D, callback) {
    const dbItem = getTypeItem(object3D, 'db');
    if (dbItem) {
      const dbId = dbItem.userData.id;
      const data = this.#dbMap[dbId].data;
      if (callback) {
        callback(data, dbItem, this.#data);
      }
    }
  }

  focus(groupId) {
    const business = this.#businessMap[groupId];
    if (business) {
      const ground = business.children[0];
      ground.children[0].material = this.#matMap.site.businessGroundFocus;
      let animationSetting = {
        delay: 2000,
        duration: 1,
        onComplete: () => {
          ground.children[0].material = this.#matMap.site.businessGround;
        },
      };
      this.#animation.push(animationSetting).start();
    }
  }

  update(data) {
    this.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material.dispose();
      }
    });
    this.remove(...this.children);
    this.#data = data;
    this.#init();
  }

  updateStatus(grayData) {
    grayData.drGroupList.forEach((drGroup) => {
      drGroup.moTypeInfoList.forEach(moType =>{
        const id = moType.moTypeId;
        if (this.#dbMap[id]) {
          const needsUpdate =
            !(this.#dbMap[id].data.isSupportGray === moType.isSupportGray &&
              this.#dbMap[id].data.podCount === moType.podCount &&
              this.#dbMap[id].data.alarmCount === moType.alarmCount &&
              this.#dbMap[id].data.isHealthy === moType.isHealthy
            );
          this.#dbMap[id].data.isSupportGray = moType.isSupportGray;
          this.#dbMap[id].data.podCount = moType.podCount;
          this.#dbMap[id].data.alarmCount = moType.alarmCount;
          this.#dbMap[id].data.isHealthy = moType.isHealthy;
          if (this.#dbMap[id].mesh && needsUpdate) {
            setDbStatus(this.#dbMap[id], this.#matMap, this.#modelMap);
          }
        }
      });
    });
  }

  updateGray(grayArr) {
    Object.keys(this.#dbMap).forEach((id) => {
      let oldVal = this.#dbMap[id].data.isGrayUpdating;
      if (grayArr.includes(parseInt(id))) {
        this.#dbMap[id].data.isGrayUpdating = 1;
      } else {
        this.#dbMap[id].data.isGrayUpdating = 0;
      }
      if (this.#dbMap[id].mesh && oldVal !== this.#dbMap[id].data.isGrayUpdating) {
        setDbStatus(this.#dbMap[id], this.#matMap, this.#modelMap);
      }
    });
  }

  #selectDb(dbId) {
    const oldSeleted = this.#selectedDbId;
    const newSelected = dbId;

    if (oldSeleted) {
      this.#removeLine();
    }
    if (this.#dbMap[newSelected]) {
      this.#createLine(newSelected);
    }
  }

  #createLine(dbId) {
    const relationship = this.#data.relationship[dbId];

    const db = this.#dbMap[dbId];
    const startPos = db.mesh.getWorldPosition(new A3D.C.Vector3());
    const modelSize = this.#podDefaultScale;
    startPos.y += modelSize * 0.5;

    const lineGroup = this.getObjectByName('lineGroup');
    relationship.moTypeLinks.forEach((linkObj) => {
      const linkDb = this.#dbMap[linkObj.to];
      const endPos = linkDb.mesh.getWorldPosition(new A3D.C.Vector3());
      endPos.y += modelSize * 0.5;
      let line;
      if (linkObj.protocol) {
        line = this.#getRelationLine(startPos, endPos, 'related', linkObj.protocol, linkObj.realation);
      } else {
        line = this.#getRelationLine(startPos, endPos, '', '', linkObj.realation);
      }

      lineGroup.add(line);
    });
    relationship.siteLinks.forEach((linkObj) => {
      const linkSite = this.#siteItemMap[linkObj.to];
      if (!linkSite) {
        return;
      }
      const endPos = linkSite.mesh.getWorldPosition(new A3D.C.Vector3()).add(linkSite.linkOffset);
      let status = 'related';
      if (linkObj.alarmCount > 0) {
        status = 'alarm';
      }

      const siteItem = this.#siteItemMap[linkObj.to]?.mesh;
      if (siteItem?.userData?.status === 'alarm') {
        status = 'alarm';
      }

      let line;
      if (linkObj.protocol) {
        line = this.#getRelationLine(startPos, endPos, 'related', linkObj.protocol, linkObj.realation);
      } else {
        line = this.#getRelationLine(startPos, endPos, '', '', linkObj.realation);
      }
      lineGroup.add(line);
      line.userData.dbId = dbId;
      line.userData.siteId = linkObj.to;
      line.userData._type = 'line';

      this.#setSiteItemStatus(linkObj.to, status);
    });
  }

  #getRelationLine(start, end, status, protocol, realation) {
    const lineGroup = new A3D.C.Group();
    const control = new A3D.C.Vector3().copy(start).add(end).multiplyScalar(0.5);
    control.y += start.distanceTo(end) * 0.4;
    let bezierCurve = new A3D.C.QuadraticBezierCurve3(start, control, end);
    const lineMat = status === 'alarm' ? this.#matMap.line.alarm : this.#matMap.line.normal;
    let line = getLine2(bezierCurve);
    let percent = 0.7;
    if (realation === 'to') {
      line.add({ start, end, lineType: 1 });
    } else {
      bezierCurve = new A3D.C.QuadraticBezierCurve3(end, control, start);
      line = getLine2(bezierCurve);
      line.add({ end, start, lineType: 1 });
      percent = 0.3;
    }

    line.renderOrder = 11;
    line.isGroup = false;
    lineGroup.add(line);

    let _line = line.children[line.children.length - 1];
    let center = line.getPointAt(_line, percent);
    if (status) {
      const circle = this.#getRelationCircle(status, center);
      const tips = this.#getRelationTips(status, center, protocol);
      lineGroup.add(circle, tips);
    }
    return lineGroup;
  }

  #getRelationCircle(status, position) {
    const circleMat = status === 'alarm' ? this.#matMap.circle.alarm : this.#matMap.circle.normal;
    const circle = new A3D.C.Sprite(circleMat);
    circle.scale.set(0.2, 0.2);
    circle.position.copy(position);
    circle.renderOrder = 12;
    circle.name = 'circle';
    return circle;
  }

  #getRelationTips(status, position, protocol) {
    const canvas = document.createElement('canvas');
    const color = status === 'alarm' ? '#E54545' : '#2E94FF';
    this.#drawCanvas(canvas, protocol, color);

    const texture = A3D.canvasToTex(canvas, true);
    const material = new A3D.C.SpriteMaterial({
      map: texture,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    sprite.position.copy(position);
    sprite.position.x += 0.1;
    sprite.position.z += 0.1;
    sprite.center.set(0, 0.5);
    const size = this.#sizeSetting2.siteDepth * 0.2;
    sprite.scale.set(canvas.width / canvas.height * size, size);
    sprite.renderOrder = 12;
    return sprite;
  }

  #drawCanvas(canvas, content, color) {
    const pixelRatio = 4;
    const lineHeight = 36 * pixelRatio;
    const fontSize = 18 * pixelRatio;
    const lineWidth = pixelRatio * 2;
    const ctx = canvas.getContext('2d');
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    const size = ctx.measureText(content);

    canvas.width = size.width + lineHeight + lineWidth * 2;
    canvas.height = lineHeight + lineWidth * 2;

    drawRoundedRect(ctx, {x: lineWidth, y: lineWidth}, canvas.width - lineWidth * 2, lineHeight, lineHeight * 0.05);
    // 填充路径
    ctx.fillStyle = '#292929';
    ctx.fill();
    // 描绘路径
    ctx.strokeStyle = color;
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = color;

    const HALF = 2;
    const top = (canvas.height - fontSize) / HALF;
    ctx.fillText(content, canvas.width / HALF, top);
  }

  #removeLine() {
    const lineGroup = this.getObjectByName('lineGroup');
    lineGroup.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material.dispose();
      }
    });
    lineGroup.remove(...lineGroup.children);

    Object.keys(this.#siteItemMap).forEach((siteItemId) => {
      this.#setSiteItemStatus(siteItemId, 'normal');
    });
  }

  /** 初始化布局-start */
  #init() {
    this.#initData();
    this.#initContainer();
    this.#initSite();

    this.#initSiteTeams();
  }

  #initData() {
    this.#dbMap = {};
    this.#businessMap = {};
    this.#siteTeamMap = {};
    this.#siteItemMap = {};
    this.#selectedDbId = '';

    this.#data.drGroupList.forEach((drGroup) => {
      drGroup.moTypeInfoList.forEach((moType) => {
        this.#dbMap[moType.moTypeId] = {data: moType};
      });
    });
    this.#data.siteInfoList.forEach((siteInfo) => {
      if (!this.#siteTeamMap[siteInfo.siteTeamId]) {
        this.#siteTeamMap[siteInfo.siteTeamId] = {children: []};
      }
      this.#siteTeamMap[siteInfo.siteTeamId].children.push(siteInfo);
    });
  }

  #initContainer() {
    const group = new A3D.C.Group();
    group.name = 'siteDrillDown';
    group.position.z = this.#size.offset;
    const siteTeamGroup = new A3D.C.Group();
    siteTeamGroup.name = 'siteTeamGroup';
    siteTeamGroup.position.z = this.#size.offset;
    const lineGroup = new A3D.C.Group();
    lineGroup.name = 'lineGroup';
    this.add(group, siteTeamGroup, lineGroup);
  }

  #initSite() {
    const siteGroup = new A3D.C.Group();
    siteGroup.name = 'site';
    this.children[0].add(siteGroup);

    this.#initGround(siteGroup);
    this.#initSiteName(siteGroup);

    if (this.#data.autoPosition) {
      const layoutData = this.#getLayoutData(this.#data);
      this.#initBusiness(layoutData, siteGroup);
    } else {
      const layoutData = this.#getFixLayoutData(this.#data);
      this.#initFixBusiness(layoutData, siteGroup);
    }
  }

  /**
   * 创建站点地板
   * @returns
   */
  #initGround(parent) {
    const {width, depth} = this.#size;
    const {groundHeight} = this.#sizeSetting;
    const options = getGroundOptions(width, depth);
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = this.#matMap.site.ground;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'siteGround';
    ground.position.y -= groundHeight;
    parent.add(ground);
  }

  #initSiteName(parent) {
    const {width, depth} = this.#size;
    const {nameSize1, space1} = this.#sizeSetting;
    const name = this.#data.name;
    const nameCount = 20;
    const plane = getTextPlane({
      x: nameSize1 * nameCount,
      y: nameSize1,
    }, name, {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
    });
    const x = -(width - nameSize1 * nameCount) * 0.5 + space1;
    const z = -(depth - nameSize1) * 0.5 + space1;
    plane.position.set(x, 0, z);
    plane.renderOrder = 1;
    parent.add(plane);
  }

  #getLayoutData(originalData) {
    const drGroupList = originalData.drGroupList;
    let count = 0;
    const children = drGroupList.map((drGroup) => {
      const curCount = drGroup.moTypeInfoList.length;
      count += curCount;
      return {
        count: curCount,
        data: drGroup,
      };
    });
    const fixedData = {
      count,
      children,
    };
    fixedData.children.sort((pre, cur) => cur.count - pre.count);
    const layout = Layout.initLayout(fixedData, 4);

    const {
      nameSize1, space1, nameSize2, space2,
    } = this.#sizeSetting;
    const {width, depth} = this.#size;
    const businessSize = {
      width: width - space1 * 2,
      depth: depth - nameSize1 - space1 * 3,
    };
    const position = {x: 0, y: 0, z: (nameSize1 + space1) / 2};
    SiteLayout.initLayout(layout, businessSize, nameSize2, space2, position);
    return layout;
  }

  #initBusiness(layoutData, parent) {
    const {nameSize2, space2} = this.#sizeSetting;
    const {podDepth, podWidth, position} = layoutData;

    this.#podDefaultScale =
      Math.max(Math.min(podDepth, podWidth, this.#podMaxSpace), this.#podMinSpace) * this.#podScaleRatio;

    const businessContainer = this.#drawBusiness(layoutData, nameSize2, space2);
    businessContainer.position.copy(position);
    parent.add(businessContainer);
  }

  #drawBusiness(layoutData, nameSize, space) {
    const businessContainer = new A3D.C.Group();
    layoutData.level1.forEach((level1Group) => {
      const businessLayer = new A3D.C.Group();
      businessLayer.position.copy(level1Group.position);
      businessContainer.add(businessLayer);
      level1Group.group.forEach((business) => {
        const business3D = this.#getBusiness(business, nameSize, space, layoutData);
        this.#businessMap[business.data.drGroupId] = business3D;
        business3D.position.copy(business.position);
        business3D.position.x += business.size.width / 2;
        business3D.position.z += business.size.depth / 2;
        businessLayer.add(business3D);
      });
    });
    return businessContainer;
  }

  #getBusiness(data, nameSize, space, layoutData) {
    const wrapper = new A3D.C.Group();
    this.#initBusinessGround(data.size, wrapper, data.data);
    this.#initBlockName(data, nameSize, space, wrapper);
    this.#initDbs(data, nameSize, space, layoutData, wrapper);
    return wrapper;
  }

  /**
   * 创建business模块的地板
   * @param {*} width
   * @param {*} depth
   * @returns
   */
  #initBusinessGround({width, depth}, wrapper) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    const groundMat = this.#matMap.site.businessGround;
    const ground = new ExtrudeShape(options, groundMat);
    ground.children[0].renderOrder = 1;
    ground.name = 'businessGround';
    wrapper.add(ground);
  }

  #initBlockName(data, nameSize, space, wrapper) {
    const {width, depth} = data.size;
    const name = data.data.drGroupName;
    const nameCount = 10;

    const plane = getTextPlane({
      x: nameSize * nameCount,
      y: nameSize,
    }, name, {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
      maxWidth: data.size.width * 40,
    });
    plane.material.color.set('#cccccc');
    const x = -(width - nameSize * nameCount) * 0.5 + space;
    const z = -(depth - nameSize) * 0.5 + space;
    plane.position.set(x, 0.1, z);
    plane.renderOrder = 1;
    plane.name = 'businessGround';
    plane.userData._type = 'businessGround';
    plane.userData._data = {
      siteId:data.data.drGroupId,
      siteName:data.data.drGroupName,
    };
    wrapper.add(plane);
  }

  #initDbs(data, nameSize, space, layoutData, wrapper) {
    const offset = {x: 0, y: 0, z: (nameSize + space) / 2};
    const podGroup = new A3D.C.Group();
    podGroup.position.copy(offset);
    wrapper.add(podGroup);

    const posArr = this.#getDbsPosition(data, space);
    let posIndex = 0;

    data.data.moTypeInfoList.forEach((moTypeInfo) => {
      const dbGroup = this.#getDb(moTypeInfo);
      dbGroup.position.copy(posArr[posIndex]);
      posIndex++;
      dbGroup.scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
      podGroup.add(dbGroup);
    });
  }

  #getDbsPosition(data, space) {
    const col = data.level2[0] || 1;
    const row = data.level2.length || 1;
    const perWidth = (data.size.width - space * 2) / col;
    const perDepth = (data.size.depth - space * 2) / row;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: -data.size.width / 2 + space + (j + 0.5) * perWidth,
          y: 0,
          z: -data.size.depth / 2 + space + (i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getDb(moTypeInfo) {
    const dbGroup = new A3D.C.Group();
    const type = moTypeInfo.moTypeType;
    let geometry;
    let material;
    let modelType;
    if (type === 2) {
      geometry = this.#modelMap.database.geometry;
      material = this.#matMap.database.normal;
      modelType = 'database';
    } else {
      geometry = this.#modelMap.app.geometry;
      material = this.#matMap.app.normal;
      modelType = 'app';
    }
    const mesh = new A3D.C.Mesh(geometry, material);
    mesh.renderOrder = 2;
    dbGroup.add(mesh);
    this.#initDbName(moTypeInfo, dbGroup);
    dbGroup.userData._type = 'db';

    this.#clickable(dbGroup, moTypeInfo.moTypeId, modelType);
    setDbStatus(this.#dbMap[moTypeInfo.moTypeId], this.#matMap, this.#modelMap);
    return dbGroup;
  }

  #initDbName(moTypeInfo, parent) {
    const modelSize = getObjectSize(this.#modelMap.app);
    const name = moTypeInfo.moTypeName;
    let material = this.#matMap.dbName[name];
    if (!material) {
      const texture = getCanvasTex(name, 6);
      material = new A3D.C.SpriteMaterial({
        map: texture,
        color: '#BBBBBB',
        depthWrite: false,
      });
      this.#matMap.dbName[name] = material;
    }
    const sprite = new A3D.C.Sprite(material);

    const nameSize = modelSize.depth * 0.4;
    sprite.position.z = modelSize.depth * 1.5 + nameSize * 0.5;
    sprite.position.y = nameSize * 0.6;

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.renderOrder = 2;

    parent.add(sprite);
  }

  #clickable(mesh, id, type) {
    mesh.userData.db = true;
    mesh.userData.id = id;
    mesh.userData.type = type;
    this.#dbMap[id].mesh = mesh;
    this.#dbMap[id].type = type;
  }

  /** 初始化布局-end */

  /** 初始化布局2-start */
  #initSiteTeams() {
    this.#initSiteTeamLayout();

    const teamIds = Object.keys(this.#siteTeamMap);
    const siteTeamGroup = this.getObjectByName('siteTeamGroup');
    teamIds.forEach((siteTeamId) => {
      const siteTeamData = this.#siteTeamMap[siteTeamId];

      const siteTeam = this.#getSiteTeam(siteTeamData);
      siteTeamGroup.add(siteTeam);
    });

    this.#setSiteItemStatus(this.#data.id, '');
  }

  #initSiteTeamLayout() {
    const teamIds = Object.keys(this.#siteTeamMap);
    const teamLength = teamIds.length;

    const {
      spaceX, spaceZ, siteWidth, siteDepth,
    } = this.#sizeSetting2;
    const {depth} = this.#size;
    const teamDepth = siteDepth + spaceZ * 2;
    const teamZ = depth / 2 + spaceZ + teamDepth / 2;
    let topCount = 0;
    if (teamLength < 4) {
      topCount = teamLength;
    } else if (teamLength < 7) {
      topCount = 3;
    } else {
      topCount = Math.ceil(teamLength / 2);
    }
    let length1 = -spaceX;
    let length2 = -spaceX;
    teamIds.forEach((teamId, index) => {
      const team = this.#siteTeamMap[teamId];
      team.width = team.children.length * (siteWidth + spaceX) + spaceX;
      team.depth = teamDepth;

      if (index < topCount) {
        length1 += spaceX + team.width;
        team.position = {x: length1 - team.width / 2, y: 0, z: -teamZ};
        team.offset = {x: 0, y: 0, z: siteDepth / 2};
      } else {
        length2 += spaceX + team.width;
        team.position = {x: length2 - team.width / 2, y: 0, z: teamZ};
        team.offset = {x: 0, y: 0, z: -siteDepth / 2};
      }
    });
    teamIds.forEach((teamId, index) => {
      const team = this.#siteTeamMap[teamId];
      if (index < topCount) {
        team.position.x -= length1 / 2;
      } else {
        team.position.x -= length2 / 2;
      }
    });
  }

  #getSiteTeam(siteTeamData) {
    const {
      siteRadius, siteDepth, siteWidth, spaceX, spaceY,
    } = this.#sizeSetting2;
    const siteTeam = new A3D.C.Group();
    siteTeam.position.copy(siteTeamData.position);
    const teamPoints = getPlanePoints(siteTeamData.width, siteTeamData.depth);
    const teamShape = new FilletShape(teamPoints, siteRadius);
    const teamPlaneGeo = new A3D.C.ShapeGeometry(teamShape);
    teamPlaneGeo.rotateX(-Math.PI / 2);
    const teamGround = new A3D.C.Mesh(teamPlaneGeo, this.#matMap.siteTeam.ground);
    const teamOptions = getGroundOptions(siteTeamData.width, siteTeamData.depth);
    teamOptions.radius = siteRadius;
    teamOptions.close = true;
    const teamLineMat = this.#matMap.siteTeam.line;
    const teamLine = getWallLine(teamLineMat, teamOptions);
    siteTeam.add(teamLine, teamGround);

    const options = getGroundOptions(siteWidth, siteDepth);
    options.depth = spaceY;
    options.radius = siteRadius;
    const groundMat = this.#matMap.siteTeam.block.normal;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'siteGround';

    const length = siteTeamData.children.length;
    siteTeamData.children.forEach((siteItemData, index) => {
      const siteItem = this.#getSiteItem(siteItemData, ground, siteItemData.alarmStatus);
      siteItem.position.x = (-length / 2 + index + 0.5) * (siteWidth + spaceX);
      siteTeam.add(siteItem);

      this.#siteItemMap[siteItemData.siteId] = {
        data: siteItemData,
        mesh: siteItem,
        linkOffset: siteTeamData.offset,
      };
    });
    return siteTeam;
  }

  #getSiteItem(data, ground, alarm) {
    const siteItem = new A3D.C.Group();
    const newGround = ground.clone();

    siteItem.add(newGround);
    this.#initSiteItemName(data.siteName, siteItem);

    siteItem.userData.siteId = data.siteId;
    siteItem.userData._type = 'site';

    if (alarm) {
      siteItem.children[0].children[0].material = this.#matMap.siteTeam.block.alarm;
      siteItem.children[1].material.color.set('#f5f5f5');
      siteItem.userData.status = 'alarm';
    }
    return siteItem;
  }

  #truncateName(name) {
    // 如果名字长度大于8，则截断并添加...
    if (name.length > 6) {
      return `${name.substring(0, 6)}...`;
    }
    return name;
  }

  #initSiteItemName(name, siteItem) {
    const {nameSize, spaceY} = this.#sizeSetting2;
    const nameCount = 10;
    const siteItemName = getTextPlane({
      x: nameSize * nameCount,
      y: nameSize,
    }, this.#truncateName(name), {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
      'text-align': 'center',
    });
    siteItemName.position.y = spaceY + nameSize * 0.01;
    siteItemName.pointerEvents = 'none';
    siteItemName.material.color.set(nameColorNormal);
    siteItem.add(siteItemName);
  }

  #setSiteItemStatus(id, status) {
    const siteItem = this.#siteItemMap[id]?.mesh;
    if (!siteItem) {
      return;
    }
    let blockMat = this.#matMap.siteTeam.block.normal;
    let nameColor = nameColorNormal;

    if (siteItem.userData.status === 'alarm') {
      return;
    }

    if (status === 'hover') {
      blockMat = this.#matMap.siteTeam.block.hover;
      nameColor = nameColorRelated;
    } else if (String(id) === String(this.#data.id)) {
      blockMat = this.#matMap.siteTeam.block.related;
      nameColor = nameColorSelect;
    } else {
      blockMat = this.#matMap.siteTeam.block.normal;
      if (status === 'related' || status === 'alarm') {
        blockMat = this.#matMap.siteTeam.block[status];
        nameColor = nameColorRelated;
      }
    }
    siteItem.children[0].children[0].material = blockMat;
    siteItem.children[1].material.color.set(nameColor);
    if (status !== 'hover') {
      siteItem.userData.status = status;
    }
  }

  /** 初始化布局2-end */

  /** 固定布局1-start */
  #getFixLayoutData(originalData) {
    if (!this.#positionSetting) {
      this.#positionSetting = originalData.positionSetting;
    }
    const fixLayoutData = originalData.positionSetting || this.#positionSetting;
    const drGroupMap = {};
    fixLayoutData.forEach((item) => {
      drGroupMap[item.groupKey] = {
        ...item,
        moTypeInfoList: [],
      };
    });
    originalData.drGroupList.forEach((drGroup) => {
      if (drGroupMap[drGroup.groupKey]) {
        drGroupMap[drGroup.groupKey] = {
          ...drGroup,
          ...drGroupMap[drGroup.groupKey],
          moTypeInfoList: drGroup.moTypeInfoList,
        };
      }
    });
    const drGroupList = Object.keys(drGroupMap).map((drGroupKey) => drGroupMap[drGroupKey]);
    let count = 0;
    const children = drGroupList.map((drGroup) => {
      const curCount = drGroup.moTypeInfoList.length;
      count += curCount;
      return {
        count: curCount,
        data: drGroup,
      };
    });
    const fixedData = {
      count,
      children,
    };
    const layout = Layout2.initLayout(fixedData);
    const {
      nameSize1, space1, nameSize2, space2,
    } = this.#sizeSetting;
    const {width, depth} = this.#size;
    const businessSize = {
      width: width - space1 * 2,
      depth: depth - nameSize1 - space1 * 3,
    };
    const position = {x: 0, y: 0, z: (nameSize1 + space1) / 2};
    SiteLayout2.initLayout(layout, businessSize, nameSize2, space2, position);
    return layout;
  }

  #initFixBusiness(layoutData, parent) {
    const {nameSize2, space2} = this.#sizeSetting;
    const {podDepth, podWidth, position} = layoutData;

    this.#podDefaultScale =
      Math.max(Math.min(podDepth, podWidth, this.#podMaxSpace), this.#podMinSpace) * this.#podScaleRatio;

    const businessContainer = this.#drawFixBusiness(layoutData, nameSize2, space2);
    businessContainer.position.copy(position);
    parent.add(businessContainer);
  }

  #drawFixBusiness(layoutData, nameSize, space) {
    const businessContainer = new A3D.C.Group();
    layoutData.level1.forEach((level1Group) => {
      const businessLayer = new A3D.C.Group();
      businessContainer.add(businessLayer);
      level1Group.group.forEach((business) => {
        const business3D = this.#getBusiness(business, nameSize, space, layoutData);
        this.#businessMap[business.data.drGroupId] = business3D;
        business3D.position.copy(business.position);
        business3D.position.x += business.size.width / 2;
        business3D.position.z += business.size.depth / 2;
        businessLayer.add(business3D);
      });
    });
    return businessContainer;
  }
}
