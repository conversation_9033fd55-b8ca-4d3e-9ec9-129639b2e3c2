import eventBus from '@pages/businesstopo/a3dPages/bus';
import { getMessage, registerResource } from '@pages/businesstopo/a3dPages/commonUtil/intl';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/podDrillDown';
import { eventBusHandler, getRouteQuery } from '../utils';
import './podDetailCard.less';
import {STATUS} from '@pages/businesstopo/const/moTypeDrill';

registerResource(i18n, 'w3d');
let home3D;

// 处理数据结构
const getI18n = () => ({
  productionArea: getMessage('w3d.productionArea'),
  grayArea: getMessage('w3d.grayArea'),
  version: getMessage('w3d.version'),
});

const getData = (data, isAgent) => {
  const uniqueData = {};
  data.businessClusterList.sort((a, b) => {
    // 将 isGray 为 0 的排在前面
    if (a.isGray === 0 && b.isGray !== 0) {
      return -1; // a 在 b 前面
    } else if (a.isGray !== 0 && b.isGray === 0) {
      return 1; // a 在 b 后面
    } else {
      return 0; // 保持原有顺序
    }
  });
  const result = {
    podCount: 0,
    productionProportion: 0,
    grayProportion: 0,
    applicationType:data.applicationType,
    environmentType:data.environmentType,
    moTypeList: data.businessClusterList.map(moType => ({
      dnId: moType.dnId,
      isGray: moType.isGray,
      name: moType.businessDisplayName || moType.businessName,
      version: moType.version,
      alarmStatus: (moType.csnState === 0 && moType.availableStatus === STATUS.normal) ? 0 : 1,
      podList: moType.podDataList.map(pod => ({
        dnId: pod.dnId,
        name: pod.podName,
        alarmCount: pod.podAlarmCount,
        alarmStatus: (pod.csnState === 0 && pod.availableStatus === STATUS.normal) ? 0 : 1,
        createTime: pod.createTime,
        status: pod.availableStatus,
        isGray: moType.isGray,
      })),
      vmList: moType.vmDataList.map(vm => ({
        dnId: vm.dnId,
        name: vm.vmName,
        ip: vm.vmIp,
        status: vm.availableStatus,
        alarmStatus: (vm.csnState === 0 && vm.availableStatus === STATUS.normal) ? 0 : 1,
      })),
    })),
    vmList: (
      data.businessClusterList.flatMap(business => {
        return (business.vmDataList || []).map(vmItem => {
          const dnId = vmItem.dnId;
          if (!uniqueData[dnId]) {
            uniqueData[dnId] = true; // 标记dnId已经存在
            return {
              dnId,
              name: vmItem.vmName,
              ip: vmItem.vmIp,
              status: vmItem.availableStatus,
              alarmStatus: (vmItem.csnState === 0 && vmItem.availableStatus === STATUS.normal) ? 0 : 1,
            };
          }
          return null; // 返回null表示重复的dnId不添加到结果数组中
        }).filter(item => item !== null); // 过滤掉为null的项
      })
    ),
    i18n: getI18n(),
    isAgent,
  };
  let productionCount = 0;
  let grayCount = 0;
  data.businessClusterList.forEach(businessItem => {
    if (businessItem.isGray) {
      grayCount += businessItem.podDataList.length;
    } else {
      productionCount += businessItem.podDataList.length;
    }
  });
  result.podCount = productionCount + grayCount;
  if (result.podCount) {
    result.productionProportion = Math.round((productionCount / result.podCount) * 100);
    result.grayProportion = Math.round((grayCount / result.podCount) * 100);
  }
  return result;
};

export const initW3D = async(container, setting) => {
  const { initData, eventArr, isAgent } = setting;
  eventArr.forEach(item => {
    eventBus.addListener(item.eventName, item.fn);
  });

  // 2D-3D对接，将数据转化成3D需要的数据
  const data = getData(initData, isAgent);
  if (!home3D) {
    home3D = new Home3D(eventBusHandler);
  }
  const routeQuery = getRouteQuery();
  routeQuery.isMM = setting.isMM;
  await home3D.init(container.current, routeQuery, { podDrillDown: data });
};

// 局部增量刷新
export const updatePodDrillData = pageData => {
  const data = getData(pageData);
  eventBusHandler.emit('to3d_updatePodDrillDownData', data);
};

export const destroyW3D = eventArr => {
  eventArr.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });
  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};
