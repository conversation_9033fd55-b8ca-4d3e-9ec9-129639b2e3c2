import arSwitchover from '@pages/businesstopo/a3dPages/w3d/assets/images/arSwitchover.svg';
import asSwitchover from '@pages/businesstopo/a3dPages/w3d/assets/images/asSwitchover.svg';
import podFault from '@pages/businesstopo/a3dPages/w3d/assets/images/podFault.svg';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import ExtrudeShape from '../common/ExtrudeShape';
import {
  getGroundOptions, getTextPlane, getWallLine, setStatus,
} from '../utils';
import { initPodLine } from '../utils/siteDatabase';
import Layout from './Layout';
import SiteLayout from './SiteLayout';

const A3D = getA3D();

const commonPodMat = new A3D.C.MeshBasicMaterial({});

export const isNullOrUndefined = (value) => value === null || value === undefined;

export default class SiteDatabase extends A3D.C.Object3D {
  #data;
  #modelMap;
  #matMap;
  #setting;
  #w3d;

  #sizeSetting = {};
  #podScaleRatio = 0.5;
  podDefaultScale = 1;
  #podScaleVec3 = new A3D.C.Vector3();
  #podMaxSpace = 3.2;
  #podMinSpace = 0.7;

  constructor(data, modelMap, matMap, setting, w3d) {
    super();

    this.#data = data;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#setting = setting;
    this.#w3d = w3d;

    const { width } = this.#setting.size;
    this.#sizeSetting.groundHeight = width * 0.012;
    this.#sizeSetting.nameSize1 = width * 0.025;
    this.#sizeSetting.space1 = width * 0.02;
    this.#sizeSetting.nameSize2 = width * 0.016;
    this.#sizeSetting.space2 = width * 0.01;

    this.#init(this.#setting.type);
  }

  update(type = 'memory') {
    if (type === this.#setting.type) {
      return;
    }
    this.traverse((child) => {
      if (child.isMesh) {
        child?.geometry.dispose();
        child?.material.dispose();
      }
    });
    this.remove(...this.children);
    if (type === 'memory') {
      this.#initSite(this.#data.memory);
    } else {
      this.#initSite(this.#data.physic);
    }
    this.#setting.type = type;
  }

  hoverIn() {
    this.children[0].children[0].material = this.#matMap.site.groundHover;
  }

  hoverOut() {
    this.children[0].children[0].material = this.#matMap.site.ground;
  }

  updateSiteDetail() {
    const mesh = this.getObjectByName('siteDetail');
    if (!mesh) {
      return;
    }
    const data = this.#data[this.#setting.type];
    const size = mesh.userData.size;
    const material = mesh.material;
    const canvas = this.#getSiteDetailCanvas(data, size, material);
    material.map = A3D.canvasToTex(canvas, true);
  }

  /** 初始化布局-start */

  #init(type) {
    if (type === 'memory') {
      this.#initSite(this.#data.memory);
    } else {
      this.#initSite(this.#data.physic);
    }
  }

  #initSite(typeData) {
    if (!typeData.business) {
      return;
    }
    const transformData = this.#transformData(typeData);
    const layoutData = this.#getLayoutData(transformData);

    this.#initGround();
    this.#initName();
    this.#initSiteDetail(transformData);
    this.#initBusiness(layoutData);
  }

  #transformData(originalData) {
    const transformData = JSON.parse(JSON.stringify(originalData));
    transformData.business.forEach((business) => {
      business.count = 0;
      business.dbs.forEach((db) => {
        db.count = db.podIds.length;
        business.count += db.count;
      });
      business.dbs.sort((pre, cur) => cur.count - pre.count);
    });
    transformData.business.sort((pre, cur) => cur.count - pre.count);
    return transformData;
  }

  #getLayoutData(transformData) {
    const fixedData = {
      count: transformData.podCount,
      children: transformData.business.map((business) => ({
        count: business.count,
        data: business,
        children: business.dbs.map((db) => ({
          count: db.count,
          data: db,
        })),
      })),
    };
    const layout = Layout.initLayout(fixedData, 4);

    const {
      nameSize1, space1, nameSize2, space2,
    } = this.#sizeSetting;
    const { width, depth } = this.#setting.size;
    const businessSize = {
      width: width - space1 * 2,
      depth: depth - nameSize1 - space1 * 3,
    };
    const position = { x: 0, y: 0, z: (nameSize1 + space1) / 2 };
    SiteLayout.initLayout(layout, businessSize, nameSize2, space2, position);
    return layout;
  }

  /**
   * 创建站点地板
   * @returns
   */
  #initGround() {
    const { width, depth } = this.#setting.size;
    const { groundHeight } = this.#sizeSetting;
    const options = getGroundOptions(width, depth);
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = this.#matMap.site.ground;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'siteGround';
    ground.position.y -= groundHeight;
    this.add(ground);
  }

  #initName() {
    const { width, depth } = this.#setting.size;
    const { nameSize1, space1 } = this.#sizeSetting;
    const name = this.#data.name;
    const nameCount = 6;
    const plane = getTextPlane({
      x: nameSize1 * nameCount,
      y: nameSize1,
    }, name, {
      width: 20 * nameCount * 1.2,
      height: 20,
      'font-weight': 'bold',
    });
    const x = -(width - nameSize1 * nameCount) * 0.5 + space1;
    const z = -(depth - nameSize1) * 0.5 + space1;
    plane.position.set(x, 0.01, z);
    plane.renderOrder = 1;
    this.add(plane);
  }

  #initSiteDetail(transformData) {
    const { depth } = this.#setting.size;
    const { nameSize1 } = this.#sizeSetting;
    const geo = new A3D.C.PlaneGeometry(nameSize1 * 36, nameSize1);
    geo.rotateX(-Math.PI / 2);
    const mat = new A3D.C.MeshBasicMaterial({
      transparent: true,
      depthWrite: false,
    });
    const mesh = new A3D.C.Mesh(geo, mat);
    mesh.name = 'siteDetail';
    mesh.position.set(
      0,
      0,
      -(depth - nameSize1) * 0.5 + nameSize1,
    );
    mesh.renderOrder = 1;
    mesh.userData.size = nameSize1 * 60;

    const canvas = this.#getSiteDetailCanvas(transformData, mesh.userData.size, mat);
    mat.map = A3D.canvasToTex(canvas, true);
    this.add(mesh);
  }

  #getSiteDetailCanvas(data, size, material) {
    const canvas = document.createElement('canvas');
    const fontSize = size * 0.8;
    const top = (size - fontSize) / 2;
    canvas.width = size * 36;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.font = `600 ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = '#ffffff';
    const text1 = `Pod(${data.podCount || 0})`;
    const width1 = ctx.measureText(text1).width;
    const text2 = `${this.#setting.i18n.NEFault}(${data.podFault || 0})`;
    const width2 = ctx.measureText(text2).width;
    const text3 = `${this.#setting.i18n.ARSwitchover}(${data.arSwitchover || 0})`;
    const width3 = ctx.measureText(text3).width;
    const text4 = `${this.#setting.i18n.ASSwitchover}(${data.asSwitchover || 0})`;
    const width4 = ctx.measureText(text4).width;
    const length = width1 + width2 + width3 + width4 + size * 4;
    const start = canvas.width / 2 - length / 2;
    const x1 = start;
    const x2 = x1 + width1 + size * 2;
    const x3 = x2 + width2 + size * 3;
    const x4 = x3 + width3 + size * 2;
    ctx.fillText(text1, x1, top);
    ctx.fillText(text2, x2, top);
    ctx.fillText(text3, x3, top);
    ctx.fillText(text4, x4, top);
    this.#onloadImage(
      podFault,
      ctx,
      { x: x2 - fontSize * 1.4, y: top - fontSize * 0.1 },
      fontSize * 1.2,
      material,
    );
    this.#onloadImage(
      arSwitchover,
      ctx,
      { x: x3 - fontSize * 1.4, y: top - fontSize * 0.1 },
      fontSize * 1.2,
      material,
    );
    this.#onloadImage(
      asSwitchover,
      ctx,
      { x: x4 - fontSize * 1.4, y: top - fontSize * 0.1 },
      fontSize * 1.2,
      material,
    );
    return canvas;
  }

  #onloadImage(src, ctx, { x, y }, fontSize, material) {
    const imgFault = document.createElement('img');
    imgFault.src = src;
    imgFault.onload = () => {
      ctx.drawImage(imgFault, x, y, fontSize, fontSize);
      material.map.needsUpdate = true;
      this.#w3d.agent.renderOnce();
    };
  }

  #initBusiness(layoutData) {
    const { nameSize2, space2 } = this.#sizeSetting;
    const { podDepth, podWidth, position } = layoutData;

    this.podDefaultScale =
    Math.max(Math.min(podDepth, podWidth, this.#podMaxSpace), this.#podMinSpace) * this.#podScaleRatio;

    const businessContainer = this.#drawBusiness(layoutData, nameSize2, space2);
    businessContainer.position.copy(position);
    this.add(businessContainer);
  }

  #drawBusiness(layoutData, nameSize, space) {
    const businessContainer = new A3D.C.Group();
    layoutData.level1.forEach((level1Group) => {
      const businessLayer = new A3D.C.Group();
      businessLayer.position.copy(level1Group.position);
      businessContainer.add(businessLayer);
      level1Group.group.forEach((business) => {
        const businessItem = new A3D.C.Group();
        const businessGround = this.#getBusinessGround(business, nameSize, space);
        businessItem.add(businessGround);
        businessItem.position.copy(business.position);
        businessItem.position.x += business.size.width / 2;
        businessItem.position.z += business.size.depth / 2;
        businessLayer.add(businessItem);
        business.level2.forEach((level2Group) => {
          const dbLayer = new A3D.C.Group();
          dbLayer.position.copy(level2Group.position);
          businessItem.add(dbLayer);
          level2Group.group.forEach((db) => {
            const db3D = this.#getDb(db, nameSize, space, layoutData);
            db3D.position.copy(db.position);
            db3D.position.x += db.size.width / 2;
            db3D.position.z += db.size.depth / 2;
            dbLayer.add(db3D);
          });
        });
      });
    });
    return businessContainer;
  }

  #getBusinessGround(data, nameSize, space) {
    const wrapper = new A3D.C.Group();
    this.#initBusinessLine(data.size, wrapper);
    this.#initBlockName(data, nameSize, space, wrapper);
    return wrapper;
  }

  #initBusinessLine({ width, depth }, wrapper) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    options.close = true;
    const lineMat = this.#matMap.site.businessLine;
    const line = getWallLine(lineMat, options);
    line.children[0].renderOrder = 1;
    line.name = 'businessLine';
    wrapper.add(line);
  }

  #initBlockName(data, nameSize, space, wrapper) {
    const { width, depth } = data.size;
    const name = data.data.name;

    const xWidth = Math.min(data.size.width - space * 2);
    const plane = getTextPlane({
      x: xWidth,
      y: nameSize,
    }, name, {
      width: xWidth / nameSize * 20 * 1.2,
      height: 20,
      'font-weight': 'bold',
    });
    plane.material.color.set('#cccccc');
    const x = -(width - xWidth) * 0.5 + space;
    const z = -(depth - nameSize) * 0.5 + space;
    plane.position.set(x, 0.01, z);
    plane.renderOrder = 2;
    wrapper.add(plane);
  }

  #getDb(data, nameSize, space, layoutData) {
    const wrapper = new A3D.C.Group();
    this.#initDbGround(data.size, wrapper);
    this.#initBlockName(data, nameSize, space, wrapper);
    this.#initPods(data, nameSize, space, layoutData, wrapper);
    return wrapper;
  }

  /**
   * 创建db模块的地板
   * @param {*} width
   * @param {*} depth
   * @returns
   */
  #initDbGround({ width, depth }, wrapper) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    const groundMat = this.#matMap.site.dbGround;
    const ground = new ExtrudeShape(options, groundMat);
    ground.children[0].renderOrder = 1;
    ground.name = 'dbGround';
    wrapper.add(ground);
  }

  #initPods(data, nameSize, space, layoutData, wrapper) {
    const offset = { x: 0, y: 0, z: (nameSize + space) / 2 };
    const podGroup = new A3D.C.Group();
    podGroup.position.copy(offset);
    wrapper.add(podGroup);

    const posArr = this.#getPodsPosition(data.col, data.row, layoutData);
    let posIndex = 0;

    const podGroupingMap = this.#getPodGroupingMap(data);
    const groupingIds = this.#getSortGroupingIds(podGroupingMap);
    groupingIds.forEach((id) => {
      if (!isNullOrUndefined(podGroupingMap[id].A)) {
        this.#initPodItem(podGroupingMap[id].A, 'A', posArr, posIndex, podGroup);
        if (isNullOrUndefined(podGroupingMap[id].S)) {
          posIndex += 1;
        } else {
          this.#initPodItem(podGroupingMap[id].S, 'S', posArr, posIndex + 1, podGroup);
          initPodLine(id, posArr, posIndex, this.#matMap.site.podLine, podGroup);
          posIndex += 2;
        }
      } else if (!isNullOrUndefined(podGroupingMap[id].S)) {
        this.#initPodItem(podGroupingMap[id].S, 'S', posArr, posIndex, podGroup);
        posIndex += 1;
      }
      if (!isNullOrUndefined(podGroupingMap[id].R)) {
        this.#initPodItem(podGroupingMap[id].R, 'R', posArr, posIndex, podGroup);
        posIndex += 1;
      }
      if (!isNullOrUndefined(podGroupingMap[id].Ra)) {
        this.#initPodItem(podGroupingMap[id].Ra, 'Ra', posArr, posIndex, podGroup);
        if (isNullOrUndefined(podGroupingMap[id].Rs)) {
          posIndex += 1;
        } else {
          this.#initPodItem(podGroupingMap[id].Rs, 'Rs', posArr, posIndex + 1, podGroup);
          initPodLine(id, posArr, posIndex, this.#matMap.site.podLine, podGroup);
          posIndex += 2;
        }
      } else if (!isNullOrUndefined(podGroupingMap[id].Rs)) {
        this.#initPodItem(podGroupingMap[id].Rs, 'Rs', posArr, posIndex, podGroup);
        posIndex += 1;
      }
      podGroupingMap[id].init.forEach((podId) => {
        this.#initPodItem(podId, 'default', posArr, posIndex, podGroup);
        posIndex += 1;
      });
    });
  }

  #getPodsPosition(col, row, layoutData) {
    const { podWidth, podDepth } = layoutData;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * podWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * podDepth,
        });
      }
    }
    return posArr;
  }

  #getPodGroupingMap(data) {
    const podGroupingMap = {};
    data.data.podIds.forEach((id) => {
      const pod = this.#setting.podMap[id];
      const shardId = pod.data.shardId;
      const designRole = pod.data.designRole;
      podGroupingMap[shardId] = podGroupingMap[shardId] || { init: [] };
      podGroupingMap[shardId].count = podGroupingMap[shardId].count || 0;
      if (designRole) {
        podGroupingMap[shardId][designRole] = id;
      } else {
        podGroupingMap[shardId].init.push(id);
      }
      podGroupingMap[shardId].count++;
    });
    return podGroupingMap;
  }

  #getSortGroupingIds(podGroupingMap) {
    const groupingIds = Object.keys(podGroupingMap);
    groupingIds.sort((pre, cur) => {
      if (podGroupingMap[pre].count > podGroupingMap[cur].count) {
        return -1;
      } else if (podGroupingMap[pre].count < podGroupingMap[cur].count) {
        return 1;
      }

      if (isNullOrUndefined(podGroupingMap[pre].A) && !isNullOrUndefined(podGroupingMap[cur].A)) {
        return 1;
      }
      return 0;
    });
    return groupingIds;
  }

  #initPodItem(podId, type, posArr, posIndex, wrapper) {
    const geometry = this.#modelMap.pod.geometry;
    const mesh = new A3D.C.Mesh(geometry, commonPodMat);
    mesh.renderOrder = 2;
    const group = new A3D.C.Group();
    group.add(mesh);
    group.position.copy(posArr[posIndex]);
    group.scale.set(this.podDefaultScale, this.podDefaultScale, this.podDefaultScale);
    this.#clickable(group, podId, type);
    setStatus(this.#setting.podMap[podId], this.#matMap);
    wrapper.add(group);
  }

  #clickable(mesh, id, type) {
    mesh.userData.pod = true;
    mesh.userData.id = id;
    mesh.userData.type = type;
    mesh.userData.startScale = this.#podScaleVec3.set(this.podDefaultScale, this.podDefaultScale, this.podDefaultScale);
    this.#setting.podMap[id].mesh = mesh;
    this.#setting.podMap[id].type = type;
  }

  /** 初始化布局-end */
}
