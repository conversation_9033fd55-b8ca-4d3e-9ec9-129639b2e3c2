.progressCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 1.5rem;

  .card {
    height: 4.375rem;
    border-radius: 0.5rem;
    background-color: #202020;
    border: 1px solid #202020;
    padding: 1rem;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    width: 48%;
    margin-bottom: 0.5rem;
    cursor: pointer;

    &:hover {
      border: 1px solid #676767;
    }

    &:active {
      // border: 1px solid #2E94FF;
      border: 1px solid #5cacff;
    }

    &.urgent {
      .process_container {
        background: rgba(229, 69, 69, 0.2);

        .process_hand {
          background: #e54545;
        }
      }

      .card_icon {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_pod.svg);
      }
    }

    &.minor {
      .process_container {
        background: rgba(255, 128, 0, 0.2);

        .process_hand {
          background: #ff8000;
        }
      }

      .card_icon {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_pod.svg);
      }
    }

    &.important {
      .process_container {
        background: rgba(255, 187, 51, 0.2);

        .process_hand {
          background: #ffbb33;
        }
      }

      .card_icon {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_pod.svg);
      }
    }

    &.hints {
      .process_container {
        background: rgba(32, 112, 243, 0.2);

        .process_hand {
          background: #2070f3;
        }
      }

      .card_icon {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_pod.svg);
      }
    }

    .card_left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .card_info {
        font-size: 0.625rem;
        color: #bbbbbb;

        span {
          font-size: 1.25rem;
          color: #f5f5f5;
          display: inline-block;
          margin-right: 0.25rem;
        }
      }
    }

    .process_container {
      position: relative;
      width: 5rem;
      height: 0.25rem;
      border-radius: 0.0625rem;

      .process_hand {
        height: 100%;
        border-radius: 0.625rem;
        transition: all 0.3s ease;
        width: 50%;
      }
    }

    .card_icon {
      display: inline-block;
      width: 3.125rem;
      height: 3.125rem;
    }
  }
}

.statusInfo {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 1rem;
  margin-bottom: 1rem;
  .statusInfo_item {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
    width: 28%;
    span {
      display: inline-block;
      &.green {
        color: #00a874;
      }
    }
    :nth-child(1) {
      font-size: 0.875rem;
      color: #f5f5f5;
    }
    :nth-child(2) {
      color: #bbbbbb;
      line-height: 1.25rem;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }
  }
}
