import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/statusCard';
registerResource(i18n, 'control2D');
const statusEnum = [
  { en: 'all', zh: getMessage('control2D.card.all') },
  { en: 'urgent', zh: getMessage('control2D.card.urgent') },
  { en: 'important', zh: getMessage('control2D.card.important') },
  { en: 'minor', zh: getMessage('control2D.card.minor') },
  { en: 'hints', zh: getMessage('control2D.card.hints') },
];
const alarmData = [
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'urgent',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'important',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'minor',
    time: '2023/09/12 12:00:00',
  },

  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'hints',
    time: '2023/09/12 12:00:00',
  },
];
const eventData = [
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'urgent',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'urgent',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'important',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'important',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'minor',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'minor',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'minor',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'hints',
    time: '2023/09/12 12:00:00',
  },
  {
    title: getMessage('control2D.card.data.title'),
    describe: getMessage('control2D.card.data.describe'),
    status: 'hints',
    time: '2023/09/12 12:00:00',
  },
];
export { alarmData, statusEnum, eventData };
