import databaseNewSite from '@pages/businesstopo/a3dPages/network/server/databaseNewSite.json';
import databaseNewSite2 from '@pages/businesstopo/a3dPages/network/server/databaseNewSite2.json';
import databaseOldSite from '@pages/businesstopo/a3dPages/network/server/databaseOldSite.json';
import databaseSitesMap from '@pages/businesstopo/a3dPages/network/server/databaseSitesMap.json';
import overviewData from '@pages/businesstopo/a3dPages/network/server/overviewData.json';
import overviewGrayBasicInfo from '@pages/businesstopo/a3dPages/network/server/overviewGrayBasicInfo.json';
import overviewUpdateData from '@pages/businesstopo/a3dPages/network/server/overviewUpdateData.json';
import siteIndicatorData from '@pages/businesstopo/a3dPages/network/server/siteIndicatorData.json';
import siteIndicatorData1 from '@pages/businesstopo/a3dPages/network/server/siteIndicatorData1.json';
import siteIndicatorData2 from '@pages/businesstopo/a3dPages/network/server/siteIndicatorData2.json';
import siteIndicatorUpdateData from '@pages/businesstopo/a3dPages/network/server/siteIndicatorUpdateData.json';
import siteIndicatorUpdateData1 from '@pages/businesstopo/a3dPages/network/server/siteIndicatorUpdateData1.json';
import drillDownSiteData from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData.json';
import drillDownSiteData2 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData2.json';
import drillDownSiteData3 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData3.json';
import drillDownSiteData4 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData4.json';
import drillDownSiteData5 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData5.json';
import drillDownSiteData6 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData6.json';
import drillDownSiteData7 from '@pages/businesstopo/a3dPages/network/server/drillDownSiteData7.json';
import drillDownGrayBasicInfo from '@pages/businesstopo/a3dPages/network/server/drillDownGrayBasicInfo.json';
import drillDownSitePositionSetting from '@pages/businesstopo/a3dPages/network/server/drillDownSitePositionSetting.json';
import axios from 'axios';

export const api = axios.create({
  baseURL: '',
  timeout: 5000,
});

api.interceptors.request.use(
  (res) => res,
  (err) => err,
);

api.interceptors.response.use(
  (res) => res.data,
  (err) => err,
);


/** 站点下钻接口 */
export const getDrillDownSiteData = (siteId, stripIUnit) => new Promise((resolve) => {
  const siteMap = {
    1: drillDownSiteData,
    2: drillDownSiteData2,
    3: drillDownSiteData3,
    4: drillDownSiteData4,
    5: drillDownSiteData5,
    6: drillDownSiteData6,
    7: drillDownSiteData7,
    237815: drillDownSiteData,
    237816: drillDownSiteData2,
    137301: drillDownSiteData,
  };
  resolve( siteMap[stripIUnit] || siteMap[siteId]);
});

export const getDrillDownSitePositionSetting = () => new Promise((resolve) => {
  resolve(drillDownSitePositionSetting);
});

/** 数据库视图接口 */
// 获取站点数据
export const getSitesDatabase = (id) => new Promise((resolve) => {
  let result = databaseSitesMap[id];
  if (!result) {
    result = databaseSitesMap[Object.keys(databaseSitesMap)[0]];
  }
  resolve(result);
});

// 获取更新站点数据
export const getDatabaseOldSite = () => new Promise((resolve) => {
  resolve(databaseOldSite);
});

// 获取更新站点数据
export const getDatabaseNewSite = () => new Promise((resolve) => {
  resolve(databaseNewSite);
});

// 获取更新站点数据
export const getDatabaseNewSite2 = () => new Promise((resolve) => {
  resolve(databaseNewSite2);
});

/** 总览视图接口 */
// 获取总览视图布局
export const getOverviewData = () => new Promise((resolve) => {
  resolve(overviewData);
});

export const getOverviewUpdateData = () => new Promise((resolve) => {
  resolve(overviewUpdateData);
});

// 获取站点指标数据
export const getSiteIndicatorData = () => new Promise((resolve) => {
  resolve(siteIndicatorData);
});

export const getSiteIndicatorUpdateData = () => new Promise((resolve) => {
  resolve(siteIndicatorUpdateData);
});

// 获取站点指标数据
export const getSiteIndicatorData1 = () => new Promise((resolve) => {
  resolve(siteIndicatorData1);
});

export const getSiteIndicatorUpdateData1 = () => new Promise((resolve) => {
  resolve(siteIndicatorUpdateData1);
});

export const getSiteIndicatorData2 = () => new Promise((resolve) => {
  resolve(siteIndicatorData2);
});

// 总览视图，灰度升级
export const queryGrayBasicInfo = () => new Promise((resolve) => {
  resolve(overviewGrayBasicInfo);
});


// 灰度升级数据
export const getDrillDownGrayBasicInfo = () => new Promise((resolve) => {
  resolve(drillDownGrayBasicInfo);
});
