import React, { useEffect, useRef } from 'react';
import { initW3D, updatePodDrillData, destroyW3D } from './podDrillDown3D';
import './podDetailCard.less';
import PropTypes from 'prop-types';

function HostDrillDown({ pageParams }) {
  const w3dContainerRef = useRef();
  const eventRef = useRef();
  useEffect(() => {
    if (pageParams.data) {
      if (pageParams.isInit) {
        eventRef.current = pageParams.events;
        initW3D(w3dContainerRef, { initData: pageParams.data, eventArr: pageParams.events});
      } else {
        updatePodDrillData(pageParams.data);
      }
    }
  }, [pageParams]);

  useEffect(() => () => destroyW3D(eventRef.current), []);

  return (
    <div className="podDrillDownTopology">
      <div ref={w3dContainerRef} className="w3d_container" />
    </div>
  );
}

HostDrillDown.propTypes = {
  pageParams: PropTypes.shape({
    events: PropTypes.array,
    isInit:PropTypes.bool,
    data: PropTypes.shape({
      businessClusterList:PropTypes.array,
    }),
  }).isRequired,
};

export default HostDrillDown;
