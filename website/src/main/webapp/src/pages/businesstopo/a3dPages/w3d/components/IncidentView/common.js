import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import RingUtils from '../common/utils/RingUtils';

const A3D = getA3D();

/** 创建一个弧形底板 */
export function initRingGround(sizeSetting, parent) {
  const { siteLayerConfig, groundHeight } = sizeSetting;
  const geo = A3D.Geo.createCylinder({
    topRadius: siteLayerConfig.radius,
    bottomRadius: siteLayerConfig.radius,
    height: 0.2,
    radiusSegments: 64,
    heightSegments: 1,
  });
  const material = new A3D.C.MeshPhysicalMaterial({
    color: 0x303030, // 基本颜色
    transparent: true, // 启用透明度
    opacity: 0.8,
    transmission: 0.1,
    onBeforeCompile: (shader) => {
      const vertexShader = shader.vertexShader.slice(0, -2);
      const fragmentShader = shader.fragmentShader.slice(0, -2);
      // 修改片元着色器
      shader.vertexShader = `varying vec3 vCenter; 
      ${vertexShader}
      #ifdef USE_TRANSMISSION
vWorldPosition = worldPosition.xyz;
#endif
vCenter = modelMatrix[3].xyz;}`;
      shader.fragmentShader = ` varying vec3 vCenter;
      ${fragmentShader} 
      float gradient = distance(vWorldPosition.z, vCenter.z) / 30.0;
if (vWorldPosition.z - vCenter.z > 0.0) {
  gl_FragColor.a *= gradient;
} else {
  gl_FragColor.a *= 0.001;
}
}`;
    },
  });

  const ground = A3D.mesh(geo, material);
  ground.pointerEvents = 'none';
  ground.scale.copy(siteLayerConfig.scale);
  ground.renderOrder = 1;

  parent.add(ground);
}

/** 计算站点实例的位置 */
export function getSiteItemPosition(sizeSetting, siteCount) {
  const { siteLayerConfig, groundHeight } = sizeSetting;
  const space = siteCount > 15 ? 0.02 : 0.035;
  const offsetZ = siteCount > 15 ? groundHeight * 10 : groundHeight * 12;

  let start = 0;
  if (siteCount % 2 === 0) {
    const count = siteCount === 2 ? 3 : siteCount;
    start = -space * ((count - 2) / 2);
  } else {
    start = -space * ((siteCount - 1) / 2);
  }

  const posArr = [];
  for (let i = 0; i < siteCount; i++) {
    const output = {};
    RingUtils.getPointAt(start + space * i, siteLayerConfig.radius, output, siteLayerConfig.startRadian);
    posArr[i] = {
      x: output.x * siteLayerConfig.scale.x,
      y: 0,
      z: output.y * siteLayerConfig.scale.z - offsetZ,
    };
  }
  return posArr;
}
