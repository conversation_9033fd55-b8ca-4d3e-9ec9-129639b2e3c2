import { baseUrl } from '@pages/businesstopo/a3dPages/global';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export default {
  ':scene': {
    matType: 'Basic',
    setting: { color: '#171719' },
  },

  'site-ground': {
    matType: 'Lambert',
    setting: {
      color: '#202123',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'business-ground': {
    matType: 'Basic',
    setting: {
      color: '#383d46',
      opacity: 0.2,
      transparent: true,
      depthWrite: false,
      blending: A3D.C.AdditiveBlending,
    },
  },
  'business-ground-focus': {
    matType: 'Lambert',
    setting: {
      color: '#202d40',
      transparent: true,
      depthWrite: false,
    },
  },
  'app-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-emissiveMap.png`,
      },
    },
  },
  'app-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-emissiveMap.png`,
      },
    },
  },
  'app-grey': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/grey/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/grey/app-emissiveMap.png`,
      },
    },
  },
  'database-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/normal/database-emissiveMap.png`,
      },
    },
  },
  'database-grey': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/grey/database-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/database/grey/database-emissiveMap.png`,
      },
    },
  },
  ring: {
    matType: 'Lambert',
    setting: {
      color: '#303030', transparent: true, opacity: 0.8, depthWrite: false,
    },
  },
  'universal-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-emissiveMap.png`,
      },
    },
  },
  'site-team-line': {
    matType: 'Basic',
    setting: {
      color: '#234465',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 3,
        repeatY: 1,
      },
    },
  },
  'site-team-ground': {
    matType: 'Basic',
    setting: {
      color: '#27282a',
      opacity: 0.5,
      transparent: true,
      depthWrite: false,
    },
  },
  'site-item-normal': {
    matType: 'Lambert',
    setting: { color: '#27282a' },
  },
  'site-item-related': {
    matType: 'Lambert',
    setting: { color: '#202d40' },
  },
  'site-item-alarm': {
    matType: 'Lambert',
    setting: { color: '#3B1D1D' },
  },
  'site-item-hover': {
    matType: 'Lambert',
    setting: { color: '#343538' },
  },

  'circle-normal': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/normal-circle.png`,
      },
    },
  },
  'circle-alarm': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/alarm-circle.png`,
      },
    },
  },
};
