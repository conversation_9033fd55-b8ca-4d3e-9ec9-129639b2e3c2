export default class Layout {
  static initLayout(level1Data, aspectRatio) {
    const num = Math.ceil(Math.sqrt(level1Data.count / 4));
    const MIN_COL = Math.min(Math.max(Math.pow(2, num - 2), 4), num * 4);
    const computedCount = level1Data.children.reduce((pre, level2Data) => {
      let level2Count = level2Data.children.reduce((pre2, cur) => {
        let result = 2;
        if (cur.count) {
          result = Math.ceil(cur.count / MIN_COL) * MIN_COL;
        }
        return pre2 + result;
      }, 0);
      const MIN_COUNT = 2;
      level2Count = Math.max(MIN_COUNT, level2Count);
      return pre + level2Count;
    }, 0);
    const maxRow = Math.ceil(Math.sqrt(computedCount / aspectRatio));
    const maxCol = maxRow * aspectRatio;

    return this.#getLayoutSetting(level1Data, maxRow, maxCol);
  }

  static #getLayoutSetting(level1Data, maxRow, maxCol) {
    const level1Layout = [{ row: maxRow, col: 0, group: [] }];
    this.#setLayout(level1Layout, level1Data, maxCol);
    const level1Setting = {
      row: level1Layout.reduce((total, cur) => total + cur.row, 0),
      col: level1Layout.reduce((total, cur) => Math.max(total, cur.col), 0),
      level1: level1Layout,
    };
    return level1Setting;
  }

  static #setLayout(level1Layout, level1Data, maxCol) {
    let level1GroupIndex = 0;
    const maxIndex = level1Layout.length;
    for (let i = 0, len = level1Data.children.length; i < len; i++) {
      const level2Data = level1Data.children[i];
      let level2Setting = this.#getLevel2Setting(level2Data, level1Layout[level1GroupIndex].row, maxCol);

      if (level1Layout[level1GroupIndex].col + level2Setting.col > maxCol) {
        if (level1GroupIndex < maxIndex - 1) {
          level1GroupIndex++;
          level2Setting = this.#getLevel2Setting(level2Data, level1Layout[level1GroupIndex].row, maxCol);
        } else {
          // 超出最长列数，重置，重新布局
          level1Layout[0].row--;
          level1Layout.forEach((level1Group) => {
            level1Group.col = 0;
            level1Group.group.length = 0;
          });
          level1Layout.push({ row: 1, col: 0, group: [] });
          this.#setLayout(level1Layout, level1Data, maxCol);
          return;
        }
      }
      level1Layout[level1GroupIndex].col += level2Setting.col;
      level1Layout[level1GroupIndex].group.push(level2Setting);
    }
    for (let i = level1Layout.length - 1; i >= 0; i--) {
      if (level1Layout[i].group.length === 0) {
        level1Layout.pop();
      }
    }
  }

  static #getLevel2Setting(level2Data, row, maxCol) {
    const level2Layout = [{ row, col: 0, group: [] }];
    this.#setLevel2Layout(level2Layout, level2Data, maxCol);
    const level2Setting = {
      data: level2Data.data || {},
      row: level2Layout.reduce((total, cur) => total + cur.row, 0),
      col: level2Layout.reduce((total, cur) => Math.max(total, cur.col), 0),
      level2: level2Layout,
    };
    return level2Setting;
  }

  static #setLevel2Layout(level2Layout, level2Data, maxCol) {
    let level2GroupIndex = 0;
    const maxIndex = level2Layout.length;
    for (let i = 0, len = level2Data.children.length; i < len; i++) {
      const level3Data = level2Data.children[i];
      let level3Setting = this.#getLevel3Setting(level3Data, level2Layout[level2GroupIndex].row);
      if (level2Layout[level2GroupIndex].col + level3Setting.col > maxCol) {
        if (level2GroupIndex < maxIndex - 1) {
          level2GroupIndex++;
          level3Setting = this.#getLevel3Setting(level3Data, level2Layout[level2GroupIndex].row);
        } else {
          // 超出最长列数，重置，重新布局
          level2Layout[0].row--;
          level2Layout.forEach((level2Group) => {
            level2Group.col = 0;
            level2Group.group.length = 0;
          });
          level2Layout.push({ row: 1, col: 0, group: [] });
          this.#setLevel2Layout(level2Layout, level2Data, maxCol);
          return;
        }
      }
      level2Layout[level2GroupIndex].col += level3Setting.col;
      level2Layout[level2GroupIndex].group.push(level3Setting);
    }
    for (let i = level2Layout.length - 1; i >= 0; i--) {
      if (level2Layout[i].group.length === 0) {
        level2Layout.pop();
      }
    }
  }

  static #getLevel3Setting(level3Data, curRow) {
    const curCount = level3Data.count || 1;
    const col = Math.ceil(curCount / curRow / 2) * 2;
    const level3Layout = [];
    let stayCount = level3Data.count;
    while (stayCount > 0) {
      level3Layout.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    const row = level3Layout.length;
    return {
      data: level3Data.data || {}, col, row, level3: level3Layout,
    };
  }
}
