import A3D from '@a3d/a3d';
import {
  getBoundingBox, getCenter, fixPathToCenter, recoverPathFromCenter,
  setShapeWithCorners, setShapeWithoutCorners, isPathClockwise,
} from '../utils/pathUtils';

const MIN_LEN = 2;
const MIN_PATH_LEN = 3;
const DEFAULT_SHAPE = [
  {
    x: 1,
    y: 0,
    z: 1,
  },
  {
    x: 1,
    y: 0,
    z: -1,
  },
  {
    x: -1,
    y: 0,
    z: -1,
  },
  {
    x: -1,
    y: 0,
    z: 1,
  },
];
const defaultMaterial = new A3D.C.MeshStandardMaterial();
const defaultQuaternion = new A3D.C.Quaternion().setFromUnitVectors(
  new A3D.C.Vector3(0, 0, 1),
  new A3D.C.Vector3(0, 1, 0),
);

const VEC3_LEN = 3;
const worldUVGenerator = { pathPosition: 0 };

/**
 * 返回顶底面的uv
 * @param {Object} size
 * @param {Number} length
 * @param {THREE.BufferGeometry} geometry
 * @param {Array} vertices
 * @param {Number} indexA
 * @param {Number} indexB
 * @param {Number} indexC
 * @returns
 */
const generateTopUV = (size, length, geometry, ...rest) => {
  const [vertices, indexA, indexB, indexC] = rest;
  const ax = vertices[ indexA * VEC3_LEN ];
  const ay = vertices[ indexA * VEC3_LEN + 1 ];
  const bx = vertices[ indexB * VEC3_LEN ];
  const by = vertices[ indexB * VEC3_LEN + 1 ];
  const cx = vertices[ indexC * VEC3_LEN ];
  const cy = vertices[ indexC * VEC3_LEN + 1 ];
  const sizeX = size.x;
  const halfX = sizeX / 2;
  const sizeY = size.z;
  const halfY = sizeY / 2;
  return [
    new A3D.C.Vector2((ax + halfX) / sizeX, (ay + halfY) / sizeY),
    new A3D.C.Vector2((bx + halfX) / sizeX, (by + halfY) / sizeY),
    new A3D.C.Vector2((cx + halfX) / sizeX, (cy + halfY) / sizeY),
  ];
};

const getDistance = (x1, y1, x2, y2) => Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));

/**
 * 返回侧面的uv
 * @param {Object} size
 * @param {Number} length
 * @param {THREE.BufferGeometry} geometry
 * @param {Array} vertices
 * @param {Number} indexA
 * @param {Number} indexB
 * @param {Number} indexC
 * @param {Number} indexD
 * @returns
 */
const generateSideWallUV = (size, length, geometry, ...rest) => {
  const [vertices, indexA, indexB, indexC, indexD] = rest;
  const ax = vertices[ indexA * VEC3_LEN ];
  const ay = vertices[ indexA * VEC3_LEN + 1 ];
  const az = vertices[ indexA * VEC3_LEN + 2 ];
  const bx = vertices[ indexB * VEC3_LEN ];
  const by = vertices[ indexB * VEC3_LEN + 1 ];
  const bz = vertices[ indexB * VEC3_LEN + 2 ];
  const cz = vertices[ indexC * VEC3_LEN + 2 ];
  const dz = vertices[ indexD * VEC3_LEN + 2 ];

  const sizeY = size.y;
  if (geometry.groups[0].count === indexA) {
    worldUVGenerator.pathPosition = 0;
  }
  const startPos = worldUVGenerator.pathPosition;
  worldUVGenerator.pathPosition = startPos + getDistance(ax, ay, bx, by) / length;
  return [
    new A3D.C.Vector2(startPos, az / sizeY),
    new A3D.C.Vector2(worldUVGenerator.pathPosition, bz / sizeY),
    new A3D.C.Vector2(worldUVGenerator.pathPosition, cz / sizeY),
    new A3D.C.Vector2(startPos, dz / sizeY),
  ];
};

const getUVGenerator = (shape, depth, length) => {
  const { size } = getBoundingBox(shape);
  size.y = depth;
  worldUVGenerator.generateTopUV = generateTopUV.bind(null, size, length);
  worldUVGenerator.generateSideWallUV = generateSideWallUV.bind(null, size, length);
  return worldUVGenerator;
};

/**
 * @config {JSON} options
 * @config {JSON} [options.path] - 拉伸的形状
 * @config {JSON} [options.depth] - 需要拉伸的深度
 * @config {JSON} [options.radius] - 拐点圆角半径
 * @config {JSON} [options.radiusSegments] - 圆角分段数
 * @config {Material|Material[]} material - 拉伸物体的材质，单材质或者长度为3的材质数组
 */
export default class ExtrudeShape extends A3D.C.Object3D {
  constructor(options, material = defaultMaterial) {
    super();
    this.name = 'ExtrudeShape';
    if (options) {
      this.parameters = A3D.util.extend(false, {
        material,
        depth: 1,
        path: DEFAULT_SHAPE,
        radius: 0,
        radiusSegments: 8,
      }, options);

      if (Array.isArray(this.parameters.material)) {
        let matlen = this.parameters.material.length;
        while (matlen < MIN_LEN) {
          this.parameters.material.push(defaultMaterial);
          matlen++;
        }
      }
      this.#init();
    }
  }

  copy(source) {
    super.copy(source, true);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    return this;
  }

  /**
   * 更新组件
   * @param {Object} options
   */
  set(options) {
    A3D.util.extend(false, this.parameters, options);
    this.#fixPathToCreateGeo((center) => {
      const geometry = this.#getGeometry();
      this.children[0].geometry.dispose();
      this.children[0].geometry = geometry;
      this.children[0].position.copy(center);
    });
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.children[0].geometry.dispose();
    this.remove(...this.children);
    this.parameters = null;
  }

  /**
   * 初始化
   */
  #init() {
    this.#fixPathToCreateGeo((center) => {
      const geometry = this.#getGeometry();
      const mesh = new A3D.C.Mesh(geometry, this.parameters.material);
      mesh.position.copy(center);
      this.add(mesh);
    });
  }

  /**
   * 在处理 geometry 前后转化数据、优化数据
   * @param {Function} createGeoFn 创建/更新 geometry 的方法
   */
  #fixPathToCreateGeo(createGeoFn) {
    const center = getCenter(this.parameters.path);
    fixPathToCenter(this.parameters.path, center);
    const isClockwise = isPathClockwise(this.parameters.path);
    if (!isClockwise) {
      this.parameters.path.reverse();
      if (Array.isArray(this.parameters.radius)) {
        this.parameters.radius.reverse();
      }
    }

    createGeoFn(center);

    if (!isClockwise) {
      this.parameters.path.reverse();
      if (Array.isArray(this.parameters.radius)) {
        this.parameters.radius.reverse();
      }
    }
    recoverPathFromCenter(this.parameters.path, center);
  }

  /**
   * 获取拉伸组件的 geometry
   * @returns
   */
  #getGeometry() {
    const shape = this.#getShape();
    const length = shape.getLength();
    const extrudeSettings = {
      curveSegments: this.parameters.radiusSegments,
      depth: this.parameters.depth,
      bevelEnabled: false,
      steps: 1,
      UVGenerator: getUVGenerator(this.parameters.path, this.parameters.depth, length),
    };
    const geometry = new A3D.C.ExtrudeGeometry(shape, extrudeSettings);
    geometry.applyQuaternion(defaultQuaternion);

    this.#resetUvGroups(geometry);
    return geometry;
  }

  /**
   * 获取 shape 对象
   * @returns
   */
  #getShape() {
    const shape = new A3D.C.Shape();
    const points = this.parameters.path;
    const isHasRadius = this.#isHasRadius();
    if (this.parameters.radiusSegments > 0 && isHasRadius && points.length >= MIN_PATH_LEN) {
      setShapeWithCorners(shape, points, this.parameters.radius, true);
    } else {
      setShapeWithoutCorners(shape, points, true);
    }
    return shape;
  }

  /**
   * 是否有大于 0 的 radius
   * @returns
   */
  #isHasRadius() {
    if (Array.isArray(this.parameters.radius)) {
      return this.parameters.radius.some((item) => Number(item) > 0);
    }
    return Number(this.parameters.radius) > 0;
  }

  /**
   * 调整 geometry 的 uv 分组
   * @param {THREE.BufferGeometry} geometry
   */
  #resetUvGroups(geometry) {
    const group = geometry.groups;
    geometry.clearGroups();
    const count = group[0].count;
    geometry.addGroup(0, count / 2, 1);
    geometry.addGroup(count / 2, count, 0);
    geometry.addGroup(count, group[1].count, 2);
  }
}
