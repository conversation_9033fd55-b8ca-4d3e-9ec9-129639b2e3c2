import { createIntl, createIntlCache } from '@cloudsop/horizon-intl';
import i18nResource from './locales';

const cache = createIntlCache();
const _lang = document.cookie.indexOf('zh-cn') === -1 ? 'en-us' : 'zh-cn';

export const _isZh = _lang === 'zh-cn';

const _langResource = {
  'en-us': {},
  'zh-cn': {},
};

const parse = (res) => {
  // 国际化资源文件有key 'en-us' 和 'zh-cn'
  if (res['en-us'] || res['zh-cn']) {
    Object.keys(res).forEach((localKey) => {
      Object.assign(_langResource[localKey], res[localKey]);
    });
  } else {
    // 国际化资源文件内容是数组形式，如 btn_ok: ['OK', '确定']
    Object.keys(res).forEach((key) => {
      const [en_, zh_] = res[key] || [null, null];
      _langResource['en-us'][key] = en_;
      _langResource['zh-cn'][key] = zh_;
    });
  }
};
// 解析默认的一些系统国际化资源
parse(i18nResource);

let _intl = null;

export const getMessage = (key, values) => {
  if (!key) {
    return '';
  }
  let result;
  if (_intl) {
    result = _intl.formatMessage({ id: key }, values);
  }
  result = result || key;
  return result;
};

const createNewIntl = (locale, messages) => createIntl({ locale, messages }, cache);
const initIntl = (langResource) => {
  parse(langResource);
  _intl = createNewIntl(_lang, _langResource[_lang]);
  return _intl;
};
const i18nCache = {};
export const registerResource = (_i18nResource, tag) => {
  if (!tag) {
    return;
  }

  if (!_intl) {
    initIntl({});
  }

  if (!i18nCache[tag]) {
    parse(_i18nResource);
    Object.assign(_intl.messages, _langResource[_lang]);
  }
};
