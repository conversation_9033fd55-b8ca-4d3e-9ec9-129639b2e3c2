import React from 'react';
import ReactDOM from 'react-dom';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {getMessage, registerResource} from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/MMOverview';
import {eventBusHandler, getRouteQuery, sortListByName} from '../utils';
import './MMOverview.less';
import Fault from '../../../components/tooltip/fault';
import Business from '../../../components/tooltip/business';
import {getOverViewGridMM, queryMMOverViewGridLine} from './api';
import {queryCurrentIds} from '../../../api';
import {isZh} from '../../../../../commonUtil';

registerResource(i18n, 'w3d');
let home3D;
let nowSolutionId;
let selectId;
let initData;

const getData = (data) => {
  data.stripeTeamList.forEach(item =>{
    item.group = sortListByName(item.groupList, 'groupName');
  });
  sortListByName(data.stripeTeamList, 'stripeTeamName');
  sortListByName(data.businessList, 'businessName');
  sortListByName(data.channelList, 'channelName');
  data.businessList.sort((a, b) => a.sortOrder - b.sortOrder);
  data.channelList.sort((a, b) => a.sortOrder - b.sortOrder);

  let stripData = data.stripeTeamList.sort((a, b) => {
    if (a.isManage === b.isManage) {
      return 0;
    }
    return a.isManage ? 1 : -1;
  });
  const result = {
    mmOverview: {
      thirdParty: data.businessList.map((business) => {
        const _business = {
          id: business.businessId,
          name: business.businessName,
          indicatorList: business.indicatorList,
          associatedChannelList: business.associatedChannelList,
          alarmCsn: business.alarmCsn,
        };
        return _business;
      }),
      channel: data.channelList.map((channel) => {
        const _channel = {
          id: channel.channelId,
          name: channel.channelName,
          indicatorList: channel.indicatorList,
          alarmCsn: channel.alarmCsn,
        };
        return _channel;
      }),
      strip: {
        strips: stripData.map((strip) => {
          const _strip = {
            id: strip.stripeTeamId,
            name: strip.stripeTeamName,
            group: strip.groupList.map((group) => {
              const _group = {
                id: group.groupId,
                name: group.groupName,
                healthy: group.isHealthy,
                alarmCount: group.alarmCount,
                alarmCsn: group.alarmCsn,
              };
              return _group;
            }),
            isManage: strip.isManage,
            indicatorList: strip.indicatorList,
          };
          return _strip;
        }),
      },
      lineThird2Channel: [],
      lineChannel2StripIns: [],
    },
    halo: {},
  };
  initData = result;
  return result;
};

async function updateChannelLine(setting) {
  selectId = setting.id;
  let topoSession = JSON.parse(
    sessionStorage.getItem('topoSession') || '{}',
  );
  let param = {
    currentTime: topoSession.selectedTime ? topoSession.selectedTime : 0,
    solutionId: nowSolutionId,
    instanceId: setting.id,
  };
  const mmOverViewGridLine = await queryMMOverViewGridLine(param);
  addIndictorName(mmOverViewGridLine);
  home3D.updateLineThird2Channel(mmOverViewGridLine.data.lineThird2Channel);
}

const events = [
  {
    eventName: 'from3d_showPanel',
    // 2D-3D对接，点击事件，联动右侧面板
    async fn(setting) {
      switch (setting.type) {
        case 'app':
          await updateChannelLine(setting);
          break;
        case 'channel':
          await updateChannelLine(setting);
          break;
        default: {
          if (selectId !== '') {
            let topoSession2 = JSON.parse(
              sessionStorage.getItem('topoSession') || '{}',
            );
            const mmOverViewGridLine2 = await queryMMOverViewGridLine({
              currentTime: topoSession2.selectedTime ? topoSession2.selectedTime : 0, solutionId: nowSolutionId,
            });
            addIndictorName(mmOverViewGridLine2);
            home3D.updateLineThird2Channel(mmOverViewGridLine2.data.lineThird2Channel);
          }
          selectId = '';
          break;
        }
      }
    },
  },
  {
    eventName: 'from3d_showCommonTip',
    // 2D-3D对接，hover业务 app/pod/vm ，显示卡片
    fn(setting) {
      setting.dom.innerHTML = '';
      if (setting.type === 'channel') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-app-card');
        dom.innerHTML = `
          <div>${setting.data.name}</div>
        `;
        setting.dom.append(dom);
      }
      if (setting.type === 'app') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-app-card');
        dom.innerHTML = `
          <div>${setting.data.name}</div>
        `;
        setting.dom.append(dom);
      }
      if (setting.type === 'strip') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-vm-card');
        dom.innerHTML = `
          <div>${setting.data.name}</div>
        `;
        setting.dom.append(dom);
      }
    },
  },
  {
    eventName: 'from3d_showPodDrillDownDetail',
    // 2D-3D对接，hover业务 app/pod/vm ，显示卡片
    fn(setting) {
      if (setting.type === 'thirdParty') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-podDetail-third-block');
        dom.innerText = getMessage('w3d.thirdParty');
        if (window.screen.width > 3800) {
          dom.style = `transform: translate(${isZh ? -155 : -170}px, 0);`;
        } else if (window.screen.width > 2400) {
          dom.style = `transform: translate(${isZh ? -60 : -20}px, 0);`;
        } else {
          dom.style = `transform: translate(${isZh ? 40 : 56}px, 0);`;
        }
        setting.dom.append(dom);
      } else if (setting.type === 'strip') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-podDetail-block');
        dom.innerText = getMessage('w3d.strips');
        if (window.screen.width > 3800) {
          dom.style = `transform: translate(${isZh ? 0 : 0}px, 0);`;
        } else if (window.screen.width > 2400) {
          dom.style = `transform: translate(${isZh ? 0 : 55}px, 0);`;
        } else {
          dom.style = `transform: translate(${isZh ? 30 : 55}px, 0);`;
        }
        setting.dom.append(dom);
      } else if (setting.type === 'channel') {
        const dom = document.createElement('div');
        dom.classList.add('w3d-podDetail-channel-block');
        if (window.screen.width > 3800) {
          dom.style = `transform: translate(${isZh ? -236 : -234}px, 0);`;
        } else if (window.screen.width > 2400) {
          dom.style = `transform: translate(${isZh ? -95 : -45}px, 0);`;
        } else {
          dom.style = `transform: translate(${isZh ? 20 : 50}px, 0);`;
        }

        dom.innerText = getMessage('w3d.channel');
        setting.dom.append(dom);
      }
    },
  },
  {
    // 2D-3D对接，hover tps连线，显示卡片
    eventName: 'from3d_showTPSCard',
    fn(setting) {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      let indicatorId = '';
      let channelData = initData.mmOverview.channel.find(item => item.id === setting.data.id);
      let stipeData = initData.mmOverview.strip.strips.find(item => item.id === setting.data.id);
      let param = {};
      if (selectId && !stipeData) {
        indicatorId = setting.data.indicatorList.find(item => item.indicatorDisplayType === 1).indicatorId;
        let businessData = initData.mmOverview.thirdParty.find(item => item.id === selectId);
        if (businessData) {
          param.businessId = selectId;
          indicatorId = businessData.indicatorList.find(item => item.indicatorDisplayType === 1).indicatorId;
        } else {
          param.channelId = selectId;
        }
      } else {
        if (channelData) {
          indicatorId = setting.data.indicatorList.find(item => item.indicatorDisplayType === 1).indicatorId;
          param.channelId = setting.data.id;
        } else {
          param.stripeId = setting.data.id;
        }
      }

      ReactDOM.render(
        <Business
          isTimeTrack={topoSession.isTimeTrack}
          selectedTime={topoSession.selectedTime}
          goldIndicator={indicatorId}
          isMM={true}
          linkData={{
            params: {
              ...param,
            },
          }}
        />,
        setting.dom,
      );
    },
  }, {
    // 2D-3D对接，hover tps连线，显示卡片
    eventName: 'from3d_showStripGroupTip',
    fn(setting) {
      if (setting.data.alarmCsn !== undefined && setting.data.alarmCsn !== '') {
        let topoSession = JSON.parse(
          sessionStorage.getItem('topoSession') || '{}',
        );
        ReactDOM.render(
          <Fault faultData={{
            params: {
              csnList: setting.data.alarmCsn,
              timestamp: topoSession.selectedTime,
            },
            isMM: true,
            isManager: setting.data.isManager,
            event:setting.event,
          }} isTimeTrack={topoSession.isTimeTrack}
          />,
          setting.dom,
        );
      }
    },
  }, {
    // 2D-3D对接，点击条带
    eventName: 'from3d_clickStrip',
    fn(setting) {
      // 点击条带
    },
  },
  {
    // 2D-3D对接，双击条带
    eventName: 'from3d_dbClickStrip',
    fn(setting) {
      let {
        solutionId,
        selectedTime,
      } = JSON.parse(sessionStorage.getItem('topoSession'));
      if (setting.isManage) {
        return;
      }
      if (selectedTime === 0) {
        destroyW3D();
        const query = `stripId=${setting.id}&stripName=${setting.name}&solutionId=${nowSolutionId}`;
        let hash = `path=/businesstopo/mmStripDrillDown&${query}&_t=${Math.round(Date.now() / 1000)}`;
        window.location.hash = hash;
        return;
      }
      let param = {
        instanceIdList: [],
        timestamp: selectedTime,
        targetTimestamp: 0,
        solutionType: 3,
      };
      param.instanceIdList.push(nowSolutionId);
      param.instanceIdList.push(setting.id);
      queryCurrentIds(param, ({data:idMap}) => {
        destroyW3D();
        const query = `stripId=${idMap[setting.id]}&stripName=${setting.name}&&solutionId=${idMap[nowSolutionId]}`;
        let hash = `path=/businesstopo/mmStripDrillDown&${query}&_t=${Math.round(Date.now() / 1000)}`;
        window.location.hash = hash;
      });
    },
  },
];

export const updateData = async(data) => {
  // 2D-3D对接，更新数据
  eventBusHandler.emit('to3d_updateMMOverviewData', data);
  eventBusHandler.emit('from3d_setGridData', data);
};


const addIndictorName = (mmOverViewGridLine) => {
  mmOverViewGridLine.data.lineThird2Channel.forEach(channel => {
    if (channel.name) {
      channel.name = channel.name.length > 10 ? `${channel.name.slice(0, 5)}...` : channel.name;
    } else {
      channel.name = '';
    }
  });
  mmOverViewGridLine.data.lineChannel2StripIns.forEach(strip => {
    if (strip.name) {
      strip.name = strip.name.length > 10 ? `${strip.name.slice(0, 5)}...` : strip.name;
    } else {
      strip.name = '';
    }
  });
};

export const initW3D = async(container, solutionId, dispatch) => {
  nowSolutionId = '';
  events.forEach((item) => {
    eventBus.addListener(item.eventName, item.fn);
  });
  let {
    isTimeTrack,
    selectedTime,
  } = JSON.parse(sessionStorage.getItem('topoSession'));
  let timestamp = 0;
  if (isTimeTrack) {
    timestamp = selectedTime;
  }
  // 2D-3D对接，将数据转化成3D需要的数据
  const mmOverViewGrid = await getOverViewGridMM({solutionId, timestamp});
  if (mmOverViewGrid.data.hasFiltered && mmOverViewGrid.data.hasFiltered === 1) {
    dispatch({
      hasFiltered: true,
    });
  }
  nowSolutionId = solutionId;
  const data = getData(mmOverViewGrid.data);
  if (!home3D) {
    home3D = new Home3D(eventBusHandler);
  }
  window.eventBusHandler = eventBusHandler;
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, data);
  const mmOverViewGridLine = await queryMMOverViewGridLine({currentTime: timestamp, solutionId});
  addIndictorName(mmOverViewGridLine);
  home3D.updateLineThird2Channel(mmOverViewGridLine.data.lineThird2Channel);
  home3D.updateLineChannel2Strip(mmOverViewGridLine.data.lineChannel2StripIns);
};

export const updateOverViewGridMM = async(solutionId) => {
  const mmOverViewGrid = await getOverViewGridMM({solutionId, timestamp: 0});
  const data = getData(mmOverViewGrid.data);
  let param = {currentTime: 0, solutionId};
  if (selectId) {
    param.instanceId = selectId;
  }
  const mmOverViewGridLine = await queryMMOverViewGridLine(param);
  addIndictorName(mmOverViewGridLine);
  home3D.updateLineThird2Channel(mmOverViewGridLine.data.lineThird2Channel);
  home3D.updateLineChannel2Strip(mmOverViewGridLine.data.lineChannel2StripIns);
  // 后节点更新数据，renderonce
  updateData(data.mmOverview);
};

export const destroyW3D = () => {
  events.forEach((item) => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};
