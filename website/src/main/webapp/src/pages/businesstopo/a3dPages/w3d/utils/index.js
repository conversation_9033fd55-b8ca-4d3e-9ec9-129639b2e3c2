import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import W3D from './W3D';
import ComponentManager from './manager/component';
import CardManager from './manager/card';
import { models, textures, images } from './global';
import { createGui } from './gui';
import { resetSetCameraByMesh } from './resetSetCamera';

export {
  getA3D, W3D, ComponentManager, CardManager, models, textures, images, createGui, resetSetCameraByMesh,
};

export const onEvent = function(eventBusHandler, events) {
  events.forEach((event) => {
    eventBusHandler.on(event.name, event.callback);
  });
};

export const offEvent = function(eventBusHandler, events) {
  events.forEach((event) => {
    eventBusHandler.off(event.name, event.callback);
  });
};
