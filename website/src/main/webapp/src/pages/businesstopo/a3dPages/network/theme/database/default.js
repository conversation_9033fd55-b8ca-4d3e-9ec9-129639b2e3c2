import { baseUrl } from '@pages/businesstopo/a3dPages/global';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export default {
  ':scene': {
    matType: 'Basic',
    setting: { color: '#171719' },
  },

  'site-ground': {
    matType: 'Lambert',
    setting: {
      color: '#222222',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'site-ground-hover': {
    matType: 'Lambert',
    setting: {
      color: '#2e2e30',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'business-line': {
    matType: 'Basic',
    setting: {
      color: '#3e4f64',
      transparent: true,
      depthWrite: false,
      side: A3D.C.DoubleSide,
    },
  },
  'db-ground': {
    matType: 'Basic',
    setting: {
      color: '#383d46',
      opacity: 0.2,
      transparent: true,
      depthWrite: false,
      blending: A3D.C.AdditiveBlending,
    },
  },
  'pod-line': {
    matType: 'Basic',
    setting: {
      color: '#5c85b4',
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 2,
        repeatY: 1,
      },
      transparent: true,
      depthWrite: false,
    },
  },
  'pod-default-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/emissiveMap.png`,
      },
    },
  },
  'pod-A-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/A-emissiveMap.png`,
      },
    },
  },
  'pod-R-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/R-emissiveMap.png`,
      },
    },
  },
  'pod-Ra-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/Ra-emissiveMap.png`,
      },
    },
  },
  'pod-Rs-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/Rs-emissiveMap.png`,
      },
    },
  },
  'pod-S-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/normal/S-emissiveMap.png`,
      },
    },
  },
  'pod-A-warning': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/A-emissiveMap.png`,
      },
    },
  },
  'pod-R-warning': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/R-emissiveMap.png`,
      },
    },
  },
  'pod-Ra-warning': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/Ra-emissiveMap.png`,
      },
    },
  },
  'pod-Rs-warning': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/Rs-emissiveMap.png`,
      },
    },
  },
  'pod-S-warning': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/warning/S-emissiveMap.png`,
      },
    },
  },
  'pod-default-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/emissiveMap.png`,
      },
    },
  },
  'pod-A-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/A-emissiveMap.png`,
      },
    },
  },
  'pod-R-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/R-emissiveMap.png`,
      },
    },
  },
  'pod-Ra-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/Ra-emissiveMap.png`,
      },
    },
  },
  'pod-Rs-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/Rs-emissiveMap.png`,
      },
    },
  },
  'pod-S-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/pod/alarm/S-emissiveMap.png`,
      },
    },
  },

};
