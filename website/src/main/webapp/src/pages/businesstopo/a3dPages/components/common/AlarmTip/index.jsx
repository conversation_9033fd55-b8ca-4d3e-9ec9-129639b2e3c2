import { useState, useEffect } from 'react';
import './index.less';
import AlarmCard from './AlarmCard';
import PropTypes from 'prop-types';

const pageSize = 3;

function AlarmTip(props) {
  const [type, setType] = useState('alarm');
  const [pageIndex, setPageIndex] = useState(1);
  const [recordCount, setRcodCount] = useState(0);
  const [mapData, setMapData] = useState([]);
  const { alarmData } = props;
  const changeData = () => {
    const list = alarmData[type];
    const start = (pageIndex - 1) * pageSize;
    const end = pageIndex * pageSize;
    setRcodCount(list.length);
    setMapData(list.slice(start, end));
  };
  useEffect(() => {
    changeData();
  }, [pageIndex]);
  useEffect(() => {
    if (pageIndex !== 1) {
      setPageIndex(1);
    }
    changeData();
  }, [type]);
  useEffect(() => {
    changeData();
  }, []);
  useEffect(() => {
    setType('alarm');
  }, [alarmData]);
  return (
    <div className='alarmTipDom'>
      <div className='pagination'>
        <span className='pre page' style={{ visibility: pageIndex === 1 ? 'hidden' : 'visible' }}
          onClick={() => {
            setPageIndex(pageIndex - 1);
          }}
        />
        <span className='next page' style={{ visibility: pageIndex * pageSize >= recordCount ? 'hidden' : 'visible' }}
          onClick={() => {
            setPageIndex(pageIndex + 1);
          }}
        />
      </div>
      <div className="alarmTip_tabs">
        <span onClick={() => setType('alarm')} className={type === 'alarm' ? 'active tip' : 'tip'}>
          告警(
          {alarmData?.alarm?.length}
          )
          <span className='line' />
        </span>
        <span onClick={() => {
          setType('event');
        }}
        className={type === 'event' ? 'active tip' : 'tip'}
        style={{ visibility: alarmData?.event?.length === 0 ? 'hidden' : 'visible' }}
        >
          事件(
          {alarmData?.event?.length}
          )
          <span className='line' />
        </span>
        <span className='close' onClick={
          () => {
            document.getElementById('alarmSeletedTooltip').style.display = 'none';
          }
        }
        />
      </div>
      <AlarmCard data={mapData} type={type} state={alarmData.type} />
    </div>
  );
}

AlarmTip.propTypes = {
  alarmData: PropTypes.shape({
    alarm: PropTypes.array,
    event: PropTypes.array,
    type: PropTypes.string,
  }).isRequired,
};

export default AlarmTip;
