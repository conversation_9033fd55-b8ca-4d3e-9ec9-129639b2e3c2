import {models} from '../../..';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {bindDblclickEvents} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/utils';

const A3D = getA3D();

export const getPodDrillDownSetting = (data, w3d) => {
  const vmGeo = models.vm.children[0].geometry;
  vmGeo.computeBoundingBox();
  const size = vmGeo.boundingBox.getSize(new A3D.C.Vector3());
  const scale = 0.6 / size.x;
  vmGeo.scale(scale, scale, scale);

  const modelMap = {
    app: models.app.children[0].clone(),
    database: models.database.children[0].clone(),
    pod: models.pod.children[0].clone(),
    vm: models.vm.children[0].clone(),
  };
  const matMap = {
    app: {
      normal: w3d.themeManager.referStyle('app-normal'),
      normalUnselect: w3d.themeManager.referStyle('app-normal-unselect'),
      alarm: w3d.themeManager.referStyle('app-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('app-alarm-unselect'),
    },
    database: {
      normal: w3d.themeManager.referStyle('database-normal'),
      normalUnselect: w3d.themeManager.referStyle('database-normal-unselect'),
      alarm: w3d.themeManager.referStyle('database-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('database-alarm-unselect'),
    },
    pod: {
      normal: w3d.themeManager.referStyle('pod-normal'),
      normalUnselect: w3d.themeManager.referStyle('pod-normal-unselect'),
      alarm: w3d.themeManager.referStyle('pod-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('pod-alarm-unselect'),
      gray: w3d.themeManager.referStyle('pod-grey'),
      grayUnselect: w3d.themeManager.referStyle('pod-grey-unselect'),
    },
    vm: {
      normal: w3d.themeManager.referStyle('vm-normal'),
      normalUnselect: w3d.themeManager.referStyle('vm-normal-unselect'),
      alarm: w3d.themeManager.referStyle('vm-alarm'),
      alarmUnselect: w3d.themeManager.referStyle('vm-alarm-unselect'),
    },
    ground: w3d.themeManager.referStyle('ground'),
    blockGround: w3d.themeManager.referStyle('block-ground'),
    blockGroundSelect: w3d.themeManager.referStyle('block-ground-select'),
    blockGroundLine: w3d.themeManager.referStyle('block-ground-line'),
    blockGroundLineSelect: w3d.themeManager.referStyle('block-ground-line-select'),
  };
  return {modelMap, matMap};
};

const createCard = (data, modelItem, w3d, componentManager, type) => {
  const id = data.dnId;
  const cardType = 'podDrillDown';
  componentManager.cardManager.updateCard(
    cardType,
    {id},
    modelItem,
  );
  const card = componentManager.cardManager.cards[cardType][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showCommonTip({
    data,
    dom: domContainer,
    type,
  });
};

const bindPodHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(info.object, (data, appItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!isSelected) {
        componentManager.events.hoverInObject(appItem);
      }
      createCard(data, appItem, w3d, componentManager, 'app');
    }, (data, podItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!isSelected) {
        componentManager.events.hoverInObject(podItem);
        createCard(data, podItem, w3d, componentManager, 'pod');
      }
    }, (data, vmItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      if (!isSelected) {
        componentManager.events.hoverInObject(vmItem);
        createCard(data, vmItem, w3d, componentManager, 'vm');
      }
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(info.object, (data, appItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!isSelected) {
        componentManager.events.hoverOutObject(appItem);
      }
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
    }, (data, podItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!isSelected) {
        componentManager.events.hoverOutObject(podItem);
        componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      }
    }, (data, vmItem, isSelected) => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      if (!isSelected) {
        componentManager.events.hoverOutObject(vmItem);
        componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      }
    });
    w3d.agent.renderOnce();
    return true;
  });
};

const bindPodClickEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'click', (e, info) => {
    compIns.select(info.object, (data, appItem, isSelectApp) => {
      componentManager.events.showPanel({
        type: 'app',
        id: data.dnId,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      if (isSelectApp) {
        componentManager.events.selectObject(null);
        componentManager.events.selectApp(appItem);
      } else {
        componentManager.events.selectObject(appItem);
      }
    }, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'pod',
        id: data.dnId,
        name: data.name,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      componentManager.events.selectObject(podItem);
    }, (data, vmItem) => {
      componentManager.events.showPanel({
        type: 'vm',
        id: data.dnId,
        name: data.name,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      componentManager.events.selectObject(vmItem);
    }, () => {
      componentManager.events.selectNull();
    });
    return true;
  });
};

export const initDefault = (compIns, w3d, componentManager) => {
  const isGray = compIns.getIsGray();
  const clickSelected = compIns.getClickSelected();
  const defaultSelected = compIns.getDefaultSelected();
  compIns.select(null);
  componentManager.events.selectObject(null);
  componentManager.events.selectApp(null);
  if (defaultSelected && (isGray || !clickSelected)) {
    compIns.select(defaultSelected, (data, appItem, isSelectApp) => {
      if (isSelectApp) {
        componentManager.events.selectApp(appItem);
      } else {
        componentManager.events.selectObject(appItem);
      }
    });
  }
  if (clickSelected) {
    compIns.select(clickSelected, null, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'pod',
        id: data.dnId,
        name: data.name,
      });
      componentManager.events.selectObject(podItem);
    }, (data, vmItem) => {
      componentManager.events.showPanel({
        type: 'vm',
        id: data.dnId,
        name: data.name,
      });
      componentManager.events.selectObject(vmItem);
    });
  }

  const cardContainerMap = compIns.getCardMap();
  Object.keys(cardContainerMap).forEach((type) => {
    const container = cardContainerMap[type];
    const id = type;
    const cardType = 'podDrillDownDeital';
    componentManager.cardManager.removeCard({id}, cardType);
    componentManager.cardManager.updateCard(
      cardType,
      {id},
      container,
    );
    const card = componentManager.cardManager.cards[cardType][id];
    const d2Object = card.children[0];
    const domContainer = d2Object.element.children[0];
    componentManager.events.showPodDrillDownDeital({
      data: {},
      dom: domContainer,
      type,
    });
  });
};

const bindDbPodClickEvents = (compIns, w3d, componentManager) => {
  bindDblclickEvents(compIns, (e, info) => {
    compIns.select(info.object, (data, appItem, isSelectApp) => {
      componentManager.events.showPanel({
        type: 'app',
        id: data.dnId,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      if (isSelectApp) {
        componentManager.events.selectObject(null);
        componentManager.events.selectApp(appItem);
      } else {
        componentManager.events.selectObject(appItem);
      }
    }, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'pod',
        id: data.dnId,
        name: data.name,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      componentManager.events.selectObject(podItem);
    }, (data, vmItem) => {
      componentManager.events.showPanel({
        type: 'vm',
        id: data.dnId,
        name: data.name,
      });
      componentManager.cardManager.removeCard({id: data.dnId}, 'podDrillDown');
      componentManager.events.selectObject(vmItem);
    }, () => {
      componentManager.events.selectNull();
    });
    return true;
  }, (e, info) => {
    compIns.drillDown(info.object, (vmItem, vmData) => {
      componentManager.events.drillDownHostType({
        alarmStatus: vmData.alarmStatus,
        dnId: vmData.dnId,
        ip: vmItem.ip,
        name: vmItem.name,
        status: vmItem.status,
      });
    });
  });
};


export const bindPodDrillDownEvents = (compIns, w3d, componentManager) => {
  initDefault(compIns, w3d, componentManager);
  bindPodHoverEvents(compIns, w3d, componentManager);
  bindDbPodClickEvents(compIns, w3d, componentManager);
};
