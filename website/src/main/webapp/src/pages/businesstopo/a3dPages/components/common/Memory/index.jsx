
import HuiCharts from '@hui/charts';
import { tpsData1, tpsData2 } from './chartData';
import StatusCard from '../StatusCard';
import React, { useRef, useEffect, useState } from 'react';
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/memory';
import PropTypes from 'prop-types';

registerResource(i18n, 'control2D');
const tabsInfo = [getMessage('control2D.tabs.message'), getMessage('control2D.tabs.thread')];

function Memory(props) {
  const chartRef = useRef(null);
  const chart2Ref = useRef(null);
  const [tabIndex, setTabIndex] = useState(0);
  useEffect(() => {
    if (tabIndex === 0) {
      chartRef.current.innerHTML = '';
      chartRef.current.removeAttribute('_echarts_instance_');
      const chartIns1 = new HuiCharts();
      chartIns1.init(chartRef?.current);
      chartIns1.setSimpleOption('AreaChart', tpsData1.option);
      setTimeout(() => {
        chartIns1.render();
      }, 50);
    } else {
      chart2Ref.current.innerHTML = '';
      chart2Ref.current.removeAttribute('_echarts_instance_');
      const chartIns2 = new HuiCharts();
      chartIns2.init(chart2Ref?.current);
      chartIns2.setSimpleOption('AreaChart', tpsData2.option);
      setTimeout(() => {
        chartIns2.render();
      }, 50);
    }
  }, [tabIndex]);
  return (
    <>
      <div className="KPI_top">
        <span className="KPI_title">
          {getMessage('control2D.overview')}
          {' '}
          {props.siteId}
        </span>
      </div>
      <div className="KPI_content">
        <div className="KPI_tabs">
          {tabsInfo.map((item, index) => (
            <span onClick={() => {
              setTabIndex(index);
            }}
            className={tabIndex === index ? 'active tip' : 'tip'} key={index}
            >
              {item}
              <span className='line' />
            </span>
          ))}
        </div>
        <div className='KPI_chart' >
          <div ref={chartRef} className='chart' style={{ display: tabIndex === 0 ? 'block' : 'none' }} />
          <div ref={chart2Ref} className='chart' style={{ display: tabIndex === 1 ? 'block' : 'none' }} />
        </div>
        <StatusCard />
      </div>
    </>
  );
}

Memory.propTypes = { siteId: PropTypes.string };

export default Memory;
