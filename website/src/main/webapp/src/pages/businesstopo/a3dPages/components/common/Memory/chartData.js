import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/memory';
registerResource(i18n, 'control3D');
import delayData from './delayData';
import usageData from './usageData';
import { getChartsData } from '../PodDetails/utils';
const chartTheme = 'dark';
const tpsData1 = {
  name: 'AreaChart',
  option: {
    theme: chartTheme,
    smooth: true,
    area: true,
    legend: { show: true },
    padding: [48, 16, 48, 0],
    color: ['#2070F3', '#00A874', '#36DDF2'],
    data: getChartsData(delayData),
    xAxis: {
      data: 'time',
      fullGrid: true,
      interval: 100,
    },
    yAxis: {
      name: getMessage('control2D.charts.yName'),
      nameTextStyle: { padding: [20, -20] },
    },
  },
};
const tpsData2 = {
  name: 'AreaChart',
  option: {
    theme: chartTheme,
    smooth: true,
    area: true,
    legend: { show: true },
    padding: [48, 16, 48, 0],
    color: ['#2070F3', '#00A874', '#36DDF2'],
    data: getChartsData(usageData),
    xAxis: {
      data: 'time',
      fullGrid: true,
      interval: 100,
    },
    yAxis: {
      name: getMessage('control2D.charts.yName'),
      nameTextStyle: { padding: [20, -20] },
    },
  },
};

export { tpsData1, tpsData2 };
