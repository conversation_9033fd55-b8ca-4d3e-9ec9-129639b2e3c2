import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { getGroundOptions, getTextPlane, getTextToTex, getTypeItem, getWallLine, drawRoundedRect } from '../utils';
import ExtrudeShape from '../common/ExtrudeShape';
import { getObjectSize, getUrlParam } from '../common/utils';
import { initRingGround, getSiteItemPosition } from './common';

const A3D = getA3D();

const NAME_COLOR_NORMAL = '#F5F5F5';
const NAME_COLOR_SELECT = '#2E94FF';
const NAME_COLOR_UNSELECT = '#BBBBBB';
const SHOW_NAME_COUNT = 8;
const MAX_ITEM_PER_ROW = 24; // 一行最多放24个网元
const MIN_ITEM_PER_ROW = 5; // 一行最少放5个网元（实际少于5个时按照5个计算每列宽度）
const ROW_RATIO = 8;
const COMMON_SCALE_RATIO = 0.5;

export default class IncidentPerception extends A3D.C.Object3D {
  #data;

  #size;

  #modelMap;

  #matMap;

  #cardMap = {};

  #w3d;

  #animation;

  #sizeSetting = {};

  #siteDefaultScale = 1;

  #appDefaultScale = 1;

  #podDefaultScale = 1;

  #vmDefaultScale = 1;

  #meshMaxSize = 1;

  #siteMap = {};

  #appMap = {};

  #podMap = {};

  #vmMap = {};

  #selectedSiteTopoId = ''; // 站点实例的siteId
  #selectedSiteId = ''; // 站点实例的dn

  #selectedAppId = ''; // 当前选中的app实例的dn

  #selectedPodId = ''; // 当前选中的pod实例的dn

  #selectedVmId = ''; // 当前选中的vm实例的dn

  #relationId = []; // 预留，用于业务分组之间的关系展示

  constructor(data, size, modelMap, matMap, w3d) {
    super();
    this.#data = data;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#w3d = w3d;
    this.#animation = new A3D.Animation(this.#w3d.agent);

    const {width, depth} = this.#size;
    const offsetY = depth * 0.15;
    this.#sizeSetting.groundHeight = width * 0.008;
    this.#sizeSetting.siteWidth = width;
    this.#sizeSetting.siteDepth = depth;
    this.#sizeSetting.appWidth = width;
    this.#sizeSetting.appDepth = depth * 1.5;
    this.#sizeSetting.podWidth = width;
    this.#sizeSetting.podDepth = depth;
    this.#sizeSetting.vmWidth = width * 0.9;
    this.#sizeSetting.vmDepth = depth * 0.5;
    this.#sizeSetting.sitePosY = offsetY;
    this.#sizeSetting.sitePosZ = -(this.#sizeSetting.siteDepth + this.#sizeSetting.appDepth);
    this.#sizeSetting.appPosY = 0;
    this.#sizeSetting.appPosZ = -(this.#sizeSetting.siteDepth + this.#sizeSetting.appDepth) / 2;
    this.#sizeSetting.podPosY = -offsetY;
    this.#sizeSetting.podPosZ = 0;
    this.#sizeSetting.vmPosY = -offsetY * 2;
    this.#sizeSetting.vmPosZ = (this.#sizeSetting.vmDepth + this.#sizeSetting.podDepth) / 2;

    this.#meshMaxSize = width * 0.04;

    this.#sizeSetting.nameSize = depth * 0.07;
    this.#sizeSetting.space = depth * 0.03;
    this.#sizeSetting.spriteNameSize = width * 0.012;
    this.#sizeSetting.spriteNameSize2 = 0.012;

    this.#sizeSetting.siteLayerConfig = {
      scale: { x: 5, y: 1, z: 1 },
      radius: 12,
      startRadian: -Math.PI / 2,
    };

    this.#init();
  }

  /**
   * 通用方法，用于处理 hoverIn 交互事件并执行特定的回调
   * 
   * 会通过 IncidentView 实例来调用
   */
  hoverIn(object3D, ...callbacks) {
    const [appCallback, podCallback, vmCallback, siteCallback] = callbacks ?? [];
    const siteItem = getTypeItem(object3D, 'site');
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (siteItem) {
      const siteId = siteItem.userData.id;
      const isSelected = this.#selectedSiteId === siteId;
      const siteData = this.#siteMap[siteId].data;
      if (siteCallback) {
        siteCallback(siteData, siteItem, isSelected);
      }
    }
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#appMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, isSelected);
      }
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedPodId === podId;
      const data = this.#podMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, isSelected);
      }
    }
    if (vmItem) {
      const vmId = vmItem.userData.id;
      const isSelected = this.#selectedVmId === vmId;
      const data = this.#vmMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, vmItem, isSelected);
      }
    }
  }

  /**
   * 通用方法，用于处理 hoverOut 交互事件并执行特定的回调
   * 
   * 会通过 IncidentView 实例来调用
   */
  hoverOut(object3D, ...callbacks) {
    const [appCallback, podCallback, vmCallback, siteCallback, otherCallback] = callbacks ?? [];
    const siteItem = getTypeItem(object3D, 'site');
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (siteItem) {
      const siteId = siteItem.userData.id;
      const isSelected = this.#selectedSiteId === siteId;
      const siteData = this.#siteMap[siteId].data;
      siteCallback?.(siteData, siteItem, isSelected);
      return;
    }
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#appMap[appId].data;
      appCallback?.(data, appItem, isSelected);
      return;
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedPodId === podId;
      const data = this.#podMap[podId].data;
      podCallback?.(data, podItem, isSelected);
      return;
    }
    if (vmItem) {
      const vmId = vmItem.userData.id;
      const isSelected = this.#selectedVmId === vmId;
      const data = this.#vmMap[vmId].data;
      vmCallback?.(data, vmItem, isSelected);
      return;
    }
    otherCallback?.({ siteId: this.#selectedSiteTopoId });
  }

  /**
   * 通用方法，用于处理 select 交互事件并执行特定的回调
   * 
   * 会通过 IncidentView 实例来调用
   */
  select(object3D, ...callbacks) {
    const [appCallback, podCallback, vmCallback, siteCallback, otherCallback] = callbacks ?? [];
    const siteItem = getTypeItem(object3D, 'site');
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'pod');
    const vmItem = getTypeItem(object3D, 'vm');
    if (siteItem) {
      const siteId = siteItem.userData.id;
      const data = this.#siteMap[siteId].data;
      this.#selectSite(data.dn, data.siteId);
      siteCallback?.(data, siteItem, this.#data.dataGroupBySite.find(item => item.siteInfo.dn === siteId));
    } else if (appItem) {
      const appId = appItem.userData.id;
      const hasSelectedBefore = appId === this.#selectedAppId;
      this.#selectedAppId = appId;
      this.#selectedPodId = '';
      this.#selectedVmId = '';
      this.#selectPod('');
      this.#selectVm('');
      this.#selectApp(appId, hasSelectedBefore);
      appCallback?.(this.#appMap[appId].data, appItem);
    } else if (podItem) {
      const podId = podItem.userData.id;
      const hasSelectedBefore = podId === this.#selectedPodId;
      this.#selectedAppId = '';
      this.#selectedPodId = podId;
      this.#selectedVmId = '';
      this.#selectApp('');
      this.#selectVm('');
      this.#selectPod(podId, hasSelectedBefore);
      podCallback?.(this.#podMap[podId].data, podItem);
    } else if (vmItem) {
      const vmId = vmItem.userData.id;
      const hasSelectedBefore = vmId === this.#selectedVmId;
      this.#selectedAppId = '';
      this.#selectedPodId = '';
      this.#selectedVmId = vmId;
      this.#selectApp('');
      this.#selectPod('');
      this.#selectVm(vmId, hasSelectedBefore);
      vmCallback?.(this.#vmMap[vmId].data, vmItem);
    } else {
      this.#selectedAppId = '';
      this.#selectedPodId = '';
      this.#selectedVmId = '';
      this.#selectApp('');
      this.#selectPod('');
      this.#selectVm('');
      otherCallback?.();
    }
  }

  // 暂时没被调用，一般是在 handler 的 update 里调用
  update(data) {
    // 暂时未用到 console.info('update invoked!');
    this.#clear();
    this.#data = data;
    this.#init();
  }

  // 通过 incidentView.updateStatus 调用
  updateStatus(data) {
    // 暂时未用到 console.info('updateStatus invoked!');
    this.#clear();
    this.#updateStatusData(data);

    this.#initContainer();
    this.#initSiteLayer();
    this.#initAppLayer();
    this.#initPodLayer();
    this.#initVmLayer();
  }

  getDefaultSelected() {
    if (!this.#selectedSiteId) {
      return null;
    }
    return this.#siteMap[this.#selectedSiteId].mesh?.children[0];
  }

  getClickSelected() {
    const app = this.#appMap[this.#selectedAppId]?.mesh?.children[0];
    const pod = this.#podMap[this.#selectedPodId]?.mesh?.children[0];
    const vm = this.#vmMap[this.#selectedVmId]?.mesh?.children[0];
    return app || pod || vm || null;
  }

  updateSelected(data) {
    // 暂时未用到 console.info('updateSelected invoked!');
  }

  getCardMap() {
    return this.#cardMap;
  }

  #clear() {
    this.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material.dispose();
      }
    });
    this.remove(...this.children);
  }

  #updateStatusData(data) {
    this.#data = data;

    this.#initData(this.#selectedSiteTopoId);
    if (!this.#appMap[this.#selectedAppId]) {
      this.#selectedAppId = '';
    }
    if (!this.#podMap[this.#selectedPodId]) {
      this.#selectedPodId = '';
    }
    if (!this.#vmMap[this.#selectedVmId]) {
      this.#selectedVmId = '';
    }
  }

  /**
   * 选中一个site实例，需要切换展示其下的app、pod、vm
   */
  #selectSite(siteId, siteTopoId) {
    if (this.#selectedSiteId === siteId) {
      return;
    }
    this.#selectedSiteId = siteId;
    this.#selectedSiteTopoId = siteTopoId;

    this.#clear();
    this.#init();
  }

  /**
   * 选中一个app实例，需要高亮其子网元，同时更新所有app实例的选中状态
   */
  #selectApp(appId, hasSelectedBefore = false) {
    if (hasSelectedBefore) {
      return;
    }
    for (const [id, appItem] of Object.entries(this.#appMap)) {
      const isSelected = appId === id;
      const { mesh, data } = appItem;
      if (mesh) {
        mesh.userData.isSelected = isSelected;
      }
      this.#setAppStatus(id, isSelected);
      this.#setPodBlockStatus(id, isSelected);
      if (data.childDNs) {
        for (const podId of data.childDNs) {
          this.#setPodStatus(podId, isSelected);
        }
      }
    }
  }

  /**
   * 选中一个pod实例，需要高亮其父网元和其部署主机，并更新所有pod实例的选中状态
   */
  #selectPod(podId, hasSelectedBefore = false) {
    if (hasSelectedBefore) {
      return;
    }
    const appHandled = {};
    const vmHandled = {};
    for (const [id, podItem] of Object.entries(this.#podMap)) {
      const { mesh, data } = podItem;
      const isPodSelected = (podId === id);
      if (mesh) {
        mesh.userData.isSelected = isPodSelected;
        this.#setPodStatus(id, isPodSelected);
      }
      if (data?.parentDN) {
        if (!appHandled[data.parentDN]) {
          // 多个pod的父网元相同时，选中其中一个pod，父网元都应该高亮
          this.#setAppStatus(data.parentDN, isPodSelected);
          appHandled[data.parentDN] = isPodSelected;
        }
      }
      if (data?.sourceDeployMoDNs) {
        for (const vmDN of data.sourceDeployMoDNs) {
          // 多个pod为同一个host部署时，选中其中一个pod，host都应该高亮
          if (!vmHandled[vmDN]) {
            this.#setVmStatus(vmDN, isPodSelected);
            vmHandled[vmDN] = isPodSelected;
          }
        }
      }
    }
  }

  /**
   * 选中一个vm实例，需要高亮其部署的pod，并更新所有vm实例的选中状态
   */
  #selectVm(vmId, hasSelectedBefore = false) {
    if (hasSelectedBefore) {
      return;
    }
    for (const [id, vmItem] of Object.entries(this.#vmMap)) {
      const { mesh, data } = vmItem;
      const isVmSelected = (vmId === id);
      if (mesh) {
        mesh.userData.isSelected = isVmSelected;
        this.#setVmStatus(id, isVmSelected);
      }
      if (data?.targetDeployMoDNs) {
        for (const podDN of data.targetDeployMoDNs) {
          this.#setPodStatus(podDN, isVmSelected);
        }
      }
    }
  }

  /** 初始化布局-start */
  #init() {
    if (!this.#selectedSiteId) {
      this.#selectedSiteId = this.#data.dataGroupBySite[0].siteInfo.dn;
      this.#selectedSiteTopoId = this.#data.dataGroupBySite[0].siteInfo.siteId;
    }
    this.#selectedAppId = '';
    this.#selectedPodId = '';
    this.#selectedVmId = '';
    this.#initData(this.#selectedSiteTopoId);

    this.#initContainer();
    this.#initSiteLayer();
    this.#initAppLayer();
    this.#initPodLayer();
    this.#initVmLayer();
  }

  /**
   * 初始化所有站点实例的数据，以及当前所选站点实例下的app、pod、vm
   */
  #initData(selectedSiteId) {
    const oldSiteMap = this.#siteMap;
    const oldAppMap = this.#appMap;
    const oldPodMap = this.#podMap;
    const oldVmMap = this.#vmMap;

    this.#siteMap = {};
    this.#appMap = {};
    this.#podMap = {};
    this.#vmMap = {};

    for (const siteItem of this.#data.dataGroupBySite) {
      const siteInfo = siteItem.siteInfo;
      this.#siteMap[siteInfo.dn] = { data: siteInfo };
      if (oldSiteMap[siteInfo.dn]) {
        // 复用之前的mesh
        this.#siteMap[siteInfo.dn].mesh = oldSiteMap[siteInfo.dn].mesh;
      }

      if (siteInfo.siteId !== selectedSiteId) {
        // 只初始化当前选中的站点实例 下的app、pod、vm
        continue;
      }

      const appList = Object.values(siteItem.appList);
      for (const appItem of appList) {
        this.#appMap[appItem.dn] = { data: appItem };
        if (oldAppMap[appItem.dn]) {
          this.#appMap[appItem.dn].mesh = oldAppMap[appItem.dn].mesh;
        }
      }

      const podList = Object.values(siteItem.podList);
      for (const podItem of podList) {
        this.#podMap[podItem.dn] = { data: podItem };
        if (oldPodMap[podItem.dn]) {
          this.#podMap[podItem.dn].mesh = oldPodMap[podItem.dn].mesh;
        }
      }

      const vmList = Object.values(siteItem.vmList);
      for (const vmItem of vmList) {
        this.#vmMap[vmItem.dn] = { data: vmItem };
        if (oldVmMap[vmItem.dn]) {
          this.#vmMap[vmItem.dn].mesh = oldVmMap[vmItem.dn].mesh;
        }
      }
    }
  }

  /**
   * 创建2个存放3D物料的组
   */
  #initContainer() {
    const container = new A3D.C.Group(); // 这个group会存入到 this.children[0]
    container.name = 'container';
    container.position.z = this.#size.offset;
    const lineGroup = new A3D.C.Group(); // 这个group会存入到 this.children[1]
    lineGroup.name = 'lineGroup';
    this.add(container, lineGroup);
  }

  #initSiteLayer() {
    const siteGroup = new A3D.C.Group();
    siteGroup.name = 'siteGroup';
    this.children[0].add(siteGroup);

    const { siteWidth, siteDepth, sitePosY, sitePosZ } = this.#sizeSetting;
    siteGroup.position.set(0, sitePosY, sitePosZ);

    initRingGround(this.#sizeSetting, siteGroup);
    this.#initSiteList(siteGroup);
    this.#initGroundName(siteGroup, siteWidth, 'site');
  }

  #initAppLayer() {
    const appGroup = new A3D.C.Group();
    appGroup.name = 'appGroup';
    this.children[0].add(appGroup);

    const {
      appWidth, appDepth, appPosY, appPosZ,
    } = this.#sizeSetting;
    appGroup.position.set(0, appPosY, appPosZ);
    this.#initGround(appGroup, appWidth, appDepth);
    this.#initAppList(appGroup);
    this.#initGroundName(appGroup, appWidth, 'app');
    this.#cardMap.app.position.x -= this.#size.width * 0.12;
  }

  #initPodLayer() {
    const podGroup = new A3D.C.Group();
    podGroup.name = 'podGroup';
    this.children[0].add(podGroup);

    const {
      podWidth, podDepth, podPosY, podPosZ,
    } = this.#sizeSetting;
    podGroup.position.set(0, podPosY, podPosZ);
    this.#initGround(podGroup, podWidth, podDepth);
    this.#initPodList(podGroup);
    this.#initGroundName(podGroup, podWidth, 'pod');
  }

  #initVmLayer() {
    const vmGroup = new A3D.C.Group();
    vmGroup.name = 'podGroup';
    this.children[0].add(vmGroup);

    let {
      vmWidth, vmDepth, vmPosY, vmPosZ,
    } = this.#sizeSetting;
    vmGroup.position.set(0, vmPosY, vmPosZ);
    this.#initGround(vmGroup, vmWidth, vmDepth);
    this.#initVmList(vmGroup);
    this.#initGroundName(vmGroup, vmWidth, 'vm');
  }

  /** 创建site内容 */
  #initSiteList(siteGroup) {
    const data = this.#data.dataGroupBySite;
    const { siteWidth, siteDepth } = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, siteWidth, siteDepth);

    const { perWidth, perDepth } = layoutData;
    this.#siteDefaultScale = Math.min(Math.min(perDepth, perWidth) * COMMON_SCALE_RATIO, this.#meshMaxSize);

    const siteListGroup = this.#drawSiteList(layoutData, data);
    siteGroup.add(siteListGroup);
  }

  #drawSiteList(layoutData, data) {
    const siteListGroup = new A3D.C.Group();
    siteListGroup.name = 'siteListGroup';

    const posArr = getSiteItemPosition(this.#sizeSetting, data.length);
    data.forEach((siteItem, index) => {
      this.#initSiteItem(siteItem.siteInfo, posArr[index], siteListGroup, true);
    });

    return siteListGroup;
  }

  #initSiteItem(siteData, position, container, isShowName) {
    let siteItem;
    if (this.#siteMap[siteData.dn].mesh) {
      siteItem = this.#siteMap[siteData.dn].mesh;
    } else {
      siteItem = new A3D.C.Group();
      const siteMesh = new A3D.C.Mesh(this.#modelMap.site.geometry, this.#matMap.site.normal);
      siteItem.add(siteMesh);
    }
    siteItem.children[0].scale.set(this.#siteDefaultScale, this.#siteDefaultScale, this.#siteDefaultScale);
    siteItem.userData.isShowName = isShowName;
    siteItem.userData.isSelected = (this.#selectedSiteId === siteData.dn || this.#relationId.includes(siteData.dn));

    const modelSize = this.#getMeshSize(siteItem.children[0]);

    const nameMesh = siteItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getSiteSprite(siteData, modelSize);
      siteItem.add(sprite);
    }

    if (siteData.eventList && siteData.eventList.length > 0) {
      const sprite = this.#initAlarmDisc(siteData.eventList.length, modelSize);
      siteItem.add(sprite);
    }

    siteItem.position.copy(position);
    container.add(siteItem);

    this.#clickable(siteItem, siteData.dn, 'site');
    this.#setSiteStatus(siteData.dn, siteItem.userData.isSelected);
  }

  #getSiteSprite(itemData, modelSize) {
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex([itemData.moName]),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth * 1.2 + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setSiteStatus(id, isSelected) {
    const siteItem = this.#siteMap[id];
    if (siteItem.data.alarmStatus === 1) {
      siteItem.mesh.children[0].material = isSelected ? this.#matMap.site.alarm : this.#matMap.site.alarmUnselect;
    } else {
      siteItem.mesh.children[0].material = isSelected ? this.#matMap.site.normal : this.#matMap.site.normalUnselect;
    }
    const nameMesh = siteItem.mesh.getObjectByProperty('type', 'Sprite');
    if (siteItem.mesh.userData.isShowName && nameMesh) {
      const nameColor = isSelected ? NAME_COLOR_SELECT : NAME_COLOR_UNSELECT;
      nameMesh.material.color.set(nameColor);
    }
  }

  /** 创建app内容 */
  #initAppList(appGroup) {
    const curSelectedSite = this.#data.dataGroupBySite.find(site => site.siteInfo.siteId === this.#selectedSiteTopoId);
    if (!curSelectedSite?.appList || curSelectedSite.appList.length === 0) {
      return;
    }

    const appList = Object.values(curSelectedSite.appList);
    const {appWidth, appDepth} = this.#sizeSetting;

    // 把app层的网元按照 业务分组+网元类型 进行分组
    const appListGrouped = {};
    for (const appItem of appList) {
      if (!appListGrouped[appItem.groupId]) {
        appListGrouped[appItem.groupId] = { name: appItem.moTypeGroupName, appCount: 0, moTypeCount: 0, list: {} };
      }
      if (!appListGrouped[appItem.groupId].list[appItem.moType]) {
        appListGrouped[appItem.groupId].list[appItem.moType] = [];
        appListGrouped[appItem.groupId].moTypeCount += 1;
      }
      appListGrouped[appItem.groupId].list[appItem.moType].push(appItem);
      appListGrouped[appItem.groupId].appCount += 1;
    }

    const layoutData = this.#getAppLayoutData(appList, appListGrouped);
    this.#resetAppLayout(layoutData, appGroup);
  }

  /**
   * 获取app的布局方式
   * @returns 一个map。map的key：业务分组ID，map的值：业务分组内部的整体布局信息和其下每个网元类型的布局信息
   */
  #getAppLayoutData(appList, appListGrouped) {
    const result = {};

    // 依次计算每个业务分组的内部布局
    for (const [businessGroupId, businessGroupData] of Object.entries(appListGrouped)) {
      const allNECount = businessGroupData.appCount;
      let totalCol = 0; // 整体布局的总列数
      let totalRow = 1; // 整体布局的总行数
      if (allNECount <= MAX_ITEM_PER_ROW) {
        // 网元总数不超过每行最大数，则所有网元放在一行，列数等于总数
        totalCol = allNECount;
        totalRow = 1;
      } else {
        totalCol = MAX_ITEM_PER_ROW;
        totalRow = Math.ceil(allNECount / totalCol);
        if (totalCol < totalRow * 4) {
          // 如果列数 col 小于 row * 4，说明行数太多，列数太少，布局不美观，此时重新计算列数，保证列数是 4 的倍数
          totalCol = Math.ceil(Math.sqrt(allNECount / 4)) * 4;
          totalRow = Math.ceil(allNECount / totalCol);
        }
      }
      // 重置总列数，在后面根据每个网元类型分组算出来的占据列数进行累加
      totalCol = 0;
      // 给每个分组分配相同的行数（totalRow），分配的列数根据分组的网元数量计算
      const layout = Object.entries(businessGroupData.list).map(([appMoType, apps]) => {
        // 给当前分组分配的行数
        const rows = totalRow;
        // 给当前分组分配的列数
        let cols = allNECount > MAX_ITEM_PER_ROW ?
          Math.floor(apps.length / allNECount * MAX_ITEM_PER_ROW) : // 按比例分配列数
          apps.length;
        // 如果分配的行数*列数 不够放分组的网元，则分组的列数递增+1
        while (cols === 0 || (rows * cols < apps.length)) {
          cols += 1;
        }
        totalCol += cols;
        const groupLayout = {
          row: rows,
          col: cols,
          data: { appMoType, name: apps[0].moTypeName, apps },
          level1: [], // 如果当前网元类型下有 50 个网元，列数 curCols 为 24，那么 level1 = [24, 24, 2]
        };
        let neCountToArrange = apps.length;
        while (neCountToArrange > 0) {
          groupLayout.level1.push(Math.min(neCountToArrange, cols));
          neCountToArrange -= cols;
        }
        return groupLayout;
      });

      result[businessGroupId] = {
        row: totalRow, // 每个业务分组占据的行数
        col: totalCol, // 每个业务分组占据的列数
        data: { name: businessGroupData.name, appCount: businessGroupData.appCount },
        children: layout, // 一个列表，每一项是一个网元类型分组的布局信息
      };
    }

    return result;
  }

  /**
   * 根据布局信息，给每个分组分配宽度和位置
   */
  #resetAppLayout(layoutData, appGroup) {
    const { appWidth, appDepth, nameSize, space } = this.#sizeSetting;

    let rows = 0; // app层底板需要拆分的行数
    let cols = 0; // app层底板需要拆分的列数
    const businessGroups = Object.entries(layoutData);
    for (const [businessGroupId, businessGroupBlock] of businessGroups) {
      rows += businessGroupBlock.row;
      cols = Math.max(cols, businessGroupBlock.col);
    }

    const outerAvailableWidth = appWidth - space * 2; // 减去业务分组的左右外边距
    const outerAvailableDepth = appDepth - space * (rows + 1); // 减去业务分组的上下外边距 .. 这里还要调整，现在业务分组的depth会超出底板
    let lastZ = -appDepth / 2 + 2 * space;
    for (const [businessGroupId, businessGroupBlock] of businessGroups) {
      businessGroupBlock.perWidth = outerAvailableWidth / cols;
      businessGroupBlock.perDepth = outerAvailableDepth / rows;
      businessGroupBlock.size = {
        width: businessGroupBlock.perWidth * businessGroupBlock.col,
        depth: businessGroupBlock.perDepth * businessGroupBlock.row,
      };
      businessGroupBlock.position = { x: 0, y: 0, z: lastZ };
      lastZ += businessGroupBlock.size.depth + space;
  
      const blockCount = businessGroupBlock.children.length;
      const innerAvailableWidth = outerAvailableWidth - (blockCount * 3 + 1) * space; // 基于业务分组的宽度，再减去类型分组自身的左右外边距
      const innerAvailableDepth = outerAvailableDepth - space * 4 - nameSize;// 基于业务分组的depth，再减去类型分组的上下外边距，以及业务分组名称的高度
      const perColWidth = innerAvailableWidth / cols;
      const perRowDepth = Math.min(innerAvailableDepth / rows, perColWidth * 1.4);
      let lastX = -appWidth / 2 + 2 * space;
      for (const moTypeBlock of businessGroupBlock.children) {
        moTypeBlock.perWidth = perColWidth;
        moTypeBlock.perDepth = perRowDepth;
        moTypeBlock.size = { width: perColWidth * moTypeBlock.col + space * 2, depth: perRowDepth * moTypeBlock.row };
        moTypeBlock.position = { x: lastX, y: 0, z: nameSize * 0.7 }; // 网元类型分组的位置会基于外层的业务分组的位置来绘制
        lastX += moTypeBlock.size.width + space;
      }

      this.#appDefaultScale = Math.min(Math.min(perRowDepth, perColWidth) * COMMON_SCALE_RATIO, this.#meshMaxSize);
      const appListGroup = this.#drawAppList(businessGroupBlock, businessGroupBlock.data.appCount);
      appGroup.add(appListGroup);
    }

    return layoutData;
  }

  #drawAppList(layoutData4CurBGroup, appCount4CurBGroup) {
    const appListGroup = new A3D.C.Group();
    appListGroup.name = 'businessGroup';
    appListGroup.position.copy(layoutData4CurBGroup.position);
    appListGroup.position.z += layoutData4CurBGroup.size.depth / 2;

    const { size: curBGroupSize } = layoutData4CurBGroup;
    this.#initBusinessBlockGround(appListGroup, curBGroupSize.width, curBGroupSize.depth, layoutData4CurBGroup);
    this.#initBlockName(appListGroup, curBGroupSize.width, curBGroupSize.depth, layoutData4CurBGroup);
    
    const isShowName = appCount4CurBGroup <= SHOW_NAME_COUNT;

    for (const moTypeLayout of layoutData4CurBGroup.children) {
      const container = new A3D.C.Group();
      container.name = 'moTypeGroup';
      container.position.copy(moTypeLayout.position);
      container.position.x += moTypeLayout.size.width / 2;
      appListGroup.add(container);

      const { width, depth } = moTypeLayout.size;
      this.#initBlockAppItems(container, layoutData4CurBGroup, moTypeLayout, isShowName);
      this.#initBlockGround(container, width, depth, moTypeLayout, 2);
      this.#initBlockLine(container, width, depth, moTypeLayout, 2);
      this.#initBlockName(container, width, depth, moTypeLayout, 3);
    }

    return appListGroup;
  }

  /**
   * 绘制一个网元类型的apps
   * @param {*} container 网元类型分组底下的元素的容器
   * @param {*} layoutData app层某个业务分组的整体布局
   * @param {*} moTypeLayout 网元类型分组的布局及数据
   * @param {*} isShowName 是否展示元素名称
   */
  #initBlockAppItems(container, layoutData, moTypeLayout, isShowName) {
    const blockAppsGroup = new A3D.C.Group();
    container.add(blockAppsGroup);

    const podGeo = this.#modelMap.pod.geometry;
    const podMat = this.#matMap.pod.normal;
    const podMesh = new A3D.C.Mesh(podGeo, podMat);

    const positionArrs = this.#getBlockItemsPosition(layoutData, moTypeLayout);
    moTypeLayout.data.apps.forEach((appData, appIndex) => {
      this.#initAppItem(appData, positionArrs[appIndex], blockAppsGroup, isShowName);
    });
  }

  #initAppItem(appData, position, container, isShowName) {
    let appItem;
    if (this.#appMap[appData.dn].mesh) {
      appItem = this.#appMap[appData.dn].mesh;
    } else {
      appItem = new A3D.C.Group();
      const appGeo = this.#modelMap.app.geometry;
      const appMat = this.#matMap.app.normal;
      const appMesh = new A3D.C.Mesh(appGeo, appMat);
      appItem.add(appMesh);
    }
    appItem.children[0].scale.set(this.#appDefaultScale, this.#appDefaultScale, this.#appDefaultScale);
    appItem.userData.isShowName = isShowName;
    appItem.userData.isSelected = appData.dn === this.#selectedAppId;

    const modelSize = this.#getMeshSize(appItem.children[0]);

    const nameMesh = appItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getAppSprite(appData, modelSize);
      appItem.add(sprite);
    }

    if (appData.eventList && appData.eventList.length > 0) {
      const sprite = this.#initAlarmDisc(appData.eventList.length, modelSize);
      appItem.add(sprite);
    }

    appItem.position.copy(position);
    container.add(appItem);

    this.#clickable(appItem, appData.dn, 'app');
    this.#setAppStatus(appData.dn, appItem.userData.isSelected);
  }

  #getAppSprite(itemData, modelSize) {
    const textArr = [itemData.moName];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth * 1.2 + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  /**
   * 更新app实例的选中状态（包括模型和文本样式）
   */
  #setAppStatus(id, isSelected) {
    const appSetting = this.#appMap[id];
    if (this.#selectedVmId || this.#selectedPodId || this.#selectedAppId) {
      // 当前手动选中了一个实例
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = isSelected ? this.#matMap.app.alarm : this.#matMap.app.alarmUnselect;
      } else {
        appSetting.mesh.children[0].material = isSelected ? this.#matMap.app.normal : this.#matMap.app.normalUnselect;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? NAME_COLOR_SELECT : NAME_COLOR_UNSELECT;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      // 当前没有手动选中，需要将app实例的样式恢复默认状态
      if (appSetting.data.alarmStatus === 1) {
        appSetting.mesh.children[0].material = this.#matMap.app.alarm;
      } else {
        appSetting.mesh.children[0].material = this.#matMap.app.normal;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(NAME_COLOR_NORMAL);
      }
    }
  }

  /**
   * 更新app实例下的pod底板样式
   */
  #setPodBlockStatus(id, isParentSelected) {
    const block = this.#appMap[id].blockMesh;
    if (!block) {
      return;
    }
    if (isParentSelected) {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = true;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGroundSelect;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLineSelect;
      }
    } else {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = false;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGround;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLine;
      }
    }
  }

  /** 创建vm内容 */
  #initVmList(vmGroup) {
    const curSelectedSite = this.#data.dataGroupBySite.find(site => site.siteInfo.siteId === this.#selectedSiteTopoId);
    if (!curSelectedSite?.vmList || curSelectedSite.vmList.length === 0) {
      return;
    }
    const data = Object.values(curSelectedSite.vmList);
    const {vmWidth, vmDepth} = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, vmWidth, vmDepth);

    const {perWidth, perDepth} = layoutData;
    this.#vmDefaultScale = Math.min(Math.min(perDepth, perWidth) * COMMON_SCALE_RATIO, this.#meshMaxSize * 0.8);

    const vmListGroup = this.#drawVmList(layoutData, data);
    vmGroup.add(vmListGroup);
  }

  #drawVmList(layoutData, data) {
    const vmListGroup = new A3D.C.Group();
    vmListGroup.name = 'vmListGroup';

    const posArr = this.#getLayoutItemPosition(layoutData);

    const isShowName = data.length <= SHOW_NAME_COUNT;
    data.forEach((itemData, index) => {
      this.#initVmItem(itemData, posArr[index], vmListGroup, isShowName);
    });
    return vmListGroup;
  }

  #initVmItem(vmData, position, container, isShowName) {
    let vmItem;
    if (this.#vmMap[vmData.dn].mesh) {
      vmItem = this.#vmMap[vmData.dn].mesh;
    } else {
      vmItem = new A3D.C.Group();
      const vmGeo = this.#modelMap.vm.geometry;
      const vmMat = this.#matMap.vm.normal;
      const vmMesh = new A3D.C.Mesh(vmGeo, vmMat);
      vmItem.add(vmMesh);
    }
    vmItem.children[0].scale.set(this.#vmDefaultScale, this.#vmDefaultScale, this.#vmDefaultScale);
    vmItem.userData.isShowName = isShowName;
    vmItem.userData.isSelected = (this.#selectedVmId === vmData.dn || this.#relationId.includes(vmData.dn));
    vmItem.position.copy(position);

    const modelSize = this.#getMeshSize(vmItem.children[0]);

    const nameMesh = vmItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getVmSprite(vmData, modelSize);
      vmItem.add(sprite);
    }

    if (vmData.eventList && vmData.eventList.length > 0) {
      const sprite = this.#initAlarmDisc(vmData.eventList.length, modelSize);
      vmItem.add(sprite);
    }

    container.add(vmItem);

    this.#clickable(vmItem, vmData.dn, 'vm');
    this.#setVmStatus(vmData.dn, vmItem.userData.isSelected);
  }

  #getVmSprite(itemData, modelSize) {
    const textArr = [itemData.moName];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setVmStatus(id, isSelected) {
    const vmSetting = this.#vmMap[id];

    if (this.#selectedVmId || this.#selectedPodId || this.#selectedAppId) {
      // 当前手动选中了某一个实例
      if (vmSetting.data.alarmStatus === 1) {
        vmSetting.mesh.children[0].material = isSelected ? this.#matMap.vm.alarm : this.#matMap.vm.alarmUnselect;
      } else {
        vmSetting.mesh.children[0].material = isSelected ? this.#matMap.vm.normal : this.#matMap.vm.normalUnselect;
      }
      const nameMesh = vmSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (vmSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? NAME_COLOR_SELECT : NAME_COLOR_UNSELECT;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      // 当前没有手动选中，恢复默认状态
      if (vmSetting.data.alarmStatus === 1) {
        vmSetting.mesh.children[0].material = this.#matMap.vm.alarm;
      } else {
        vmSetting.mesh.children[0].material = this.#matMap.vm.normal;
      }
      const nameMesh = vmSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (vmSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(NAME_COLOR_NORMAL);
      }
    }
  }

  /** 创建pod内容 */
  #initPodList(podGroup) {
    const curSelectedSite = this.#data.dataGroupBySite.find(site => site.siteInfo.siteId === this.#selectedSiteTopoId);
    if (!curSelectedSite?.podList || curSelectedSite.podList.length === 0) {
      return;
    }
    const podList = Object.values(curSelectedSite.podList);
    const podListGroupByMoType = {};
    for (const podItem of podList) {
      if (!podListGroupByMoType[podItem.moType]) {
        podListGroupByMoType[podItem.moType] = [];
      }
      podListGroupByMoType[podItem.moType].push(podItem);
    }
    const layoutData = this.#getPodLayoutData(podList, podListGroupByMoType);
    this.#resetPodLayout(layoutData);

    const { perWidth, perDepth } = layoutData;
    this.#podDefaultScale = Math.min(Math.min(perDepth, perWidth) * COMMON_SCALE_RATIO, this.#meshMaxSize);

    const podListGroup = this.#drawPodList(layoutData, podList.length);
    podGroup.add(podListGroup);
  }

  /**
   * 获取pod的布局方式
   * @returns 一个包含整体布局信息和每个分组布局信息的对象
   */
  #getPodLayoutData(podList, podListGroupByMoType) {
    const allPodCount = podList.length;
    let totalCol = 0; // 整体布局的总列数
    let totalRow = 1; // 整体布局的总行数
    if (allPodCount <= MAX_ITEM_PER_ROW) {
      // pod 总数小于等于每行最大数 MAX_POD_PER_ROW，那么所有 pod 放在一行，列数等于总数。
      totalCol = allPodCount;
      totalRow = 1;
    } else {
      totalCol = MAX_ITEM_PER_ROW;
      totalRow = Math.ceil(allPodCount / totalCol);
      if (totalCol < totalRow * 4) {
        // 如果列数 col 小于 row * 4，说明行数太多，列数太少，布局不美观，此时重新计算列数，保证列数是 4 的倍数
        totalCol = Math.ceil(Math.sqrt(allPodCount / 4)) * 4;
        totalRow = Math.ceil(allPodCount / totalCol);
      }
    }
    // 重置总列数，在后面根据每个分组算出来的占据列数进行累加
    totalCol = 0;
    // 算法：给每个分组分配相同的行数（totalRow），分配的列数根据分组的pod数量计算
    const layout = Object.entries(podListGroupByMoType).map(([podMoType, pods]) => {
      // 给当前分组分配的行数
      const rows = totalRow;
      // 给当前分组分配的列数
      let cols = allPodCount > MAX_ITEM_PER_ROW ?
        Math.floor(pods.length / allPodCount * MAX_ITEM_PER_ROW) : // 按比例分配列数
        pods.length;
      // 如果分配的行数*列数 不够放分组的pod，则分组的列数递增+1
      while (cols === 0 || (rows * cols < pods.length)) {
        cols += 1;
      }
      totalCol += cols;
      const groupLayout = {
        row: rows,
        col: cols,
        data: { podMoType, name: pods[0].moTypeName, pods },
        level1: [], // 如果当前类型有 50 个 pod，列数 curCols 为 24，那么 level1 = [24, 24, 2]
      };
      let podsCountToArrange = pods.length;
      while (podsCountToArrange > 0) {
        groupLayout.level1.push(Math.min(podsCountToArrange, cols));
        podsCountToArrange -= cols;
      }
      return groupLayout;
    });
  
    return {
      row: totalRow,
      col: totalCol,
      data: this.#data,
      children: layout, // 分组列表，每一项是一个分组的布局信息
    };
  }

  /**
   * 根据布局信息，给每个分组分配宽度和位置
   */
  #resetPodLayout(layoutData) {
    const { podWidth, podDepth, nameSize, space } = this.#sizeSetting;
    const blockCount = layoutData.children.length;
    const widthCanArrange = podWidth - (blockCount * 3 + 1) * space;
    const depthCanArrange = podDepth - 4 * space;
    const perColWidth = widthCanArrange / layoutData.col;
    const perRowDepth = Math.min(depthCanArrange / layoutData.row, perColWidth * 1.4);

    layoutData.perWidth = perColWidth;
    layoutData.perDepth = perRowDepth;
    layoutData.position = { x: 0, y: 0, z: 0 };

    let lastX = -podWidth / 2 + space;
    for (const group of layoutData.children) {
      group.size = { width: perColWidth * group.col + space * 2, depth: depthCanArrange + space * 2 };
      group.position = { x: lastX, y: 0, z: 0 };
      lastX += group.size.width + space;
    }

    return layoutData;
  }

  /**
   * 分区展示
   */
  #drawPodList(layoutData, allPodCount) {
    const podListGroup = new A3D.C.Group();
    podListGroup.name = 'podListGroup';
    podListGroup.position.copy(layoutData.position);
    const isShowName = allPodCount <= SHOW_NAME_COUNT;

    for (const moTypeLayout of layoutData.children) {
      const container = new A3D.C.Group();
      container.name = 'blockGroup';
      container.position.copy(moTypeLayout.position);
      container.position.x += moTypeLayout.size.width / 2;
      podListGroup.add(container);

      const { width, depth } = moTypeLayout.size;
      this.#initBlockPodItems(container, layoutData, moTypeLayout, isShowName);
      this.#initBlockGround(container, width, depth, moTypeLayout);
      this.#initBlockLine(container, width, depth, moTypeLayout);
      this.#initBlockName(container, width, depth, moTypeLayout);
  
      for (const podItem of moTypeLayout.data.pods) {
        // 方便后续点击某个app实例时，亮起子pod所在的底板
        const appId = podItem.parentDN;
        this.#appMap[appId].blockMesh = container;
      }
    }

    return podListGroup;
  }

  /**
   * 绘制一个网元类型分组的pods
   * @param {*} container 网元类型分组底下的元素的容器
   * @param {*} layoutData pod层整体布局
   * @param {*} blockLayout 网元类型分组的布局
   * @param {*} isShowName 是否展示元素名称
   */
  #initBlockPodItems(container, layoutData, blockLayout, isShowName) {
    const blockPodsGroup = new A3D.C.Group();
    container.add(blockPodsGroup);

    const podGeo = this.#modelMap.pod.geometry;
    const podMat = this.#matMap.pod.normal;
    const podMesh = new A3D.C.Mesh(podGeo, podMat);

    const podsPosition = this.#getBlockItemsPosition(layoutData, blockLayout);
    blockLayout.data.pods.forEach((podData, podIndex) => {
      let podItem;
      if (this.#podMap[podData.dn].mesh) {
        podItem = this.#podMap[podData.dn].mesh;
      } else {
        podItem = new A3D.C.Group();
        podItem.add(podMesh.clone());
      }
      podItem.children[0].scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
      podItem.userData.isShowName = isShowName;
      podItem.userData.isSelected = (this.#selectedPodId === podData.dn || this.#relationId.includes(podData.dn));
      podItem.userData.isBlockSelected = this.#selectedAppId === podData.parentDN;
      podItem.position.copy(podsPosition[podIndex]);

      const modelSize = this.#getMeshSize(podItem.children[0]);

      const nameMesh = podItem.getObjectByProperty('type', 'Sprite');
      if (nameMesh) {
        nameMesh.parent.remove(nameMesh);
      }
      if (isShowName) {
        const sprite = this.#getPodSprite(podData, modelSize);
        podItem.add(sprite);
      }

      if (podData.eventList && podData.eventList.length > 0) {
        const sprite = this.#initAlarmDisc(podData.eventList.length, modelSize);
        podItem.add(sprite);
      }

      blockPodsGroup.add(podItem);

      this.#clickable(podItem, podData.dn, 'pod');
      this.#setPodStatus(podData.dn, podItem.userData.isSelected);
    });
  }

  #getPodSprite(itemData, modelSize) {
    const textArr = [itemData.moName];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      transparent: true,
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    sprite.position.z = modelSize.depth + spriteNameSize * 0.5;
    sprite.position.y = spriteNameSize * 0.4;
    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * spriteNameSize, spriteNameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setPodStatus(id, isSelected) {
    const podSetting = this.#podMap[id];
    if (!podSetting?.mesh) {
      return;
    }
    const mesh = podSetting.mesh.children[0];
    if (this.#selectedVmId || this.#selectedPodId || this.#selectedAppId) {
      // 当前手动选中了某个实例
      if (podSetting.data.alarmStatus === 1) {
        mesh.material = isSelected ? this.#matMap.pod.alarm : this.#matMap.pod.alarmUnselect;
      } else if (podSetting.data.isGray === 1) {
        mesh.material = isSelected ? this.#matMap.pod.gray : this.#matMap.pod.grayUnselect;
      } else {
        mesh.material = isSelected ? this.#matMap.pod.normal : this.#matMap.pod.normalUnselect;
      }
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? NAME_COLOR_SELECT : NAME_COLOR_UNSELECT;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      // 当前没有手动选中，恢复默认状态
      if (podSetting.data.alarmStatus === 1) {
        mesh.material = this.#matMap.pod.alarm;
      } else if (podSetting.data.isGray === 1) {
        mesh.material = this.#matMap.pod.gray;
      } else {
        mesh.material = this.#matMap.pod.normal;
      }
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(NAME_COLOR_NORMAL);
      }
    }
  }

  /** 创建布局通用方法 */
  #getLayoutData(data, width, depth) {
    const itemCount = data.length;
    const {space} = this.#sizeSetting;
    const layout = { perWidth: 0, perDepth: depth, count: itemCount, level1: [] };
    let col = 0;
    let row = 1;
    if (itemCount <= MIN_ITEM_PER_ROW) {
      col = MIN_ITEM_PER_ROW;
      row = 1;
      layout.perWidth = (width - space * 2) / col;
    } else {
      col = Math.ceil(Math.sqrt(itemCount / ROW_RATIO)) * ROW_RATIO; // 确保列数是8的倍数？
      row = Math.ceil(itemCount / col);
      layout.perWidth = (width - space * 2) / col;
      layout.perDepth = (depth - space * 2) / row;
    }
    let itemCountToArrange = itemCount;
    while (itemCountToArrange > 0) {
      layout.level1.push(Math.min(itemCountToArrange, col));
      itemCountToArrange -= col;
    }
    return layout;
  }

  /** 获取元素位置通用方法 */
  #getLayoutItemPosition(layoutData) {
    const {perWidth, perDepth} = layoutData;
    const col = layoutData.level1[0];
    const row = layoutData.level1.length;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getCanvasTex(text, nameCount = 12, extendSetting = {}) {
    const setting = {
      pixelRatio: 4,
      text,
      color: '#ffffff',
      'text-align': 'center',
      'font-size': 18,
      'font-weight': 'bold',
      'line-height': 22.5,
      maxWidth: 18 * nameCount,
      ...extendSetting,
    };
    return getTextToTex(setting);
  }

  #clickable(mesh, id, type) {
    mesh.userData[type] = true;
    mesh.userData.id = id;
    mesh.userData._type = type;
    if (type === 'site') {
      this.#siteMap[id].mesh = mesh;
    } else if (type === 'app') {
      this.#appMap[id].mesh = mesh;
    } else if (type === 'pod') {
      this.#podMap[id].mesh = mesh;
    } else if (type === 'vm') {
      this.#vmMap[id].mesh = mesh;
    }
  }

  #getMeshSize(mesh) {
    const cloneMesh = mesh.clone();
    cloneMesh.remove(...cloneMesh.children);
    return getObjectSize(cloneMesh);
  }

  /** 获取需要分组展示的元素的位置 */
  #getBlockItemsPosition(layoutData, blockLayout) {
    const {perWidth, perDepth} = layoutData;
    const col = blockLayout.level1[0]; // 每行的列数是一样的，所以取第一个即可？（不太明白，最后一行可能不一样）
    const row = blockLayout.level1.length;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  /**
   * 创建一个底板
   */
  #initGround(parent, width, depth, customGroundMat) {
    const {groundHeight} = this.#sizeSetting;
    const options = getGroundOptions(width, depth);
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = customGroundMat ?? this.#matMap.ground;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'commonGround';
    ground.position.y -= groundHeight;
    parent.add(ground);
  }

  /**
   * 创建一个底板的名称卡片，用于渲染自定义的内容
   */
  #initGroundName(parent, width, type) {
    const detailGroup = new A3D.C.Group();
    detailGroup.position.x = -(width * 0.5 + this.#size.width * 0.06);
    parent.add(detailGroup);

    this.#cardMap[type] = detailGroup;
  }

  /**
   * 绘制一个网元类型分组的底板
   */
  #initBlockGround(parent, width, depth, block, renderOrder = 1) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;

    const ground = new ExtrudeShape(options, this.#matMap.blockGround);
    ground.children[0].renderOrder = renderOrder; // 数值越小，绘制层级越低
    ground.name = 'blockGround';

    parent.add(ground);
  }

  /**
   * 绘制一个业务分组的底板
   */
  #initBusinessBlockGround(parent, width, depth, block, renderOrder = 1) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;

    const ground = new ExtrudeShape(options, this.#matMap.businessGround);
    ground.children[0].renderOrder = renderOrder; // 数值越小，绘制层级越低
    ground.name = 'businessGround';

    parent.add(ground);
  }

  /**
   * 绘制一个分组的底板边框
   */
  #initBlockLine(parent, width, depth, block, renderOrder = 1) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    options.close = true;

    const line = getWallLine(this.#matMap.blockGroundLine, options);
    line.children[0].renderOrder = renderOrder;
    line.name = 'blockLine';

    parent.add(line);
  }

  /**
   * 绘制一个分组的名称
   */
  #initBlockName(parent, width, depth, block, renderOrder = 2) {
    const { nameSize, space } = this.#sizeSetting;
    const nameCount = 20;
    const plane = getTextPlane(
      {
        x: nameSize * nameCount,
        y: nameSize,
      },
      block.data.name,
      {
        width: 20 * nameCount * 1.5,
        height: 20,
        'font-weight': 'bold',
      },
    );
    const x = -(width - nameSize * nameCount) * 0.5;
    const z = (depth + nameSize) * 0.5;
    plane.position.set(x, 0, z - depth);
    plane.renderOrder = renderOrder;
    parent.add(plane);
    return plane;
  }

  /**
   * 绘制告警角标
   */
  #initAlarmDisc(alarmCount, modelSize) {
    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawAlarmDisc(canvas, content);

    const material = new A3D.C.SpriteMaterial({
      map: A3D.canvasToTex(canvas, true),
      color: '#FFFFFF',
      depthWrite: false,
    });
    const alarmSize = modelSize.depth * 0.32;
    const sprite = new A3D.C.Sprite(material);
    sprite.renderOrder = 12;
    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
    sprite.center.x = 0;
    sprite.position.y = modelSize.height * 1.4; // 待优化，不同的模型需要微调一下这个倍数
    sprite.position.x = modelSize.width * 0.3; // 待优化，不同的模型需要微调一下这个倍数

    return sprite;
  }

  #drawAlarmDisc(canvas, content) {
    const pixelRatio = 4;
    const lineHeight = 24 * pixelRatio;
    const fontSize = 18 * pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    const size = ctx.measureText(content.slice(1));
    canvas.width = size.width + lineHeight + 2;
    canvas.height = lineHeight + 2;
    drawRoundedRect(ctx, {x: 1, y: 1}, canvas.width - 2, lineHeight, lineHeight * 0.5);
    ctx.fillStyle = '#F43146';
    ctx.fill();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = '#ffffff';

    const HALF = 2;
    const FONT_SPACE = 0.1;
    const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
    ctx.fillText(content, canvas.width / HALF, top);
  }
}
