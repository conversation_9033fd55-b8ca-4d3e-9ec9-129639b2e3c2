import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import Sites from './Sites';
import {getObjectSize} from '../common/utils';
import WaterfallFlow from './WaterfallFlow';
import GroupItem from './GroupItem';
import SiteItem from './SiteItem';

const A3D = getA3D();

const sitesSpaceRatio = 0.05;
const siteWidthSpaceRatio = 0.05;

export default class SitesGroups extends A3D.C.Object3D {
  #data;

  #size;

  #modelMap;

  #matMap;

  #waterfallFlow;

  #selectSiteItem;

  constructor(data, size, modelMap, matMap) {
    super();
    this.#data = data;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;

    this.#init();
  }

  update(data) {
    if (data) {
      this.#data = data;
      this.children.forEach((sites, index) => {
        if (data[index]) {
          sites.update(data[index]);
        }
      });
    }
  }

  setGrayUpgrade(ids) {
    this.children.forEach((sites) => {
      sites.setGrayUpgrade(ids);
    });
  }

  getSitePosition() {
    const result = {};
    this.children.forEach((sites) => {
      const siteItemsGroup = sites.getObjectByName('siteItemsGroup');
      siteItemsGroup.children.forEach((siteItem) => {
        result[siteItem.data.siteId] = siteItem.getSize();
      });
    });
    return result;
  }

  hoverIn(object3D, groupItemCb, siteItemCb, siteNameCb) {
    if (object3D.userData && object3D.userData._type === 'sitePlane') {
      siteNameCb(object3D.userData._data, object3D);
      return;
    }
    const groupItem = this.#getGroupItem(object3D);
    if (groupItem) {
      groupItem.hoverIn();
      if (groupItemCb) {
        groupItemCb(groupItem.data, groupItem);
      }
      return;
    }
    const siteItem = this.#getSiteItem(object3D);
    if (siteItem) {
      siteItem.hoverIn();
      if (siteItemCb) {
        siteItemCb();
      }
    }
  }

  hoverOut(object3D, groupItemCb, siteItemCb, siteNameCb) {
    if (object3D.userData && object3D.userData._type === 'sitePlane') {
      siteNameCb(object3D.userData._data, object3D);
      return;
    }

    const groupItem = this.#getGroupItem(object3D);
    if (groupItem) {
      groupItem.hoverOut();
      if (groupItemCb) {
        groupItemCb(groupItem.data, groupItem);
      }
      return;
    }
    const siteItem = this.#getSiteItem(object3D);
    if (siteItem) {
      siteItem.hoverOut();
      if (siteItemCb) {
        siteItemCb();
      }
    }
  }

  select(object3D, groupItemCb, siteItemCb) {
    const groupItem = this.#getGroupItem(object3D);
    if (groupItem) {
      if (groupItemCb) {
        groupItemCb(groupItem.data, groupItem);
      }
      return;
    }
    const siteItem = this.#getSiteItem(object3D);
    if (this.#selectSiteItem && this.#selectSiteItem !== siteItem) {
      this.#selectSiteItem.unselect();
    }

    if (siteItem) {
      if (siteItem !== this.#selectSiteItem) {
        siteItem.select();
        this.#selectSiteItem = siteItem;
        if (siteItemCb) {
          siteItemCb(siteItem.data, siteItem);
        }
      }
    } else {
      if (siteItemCb && this.#selectSiteItem) {
        siteItemCb(null, null);
      }
      this.#selectSiteItem = null;
    }
  }

  getSelected() {
    return this.#selectSiteItem;
  }

  drillDown(object3D, groupItemCb, siteItemCb) {
    const groupItem = this.#getGroupItem(object3D);
    if (groupItem) {
      if (groupItemCb) {
        groupItemCb(groupItem.data, groupItem);
      }
      return;
    }
    const siteItem = this.#getSiteItem(object3D);
    if (siteItem) {
      if (siteItemCb) {
        siteItemCb(siteItem.data, siteItem);
      }
    }
  }

  #init() {
    this.#initLayout();
  }

  /**
   * 创建站点组子节点，并根据子节点尺寸，规划流式布局
   */
  #initLayout() {
    const row = Math.ceil(Math.sqrt((this.#data.length * 3) / 2));
    const col = Math.ceil((this.#data.length * 3) / row);
    const siteGroupCol = this.#data.length > 3 ? Math.ceil(col / 3) : 2;
    const sitesWidth = this.#getSitesWidth(sitesSpaceRatio, siteGroupCol);
    this.#waterfallFlow = this.#getWaterfallFlow(sitesSpaceRatio, sitesWidth, siteGroupCol);
    this.#initSites(sitesWidth);
    this.#updateSitesPosition();
  }

  #getSitesWidth(space, siteGroupCol) {
    let sitesWidth = this.#size.width;
    if (this.#data.length > 1) {
      sitesWidth = sitesWidth / (siteGroupCol > 2 ? (siteGroupCol - 0.1) : 2 + space);
    }
    return sitesWidth;
  }

  #getWaterfallFlow(space, sitesWidth, siteGroupCol) {
    return new WaterfallFlow({
      width: sitesWidth,
      count: siteGroupCol,
      space: sitesWidth * space,
    }, {space: sitesWidth * space}, 'center');
  }

  #initSites(sitesWidth) {
    let maxSiteCount = 0;
    this.#data.forEach((sitesData) => {
      maxSiteCount = Math.max(sitesData.siteList.length, maxSiteCount);
    });
    const minColumn = 3;
    const ratio = 3 / 2; // 比例 3:2
    // 初始计算列数，尽量保持比例
    let siteColumn = Math.max(Math.ceil(Math.sqrt(maxSiteCount * ratio)), minColumn);
    const siteWidth = sitesWidth / (siteWidthSpaceRatio * (siteColumn + 1) + siteColumn);

    const sizeSetting = {
      sitesWidth,
      siteWidth,
      siteWidthSpaceRatio,
      siteDepth: siteWidth * this.#size.siteDepthRatio,
      siteModelSize: siteWidth / siteColumn * this.#size.siteModelRatio,
    };

    this.#data.forEach((sitesData) => {
      const siteItem = new Sites(sitesData, sizeSetting, this.#modelMap, this.#matMap);
      this.add(siteItem);

      const size = getObjectSize(siteItem);
      this.#waterfallFlow.add(size.depth);
    });
  }

  #updateSitesPosition() {
    const offset = {x: -this.#size.width / 2, y: 0};
    const allHeight = this.#waterfallFlow.getHeight();
    offset.y = Math.min(allHeight, this.#size.depth) / 2;
    this.children.forEach((item, index) => {
      const position = this.#waterfallFlow.getIndex(index);
      item.position.set(position.x + offset.x, 0, -position.y + offset.y);
    });
  }

  #getGroupItem(object3D) {
    let node3D = null;
    if (object3D instanceof A3D.C.Object3D) {
      node3D = object3D;
      while (node3D && !(node3D instanceof GroupItem) && node3D.parent !== this) {
        node3D = node3D.parent;
      }
      node3D = node3D instanceof GroupItem ? node3D : null;
    }
    return node3D;
  }

  #getSiteItem(object3D) {
    let node3D = null;
    if (object3D instanceof A3D.C.Object3D) {
      node3D = object3D;
      while (node3D && !(node3D instanceof SiteItem) && node3D.parent !== this) {
        node3D = node3D.parent;
      }
      node3D = node3D instanceof SiteItem ? node3D : null;
    }
    return node3D;
  }
}
