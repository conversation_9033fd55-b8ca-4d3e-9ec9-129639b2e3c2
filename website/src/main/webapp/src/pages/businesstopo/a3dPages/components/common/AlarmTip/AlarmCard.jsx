import './index.less';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const statusEnum = [
  { en: 'emergency', zh: '紧急' },
  { en: 'general', zh: '重要' },
  { en: 'important', zh: '次要' },
  { en: 'hints', zh: '提示' },
];

function AlarmCard(props) {
  return (
    <div className='alarmTip_center'>
      {props.data.map((item, index) => {
        if (props.type === 'alarm') {
          return (
            <div className={`alarmCard ${props.state}`} key={index}>
              <div className='title'>
                {' '}
                <span className='icon' />
                {item.name}
              </div>
              <span className='tag'>
                {statusEnum.filter((ele) => ele.en === props.state)[0].zh}
              </span>
              <div className='center'>
                <div className='secondTitle' style={{ height: '2.625rem' }}>
                  <div className='gapDiv'>
                    <span className='icon des' />
                    告警说明
                  </div>
                  {item.alarmDes}
                </div>
                <div className='secondTitle'>
                  <div className='gapDiv'>
                    <span className='icon source' />
                    告警源
                  </div>
                  {item.alarmSource}
                </div>
                <div className='secondTitle'>
                  <div className='gapDiv'>
                    <span className='icon time' />
                    发生时间
                  </div>
                  {item.alarmTime}
                </div>
              </div>
              <div className='button'>
                告警详情
              </div>
            </div>
          );
        }
        return (
          <div className='alarmCard' key={index}>
            <div className='title'>
              <span className='icon even' />
              {' '}
              {item.name}
              {' '}
            </div>
            <div className='center'>
              <div className='secondTitle' style={{ height: '2.625rem' }}>
                <div className='gapDiv'>
                  <span className='icon address' />
                  定位信息
                </div>
                {item.locationInfo}
              </div>
              <div className='secondTitle'>
                <div className='gapDiv'>
                  <span className='icon time' />
                  发生时间
                </div>
                {item.time}
              </div>
            </div>
            <div className='button'>
              <span />
              事件回溯
            </div>
          </div>
        );
      })}
    </div>
  );
}

AlarmCard.propTypes = {
  data: PropTypes.shape({ map: PropTypes.array }).isRequired,
  type: PropTypes.string,
  state: PropTypes.string,
};

export default AlarmCard;
