import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import RingUtils from '../common/utils/RingUtils';
import {getObjectSize} from '@pages/businesstopo/a3dPages/w3d/components/common/utils';
import {drawRoundedRect} from '@pages/businesstopo/a3dPages/w3d/components/utils';
import {getBusinessModelType} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/business';

const A3D = getA3D();

let _geo = A3D.Geo.createBox({width: 1, height: 1, deoth: 1});
let _mat = A3D.mat('Basic', {color: '#ff0000'});
let defaultModel = A3D.mesh(_geo, _mat);

const _startColor = new A3D.C.Color('#1e1e1e');
const _endColor = new A3D.C.Color('#333333');

export default class Disc extends A3D.C.Group {
  #output = {};

  #ring = A3D.createGroup();

  #app = A3D.createGroup();

  #matrix = [];

  #startMatrixIndex = [];

  #endMatrixIndex = [];

  #map;

  #w3d;

  constructor(props) {
    super();

    if (props) {
      this.parameters = A3D.util.extend(true, {
        onSelectCallback: (modelIns) => modelIns,
        onChangeCallback: (modelIns) => modelIns,
        animate: false,
        space: Math.PI / 20,
        startColor: _startColor,
        endColor: _endColor,
        radius: 10,
        startRadian: 0,
        scale: {x: 1, y: 1, z: 1},
        order: 0,
      }, props);
      this.selectIndex = -1;
      this.add(this.#ring);
      this.add(this.#app);
      this.#addRing();
      this.#w3d = props.w3d;
      this.#map = new Map();
    }
  }

  copy(source) {
    super.copy(source, false);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    this.selectIndex = -1;
    return this;
  }

  set(data) {
    const position = data.position;
    if (position) {
      this.position.copy(position).add(this.parameters.offset);
    }

    const dom = this.children[0].element.children[0];
    this.parameters.updateDom(dom, data);
  }

  add(data = {}, ...rest) {
    if (data.isObject3D) {
      super.add(data, ...rest);
      return this;
    }
    this.#addItem(data);
    return this;
  }

  select(object3D, onStartCb, onUpdateCb, onCompleteCb) {
    let item = this.#getItem(object3D);
    const curSelectIndex = this.#app.children.indexOf(item);
    if (curSelectIndex === this.selectIndex) {
      if (item) {
        this.parameters.onSelectCallback(item, this.selectIndex);
      }
      return;
    }
    this.#unselectItem(this.selectIndex);
    if (onStartCb) {
      onStartCb(item);
    }
    this.#selectItem(curSelectIndex, onUpdateCb, () => {
      this.selectIndex = curSelectIndex;
      if (item) {
        this.parameters.onSelectCallback(item, this.selectIndex);
        if (onCompleteCb) {
          onCompleteCb(item);
        }
      }
    });
  }

  hoverIn(object3D, callback) {
    this.#hoverEvent(object3D, callback);
  }

  hoverOut(object3D, callback) {
    this.#hoverEvent(object3D, callback);
  }

  enableAnimation(enabled) {
    this.parameters.animate = enabled;
  }

  getSelectItem() {
    return this.#app.children[this.selectIndex];
  }

  #addItem(data) {
    let _model = data.model || defaultModel.clone();
    let count = this.#app.children.length;
    _model.userData.index = count;
    _model.userData.data = data;
    this.#app.add(_model);
    this.#setInitMatrix(this.#app.children);
    this.#initWarn(data);
    this.#map.set(data.modelId, data);
  }

  update(data) {
    for (let business of data) {
      let oldData = this.#map.get(business.modelId);
      let oldCount = oldData.alarmCsn ? oldData.alarmCsn.split(',').length : 0;
      let newCount = business.alarmCsn ? business.alarmCsn.split(',').length : 0;
      oldData.alarmCsn = business.alarmCsn;
      this.#map.set(business.modelId, oldData);
      const modelType = getBusinessModelType(business.iconType);
      if (newCount !== oldCount) {
        this.#updateModel(modelType, oldData.model, newCount);
        this.#updateWarn(oldData, oldData.model, newCount);
      }
    }
  }

  #updateModel(modelType, model, alarmCount) {
    const groupMatNormal = this.#w3d.themeManager.cloneStyle(`${modelType}-normal`);
    const groupMatAlarm = this.#w3d.themeManager.cloneStyle(`${modelType}-alarm`);
    const mat = alarmCount === 0 ? groupMatNormal : groupMatAlarm;
    model.material = mat;
  }

  #updateWarn(oldData, model, alarmCount) {
    const warn = model.children.filter(item => item.name === 'alarmTag')[0];
    if (alarmCount <= 0) {
      if (warn) {
        warn.geometry.dispose();
        warn.material.dispose();
        model.remove(warn);
      }
    } else {
      if (warn) {
        this.#updateWarnCanvas(warn, alarmCount);
      } else {
        this.#initWarn(oldData);
      }
    }
  }

  #updateWarnCanvas(warn, alarmCount) {
    const sprite = warn;
    const alarmSize = sprite.scale.y;

    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    sprite.material.map = A3D.canvasToTex(canvas, true);

    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
  }

  #getItem(object3D) {
    let node3D = null;
    if (object3D instanceof A3D.C.Object3D) {
      node3D = object3D;
      while (node3D && node3D.parent !== this.#app) {
        node3D = node3D.parent;
      }
      node3D = node3D || null;
    }
    return node3D;
  }

  #selectItem(index, onUpdateCb, onCompleteCb) {
    this.#setMatrix(this.selectIndex, this.#startMatrixIndex);
    this.#setMatrix(index, this.#endMatrixIndex);
    let parameters = this.parameters;
    let animation = parameters.animationOption.animation;
    if (animation && parameters.animate && !animation.isPlaying()) {
      animation.stop();
      animation.push({
        duration: 1000,
        onUpdate: (e) => {
          this.#updateMatrix(e.value, index, onUpdateCb);
        },
        onComplete: () => {
          if (onCompleteCb) {
            onCompleteCb();
          }
        },
      });
      animation.start();
    } else {
      this.#updateMatrix(1, index, onUpdateCb);
      if (onCompleteCb) {
        onCompleteCb();
      }
    }
  }

  #unselectItem(index) {
    let item = this.#app.children[index];
    if (item) {
      this.parameters.onUnselectCallback(item, index);
    }
  }

  #hoverEvent(object3D, callback) {
    let item = this.#getItem(object3D);
    const curSelectIndex = this.#app.children.indexOf(item);
    if (item && callback) {
      callback(item, curSelectIndex);
    }
  }

  #initWarn(data) {
    if (!data.alarmCsn) {
      return;
    }
    const alarmCount = data.alarmCsn.split(',').length;
    if (alarmCount <= 0) {
      return;
    }
    const modelSize = {
      max: {
        x: 0.84,
        y: 0.77,
        z: 12.68,
      },
      min: {
        x: -0.84,
        y: -1.1,
        z: 11.54,
      },
      width: 1.68,
      height: 1.87,
      depth: 1.14,
    };
    const alarmSize = 1.32 * 0.36;

    const canvas = this.#initCanvas(alarmCount);
    const texture = A3D.canvasToTex(canvas, true);
    const material = new A3D.C.SpriteMaterial({
      map: texture,
      color: '#FFFFFF',
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    sprite.renderOrder = 5;
    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
    sprite.center.x = 0;
    sprite.position.y = modelSize.height * 0.6;
    sprite.position.x = modelSize.width * 0.1;
    sprite.name = 'alarmTag';
    data.model.add(sprite);
  }

  #initCanvas(alarmCount) {
    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    return canvas;
  }

  #drawCanvas(canvas, content) {
    const pixelRatio = 4;
    const lineHeight = 24 * pixelRatio;
    const fontSize = 18 * pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    const size = ctx.measureText(content.slice(1));

    canvas.width = size.width + lineHeight + 2;
    canvas.height = lineHeight + 2;

    drawRoundedRect(ctx, {x: 1, y: 1}, canvas.width - 2, lineHeight, lineHeight * 0.5);
    // 填充路径
    ctx.fillStyle = '#F43146';
    ctx.fill();

    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = '#ffffff';

    const HALF = 2;
    const FONT_SPACE = 0.1;
    const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
    ctx.fillText(content, canvas.width / HALF, top);
  }

  #setMatrix(index, matrix) {
    matrix.length = 0;
    if (this.#app.children.length === 2) {
      if (index === 0) {
        matrix.push(1, 2);
      } else {
        matrix.push(0, 1);
      }
      return;
    }
    let mid;
    if (this.#matrix.length % 2 === 0) {
      mid = this.#matrix.length / 2 - 1;
    } else {
      mid = Math.floor(this.#matrix.length / 2);
    }
    let offset = mid - index;
    for (let i = 0, len = this.#matrix.length; i < len; i++) {
      matrix.push(Math.abs((i + offset + len) % len));
    }
  }

  #updateMatrix(alpha, selectIndex, onUpdateCb) {
    this.#app.children.forEach((item, index) => {
      let startIndex = this.#startMatrixIndex[index];
      let endIndex = this.#endMatrixIndex[index];
      let start = this.#matrix[startIndex];
      let end = this.#matrix[endIndex];
      let len = this.#app.children.length;
      let dis = Math.abs(endIndex - startIndex);
      let flag;
      if (this.#app.children.length > 2) {
        if (len % 2 === 0 && dis === len / 2) {
          flag = startIndex >= Math.ceil(len / 2);
        } else {
          flag = dis >= Math.ceil(len / 2);
        }
        if (flag) {
          if (start < 0) {
            start = (start + 1) % 2;
          } else {
            end = (end + 1) % 2;
          }
          if (alpha > 0.05 && alpha < 0.95) {
            item.traverse((i) => {
              i.visible = false;
            });
          } else {
            item.traverse((i) => {
              i.visible = true;
            });
          }
        }
      }
      let scale = this.parameters.scale;
      let cur = start + (end - start) * alpha;
      RingUtils.getPointAt(cur, this.parameters.radius, this.#output, this.parameters.startRadian);
      item.position.x = this.#output.x * scale.x;
      item.position.z = this.#output.y * scale.z;
      item.position.y = 0;
      if (onUpdateCb) {
        onUpdateCb(item, index, alpha, selectIndex);
      }
    });
  }

  #setInitMatrix(items, space = 0.035) {
    let len = items.length;
    let start = 0;
    if (len === 2) {
      len = 3;
    }
    if (len % 2 === 0) {
      start = -space * ((len - 2) / 2);
    } else {
      start = -space * ((len - 1) / 2);
    }
    let scale = this.parameters.scale;
    for (let i = 0; i < len; i++) {
      this.#matrix[i] = start + space * i;
      RingUtils.getPointAt(this.#matrix[i], this.parameters.radius, this.#output, this.parameters.startRadian);
      if (items[i]) {
        items[i].position.x = this.#output.x * scale.x;
        items[i].position.z = this.#output.y * scale.z;
        this.parameters.onChangeCallback(items[i], i);
      }
    }
  }

  #addRing() {
    const parameters = this.parameters;
    const radius = parameters.radius;
    const geo = A3D.Geo.createCylinder({
      topRadius: radius,
      bottomRadius: radius,
      height: 0.1,
      radiusSegments: 64,
      heightSegments: 1,
    });
    const mesh = A3D.mesh(geo, this.parameters.material);
    this.#ring.add(mesh);
    mesh.pointerEvents = 'none';
    this.#ring.pointerEvents = 'none';
    this.#ring.scale.copy(this.parameters.scale);
    mesh.renderOrder = this.parameters.order;
  }
}
