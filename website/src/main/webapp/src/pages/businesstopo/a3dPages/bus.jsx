class EventEmitter {
  constructor() {
    // 存储事件及其对应的处理函数
    this.events = {};
  }

  addListener(eventName, listener) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }

    // 检查 listener 是否已经存在
    if (!this.events[eventName].includes(listener)) {
      this.events[eventName].push(listener);
    }
  }

  // 移除事件监听器
  removeListener(eventName, listener) {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(eventListener => eventListener !== listener);
    }
  }

  // 移除事件监听器
  removeAllListener() {
    this.events = {};
  }

  // 订阅事件
  on(eventName, listener) {
    // 如果事件名不存在，创建一个新的事件数组
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }

    // 将处理函数添加到事件数组中
    this.events[eventName].push(listener);
  }

  // 发布事件
  emit(eventName, ...args) {
    // 检查事件名对应的处理函数是否存在
    if (this.events[eventName]) {
      // 遍历调用所有事件处理函数，并传递参数
      this.events[eventName].forEach(listener => {
        listener(...args);
      });
    }
  }

  // 取消订阅事件
  off(eventName, listener) {
    // 检查事件名对应的处理函数是否存在
    if (this.events[eventName]) {
      // 移除特定的处理函数
      this.events[eventName] = this.events[eventName].filter(eventListener => eventListener !== listener);
    }
  }
}
const eventBus = new EventEmitter();
export default eventBus;
