.Split {
  display: flex;
  flex: 1 1 0%;
  height: calc(100vh - 4rem);
  outline: none;
  overflow: hidden;
  user-select: text;
  flex-direction: row;
  left: 0px;
  right: 0px;
  position: relative;

  .PaneLeft {
    flex: 1 1 0%;
    outline: none;
    background-color: #171719;
    overflow: hidden;
  }

  .PaneRight {
    flex: 0 0 auto;
    outline: none;
    width: 300px;
    background-color: #272727;
    z-index: 2;
    position: relative;
    // transition: 0.3s ease;
    // transition-property: width;

    .SplitResize {
      position: absolute;
      top: 50%;
      left: -0.75rem;
      width: 0.75rem;
      height: 5rem;
      cursor: pointer;
      transform: translateY(-50%);
      cursor: pointer;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &.expansion_wb_cls {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/foldIcon.svg);
      }

      &.expansion_eb_cls {
        background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/unFoldIcon.svg);
      }
    }

    .PaneRightContents {
      overflow-x: hidden;
      flex: 0 0 auto;
      outline: none;
      height: 100%;
    }
  }
}
