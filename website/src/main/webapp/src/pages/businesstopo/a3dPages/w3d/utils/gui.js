import * as THREE from 'three';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { GammaCorrectionShader } from 'three/examples/jsm/shaders/GammaCorrectionShader';
import { getObjectSize } from '../components/common/utils';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

const readFile = (formatTypes, cb) => {
  const input = document.createElement('input');
  input.type = 'file';
  input.onchange = (e) => {
    const file = e.target.files[0];
    // 打印：('文件大小', `${(file.size / 1000).toFixed(1)}kb`)
    const format = file.name.split('.').slice(-1)[0];
    if (formatTypes.includes(format)) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (theFile) => {
        const fileData = theFile.target.result;
        cb(fileData);
      };
    }
  };
  document.body.append(input);
  input.click();
  input.remove();
};

const gltfCtrl = function(parentGui, agent, w3d) {
  let control = {
    模型替换: () => {
      readFile(['gltf', 'glb'], (fileData) => {
        w3d.loadModel(fileData).then((gltf) => {
          const testModel = gltf.scene.children[0];
          testModel.name = 'testModel';
          testModel.traverse((child) => {
            child.castShadow = true;
          });
          const scene = agent.scene.children[0];
          const oldModel = scene.getObjectByName('testModel');
          if (oldModel) {
            oldModel.parent.remove(oldModel);
          }
          let calls = agent.env.Postprocessing.rendererInfo.render.calls;
          let triangles = agent.env.Postprocessing.rendererInfo.render.triangles;
          scene.add(testModel);
          agent.renderOnce();
          setTimeout(() => {
            const obj = {
              scale: testModel.scale,
              rotation: testModel.rotation,
              position: testModel.position,
              calls: agent.env.Postprocessing.rendererInfo.render.calls - calls,
              triangles: agent.env.Postprocessing.rendererInfo.render.triangles - triangles,
              size: getObjectSize(testModel),
            };
            // 打印：obj
          }, 1000);
        });
      });
    },
  };
  parentGui.add(control, '模型替换');
};

const bgCtrl = function(parentGui, agent, w3d) {
  let gui = parentGui.addFolder('背景图');
  const control = {
    hdrBg: true,
    hdr替换: () => {
      readFile(['hdr'], (fileData) => {
        w3d.setHDR(fileData);
        agent.renderOnce();
      });
    },
  };
  gui.add(control, 'hdr替换');
};

const cameraCtrl = function(parentGui, agent) {
  let gui = parentGui.addFolder('摄像头');
  let lonLat = A3D.util.getLonLatAtSphere(agent.camera.position, agent.env.lookAt.position);
  const control = {
    广角: agent.camera.fov,
    水平旋转: A3D.C.MathUtils.radToDeg(lonLat.lon),
    垂直旋转: A3D.C.MathUtils.radToDeg(lonLat.lat),
    到中心点的距离: lonLat.radius,
  };
  let data = { lon: lonLat.lon, lat: lonLat.lat, dis: lonLat.radius };
  let obj = new THREE.Object3D();
  obj.position.set(agent.env.lookAt.position.x, agent.env.lookAt.position.y, agent.env.lookAt.position.z);
  gui.add(control, '广角', 10, 90, 1).onChange((fov) => {
    agent.camera.fov = fov;
    agent.camera.updateProjectionMatrix();
    agent.renderOnce();
  });
  gui.add(control, '水平旋转', 0, 360, 1).onChange((e) => {
    data.lon = A3D.C.MathUtils.degToRad(e);
    agent.setCameraByMesh(obj, {
      agent,
      distance: data.dis,
      lat: data.lat,
      lon: data.lon,
      animate: false,
    });
  });
  gui.add(control, '垂直旋转', 10, 90, 1).onChange((e) => {
    data.lat = A3D.C.MathUtils.degToRad(e);
    agent.setCameraByMesh(obj, {
      agent,
      distance: data.dis,
      lat: data.lat,
      lon: data.lon,
      animate: false,
    });
  });
  gui.add(control, '到中心点的距离', 0, 360, 1).onChange((e) => {
    data.dis = e;
    agent.setCameraByMesh(obj, {
      agent,
      distance: data.dis,
      lat: data.lat,
      lon: data.lon,
      animate: false,
    });
  });
};

const addDirLight = function(agent, parent, control) {
  let directionalLight = agent.scene.getObjectByProperty('type', 'DirectionalLight');
  let directionalCtrl = {
    x: directionalLight.position.x,
    y: directionalLight.position.y,
    z: directionalLight.position.z,
  };
  let guiDir = parent.addFolder('平行光');
  guiDir.add(control, '平行光强度', 0, 2, 0.01).onChange((num) => {
    directionalLight.intensity = num;
    agent.renderOnce();
  });
  guiDir.add(directionalCtrl, 'x', -400, 400, 1).onChange((num) => {
    directionalLight.position.x = num;
    agent.renderOnce();
  });
  guiDir.add(directionalCtrl, 'y', -400, 400, 1).onChange((num) => {
    directionalLight.position.y = num;
    agent.renderOnce();
  });
  guiDir.add(directionalCtrl, 'z', -400, 400, 1).onChange((num) => {
    directionalLight.position.z = num;
    agent.renderOnce();
  });
};

const addSpotLight = function(agent, parent, control) {
  let spotLight = agent.scene.getObjectByProperty('type', 'SpotLight');
  let spotCtrl = {
    x: spotLight.position.x,
    y: spotLight.position.y,
    z: spotLight.position.z,
  };
  let guiSpo = parent.addFolder('聚光灯');
  guiSpo.add(control, '聚光灯强度', 0, 2, 0.01).onChange((num) => {
    spotLight.intensity = num;
    agent.renderOnce();
  });
  guiSpo.add(spotCtrl, 'x', -400, 400, 1).onChange((num) => {
    spotLight.position.x = num;
    agent.renderOnce();
  });
  guiSpo.add(spotCtrl, 'y', -400, 400, 1).onChange((num) => {
    spotLight.position.y = num;
    agent.renderOnce();
  });
  guiSpo.add(spotCtrl, 'z', -400, 400, 1).onChange((num) => {
    spotLight.position.z = num;
    agent.renderOnce();
  });
};

const lightCtrl = function(parentGui, agent) {
  let gui = parentGui.addFolder('灯光');
  const control = {};
  const ambientLight = agent.scene.getObjectByProperty('type', 'AmbientLight');
  if (ambientLight) {
    control['环境光强度'] = ambientLight.intensity;
    let guiAmb = gui.addFolder('环境光');
    guiAmb.add(control, '环境光强度', 0, 1, 0.1).onChange((num) => {
      const light = agent.scene.getObjectByProperty('type', 'AmbientLight');
      light.intensity = num;
      agent.renderOnce();
    });
  }
  const directionalLight = agent.scene.getObjectByProperty('type', 'DirectionalLight');
  if (directionalLight) {
    control['平行光强度'] = directionalLight.intensity;
    addDirLight(agent, gui, control);
  }
  const spotLight = agent.scene.getObjectByProperty('type', 'SpotLight');
  if (spotLight) {
    control['聚光灯强度'] = spotLight.intensity;
    addSpotLight(agent, gui, control);
  }
};

const themeCtrl = function(gui, control, w3d) {
  gui.add(control, '换肤', {
    default: 'default',
    dark: 'dark',
  }).onChange((type) => {
    w3d.themeManager.switch(type);
    w3d.agent.renderOnce();
  });
};

export const createGui = (gui, w3d) => {
  const agent = w3d.agent;

  const control = {
    toneMapping: THREE.NoToneMapping,
    toneMappingExposure: 1,
    GammaCorrection: false,
    换肤: 'default',
  };
  gui.add(control, 'toneMapping', {
    No: THREE.NoToneMapping,
    ACESFilmic: THREE.ACESFilmicToneMapping,
    Linear: THREE.LinearToneMapping,
    Reinhard: THREE.ReinhardToneMapping,
    Cineon: THREE.CineonToneMapping,
  }).onChange((type) => {
    agent.renderer.toneMapping = type;
    agent.renderOnce();
  });
  gui.add(control, 'toneMappingExposure', 0, 3, 0.001).onChange((num) => {
    agent.renderer.toneMappingExposure = num;
    agent.renderOnce();
  });
  gui.add(control, 'GammaCorrection').onChange((flag) => {
    const composer = agent.env.Postprocessing.composer;
    if (flag) {
      composer.addPass(new ShaderPass(GammaCorrectionShader));
    } else {
      composer.removePass(composer.passes.at(-1));
    }
    agent.renderOnce();
  });
  gltfCtrl(gui, agent, w3d);
  bgCtrl(gui, agent, w3d);
  cameraCtrl(gui, agent);
  lightCtrl(gui, agent);
  themeCtrl(gui, control, w3d);
};

