import {
  serviceData, tpsData, tabsInfo, option,
} from './chartData';
import StatusCard from '../common/StatusCard';
import React, { useState, useRef, useEffect } from 'react';
import HuiCharts from '@hui/charts';

function RightPanel() {
  const [showPanel, setShowPanel] = useState(true);
  const [selectVal, setSelectValue] = useState(0);
  const [tabIndex, setTabIndex] = useState(0);
  const chartRef = useRef(null);
  const chart2Ref = useRef(null);
  const chartIns = useRef(new HuiCharts());
  useEffect(() => {
    chartIns.current.init(chartRef?.current);
    chartIns.current.setSimpleOption(serviceData.name, serviceData.option);
    chartIns.current.render();
  }, [chartRef]);
  useEffect(() => {
    if (tabIndex === 0) {
      chart2Ref.current.innerHTML = '';
      chart2Ref.current.removeAttribute('_echarts_instance_');
      const chartIns2 = new HuiCharts();
      chartIns2.init(chart2Ref?.current);
      chartIns2.setSimpleOption(tpsData.name, tpsData.option);
      setTimeout(() => {
        chartIns2.render();
      }, 50);
    }
  }, [tabIndex]);
  return (
    <>
      <div className="KPI_top">
        <span className="KPI_title">
          KPI总览
        </span>
        <span className={`KPI_panel ${showPanel ? 'panelShow' : 'panelHide'}`} onClick={() => setShowPanel(!showPanel)} />
      </div>
      <div className="KPI_content" style={{ display: showPanel ? 'block' : 'none' }}>
        <div className='KPI_chart'>
          <div className='title'>
            {' '}
            {selectVal === 1 ? '业务数据分布' : '站点流量分布'}
          </div>
          <div ref={chartRef} className='chart' />
        </div>
        <div className="KPI_tabs">
          {tabsInfo.map((item, index) => (
            <span onClick={() => setTabIndex(index)}
              className={`tip ${tabIndex === index ? 'active' : ''}`}
              key={index}
            >
              {item}
              <span className='line' />
            </span>
          ))}
        </div>
        <div className='KPI_chart'>
          <div ref={chart2Ref} className='chart' />
        </div>
      </div>
      <StatusCard />
    </>
  );
}
export default RightPanel;
