import A3D from '@a3d/a3d';
import bimsFn from '@a3d/a3d/lib/ex/BIMs.min.js';
import { mergeGeometries } from 'three/examples/jsm/utils/BufferGeometryUtils';
import { getCurveInCorner, isPointsEqual } from '../utils';

const defaultMaterial = new A3D.C.MeshStandardMaterial({ flatShading: true });

export default class Wall extends A3D.C.Object3D {
  #cachedGeometry = [];
  #mergeMesh;
  constructor(material = defaultMaterial, options = {}) {
    super();
    this.name = 'Wall';
    if (options) {
      this.parameters = A3D.util.extend(false, {
        material,
        merge: false,
        radius: 0,
        radiusSegments: 8,
        normalizedUv: false,
      }, options);
    }
  }

  copy(source) {
    super.copy(source, true);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    return this;
  }

  add(...data) {
    if (data[0].isObject3D) {
      super.add(...data);
      return this;
    }

    const allGeo = data.map((dataItem) => this.#createWallGeometryWithHole(dataItem));
    this.#cachedGeometry.push(...allGeo);

    if (this.parameters.merge) {
      this.#addMergeWalls();
    } else {
      this.#addWalls(allGeo);
    }

    return this;
  }

  /**
   * 更新墙的数据
   * @param  {...any} rest
   * @returns
   */
  set(...data) {
    this.#cachedGeometry = data.map((itemData) => this.#createWallGeometryWithHole(itemData));
    if (this.parameters.merge) {
      this.#addMergeWalls();
    } else {
      this.#destroyChildren();
      this.#addWalls(this.#cachedGeometry);
    }
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.#destroyChildren();
    this.#destroyCachedGeometry();
    this.parameters = null;
  }

  #destroyChildren() {
    this.children.forEach((wallItem) => {
      if (wallItem.geometry) {
        wallItem.geometry.dispose();
      }
    });
    this.remove(...this.children);
  }

  #destroyCachedGeometry() {
    for (let i = 0, len = this.#cachedGeometry.length; i < len; i++) {
      this.#cachedGeometry[i].dispose();
    }
    this.#cachedGeometry.length = 0;
  }

  /**
   * 增加墙体到合并后的模型中
   */
  #addMergeWalls() {
    const mergeGeo = mergeGeometries(this.#cachedGeometry, false);
    if (this.#mergeMesh) {
      this.#mergeMesh.geometry.dispose();
      if (this.#mergeMesh.parent) {
        this.#mergeMesh.parent.remove(this.#mergeMesh);
      }
    }
    this.#mergeMesh = new A3D.C.Mesh(mergeGeo, this.parameters.material);
    this.add(this.#mergeMesh);
  }

  /**
   * 增加墙体到 children 中
   * @param {THREE.BufferGeometry[]} allGeo
   */
  #addWalls(allGeo) {
    allGeo.forEach((itemGeo) => {
      const finalWall = new A3D.C.Mesh(itemGeo, this.parameters.material);
      if (Object.prototype.toString.apply(this.parameters.updateMesh).indexOf('Function') > -1) {
        this.parameters.updateMesh(finalWall);
      }
      this.add(finalWall);
    });
  }

  /**
   * 创建带洞的墙体
   * @param {JSON} data
   * @returns
   */
  #createWallGeometryWithHole(data) {
    const wallData = {
      path: [],
      width: 0.2,
      height: 4,
      close: false,
      radius: this.parameters.radius,
      radiusArray: [],
      radiusSegments: this.parameters.radiusSegments,
      ...data,
    };

    this.#fixPathClose(wallData);
    if (wallData.path.length < 2) {
      return new A3D.C.BufferGeometry();
    }
    if (wallData.path.length > 2) {
      this.#getRadiusArray(wallData);
      this.#fixPathToRadius(wallData);
      this.#fixPathClose(wallData);
    }

    let wall = this.#createWall(wallData);
    const clipMesh = this.#getClipMesh(wallData);
    if (clipMesh.length) {
      wall = A3D.Geo.clip(wall, { clipMesh });
    }

    return wall.geometry;
  }

  /**
   * 根据路径起点终点是否闭合，修正闭合参数
   * @param {THREE.Vector3[]|Object[]} path
   * @param {Boolean} close
   * @returns
   */
  #fixPathClose(wallData) {
    const path = wallData.path.map((point) => new A3D.C.Vector3().copy(point));
    if (path.length > 2 && isPointsEqual(path[0], path[path.length - 1])) {
      wallData.close = true;
      path.length -= 1;
    }
    wallData.path = path;
  }

  #getRadiusArray(wallData) {
    const path = wallData.path;
    if (typeof wallData.radius === 'number') {
      const radius = wallData.radius > 0 ? Math.max(wallData.radius, wallData.width) : 0;
      wallData.radiusArray = path.map(() => radius);
    }
    if (Array.isArray(wallData.radius)) {
      wallData.radiusArray = path.map((item, index) => {
        const itemRadius = wallData.radius[index];
        return typeof itemRadius === 'number' && itemRadius > 0 ? Math.max(itemRadius, wallData.width) : 0;
      });
    }
  }

  /**
   * 根据拐角半径，对所有拐角做合理的插值。
   * 合理插值的逻辑：每个拐角半径处理成不大于任意邻边的一半，否则模型会出现异常
   * @param {Object} wallData
   * @returns {THREE.Vector3[]}
   */
  #fixPathToRadius(wallData) {
    const path = wallData.path;
    const radiusArray = wallData.radiusArray;
    const radiusSegments = wallData.radiusSegments;
    const close = wallData.close;
    if (radiusSegments > 0) {
      wallData.path = getCurveInCorner(path, radiusArray, radiusSegments, close);
    }
  }

  #normalizedUv(wall) {
    if (this.parameters.normalizedUv) {
      const uv = wall.geometry.attributes.uv;
      const parameters = wall.geometry.parameters;
      const xlens = parameters.xlens;
      const ylens = parameters.ylens;
      let thickness = 0;
      for (let i = 0, len = uv.count; i < len; i++) {
        thickness = Math.min(parameters.shape.currentPoint.width, parameters.shape.currentPoint.height);
        // UV从0开始会导致截面无法覆盖材质，所以加了Number.EPSILON，这是一个问题
        uv.setX(i, Math.min(uv.getX(i) / (xlens + thickness * 4) + Number.EPSILON, 1));
        uv.setY(i, uv.getY(i) / ylens);
      }
    }
  }

  #createWall(wallData) {
    const wallSetting = {
      points: wallData.path,
      thickness: wallData.width,
      height: wallData.height,
      closed: wallData.close,
      withEndFaces: true,
      forceYAxis: true,
      shapeType: 'rectangle',
    };

    const BIMs = bimsFn();
    const wall = BIMs.wall(wallSetting);
    this.#normalizedUv(wall);
    return wall;
  }

  #getClipMesh(wallData) {
    const holeData = wallData.hole;

    let clipMesh = [];
    if (Array.isArray(holeData) && holeData.length) {
      clipMesh = holeData.map((holeItem) => {
        const holeItemFix = {
          height: 1,
          ...holeItem,
          width: wallData.width,
          close: false,
        };
        this.#fixPathClose(holeItemFix);
        const hole = this.#createWall(holeItemFix);
        return {
          mesh: hole,
          method: 'subtract',
          threshold: 0.01,
        };
      });
    }
    return clipMesh;
  }
}
