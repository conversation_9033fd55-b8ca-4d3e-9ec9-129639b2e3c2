{"resultCode": 0, "resultMessage": "200", "data": {"status": "1", "grayStepList": [{"step": 1, "stepName": "灰度准备", "status": "2"}, {"step": 2, "stepName": "部署初始灰度", "status": "2"}, {"step": 3, "stepName": "友好用户测试", "status": "2"}, {"step": 4, "stepName": "切换10%灰度用户", "status": "2"}, {"step": 5, "stepName": "全量用户升级", "status": "2"}, {"step": 6, "stepName": "灰度结束", "status": "2"}], "updatingSiteIds": [], "graySiteInfo": {"siteId": 1, "isGrayUpdating": 1, "grayMoTypeInfoList": [{"moType": "BBF", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 99, "alarmRecordList": []}, {"moType": "UVCDB", "isSupportGray": true, "isGrayUpdating": 1, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "TMP", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "VMP", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "VCP", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 4, "podErrorCount": 0, "alarmRecordList": [{"alarmId": "999999999", "alarmName": "网管服务器与网元通讯异常", "alarmLevel": "2", "alarmAdditionalInfo": "地址=*************, 协议=RESTFUL_UNIAGENT, 端口=65002", "alarmOccurTime": "1708937574659", "alarmSource": "host-10-139-199-74", "nativeMeDn": "7dc08f7069d84eca960bc", "csn": "188139088", "occurUtc": "1708937574659", "localization": null, "category": null, "moi": null, "siteName": null, "siteId": null, "moTypeId": null}, {"alarmId": "999999999", "alarmName": "网管服务器与网元通讯异常", "alarmLevel": "2", "alarmAdditionalInfo": "地址=*************, 协议=RESTFUL_UNIAGENT, 端口=65002", "alarmOccurTime": "1708937574625", "alarmSource": "host-10-139-209-52", "nativeMeDn": "7dc08f720eddae12330c2", "csn": "188139089", "occurUtc": "1708937574625", "localization": null, "category": null, "moi": null, "siteName": null, "siteId": null, "moTypeId": null}, {"alarmId": "999999999", "alarmName": "网管服务器与网元通讯异常", "alarmLevel": "2", "alarmAdditionalInfo": "地址=*************, 协议=RESTFUL_UNIAGENT, 端口=65002", "alarmOccurTime": "1708937574239", "alarmSource": "host-10-139-199-70", "nativeMeDn": "7dc08f7ba481a81bf50b4", "csn": "188139086", "occurUtc": "1708937574239", "localization": null, "category": null, "moi": null, "siteName": null, "siteId": null, "moTypeId": null}, {"alarmId": "999999999", "alarmName": "网管服务器与网元通讯异常", "alarmLevel": "2", "alarmAdditionalInfo": "地址=*************, 协议=RESTFUL_UNIAGENT, 端口=65002", "alarmOccurTime": "1708937574581", "alarmSource": "host-10-139-199-65", "nativeMeDn": "7dc08f73c7fb1a24c30b8", "csn": "188139087", "occurUtc": "1708937574581", "localization": null, "category": null, "moi": null, "siteName": null, "siteId": null, "moTypeId": null}, {"alarmId": "999999999", "alarmName": "网管服务器与网元通讯异常", "alarmLevel": "2", "alarmAdditionalInfo": "地址=*************, 协议=RESTFUL_UNIAGENT, 端口=65002", "alarmOccurTime": "1708937572738", "alarmSource": "host-10-139-199-78", "nativeMeDn": "7dc08f7d6f3152922a0be", "csn": "188139084", "occurUtc": "1708937572738", "localization": null, "category": null, "moi": null, "siteName": null, "siteId": null, "moTypeId": null}]}, {"moType": "invgmdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "billdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "invgendb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 1, "alarmRecordList": []}, {"moType": "edrhisdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "BiliManager", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "hugebillgen", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "<PERSON><PERSON><PERSON>", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "UPC", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "GL", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "DC", "isSupportGray": true, "isGrayUpdating": 1, "podCount": 1, "podErrorCount": 10, "alarmRecordList": []}, {"moType": "AR", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "MEP", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "MBN", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Digital Fou", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "MEDDB", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "MDB", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "MBC", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Aggregation", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "adaptgmdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "cdfgmdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "cbpgmdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Notification", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "OnlineChar", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "RecurChar", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "BizMngChar", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Rerating", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Converged", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "CDFAPPP", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "dsfagent", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 0, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "CDRProcess", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "DigitalView", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "cbpgmdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "userdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "bmpdb", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "pdbsqiagent", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "BM", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "REPDB", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "RportApp", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "DataInteger", "isSupportGray": false, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 500, "alarmRecordList": []}, {"moType": "<PERSON><PERSON><PERSON>", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "see", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 0, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "Internaladapter", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "AccessFaca", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "NSLB", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "SouthCGW", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}, {"moType": "NorthCGW", "isSupportGray": true, "isGrayUpdating": 0, "podCount": 1, "podErrorCount": 0, "alarmRecordList": []}]}, "userVolume": 1}}