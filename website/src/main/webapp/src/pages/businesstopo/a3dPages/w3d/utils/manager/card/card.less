.w3d_container {
  .w3d-card.w3d-pod {
    transform: translate(0, -50%);
    pointer-events: initial;
  }

  .w3d-card.w3d-tps {
    transform: translate(52%, 0%);
    pointer-events: initial;
  }

  .w3d-card.w3d-groupDetail {
    transform: translate(50%, 0%);
    pointer-events: initial;
  }

  .w3d-card.w3d-db {
    transform: translate(0%, -50%);
    pointer-events: initial;
  }

  .w3d-card.w3d-dbLine {
    transform: translate(50%, 0%);
    pointer-events: initial;
  }

  .w3d-card.w3d-podDetail {
    transform: translate(-50%, 0%);
  }

  .w3d-card.w3d-nameDetail {
    transform: translate(0, -50%);

    min-width: 3.25rem;
    width: max-content;
    padding: 0 0.5rem;
    height: 2rem;
    border-radius: 0.25rem;
    background-color: #393939;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    font-size: 0.875rem;
    color: #FFFFFF;
    text-align: center;
    line-height: 2rem;
  }

  .w3d-card.w3d-nameDetailRight {
    transform: translate(-50%, -50%);
  }

  .w3d-card.w3d-incidentPanelDetail {
    transform: translate(50%, 100%);
  }
}