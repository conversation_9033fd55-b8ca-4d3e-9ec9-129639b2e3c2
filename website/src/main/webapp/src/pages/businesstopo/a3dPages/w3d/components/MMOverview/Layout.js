export default class Layout {
  static initLayout(level1Data) {
    const totalNumber = level1Data.children.length > 10 ? 2 : 1;
    const maxRow = Math.ceil(level1Data?.children?.[0]?.count / 2);
    const maxCol = totalNumber === 2 ? Math.ceil(level1Data.children.length / 2) * 2 : level1Data.children.length * 2;
    return this.#getLayoutSetting(level1Data, maxRow, maxCol, totalNumber);
  }

  static #getLayoutSetting(level1Data, maxRow, maxCol) {
    const level1Layout = [{ row: maxRow, col: 0, group: [] }, { row: maxRow, col: 0, group: [] }];
    this.#setLayout(level1Layout, level1Data, maxCol);
    const level1Setting = {
      data: level1Data.data || {},
      row: level1Layout.reduce((total, cur) => total + cur.row, 0),
      col: level1Layout.reduce((total, cur) => Math.max(total, cur.col), 0),
      level1: level1Layout,
    };
    return level1Setting;
  }

  static #setLayout(level1Layout, level1Data, maxCol) {
    let level1GroupIndex = 0;
    const maxIndex = level1Layout.length;
    for (let i = 0, len = level1Data.children.length; i < len; i++) {
      const level2Data = level1Data.children[i];
      let level2Setting = this.#getLevel1Setting(level2Data, level1Layout[level1GroupIndex].row);
      if (level1Layout[level1GroupIndex].col + level2Setting.col > maxCol) {
        if (level1GroupIndex < maxIndex - 1) {
          level1GroupIndex++;
          level2Setting = this.#getLevel1Setting(level2Data, level1Layout[level1GroupIndex].row);
        }
      }
      level1Layout[level1GroupIndex].col += level2Setting.col;
      level1Layout[level1GroupIndex].group.push(level2Setting);
    }
    for (let i = level1Layout.length - 1; i >= 0; i--) {
      if (level1Layout[i].group.length === 0) {
        level1Layout.pop();
      }
    }
  }

  static #getLevel1Setting(level2Data) {
    const curCount = level2Data.count || 1;
    const col = curCount > 1 ? 2 : 1;
    const level2Layout = [];
    let stayCount = level2Data.count;
    while (stayCount > 0) {
      level2Layout.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    const row = level2Layout.length;
    return {
      data: level2Data.data || {}, col, row, level2: level2Layout,
    };
  }
}
