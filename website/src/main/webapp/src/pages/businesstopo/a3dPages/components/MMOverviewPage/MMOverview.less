
.w3d-podDetail-third-block {
  font-size: 1rem;
  line-height: 1.25rem;
  color: #f5f5f5;
  position: relative;
  left: -115px;
  .w3d-podDetail-gray {
    color: #676767;
  }
}

.w3d-podDetail-channel-block {
  font-size: 1rem;
  line-height: 1.25rem;
  color: #f5f5f5;
  position: relative;
  left: -130px;
  .w3d-podDetail-gray {
    color: #676767;
  }
}

.w3d-podDetail-block {
  font-size: 1rem;
  line-height: 1.25rem;
  color: #f5f5f5;

  &:not(:first-child) {
    margin-top: 2rem;
  }

  .w3d-podDetail-gray {
    color: #676767;
  }
}

.w3d-podDetail-block2 {
  font-size: 1rem;
  line-height: 1.25rem;
  color: #f5f5f5;
  margin-right: 2rem;
  &:not(:first-child) {
    margin-top: 2rem;
  }

  .w3d-podDetail-gray {
    color: #676767;
  }
}

.w3d-app-card, 
.w3d-vm-card {
  min-width: 3.25rem;
  width: max-content;
  padding: 0 0.5rem;
  border-radius: 0.25rem;
  background-color: #393939;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  font-size: 0.875rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 2rem;
}