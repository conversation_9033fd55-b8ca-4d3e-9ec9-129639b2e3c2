const getMinIndex = (array) => {
  let min = Infinity;
  let result = -1;
  array.forEach((item, index) => {
    if (item < min) {
      min = item;
      result = index;
    }
  });
  return result;
};

const getOffset = (align, width, height) => {
  let offset = {
    x: 0,
    y: 0,
  };
  if (align === 'center') {
    offset.x -= width / 2;
    offset.y -= height / 2;
  }
  return offset;
};

export default class WaterfallFlow {
  #columns;
  #result;
  #column;
  #row;
  #itemAlign;
  #maxHeight = 0;

  constructor(column = { width: 10, count: 2, space: 1 }, row = { space: 1 }, itemAlign = 'center') {
    this.#columns = new Array(column.count).fill(0);
    this.#result = [];
    this.#column = column;
    this.#row = row;

    this.#itemAlign = itemAlign;
  }

  add(height) {
    const minIndex = getMinIndex(this.#columns);
    const x = minIndex * (this.#column.width + this.#column.space);
    const y = this.#columns[minIndex];
    this.#columns[minIndex] += this.#row.space + height;
    this.#maxHeight = Math.max(this.#maxHeight, this.#columns[minIndex]);

    const offset = getOffset(this.#itemAlign, this.#column.width, height);
    const position = { x: x - offset.x, y: y - offset.y };
    this.#result.push(position);
    return position;
  }

  getIndex(index) {
    return this.#result[index];
  }

  getHeight() {
    return this.#maxHeight;
  }
}
