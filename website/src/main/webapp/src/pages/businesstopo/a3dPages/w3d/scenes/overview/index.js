import { baseUrl } from '@pages/businesstopo/a3dPages/global';
import defaultTheme from '@pages/businesstopo/a3dPages/network/theme/overview/default';
import {
  ComponentManager, createGui, offEvent, onEvent, W3D,
} from '@pages/businesstopo/a3dPages/w3d/utils';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import { resetTheme } from './handler/theme';
import HoverHandler from '../commonHandler/HoverHandler';

let cameraSetting = {
  fov: 30,
  lat: Math.PI / 78,
  lon: 0,
  distance: 65,
  minDistance: 10,
  maxDistance: 80,
  near: 0.1,
  far: 500,

  // 相机焦点
  target: {
    x: 0,
    y: 0,
    z: 0,
  },
  animate: false,
  resetSetCameraByMesh: true,
};

const A3D = getA3D();
const resource = {
  modelsUrl: {
    '4G': `${baseUrl}w3d/models/4G.obj`,
    '5G': `${baseUrl}w3d/models/5G.obj`,
    group: `${baseUrl}w3d/models/group.obj`,
    smpp: `${baseUrl}w3d/models/smpp.obj`,
    universal: `${baseUrl}w3d/models/universal.obj`,
  },
  textureUrl: {
    select: {
      select: `${baseUrl}w3d/theme/images/select.png`,
      subSelect: `${baseUrl}w3d/theme/images/subSelect.png`,
      halo: `${baseUrl}w3d/theme/images/halo.png`,
    },
    flow: `${baseUrl}w3d/theme/images/flow.png`,
    circle: {
      normal: `${baseUrl}w3d/theme/images/normal-circle.png`,
      alarm: `${baseUrl}w3d/theme/images/alarm-circle.png`,
    },
  },
};

export default class Overview3D {
  resize = null;
  piuAttrs = {};

  constructor(eventBusHandler) {
    this.eventBusHandler = eventBusHandler;
    this.w3d = null;
    this.componentManager = null;
    this.#initEventHandlers();
    this.#initEvents();
  }

  // 初始化监听事件
  #initEventHandlers() {
    const events1 = this.#getEvents1();
    this.eventHandlers = [
      { name: 'to3d_zoomIn', callback: () => this.w3d.zoomIn() },
      { name: 'to3d_zoomOut', callback: () => this.w3d.zoomOut() },
      { name: 'to3d_resetCamera', callback: () => this.w3d.resetCamera() },
      {
        name: 'to3d_switchTheme',
        callback: (e) => {
          this.w3d.themeManager.switch(e);
        },
      },
      {
        name: 'to3d_updateGrayUpgrade',
        callback: (grayUpgradeIds) => {
          const site = this.componentManager.comps.site;
          site.setGrayUpgrade(grayUpgradeIds);
        },
      },
      {
        name: 'to3d_updateBusiness',
        callback: (currentBusinessId) => {
          const north = this.componentManager.comps.north;
          const south = this.componentManager.comps.south;
          let business = north.getObjectByName(currentBusinessId) || south.getObjectByName(currentBusinessId);
          if (business) {
            A3D.DOM.trigger(this.w3d.agent, 'click', { mesh: business });
          }
        },
      },
      ...events1,
    ];
  }

  #getEvents1() {
    return [];
  }

  // 初始化模型、卡片的点击交互事件
  #initEvents() {
    const hoverEvents = this.#getHoverEvents();
    this.events = {
      showPanel: (setting) => {
        this.eventBusHandler.emit('from3d_showPanel', setting);
      },
      switchBusiness: (setting) => {
        this.eventBusHandler.emit('from3d_switchBusiness', setting, this.piuAttrs.isPiu);
      },
      drillDownSite: (setting) => {
        this.eventBusHandler.emit('from3d_drillDownSite', setting);
      },
      showBusinessGroupTip: (setting) => {
        this.eventBusHandler.emit('from3d_showBusinessGroupTip', setting, this.piuAttrs.piuData);
      },
      showBusinessTip: (setting) => {
        this.eventBusHandler.emit('from3d_showBusinessTip', setting);
      },
      showTPSCard: (setting) => {
        this.eventBusHandler.emit('from3d_showTPSCard', setting);
      },
      showSiteNameCard: (setting) => {
        this.eventBusHandler.emit('from3d_showSiteNameCard', setting);
      },
      ...hoverEvents,
    };
  }

  #getHoverEvents() {
    const events = {
      hoverInObject: (object) => {
        this.hoverHandler.hoverIn(object);
      },
      hoverOutObject: (object) => {
        this.hoverHandler.hoverOut(object);
      },
      hoverInLine: (line) => {
        this.hoverHandler.hoverInLine(line);
      },
      hoverOutLine: (line) => {
        this.hoverHandler.hoverOutLine(line);
      },
    };
    return events;
  }

  async init(wrapper, routeQuery, data, extraData) {
    if (this.w3d) {
      this.w3d.destroy();
    }
    // 初始化w3d
    if (extraData) {
      this.piuAttrs = {
        isPiu: extraData.isPiu,
        piuCameraSetting: extraData.piuCameraSetting,
        piuData: extraData.piuData,
      };
    }
    this.w3d = this.#initW3d(wrapper, routeQuery);
    this.w3d.agent.env.raycaster.params.Line2 = { threshold: 10 };
    // 监听事件
    onEvent(this.eventBusHandler, this.eventHandlers);

    // 创建组件管理器
    this.componentManager = new ComponentManager(this.w3d, resource, this.events);
    // 设置卡片告警态。无配置show情况下，卡片告警态显示，非告警态隐藏
    this.componentManager.cardManager.setWarnStatus({ status: ['alarm'] });
    // 加载资源
    await this.componentManager.loadResource();
    // resize
    this.resize = () => {
      this.w3d.resize();
    };
    // 初始化灯光
    this.#initLights();
    // 初始化材质管理器
    await this.#initTheme(data);
    // 根据静态数据，创建组件
    this.update(data);
    this.resize();
    // 显示GUI
    if (routeQuery.showGui) {
      createGui(this.w3d.gui, this.w3d);
    }
    this.hoverHandler = new HoverHandler(this.w3d.agent);
    window.addEventListener('resize', this.resize);
    this.#bindAgentEventListener();
  }

  update(updateData) {
    this.componentManager.updateComp('halo', { id: 'halo' });
    Object.keys(updateData).forEach((key) => {
      this.componentManager.updateComp(key, updateData[key]);
    });
  }

  destroy() {
    window.removeEventListener('resize', this.resize);
    offEvent(this.eventBusHandler, this.eventHandlers);
    if (this.w3d) {
      this.w3d.destroy();
    }
  }

  #initW3d(wrapper, routeQuery) {
    const w3d = new W3D(
      wrapper, routeQuery,
      { ...cameraSetting, ...this.piuAttrs.piuCameraSetting }, {
        customize: false,
        maxPolarAngle: Math.PI * 0.99,
        enableRotate: false,
        screenSpacePanning: true,
      }, {
        logarithmicDepthBuffer: true,
        // 若不使用outline，关闭后期处理可减少锯齿影响
        enableOutline: false,
        // enableGammaCorrection: true,
        loopRender: true,
      },
    );
    const { MOUSE, TOUCH } = A3D.C;
    w3d.orbit.mouseButtons = { LEFT: MOUSE.PAN, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.ROTATE };
    w3d.orbit.touches = { ONE: TOUCH.PAN, TWO: TOUCH.DOLLY_ROTATE };
    w3d.agent.setInterval(30);
    return w3d;
  }

  #initLights() {
    let lightGroup = A3D.createGroup();
    lightGroup.name = 'lightGroup';
    this.w3d.agent.add(lightGroup);
    this.w3d.agent.addLight('ambient', { parent: lightGroup, color: '#fff', intensity: 0.3 });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 1,
      x: 0,
      y: -10,
      z: 30,
    });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 0.8,
      x: -30,
      y: 0,
      z: 0,
    });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 0.8,
      x: 30,
      y: 0,
      z: 0,
    });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 0.8,
      x: 0,
      y: 50,
      z: -30,
    });
  }

  async #initTheme(data) {
    // 初始化材质管理器
    const darkTheme = JSON.parse(JSON.stringify(defaultTheme));
    await resetTheme(defaultTheme, darkTheme, data, this.w3d);
    this.w3d.initThemeManager(defaultTheme, darkTheme);
    this.w3d.themeManager.switch('default');
  }

  #bindAgentEventListener = () => {
    this.w3d.agent.on('click', () => {
      const site = this.componentManager.comps.site;
      if (site && site.getSelected()) {
        site.select(null, null, () => {
          this.events.showPanel({
            currentSiteName: '',
            currentSiteId: -1,
          });
        });
        this.w3d.agent.renderOnce();
      }
    });
  };
}
