
.moType_tip_container {
  height: 84px;
  color: #f5f5f5;
  background-color: #393939;
  padding: 16px;
  box-sizing: border-box;
}

.moType_tipTitle {
  font-size: 16px;
  line-height: 20px;
  display: inline-block;
}

.moType_status {
  height: 21px;
  font-size: 12px;
  color: #00a874;
  border: 1px solid #00a874;
  border-radius: 2px;
  display: inline-block;
  margin-left: 8px;
  text-align: center;
  line-height: 21px;
  padding: 0 2px;
}


.moType_abnormal_status {
  height: 21px;
  font-size: 12px;
  color: #e10f35;
  border: 1px solid #e10f35;
  border-radius: 2px;
  display: inline-block;
  margin-left: 8px;
  text-align: center;
  line-height: 21px;
  padding: 0 2px;
}

.moType_tipValue {
  font-size: 14px;
  line-height: 16px;
  margin-top: 16px;
}

.moType_topology_text_app {
  font-size: 14px;
  color: #f5f5f5;
  margin-bottom: 6rem;
}
.moType_topology_text_vm {
  font-size: 14px;
  color: #f5f5f5;
  margin-top: 6rem;
}
.moType_topology_text_title {
  font-size: 14px;
  color: #f5f5f5;
}
.moType_topology_text_podCount {
  font-size: 14px;
  color: #f5f5f5;
  display: inline-block;
  margin-top: 32px;
}
.moType_topology_text_podCountUnit {
  font-size: 14px;
  color: #676767;
  display: inline-block;
}
.moType_topology_text_value {
  font-size: 14px;
  color: #f5f5f5;
  margin-top: 24px;
}
.moType_topology_text_text {
  font-size: 12px;
  color: #676767;
  margin-top: 4px;
}

.podPanelChartContainer {
  position: relative;
  background: #272727;
  height: 100%;
  .eui_tab {
    background: #272727;
  }
  .eui_tab_content.active {
    height: 80%;
  }
  .eui_tab_bar {
    display: none;
  }
  .eui_tab_title {
    padding-left: 0;
    font-size: 16px;
    .active:focus {
      outline: none;
    }
  }
  .eui_tab_title.active {
    margin-bottom: -0.0625rem;
    height: 1.875rem;
    line-height: 1.875rem;
    color: #2e94ff;
    font-weight: 700;
    border-bottom: 0.125rem solid #2e94ff;
    flex-shrink: 0;
  }
  .eui_tab_title:hover {
    color: #2e94ff;
  }
  .eui_tab_title.active.lastActive {
    padding-right: 0;
  }

  .eui_tab_title.display {
    display: inline-block;
    max-width: 100px;
  }
}
.indicator_select_btn{
  .eui-btn-icon {
    height: 1.5rem;
    width: 1.2rem;
  }
}
