import React, {useEffect, useRef} from 'react';
import ReactDOM from 'react-dom';
import i18n from '@pages/businesstopo/locales/moTypeDrill';
import {$t, registerResource} from '@util';
import {getMoTypeDrillData, getPodDeployData, getVMDeployData} from '@pages/businesstopo/api/moTypeDrill';
import {
  destroyW3D,
  initW3D,
  updatePodDrillData,
} from '@pages/businesstopo/a3dPages/components/PodDrillDownPage/podDrillDown3D';
import {eventBusHandler} from '@pages/businesstopo/a3dPages/components/utils';
import {STATUS} from '@pages/businesstopo/const/moTypeDrill';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import './css/moTypeDrill.less';

const PodView = (props) => {
  registerResource(i18n, 'moTypeDrillI18n');
  const w3dContainerRef = useRef(null);
  const allAppData = useRef({});
  const allPodData = useRef({});
  const allVMData = useRef({});
  let allPodNumber = useRef(0);
  let productRate = useRef(0);

  let {
    style = {},
    moTypeObj = {
    },
    errorIdList = [],
    alarmSetting = {},
    updatePage,
  } = props;

  // 查询钻取数据后处理
  const handleQueryPageData = appDataList => {
    for (let app of appDataList) {
      allAppData.current[app.dnId] = app;
      app.podDataList.map(pod => {
        allPodData.current[pod.dnId] = pod;
      });
      if (app.vmDataList && app.vmDataList.length > 0) {
        app.vmDataList.map(vm => {
          allVMData.current[vm.dnId] = vm;
        });
      }
    }
  };

  const handlePreQueryPageData = (data) => {
    for (let app of data.businessClusterList) {
      if (errorIdList.map(String).includes(app.dnId)) {
        app.csnState = 1;
      } else {
        app.csnState = 0;
      }

      for (let pod of app.podDataList) {
        if (errorIdList.map(String).includes(pod.dnId)) {
          pod.csnState = 1;
        } else {
          pod.csnState = 0;
        }
      }
      for (let vm of app.vmDataList) {
        if (errorIdList.map(String).includes(vm.dnId)) {
          vm.csnState = 1;
        } else {
          vm.csnState = 0;
        }
      }
    }
  };

  const showTips = (setting) => {
    const dnId = setting.data.dnId;
    let tipStr;
    setting.dom.innerHTML = '';
    if (setting.type === 'pod') {
      let podId = setting.data.dnId;
      let podData = allPodData.current[podId];
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (podData.availableStatus === STATUS.normal && podData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      tipStr =
        <div className="moType_tip_container">
          <div className="moType_tipTitle">{setting.data.name}</div>
          <div className={className}>{status}</div>
          <div
            className="moType_tipValue"
          >{$t('moType.drill.rightPanel.time')} : {formatDate2SysSetting(parseInt(podData.createTime))}
          </div>
        </div>;
    }
    if (setting.type === 'app') {
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      let appData = allAppData.current[parseInt(setting.data.dnId)];
      if (appData.availableStatus === STATUS.normal && appData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      tipStr =
        <div className="moType_tip_container">
          <div className="moType_tipTitle">{setting.data.name}</div>
          <div className={className}>{status}</div>
          <div className="moType_tipValue">{$t('w3d.version')}:{setting.data.version}</div>
        </div>;
    }
    if (setting.type === 'vm') {
      let vmId = setting.data.dnId;
      let vmData = allVMData.current[vmId];

      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (vmData.availableStatus === STATUS.normal && vmData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }

      tipStr =
        <div className="moType_tip_container">
          <div className="moType_tipTitle">{setting.data.name}</div>
          <div className={className}>{status}</div>
          <div className="moType_tipValue">{$t('moType.drill.vm.tooltip.vmIp')} : {setting.data.ip || 'NA'}</div>
        </div>;
    }
    if (tipStr) {
      ReactDOM.render(
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '0.5rem',
        }}
        >
          {tipStr}
          {
            alarmSetting[dnId] ? <div>{alarmSetting[dnId].msgDiv}</div> : ''
          }
        </div>,
        setting.dom,
      );
    }
  };

  const updatePageData = (param) => {
    moTypeObj = param.moTypeObj;
    errorIdList = param.errorIdList;
    alarmSetting = param.alarmSetting;
    let params = {
      instanceId: moTypeObj.moTypeId,
    };
    getMoTypeDrillData(params, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      let events = null;
      if (resp.data && resp.data.businessClusterList && resp.data.businessClusterList.length > 0) {
        handlePreQueryPageData(resp.data);
        events = getA3dEvents(
          resp.data.businessClusterList, resp.data.environmentType === 1,
        );
        updatePodDrillData(resp.data);
        handleQueryPageData(resp.data.businessClusterList);
      }
    });
  };

  // 查询pod部署关系
  const queryPodDeployData = podId => new Promise((resolve, reject) => {
    let param = {instanceId: podId};
    getPodDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      resolve({
        respData: resp.data, dnId: resp.data.dnId, relation: resp.data.vmDataList.map(vm => vm.dnId),
      });
    });
  });

  // 查询VM部署关系
  const queryVmDeployData = vmId => new Promise((resolve, reject) => {
    let param = {instanceId: vmId};
    getVMDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      resolve({
        respData: resp.data, dnId: resp.data.dnId, relation: resp.data.podInstanceIdList.map(num => num.toString()),
      });
    });
  });

  const getA3dEvents = (appDataList, isVMScenario) => {
    let productNumber = 0;
    let isShowLeftText = true;
    appDataList.map(app => {
      allPodNumber.current += app.podDataList.length;
      if (app.isGray === 0) {
        productNumber += app.podDataList.length;
      }
      if (app.isGray === 2) {
        isShowLeftText = false;
      }
    });
    productRate.current = Math.trunc((productNumber / allPodNumber.current) * 100);
    return [
      {
        // 悬浮事件，显示tooltip
        eventName: 'from3d_showCommonTip', fn(setting) {
          showTips(setting);
        },
      },
      {
        // 显示左侧详情
        eventName: 'from3d_showPodDrillDownDetail', fn(setting) {
          if (setting.type === 'app') {
            const dom = document.createElement('div');
            dom.classList.add('w3d-podDetail-block');
            const applicationType = moTypeObj.applicationType;
            if (parseInt(applicationType) === 3) {
              dom.innerText = 'DB';
            } else {
              dom.innerText = $t('moType.drill.leftText.app');
            }
            setting.dom.append(dom);
          } else if (setting.type === 'vm') {
            const dom = document.createElement('div');
            dom.classList.add('w3d-podDetail-block');
            dom.innerText = 'HOST';
            setting.dom.append(dom);
          } else if (setting.type === 'pod') {
            const dom = document.createElement('div');
            dom.innerHTML = (
              `
               <div class='w3d-podDetail-block'>${isVMScenario ? $t('moType.drill.leftText.pod.instance') : $t('moType.drill.leftText.pod')}</div>
               <div class='w3d-podDetail-block'>
                 <span>${allPodNumber.current}</span><span class='w3d-podDetail-gray'>${$t('moType.drill.leftText.unit')}</span>
               </div> 
              `
            );

            if (isShowLeftText) {
              dom.innerHTML += (
                `
                  <div class='w3d-podDetail-block'>
                    <div>${productRate.current}%</div>
                    <div class='w3d-podDetail-gray'>${$t('moType.drill.leftText.product')}</div>
                   </div>
                  <div class='w3d-podDetail-block'>
                   <div>${100 - productRate.current}%</div>
                   <div class='w3d-podDetail-gray'>${$t('moType.drill.leftText.gray')}</div>
                  </div>
                `
              );
            }
            setting.dom.append(dom);
          }
        },
      },
      {
        // 点击事件，联动右侧面板
        eventName: 'from3d_showPanel', async fn(setting) {
          if (setting.type === '' || setting.type === 'app') {
            return;
          }
          if (setting.type === 'pod') {
            let podDeployData = await queryPodDeployData(setting.id);
            podDeployData.dnId = setting.id;
            eventBusHandler.emit('to3d_updateSelectedRelation', podDeployData);
            return;
          }
          if (setting.type === 'vm') {
            let vmDeployData = await queryVmDeployData(setting.id);
            eventBusHandler.emit('to3d_updateSelectedRelation', vmDeployData);
          }
        },
      },
    ];
  };

  useEffect(() => {
    updatePage(updatePageData);
    let params = {
      instanceId: moTypeObj.moTypeId,
    };

    let events = null;
    getMoTypeDrillData(params, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }

      if (resp.data && resp.data.businessClusterList && resp.data.businessClusterList.length > 0) {
        handlePreQueryPageData(resp.data);
        events = getA3dEvents(
          resp.data.businessClusterList, resp.data.environmentType === 1,
        );
        initW3D(w3dContainerRef, {initData: resp.data, eventArr: events, isMM: false, isAgent: true});
        handleQueryPageData(resp.data.businessClusterList);
      }
    });

    return () => {
      destroyW3D(events);
    };
  }, []);

  return (
    <div style={{background: style.background}}>
      <div className='podDrillDownTopology'>
        <div ref={w3dContainerRef} className='w3d_container' />
      </div>
    </div>
  );
};


export default PodView;
