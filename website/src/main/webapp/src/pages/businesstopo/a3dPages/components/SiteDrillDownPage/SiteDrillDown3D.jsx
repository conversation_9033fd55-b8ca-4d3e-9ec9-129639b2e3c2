import eventBus from '@pages/businesstopo/a3dPages/bus';
import { getMessage, registerResource } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/siteDrillDown';
import { eventBusHandler, getRouteQuery, toPodDrillDownPage } from '../utils';

registerResource(i18n, 'w3d');
let home3D;

const getDrGroupList = data => {
  const drGroupList = data.drGroupList.map(drGroup => {
    const newDrGroup = { ...drGroup };
    newDrGroup.moTypeInfoList = drGroup.moTypeInfoList.filter(moType => moType.moTypeInstanceCount > 0);
    return newDrGroup;
  });
  return drGroupList;
};

const getRelationship = (data, drGroupList) => {
  const relationship = {};
  drGroupList.forEach(drGroup => {
    drGroup.moTypeInfoList.forEach(moType => {
      moType.isSupportGray = false;
      moType.isGrayUpdating = 0;
      moType.podCount = 0;
      moType.podErrorCount = 0;
      relationship[moType.moTypeId] = {
        moTypeLinks: new Set(),
        siteLinks: [],
      };
    });
  });
  data.moTypeLinks.forEach(relation => {
    const id1 = relation.fromMoTypeId;
    const id2 = relation.toMoTypeId;
    if (relationship[id1] && relationship[id2]) {
      relationship[id1].moTypeLinks.add(id2);
      relationship[id2].moTypeLinks.add(id1);
    }
  });
  Object.keys(relationship).forEach(moTypeId => {
    relationship[moTypeId].moTypeLinks = [...relationship[moTypeId].moTypeLinks].map(id => ({ to: id }));
  });
  data.siteInfoList.forEach(siteInfo => {
    if (data.siteId === siteInfo.siteId) {
      return;
    }
    siteInfo.moTypeList?.forEach(moType => {
      if (relationship[moType.moTypeId]) {
        relationship[moType.moTypeId].siteLinks.push({
          to: siteInfo.siteId,
          alarmCount: moType.alarmCount || 0,
          protocol: moType.protocol || 'REST',
        });
      }
    });
  });
  return relationship;
};

// 处理数据结构
let lastResult;
const getData = (data, positionSetting) => {
  const drGroupList = getDrGroupList(data);
  const relationship = getRelationship(data, drGroupList);
  const result = {
    id: data.siteId,
    name: data.siteName,
    autoPosition: true,
    positionSetting,
    drGroupList,
    relationship,
    siteInfoList: data.siteInfoList.map(siteInfo => {
      const newSiteInfo = { ...siteInfo };
      delete newSiteInfo.moTypeList;
      return newSiteInfo;
    }),
  };
  lastResult = result;
  return result;
};

const getGraySiteInfo = graySiteInfo => {
  const moTypeMap = {};
  lastResult.drGroupList.forEach(drGroup => {
    drGroup.moTypeInfoList.forEach(moTypeInfo => {
      moTypeMap[moTypeInfo.moTypeName] = moTypeInfo.moTypeId;
    });
  });

  graySiteInfo.grayMoTypeInfoList.forEach(grayMoType => {
    if (!grayMoType.moTypeId) {
      grayMoType.moTypeId = moTypeMap[grayMoType.moType];
    }
  });
  return graySiteInfo;
};

const events = [
  {
    eventName: 'from3d_showPanel',

    // 2D-3D对接，点击事件，联动右侧面板
    fn(setting) {
      setting;

      // 打印 'showPanel', setting
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showDbTip',
    fn(setting) {
      const dom = document.createElement('div');
      dom.style.background = '#393939FF';
      dom.style.boxShadow = '0px 4px 16px 0px #00000080';
      dom.style.width = '100px';
      dom.style.height = '100px';
      dom.style.borderRadius = '8px';

      setting.dom.append(dom);
    },
  },
  {
    // 2D-3D对接，hover业务 dbLine ，显示卡片
    eventName: 'from3d_showDbLineTip',
    fn(setting) {
      const dom = document.createElement('div');
      dom.style.background = '#393939FF';
      dom.style.boxShadow = '0px 8px 32px 0px #00000080';
      dom.style.width = '100px';
      dom.style.height = '100px';
      dom.style.borderRadius = '8px';

      setting.dom.append(dom);
    },
  },
  {
    // 2D-3D对接，重置选中站点
    eventName: 'from3d_changeSite',
    async fn(setting) {
      this.changeSite(setting);
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_drillDownMoType',
    fn(setting) {
      setTimeout(() => {
        toPodDrillDownPage(setting);
      }, 0);
    },
  },
];

export const initW3D = async (container, setting) => {
  // setting下的变量：const setting = { siteData, positionSetting, groupId, changeSite }
  events.forEach(item => {
    item.fn = item.fn.bind(setting);
    eventBus.addListener(item.eventName, item.fn);
  });

  // 将数据转化成3D需要的数据
  const data = getData(setting.siteData, setting.positionSetting);

  home3D = new Home3D(eventBusHandler);
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, { sitesDrillDown: data });
  if (setting.groupId) {
    eventBus.emit('to3d_focusGroup', setting.groupId);
  }
};

export const updateSiteData = data => {
  // 2D-3D对接，切换站点更新站点数据
  const siteData = getData(data);
  eventBus.emit('to3d_updateDrillDownData', siteData);
};

export const updateGray = grayBasicInfo => {
  // 2D-3D对接，灰度升级，站点闪烁
  const graySiteInfo = getGraySiteInfo(grayBasicInfo.graySiteInfo);
  eventBus.emit('to3d_updateDrillDownStatus', graySiteInfo);

  let grayList = graySiteInfo.grayMoTypeInfoList
    .filter(item => item.isGrayUpdating)
    .map(item => item.moTypeId);
  eventBus.emit('to3d_updateDrillDownGray', grayList);
};

export const destroyW3D = () => {
  events.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};
