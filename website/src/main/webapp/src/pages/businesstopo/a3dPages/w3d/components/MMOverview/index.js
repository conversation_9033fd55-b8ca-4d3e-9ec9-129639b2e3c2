import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {
  drawRoundedRect,
  getGroundOptions, getTextPlane, getTextToTex, getTypeItem, setDbStatus,
} from '../utils';
import ExtrudeShape from '../common/ExtrudeShape';
import {getObjectSize} from '../common/utils';
import Line from '../Line';
import Layout from './Layout';
import StripLayout from './StripLayout';
import {
  createLineThird2Channel,
  createLineChannel2Strip,
  getBezier3,
  updateLineThird2Channel,
  updateLineChannel2Strip,
  getBezier4,
  getBezier5,
  getBezier6,
  bindChannelLineEvents,
  bindStripLineEvents,
} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/line';
import Wall from '@pages/businesstopo/a3dPages/w3d/components/common/Wall/index.js';

const A3D = getA3D();

const nameColorNormal = '#F5F5F5';
const nameColorSelect = '#2E94FF';
const nameColorUnselect = '#BBBBBB';
const showNameCount = 100;

export default class MMOverview extends A3D.C.Object3D {
  #data;

  #size;

  #modelMap;

  #matMap;

  #w3d;

  #componentManager;

  #animation;

  #sizeSetting = {};

  #rowRatio = 10;

  #appScaleRatio = 0.6;

  #podScaleRatio = 0.4;

  #appDefaultScale = 1;

  #podDefaultScale = 1;

  #meshMaxSize = 1;

  #thirdPartyMap = {};

  #channelMap = {};

  #stripMap = {};

  #cardMap = {};

  #selectedAppId;

  #selectedId;

  #relationId = [];

  #isGray = false;

  #podMaxSpace = 3.2;

  #podMinSpace = 1.8;

  #thirdLinePos;

  #channelLinePos;

  #channelPositions = {};

  #stripPositions = {};

  #lineThird2ChannelIns;

  #lineChannel2StripIns;

  #stripLayoutData;

  #stripsRow = 1;

  // 展示虚线
  #stripShowWall = {};

  // 隐藏虚线
  #stripHiddenWall = {};

  constructor(data, size, modelMap, matMap, componentManager, w3d) {
    super();
    this.#data = data;
    this.#size = size;
    this.#modelMap = modelMap;
    this.#matMap = matMap;
    this.#componentManager = componentManager;
    this.#w3d = w3d;
    this.#animation = new A3D.Animation(this.#w3d.agent);

    const {width, depth} = this.#size;
    const offsetY = depth * 0.15 * 2;
    this.#sizeSetting.groundHeight = width * 0.008;
    this.#sizeSetting.thirdPartyWidth = width;
    this.#sizeSetting.thirdPartyDepth = depth;
    this.#sizeSetting.channelWidth = width;
    this.#sizeSetting.channelDepth = depth * 0.7;
    this.#sizeSetting.stripWidth = width * 1.1;
    this.#sizeSetting.mangerWidth = width * 0.15;
    this.#sizeSetting.stripDepth = depth * 1.28;
    this.#sizeSetting.nameSize1 = width * 0.03;
    this.#sizeSetting.space1 = width * 0.015;
    this.#sizeSetting.nameSize2 = width * 0.03;
    this.#sizeSetting.space2 = width * 0.01;
    this.#sizeSetting.space3 = width * 0.02;
    this.#sizeSetting.thirdPartyPosY = offsetY;
    this.#sizeSetting.thirdPartyPosZ = -(this.#sizeSetting.thirdPartyDepth + this.#sizeSetting.channelDepth) / 2 + 2;

    this.#sizeSetting.channelPosY = 0;
    if (data.strip.strips.length > 10) {
      this.#stripsRow = 2;
    }
    this.#sizeSetting.channelPosZ = this.#stripsRow === 2 ? -5 : -3;

    this.#sizeSetting.stripPosY = -offsetY;
    this.#sizeSetting.stripPosZ = (this.#sizeSetting.stripDepth + this.#sizeSetting.channelDepth) / 2 - 6;

    this.#meshMaxSize = width * 0.04;

    this.#sizeSetting.nameSize = depth * 0.07;
    this.#sizeSetting.space = depth * 0.025;
    this.#sizeSetting.spriteNameSize = width * 0.012;
    this.#sizeSetting.spriteNameSize2 = 0.012;

    this.#init();

    this.#lineThird2ChannelIns = this.lineThird2Channel(this.#data.lineThird2Channel);
    this.#lineChannel2StripIns = this.lineChannel2StripIns(this.#data.lineChannel2StripIns);
  }

  lineChannel2StripIns(data) {
    // 添加分组 再创建
    const lineGroup = this.children[1];

    let lineChannel2StripGroup = new A3D.C.Group();
    lineChannel2StripGroup.name = 'lineChannel2StripGroup';
    lineGroup.add(lineChannel2StripGroup);

    // 标准化data

    // 获取渠道的中间坐标
    let channelPos = new A3D.C.Vector3();
    this.children[0].children[1].getWorldPosition(channelPos);
    channelPos.z += this.#sizeSetting.channelDepth / 2;
    channelPos.y -= this.#sizeSetting.groundHeight * 0.8;
    this.#channelLinePos = channelPos;

    // 获取每个条带的坐标
    const stripPositions = {};
    const allStrip = [];
    this.traverse((object) => {
      if (object.name === 'stripGround') {
        allStrip.push(object);
      }
    });

    const firstRowLength = this.#stripLayoutData?.level1?.[0]?.group?.length || 0;
    allStrip.forEach((group, index) => {
      let groupPos = new A3D.C.Vector3();
      group.getWorldPosition(groupPos);
      groupPos.z -= this.#stripLayoutData.size.depth / 2 / this.#stripLayoutData.level1.length - this.#sizeSetting.space2 / 2;
      groupPos.y = groupPos.y + ((firstRowLength > index) ? 1.1 : 0); // 第一行加
      stripPositions[this.#data.strip.strips[index].id] = groupPos;
    });
    this.#stripPositions = stripPositions;

    // 根据坐标 画线
    let line = new Line({
      linewidth: 1,
      onChangeCallback: (_data, lineMesh, flowMesh) => {
        updateLineChannel2Strip(_data, lineMesh, flowMesh, this.#w3d);
      },
      onShowCallback: (_line) => {
        _line.children[1]?.traverse((item) => {
          item.visible = true;
        });
      },
      getPoints: {
        1: (start, end) => getBezier4(start, end),
        2: (start, end) => getBezier5(start, end),
        3: (start, end) => getBezier6(start, end),
      },
    });
    // 在线上画点和tps
    createLineChannel2Strip(
      channelPos,
      stripPositions,
      data,
      line,
      this.#w3d,
      this.#stripLayoutData,
      this.#componentManager.store.data.mmOverview?.strip?.strips || [],
    );
    lineChannel2StripGroup.add(line);
    // 定义事件函数
    bindStripLineEvents(line, this.#componentManager, this.#w3d);
    return line;
  }

  updateLineChannel2Strip(data) {
    const lineGroup = this.children[1];
    let lineChannel2StripGroup = lineGroup.getObjectByName('lineChannel2StripGroup');
    if (!lineChannel2StripGroup || !data || !this.#lineChannel2StripIns) {
      return;
    }

    // 遍历当前有哪些线
    const showIds = lineChannel2StripGroup.children[0].children.map((v) => v.name);
    // 现在要画哪些线
    const dataIds = data.map((v) => v.to);

    // 判断哪些线要新增
    const addIds = dataIds.filter((v) => !showIds.includes(String(v)));
    createLineChannel2Strip(
      this.#channelLinePos,
      this.#stripPositions,
      data.filter((v) => addIds.includes(v.to)),
      this.#lineChannel2StripIns,
      this.#w3d,
      this.#stripLayoutData,
      this.#componentManager.store.data.mmOverview?.strip?.strips || [],
    );

    // 更新卡片信息
    data.forEach((v) => {
      const oneLine = this.#lineChannel2StripIns.getObjectByName(String(v.to));
      if (oneLine) {
        this.#lineChannel2StripIns.set(oneLine, v);
      }
    });
  }

  lineThird2Channel(data) {
    // 添加分组 再创建
    const lineGroup = this.children[1];

    let lineThird2ChannelGroup = new A3D.C.Group();
    lineThird2ChannelGroup.name = 'lineThird2ChannelGroup';
    lineGroup.add(lineThird2ChannelGroup);

    // data已经是处理好的数据了

    // 获取第三方的中间坐标
    let thirdPos = new A3D.C.Vector3();
    this.children[0].children[0].getWorldPosition(thirdPos);
    thirdPos.z += this.#sizeSetting.thirdPartyDepth / 2;
    thirdPos.y -= this.#sizeSetting.groundHeight * 0.8;
    this.#thirdLinePos = thirdPos;

    // 获取每个渠道的坐标
    const channelPositions = {};
    this.children[0].children[1].children[1].children
      .filter((v) => v.name === 'channelGround')
      .forEach((group, index) => {
        let groupPos = new A3D.C.Vector3();
        group.getWorldPosition(groupPos);
        groupPos.z -= 4;
        groupPos.y += this.#sizeSetting.groundHeight;
        channelPositions[this.#data.channel[index].id] = groupPos;
      });
    this.#channelPositions = channelPositions;

    // 根据坐标 画线
    let line = new Line({
      linewidth: 1,
      onChangeCallback: (_data, lineMesh, flowMesh) => {
        updateLineThird2Channel(_data, lineMesh, flowMesh, this.#w3d);
      },
      onShowCallback: (_line) => {
        _line.children[1]?.traverse((item) => {
          item.visible = true;
        });
      },
      getPoints: {
        1: (start, end) => getBezier3(start, end, this.#stripsRow === 2),
      },
    });
    // 在线上画点和tps
    createLineThird2Channel(
      thirdPos,
      channelPositions,
      data,
      line,
      this.#w3d,
      this.#componentManager.store.data.mmOverview?.channel || [],
    );
    lineThird2ChannelGroup.add(line);
    // 定义事件函数
    bindChannelLineEvents(line, this.#componentManager, this.#w3d);
    line.renderOrder = 1;
    return line;
  }

  updateLineThird2Channel(data) {
    const lineGroup = this.children[1];
    let lineThird2ChannelGroup = lineGroup.getObjectByName('lineThird2ChannelGroup');
    if (!lineThird2ChannelGroup || !data || !this.#lineThird2ChannelIns) {
      return;
    }

    // 遍历当前有哪些线
    const showIds = lineThird2ChannelGroup.children[0].children.map((v) => v.name);
    // 现在要画哪些线
    const dataIds = data.map((v) => v.to);

    // 判断哪些线要隐藏
    const hiddenIds = showIds.filter((v) => !dataIds.includes(parseInt(v)));
    hiddenIds.forEach((v) => {
      this.#lineThird2ChannelIns.destoryOneLine(v);
    });

    // 判断哪些线要新增
    const addIds = dataIds.filter((v) => !showIds.includes(String(v)));
    createLineThird2Channel(
      this.#thirdLinePos,
      this.#channelPositions,
      data.filter((v) => addIds.includes(v.to)),
      this.#lineThird2ChannelIns,
      this.#w3d,
      this.#componentManager.store.data.mmOverview?.channel || [],
    );
    // 更新卡片信息
    data.forEach((v) => {
      const oneLine = this.#lineThird2ChannelIns.getObjectByName(String(v.to));
      if (oneLine) {
        this.#lineThird2ChannelIns.set(oneLine, v);
      }
    });
  }

  hoverIn(object3D, appCallback, podCallback, vmCallback, stripBlockCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'channel');
    const stripItem = getTypeItem(object3D, 'strip');
    const stripNameItem = getTypeItem(object3D, 'stripBlockName'); 
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#thirdPartyMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, isSelected);
      }
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedId === podId;
      const data = this.#channelMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, isSelected);
      }
    }
    if (stripItem) {
      const vmId = stripItem.userData.id;
      const isSelected = this.#selectedId === vmId;
      const data = this.#stripMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, stripItem, isSelected);
      }
    }
    if (stripNameItem) {
      if (stripBlockCallback) {
        stripBlockCallback(stripNameItem.userData.id, stripNameItem);
      }
    }
  }

  hoverOut(object3D, appCallback, podCallback, vmCallback, stripBlockCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const podItem = getTypeItem(object3D, 'channel');
    const stripItem = getTypeItem(object3D, 'strip');
    const stripNameItem = getTypeItem(object3D, 'stripBlockName');
    if (appItem) {
      const appId = appItem.userData.id;
      const isSelected = this.#selectedAppId === appId;
      const data = this.#thirdPartyMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, isSelected);
      }
    }
    if (podItem) {
      const podId = podItem.userData.id;
      const isSelected = this.#selectedId === podId;
      const data = this.#channelMap[podId].data;
      if (podCallback) {
        podCallback(data, podItem, isSelected);
      }
    }
    if (stripItem) {
      const vmId = stripItem.userData.id;
      const isSelected = this.#selectedId === vmId;
      const data = this.#stripMap[vmId].data;
      if (vmCallback) {
        vmCallback(data, stripItem, isSelected);
      }
    }
    if (stripNameItem) {
      if (stripBlockCallback) {
        stripBlockCallback(stripNameItem.userData.id, stripNameItem);
      }
    }
  }

  select(object3D, appCallback, podCallback, vmCallback, otherCallback) {
    const appItem = getTypeItem(object3D, 'app');
    const channelItem = getTypeItem(object3D, 'channel');
    const stripItem = this.isStripGroup(object3D);

    this.hiddenWall();
    if (appItem) {
      const appId = appItem.userData.id;
      this.#selectApp(appId);
      this.#selectModels('', []);
      const data = this.#thirdPartyMap[appId].data;
      if (appCallback) {
        appCallback(data, appItem, true);
      }
    } else if (channelItem) {
      const podId = channelItem.userData.id;
      this.#selectApp('');
      this.#selectModels(podId, []);
      const data = this.#channelMap[podId].data;
      if (podCallback) {
        podCallback(data, channelItem, this.#isGray);
      }
    } else if (stripItem) {
      this.#selectApp('');
      this.#selectModels('', []);
      const id = stripItem.userData.data.id;
      this.showWall(id);
      const data = stripItem.userData.data;
      if (vmCallback) {
        vmCallback(data, stripItem, false);
      }
    } else {
      this.#selectApp('');
      this.#selectModels('', []);
      if (otherCallback) {
        otherCallback(this.#isGray);
      }
    }
  }

  update(data) {
    this.#clear();
    this.#data = data;
    this.#init();
  }

  updateStatus(data) {
    // 更新第三方数据
    data.thirdParty.forEach((third) => {
      this.#thirdPartyMap[third.id].data = third;
      this.#setAppStatus(third.id);
      this.#updateWarn(this.#thirdPartyMap[third.id]);
    });
    // 更新渠道数据
    data.channel.forEach((channel) => {
      this.#channelMap[channel.id].data = channel;
      this.#setChannelStatus(channel.id);
      this.#updateWarn(this.#channelMap[channel.id]);
    });

    // 更新条带数据
    data.strip.strips.forEach((strip) => {
      strip.group.forEach((node) => {
        if (this.#stripMap[node.id]) {
          this.#stripMap[node.id].data = node;
          setDbStatus(this.#stripMap[node.id], this.#matMap, this.#modelMap);
        }
      });
    });
  }

  #updateWarn(third) {
    let data = third.data;
    let appItem = third.mesh;
    let alarmCount = 0;
    if (data.alarmCsn && data.alarmCsn.length > 0) {
      const arr = data.alarmCsn.split(',').filter(item => item !== '');
      alarmCount = arr.length;
    }
    const warn = appItem.children[2];
    if (alarmCount <= 0) {
      if (warn) {
        warn.geometry.dispose();
        warn.material.dispose();
        appItem.remove(warn);
      }
    } else {
      if (warn) {
        this.#updateWarnCanvas(warn, alarmCount);
      } else {
        this.#initWarn(appItem, data.alarmCsn);
      }
    }
  }

  #updateWarnCanvas(warn, alarmCount) {
    const sprite = warn;
    const alarmSize = sprite.scale.y;
    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    sprite.material.map = A3D.canvasToTex(canvas, true);

    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
  }

  getDefaultSelected() {
    return null;
  }

  getClickSelected() {
    const selectedId = this.#selectedId;
    const pod = this.#channelMap[selectedId]?.mesh?.children[0];
    const vm = this.#stripMap[selectedId]?.mesh?.children[0];
    return pod || vm || null;
  }

  getIsGray() {
    return this.#isGray;
  }

  updateSelected(data) {
    this.#selectModels(data.id, data.relation);
  }

  getCardMap() {
    return this.#cardMap;
  }

  #clear() {
    this.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material.dispose();
      }
    });
    this.remove(...this.children);
  }

  #updateStatusData(data) {
    this.#data = data;

    this.#initData();
    if (!this.#thirdPartyMap[this.#selectedAppId]) {
      this.#selectedAppId = '';
    }
    if (!(this.#channelMap[this.#selectedId] || this.#stripMap[this.#selectedId])) {
      this.#selectedId = '';
    }
  }

  #selectApp(appId) {
    this.#selectedAppId = appId;
    Object.keys(this.#thirdPartyMap).forEach((id) => {
      const mesh = this.#thirdPartyMap[id].mesh;
      if (mesh) {
        this.#setAppStatus(id);
      }
    });
  }

  #selectModels(selectId, relationId = []) {
    this.#selectedId = selectId;
    this.#relationId = relationId;
    Object.keys(this.#channelMap).forEach((id) => {
      const mesh = this.#channelMap[id].mesh;
      if (mesh) {
        if (selectId === parseInt(id) || relationId.includes(id)) {
          mesh.userData.isSelected = true;
        } else {
          mesh.userData.isSelected = false;
        }
        this.#setChannelStatus(id);
      }
    });
  }

  /** 初始化布局-start */
  #init() {
    this.#initData();
    this.#selectedAppId = '';
    this.#selectedId = '';
    this.#initContainer();

    this.#initThirdParty();
    this.#initChannel();
    this.#initStrip();
  }

  #initData() {
    const oldThirdPartyMap = this.#thirdPartyMap;
    const oldChannelMap = this.#channelMap;

    this.#thirdPartyMap = {};
    this.#channelMap = {};
    this.#stripMap = {};

    this.#data.thirdParty.forEach((v) => {
      this.#thirdPartyMap[v.id] = {data: v};
      if (oldThirdPartyMap[v.id]) {
        this.#thirdPartyMap[v.id].mesh = oldThirdPartyMap[v.id].mesh;
      }
    });
    this.#data.channel.forEach((v) => {
      this.#channelMap[v.id] = {data: v};
      if (oldChannelMap[v.id]) {
        this.#channelMap[v.id].mesh = oldChannelMap[v.id].mesh;
      }
    });
    this.#data.strip.strips.forEach((strip) => {
      strip.group.forEach((node) => {
        this.#stripMap[node.id] = {data: node};
      });
    });
  }

  #initContainer() {
    const container = new A3D.C.Group();
    container.name = 'container';
    container.position.z = this.#size.offset;
    const lineGroup = new A3D.C.Group();
    lineGroup.name = 'lineGroup';
    this.add(container, lineGroup);
  }

  #initThirdParty() {
    const thirdPartyGroup = new A3D.C.Group();
    thirdPartyGroup.name = 'thirdPartyGroup';
    this.children[0].add(thirdPartyGroup);

    const {
      thirdPartyWidth, thirdPartyDepth, thirdPartyPosY, thirdPartyPosZ,
    } = this.#sizeSetting;
    thirdPartyGroup.position.set(0, thirdPartyPosY, thirdPartyPosZ);
    this.#initGround(thirdPartyGroup, thirdPartyWidth, thirdPartyDepth);
    this.#initAppList(thirdPartyGroup);

    this.#initGroundName(thirdPartyGroup, thirdPartyWidth, 'thirdParty');
    this.#cardMap.thirdParty.position.x -= this.#size.width * 0.12;
    thirdPartyGroup.rotateX(0.05);
  }

  #initChannel() {
    const channelGroup = new A3D.C.Group();
    channelGroup.name = 'channelGroup';
    this.children[0].add(channelGroup);

    const {
      channelWidth, channelDepth, channelPosY, channelPosZ,
    } = this.#sizeSetting;
    channelGroup.position.set(0, channelPosY, channelPosZ);

    this.#initGround(channelGroup, channelWidth, channelDepth);
    this.#initChannelList(channelGroup);

    this.#initGroundName(channelGroup, channelWidth, 'channel');
  }

  #initStrip() {
    const stripGroup = new A3D.C.Group();
    stripGroup.name = 'stripGroup';
    this.children[0].add(stripGroup);
    const fixData = this.fixData();
    const {
      stripWidth, stripDepth, stripPosY, stripPosZ,
    } = this.#sizeSetting;
    stripGroup.position.set(0, stripPosY, stripPosZ);
    this.#initGround(stripGroup, stripWidth, stripDepth);
    this.#initStripList(stripGroup, fixData);

    this.#initGroundName(stripGroup, stripWidth, 'strip');
  }

  /**
   * 创建站点地板
   * @returns
   */
  #initGround(parent, width, depth) {
    const {groundHeight} = this.#sizeSetting;
    const options = getGroundOptions(width, depth);
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = this.#matMap.ground;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'thirdPartyGround';
    ground.position.y -= groundHeight;
    parent.add(ground);
  }

  #initGroundName(parent, width, type) {
    const detailGroup = new A3D.C.Group();
    detailGroup.position.x = -(width * 0.5 + this.#size.width * 0.06);
    parent.add(detailGroup);

    this.#cardMap[type] = detailGroup;
  }

  /** 创建app内容 */
  #initAppList(appGroup) {
    const data = this.#data.thirdParty;
    const {thirdPartyWidth, thirdPartyDepth} = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, thirdPartyWidth, thirdPartyDepth);

    const {perWidth, perDepth} = layoutData;
    let appScaleRatio = this.#appScaleRatio;
    if (layoutData.count > 20) {
      appScaleRatio = appScaleRatio * 0.5;
    }
    this.#appDefaultScale = Math.min(Math.min(perDepth, perWidth) * appScaleRatio, this.#meshMaxSize);

    const appListGroup = this.#drawAppList(layoutData, data);
    appGroup.add(appListGroup);
  }

  #drawAppList(layoutData, data) {
    const appListGroup = new A3D.C.Group();
    appListGroup.name = 'appListGroup';

    const posArr = this.#getLayoutItemPosition(layoutData);

    const isShowName = data.length <= showNameCount;
    data.forEach((itemData, index) => {
      this.#initAppItem(itemData, posArr[index], appListGroup, isShowName);
    });
    return appListGroup;
  }

  #getWallLine(material, options) {
    const wall = new Wall(material, options);
    wall.add({
      height: 0.002,
      width: 0.1,
      ...options,
    });
    return wall;
  }

  #initStripGroundLine({
    width,
    depth,
  }, wrapper) {
    const groundOptions = getGroundOptions(width, depth);
    groundOptions.close = true;

    const material = this.#w3d.themeManager.referStyle('ground-line');
    const groundLine = this.#getWallLine(material, groundOptions);
    groundLine.position.x = 0;
    groundLine.children[0].renderOrder = 1;
    groundLine.visible = false;
    wrapper.add(groundLine);
    return groundLine;
  }

  #initChannelItem(itemData, position, container, isShowName) {
    this.#initChannelGround({
      width: 4.4,
      depth: 8,
      position,
    }, container);

    let channelItem;
    if (this.#channelMap[itemData.id].mesh) {
      channelItem = this.#channelMap[itemData.id].mesh;
    } else {
      channelItem = new A3D.C.Group();
      const channelGeo = this.#modelMap.channel.geometry;
      const channelMat = this.#matMap.channel.normal;
      const channelMesh = new A3D.C.Mesh(channelGeo, channelMat);
      channelItem.add(channelMesh);
    }
    channelItem.children[0].scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
    channelItem.userData.isShowName = isShowName;
    channelItem.userData.isSelected = itemData.id === this.#selectedAppId;
    const nameMesh = channelItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getSprite(itemData, channelItem.children[0]);
      channelItem.add(sprite);
    }
    channelItem.position.copy(position);
    container.add(channelItem);
    this.#clickable(channelItem, itemData.id, 'channel');
    this.#setChannelStatus(itemData.id);
    this.#initWarn(channelItem, itemData.alarmCsn);
  }

  /**
   * 创建渠道的地板
   * @param {*} width
   * @param {*} depth
   * @param {*} position
   * @returns
   */
  #initChannelGround({width, depth, position}, wrapper) {
    const options = getGroundOptions(width, depth);
    const {groundHeight} = this.#sizeSetting;
    options.depth = groundHeight * 0.99;
    options.radius = 0.4;
    const groundMat = this.#matMap.businessGround;
    const ground = new ExtrudeShape(options, groundMat);
    ground.name = 'channelGround';
    ground.position.x = position.x;
    wrapper.add(ground);
  }

  /**
   * 创建泳道的地板
   * @param {*} width
   * @param {*} depth
   * @param {*} position
   * @returns
   */
  #initStripGround({width, depth}, wrapper) {
    const options = getGroundOptions(width, depth);
    options.radius = 0.4;
    const groundMat = this.#matMap.businessGround;
    const ground = new ExtrudeShape(options, groundMat);
    ground.children[0].renderOrder = 1;
    ground.name = 'stripGround';
    wrapper.add(ground);
    return ground;
  }

  #initAppItem(itemData, position, container, isShowName) {
    let appItem;
    if (this.#thirdPartyMap[itemData.id].mesh) {
      appItem = this.#thirdPartyMap[itemData.id].mesh;
    } else {
      appItem = new A3D.C.Group();
      const appGeo = this.#modelMap.thirdParty.geometry;
      let appMat = this.#matMap.thirdParty.normal;
      const appMesh = new A3D.C.Mesh(appGeo, appMat);
      appItem.add(appMesh);
    }
    appItem.children[0].scale.set(this.#appDefaultScale, this.#appDefaultScale, this.#appDefaultScale);
    appItem.userData.isShowName = isShowName;
    appItem.userData.isSelected = itemData.id === this.#selectedAppId;
    const nameMesh = appItem.getObjectByProperty('type', 'Sprite');
    if (nameMesh) {
      nameMesh.parent.remove(nameMesh);
    }
    if (isShowName) {
      const sprite = this.#getSprite(itemData, appItem.children[0]);
      appItem.add(sprite);
    }
    appItem.position.copy(position);
    this.#initWarn(appItem, itemData.alarmCsn);
    container.add(appItem);

    this.#clickable(appItem, itemData.id, 'app');
  }

  #getSprite(itemData, mesh) {
    const cloneMesh = mesh.clone();
    cloneMesh.remove(...cloneMesh.children);
    const modelSize = getObjectSize(cloneMesh);
    const textArr = [itemData.name];
    const material = new A3D.C.SpriteMaterial({
      map: this.#getCanvasTex(textArr),
      color: '#BBBBBB',
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    const {spriteNameSize} = this.#sizeSetting;
    const nameSize = spriteNameSize * 2;
    sprite.position.z = modelSize.depth * 1.2 + nameSize * 1.2;
    sprite.position.y = nameSize * 0.4;

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.renderOrder = 2;
    return sprite;
  }

  #setAppStatus(id) {
    const appSetting = this.#thirdPartyMap[id];
    const isSelected = this.#selectedAppId === parseInt(id);
    const mesh = appSetting.mesh.children[0];
    let hasAlarm = false;
    if (appSetting.data.alarmCsn && appSetting.data.alarmCsn.length > 0) {
      hasAlarm = true;
    }
    if (this.#selectedAppId) {
      if (isSelected) {
        mesh.material = hasAlarm ? this.#matMap.thirdParty.alarm : this.#matMap.thirdParty.normal;
      } else {
        mesh.material = hasAlarm ? this.#matMap.thirdParty.alarmUnselect : this.#matMap.thirdParty.normalUnselect;
      }
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      mesh.material = hasAlarm ? this.#matMap.thirdParty.alarm : this.#matMap.thirdParty.normal;
      const nameMesh = appSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (appSetting.mesh.userData.isShowName && nameMesh) {
        nameMesh.material.color.set(nameColorNormal);
      }
    }
    this.#setBlockStatus(id, isSelected);
  }

  #setBlockStatus(id, isSelected) {
    const block = this.#thirdPartyMap[id].blockMesh;
    if (!block) {
      return;
    }
    if (isSelected) {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = true;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGroundSelect;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLineSelect;
      }
    } else {
      block.children[0].children.forEach((podItem) => {
        podItem.userData.isBlockSelected = false;
        podItem.userData.isSelected = false;
      });
      if (block.children[1]) {
        block.children[1].children[0].material = this.#matMap.blockGround;
      }
      if (block.children[2]) {
        block.children[2].children[0].material = this.#matMap.blockGroundLine;
      }
    }
  }

  /** 创建vm内容 */
  #initStripList(stripGroup, fixData) {
    const layoutData = this.#getStripLayoutData(fixData);
    this.#stripLayoutData = layoutData;
    this.#initStripGroup(layoutData, stripGroup);
  }

  fixData() {
    const data = this.#data.strip.strips;
    let count = 0;
    const children = data.map((strip) => {
      const curCount = strip.group.length;
      count += curCount;
      return {
        count: curCount,
        data: strip,
      };
    });
    const fixedData = {
      count,
      children,
    };
    return fixedData;
  }

  #initStripGroup(layoutData, parent) {
    let {nameSize2, space3} = this.#sizeSetting;
    const {podDepth, podWidth, position} = layoutData;
    const fixData = this.fixData();
    if (fixData.children.length <= 10) {
      this.#podScaleRatio = 0.5;
    } else {
      nameSize2 = nameSize2 * 0.5;
      this.#podScaleRatio = 0.33;
    }
    this.#podDefaultScale =
      Math.max(Math.min(podDepth, podWidth, this.#podMaxSpace), this.#podMinSpace) * this.#podScaleRatio;
    const businessContainer = this.#drawStripGroup(layoutData, nameSize2, space3);
    businessContainer.position.copy(position);
    parent.add(businessContainer);
  }

  #drawStripGroup(layoutData, nameSize, space) {
    const businessContainer = new A3D.C.Group();
    layoutData.level1.forEach((level1Group) => {
      const businessLayer = new A3D.C.Group();
      businessLayer.position.copy(level1Group.position);
      businessContainer.add(businessLayer);
      level1Group.group.forEach((business) => {
        const strip3D = this.#getStripGroup(business, nameSize, space);
        strip3D.position.copy(business.position);
        strip3D.position.x += business.size.width / 2;
        strip3D.position.z += business.size.depth / 2;
        businessLayer.add(strip3D);
      });
    });
    return businessContainer;
  }

  #getStripGroup(data, nameSize, space) {
    const wrapper = new A3D.C.Group();
    wrapper.name = 'stripGroundGroup';
    // 添加数据，方便点击的时候获取
    wrapper.userData.data = data.data;

    const ground = this.#initStripGround(data.size, wrapper);
    const wall = this.#initStripGroundLine(data.size, wrapper);
    // 添加回调事件，方便直接调用
    this.#stripShowWall[data.data.id] = () => {
      wall.visible = true;
      ground.children[0].material = this.#matMap.businessGroundSelect;
    };
    this.#stripHiddenWall[data.data.id] = () => {
      wall.visible = false;
      ground.children[0].material = this.#matMap.businessGround;
    };
    this.#initStripBlockName(data, nameSize, space, wrapper);
    this.#initGroup(data, nameSize, space, wrapper);
    return wrapper;
  }

  isStripGroup(object3D) {
    let node3D = null;
    if (object3D instanceof A3D.C.Object3D) {
      node3D = object3D;
      while (node3D && node3D.name !== 'stripGroundGroup') {
        node3D = node3D.parent;
      }
    }
    return node3D;
  }

  showWall(id) {
    this.#stripShowWall[id]?.();
  }

  hiddenWall(id) {
    if (!id) {
      Object.values(this.#stripHiddenWall).forEach((value) => {
        value();
      });
    }

    this.#stripHiddenWall[id]?.();
  }

  #initGroup(data, nameSize, space, wrapper) {
    const offset = {x: 0, y: 0, z: (nameSize + space) / 2};
    const podGroup = new A3D.C.Group();
    podGroup.position.copy(offset);
    wrapper.add(podGroup);

    const posArr = this.#getGroupPosition(data, space);
    let posIndex = 0;

    data.data.group.forEach((moTypeInfo) => {
      const dbGroup = this.#getGroupItem(moTypeInfo);
      dbGroup.position.copy(posArr[posIndex]);
      posIndex++;
      dbGroup.scale.set(this.#podDefaultScale, this.#podDefaultScale, this.#podDefaultScale);
      podGroup.add(dbGroup);
    });
  }

  #getGroupPosition(data, space) {
    const col = data.level2[0] || 1;
    const row = data.level2.length || 1;
    const perWidth = (data.size.width - space * 2) / col;
    const perDepth = (data.size.depth - space * 2) / row;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: -data.size.width / 2 + space + (j + 0.5) * perWidth,
          y: 0,
          z: -data.size.depth / 2 + space + (i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getGroupItem(moTypeInfo) {
    const dbGroup = new A3D.C.Group();
    let geometry;
    let material;
    geometry = this.#modelMap.strip.geometry;
    material = this.#matMap.strip.group.normal;
    const mesh = new A3D.C.Mesh(geometry, material);
    mesh.renderOrder = 2;
    dbGroup.add(mesh);
    this.#initGroupName(moTypeInfo, dbGroup);
    dbGroup.userData._type = 'strip';
    this.#clickable(dbGroup, moTypeInfo.id, 'strip');
    setDbStatus(this.#stripMap[moTypeInfo.id], this.#matMap, this.#modelMap);
    return dbGroup;
  }

  #initGroupName(moTypeInfo, parent) {
    const modelSize = getObjectSize(this.#modelMap.strip);
    const data = this.fixData();
    const name = moTypeInfo.name;
    const texture = this.#getCanvasTex(name, 6);
    let material = new A3D.C.SpriteMaterial({
      map: texture,
      color: '#F5F5F5',
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    sprite.material.color.set(nameColorNormal);
    const nameSize = modelSize.depth * 0.8;
    sprite.position.z = modelSize.depth * 0.9 + nameSize * (data.children.length > 10 ? 0.4 : 0.6);
    sprite.position.y = nameSize * 0.6;

    const canvas = material.map.image;
    sprite.scale.set(canvas.width / canvas.height * nameSize, nameSize);
    sprite.renderOrder = 2;

    parent.add(sprite);
  }

  #initWarn(data, alarmCsn) {
    if (!alarmCsn) {
      return;
    }
    const arr = alarmCsn.split(',').filter(item => item !== '');
    const alarmCount = arr.length;
    if (alarmCount <= 0) {
      return;
    }
    const modelSize = getObjectSize(this.#modelMap.thirdParty);
    const alarmSize = modelSize.depth * 0.9;

    const canvas = this.#initCanvas(alarmCount);
    const texture = A3D.canvasToTex(canvas, true);
    const material = new A3D.C.SpriteMaterial({
      map: texture,
      color: '#FFFFFF',
      depthWrite: false,
    });
    const sprite = new A3D.C.Sprite(material);
    sprite.renderOrder = 5;
    sprite.scale.set(canvas.width / canvas.height * alarmSize, alarmSize);
    sprite.center.x = 0;
    sprite.position.y = modelSize.height * 2;
    sprite.position.x = modelSize.width * 0.8 - 0.1;
    sprite.name = 'alarmTag';
    data.add(sprite);
  }

  #initCanvas(alarmCount) {
    const content = String(alarmCount > 99 ? '99+' : alarmCount);
    const canvas = document.createElement('canvas');
    this.#drawCanvas(canvas, content);
    return canvas;
  }

  #drawCanvas(canvas, content) {
    const pixelRatio = 4;
    const lineHeight = 24 * pixelRatio;
    const fontSize = 18 * pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    const size = ctx.measureText(content.slice(1));

    canvas.width = size.width + lineHeight + 2;
    canvas.height = lineHeight + 2;

    drawRoundedRect(ctx, {x: 1, y: 1}, canvas.width - 2, lineHeight, lineHeight * 0.5);
    // 填充路径
    ctx.fillStyle = '#F43146';
    ctx.fill();

    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.font = `bold ${fontSize}px "Arial", "Microsoft YaHei"`;
    ctx.fillStyle = '#ffffff';

    const HALF = 2;
    const FONT_SPACE = 0.1;
    const top = (canvas.height - fontSize) / HALF + fontSize * FONT_SPACE;
    ctx.fillText(content, canvas.width / HALF, top);
  }

  #initStripBlockName(data, nameSize, space, wrapper) {
    const {width, depth} = data.size;
    const name = data.data.name;
    const nameCount = width;
    const plane = getTextPlane({
      x: nameSize * nameCount,
      y: nameSize,
    }, name, {
      width: 20 * nameCount * 1.5,
      height: 20,
      'font-weight': 'bold',
      maxWidth: data.size.width * 20,
    });
    plane.material.color.set('#cccccc');
    const x = -(width - nameSize * nameCount) * 0.5 + space;
    const z = -(depth - nameSize) * 0.5 + space;
    plane.position.set(x, 0.2, z);
    plane.renderOrder = 2;
    plane.name = 'stripBlockName';
    plane.userData._type = 'stripBlockName';
    plane.userData.id = data.data.id;
    wrapper.add(plane);
  }

  #getStripLayoutData(fixedData) {
    const layout = Layout.initLayout(fixedData);
    const {
      nameSize1, space1, nameSize2, space2,
    } = this.#sizeSetting;
    const {stripWidth, stripDepth} = this.#sizeSetting;
    const businessSize = {
      width: stripWidth - space1 * 2,
      depth: stripDepth - nameSize1 - space1 * 3,
    };
    const position = {x: 0, y: 0, z: (nameSize1 + space1) / 2};
    StripLayout.initLayout(layout, businessSize, nameSize2, space2, position);
    return layout;
  }

  /** 创建pod内容 */
  #initChannelList(channelGroup) {
    const data = this.#data.channel;
    const {thirdPartyWidth, thirdPartyDepth} = this.#sizeSetting;
    const layoutData = this.#getLayoutData(data, thirdPartyWidth, thirdPartyDepth);

    const {perWidth, perDepth} = layoutData;
    this.#podDefaultScale = Math.min(Math.min(perDepth, perWidth) * this.#podScaleRatio, this.#meshMaxSize);

    const podListGroup = this.#drawPodList(layoutData, data);
    channelGroup.add(podListGroup);
  }

  #drawPodList(layoutData, data) {
    const channelListGroup = new A3D.C.Group();
    channelListGroup.name = 'channelListGroup';
    const posArr = this.#getLayoutItemPosition(layoutData);

    const isShowName = data.length <= showNameCount;
    data.forEach((itemData, index) => {
      this.#initChannelItem(itemData, posArr[index], channelListGroup, isShowName);
    });
    return channelListGroup;
  }

  #setChannelStatus(id) {
    const podSetting = this.#channelMap[id];
    const isSelected = podSetting.mesh.userData.isSelected;
    const mesh = podSetting.mesh.children[0];
    let hasAlarm = false;
    if (podSetting.data.alarmCsn && podSetting.data.alarmCsn.length > 0) {
      hasAlarm = true;
    }
    if (this.#selectedId) {
      if (isSelected) {
        mesh.material = hasAlarm ? this.#matMap.channel.alarm : this.#matMap.channel.normal;
      } else {
        mesh.material = hasAlarm ? this.#matMap.channel.alarmUnselect : this.#matMap.channel.normalUnselect;
      }
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = isSelected ? nameColorSelect : nameColorUnselect;
        nameMesh.material.color.set(nameColor);
      }
    } else {
      mesh.material = hasAlarm ? this.#matMap.channel.alarm : this.#matMap.channel.normal;
      const nameMesh = podSetting.mesh.getObjectByProperty('type', 'Sprite');
      if (podSetting.mesh.userData.isShowName && nameMesh) {
        const nameColor = this.#isGray ? nameColorNormal : this.#getNoGrayPodNameColor(podSetting);
        nameMesh.material.color.set(nameColor);
      }
    }
  }

  #getNoGrayPodNameColor(podSetting) {
    const selectedColor = podSetting.mesh.userData.isBlockSelected ? nameColorSelect : nameColorUnselect;
    const noGrayNameColor = this.#selectedAppId ? selectedColor : nameColorNormal;
    return noGrayNameColor;
  }

  /** 创建内容通用方法 */
  #getLayoutData(data, width, depth) {
    const appCount = data.length;
    const {space} = this.#sizeSetting;
    const layout = {
      perWidth: 0, perDepth: depth, count: appCount, level1: [],
    };
    let col = 0;
    let row = 1;
    if (appCount <= this.#rowRatio) {
      col = appCount;
      layout.perWidth = (width - space * 2) / col;
    } else if (appCount <= this.#rowRatio * 2) {
      row = 2;
      col = Math.ceil(appCount / row);
      layout.perWidth = (width - space * 2) / col;
      layout.perDepth = (depth - space * 2) / row;
    } else {
      col = this.#rowRatio;
      row = Math.ceil(appCount / col);
      layout.perWidth = (width - space * 2) / col;
      layout.perDepth = (depth - space * 2) / row;
    }
    let stayCount = appCount;
    while (stayCount > 0) {
      layout.level1.push(Math.min(stayCount, col));
      stayCount -= col;
    }
    return layout;
  }

  #getLayoutItemPosition(layoutData) {
    const {perWidth, perDepth} = layoutData;
    const col = layoutData.level1[0];
    const row = layoutData.level1.length;
    const posArr = [];
    for (let i = 0; i < row; i++) {
      for (let j = 0; j < col; j++) {
        posArr.push({
          x: (-col / 2 + j + 0.5) * perWidth,
          y: 0,
          z: (-row / 2 + i + 0.5) * perDepth,
        });
      }
    }
    return posArr;
  }

  #getCanvasTex(text, nameCount = 12, extendSetting = {}) {
    const setting = {
      pixelRatio: 4,
      text,
      color: '#ffffff',
      'text-align': 'center',
      'font-size': 16,
      'font-weight': '800',
      'line-height': 40,
      maxWidth: (nameCount === 6 ? 12 : 6) * nameCount,

      ...extendSetting,
    };
    return getTextToTex(setting);
  }

  #clickable(mesh, id, type) {
    mesh.userData[type] = true;
    mesh.userData.id = id;
    mesh.userData._type = type;
    mesh.userData.type = type;
    if (type === 'app') {
      this.#thirdPartyMap[id].mesh = mesh;
    } else if (type === 'channel') {
      this.#channelMap[id].mesh = mesh;
    } else if (type === 'strip') {
      this.#stripMap[id].mesh = mesh;
      this.#stripMap[id].type = type;
    }
  }
}
