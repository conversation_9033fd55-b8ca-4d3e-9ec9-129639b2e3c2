import { models } from '../../..';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export const getSitesDatabaseSetting = (data, w3d) => {
  const modelMap = { pod: models.sitesDatabase.children[0].clone() };
  const matMap = {
    site: {
      ground: w3d.themeManager.referStyle('site-ground'),
      groundHover: w3d.themeManager.referStyle('site-ground-hover'),
      businessLine: w3d.themeManager.referStyle('business-line'),
      dbGround: w3d.themeManager.referStyle('db-ground'),
      podLine: w3d.themeManager.referStyle('pod-line'),
      podRelationLine: w3d.themeManager.referStyle('podRelationLine'),
    },
    pod: {
      default: {
        normal: w3d.themeManager.referStyle('pod-default-normal'),
        alarm: w3d.themeManager.referStyle('pod-default-alarm'),
      },
      A: {
        normal: w3d.themeManager.referStyle('pod-A-normal'),
        warning: w3d.themeManager.referStyle('pod-A-warning'),
        alarm: w3d.themeManager.referStyle('pod-A-alarm'),
      },
      S: {
        normal: w3d.themeManager.referStyle('pod-S-normal'),
        warning: w3d.themeManager.referStyle('pod-S-warning'),
        alarm: w3d.themeManager.referStyle('pod-S-alarm'),
      },
      R: {
        normal: w3d.themeManager.referStyle('pod-R-normal'),
        warning: w3d.themeManager.referStyle('pod-R-warning'),
        alarm: w3d.themeManager.referStyle('pod-R-alarm'),
      },
      Ra: {
        normal: w3d.themeManager.referStyle('pod-Ra-normal'),
        warning: w3d.themeManager.referStyle('pod-Ra-warning'),
        alarm: w3d.themeManager.referStyle('pod-Ra-alarm'),
      },
      Rs: {
        normal: w3d.themeManager.referStyle('pod-Rs-normal'),
        warning: w3d.themeManager.referStyle('pod-Rs-warning'),
        alarm: w3d.themeManager.referStyle('pod-Rs-alarm'),
      },
    },
    podWarn: {
      asSwitchover: w3d.themeManager.referStyle('asSwitchover'),
      arSwitchover: w3d.themeManager.referStyle('arSwitchover'),
    },
  };
  return { modelMap, matMap };
};

const createHoverCard = (data, podItem, w3d, componentManager) => {
  const id = data.id;
  const type = 'pod';
  componentManager.cardManager.updateCard(type, data, podItem);
  const card = componentManager.cardManager.cards[type][id];
  const d2Object = card.children[0];
  const domContainer = d2Object.element.children[0];
  componentManager.events.showPodDataBaseTip({
    data,
    dom: domContainer,
  });
};

const createNameCard = (name, modelItem, w3d, componentManager) => {
  const id = `detail_${name}`;
  const type = 'nameDetail';
  componentManager.cardManager.updateCard(
    type,
    { id, name },
    modelItem,
  );
};

const removeNameCard = (name, modelItem, w3d, componentManager) => {
  const id = `detail_${name}`;
  const type = 'nameDetail';
  componentManager.cardManager.removeCard({ id, name }, type);
};

const bindSiteHoverEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'hoverIn', (e, info) => {
    compIns.hoverIn(info.object, (data, podItem, selected) => {
      if (data) {
        w3d.agent.wrapper.children[0].style.cursor = 'pointer';
        createHoverCard(data, podItem, w3d, componentManager);
        if (!selected) {
          componentManager.events.hoverInObject(podItem);
        }
      } else {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
      }
    }, () => {
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
    }, (nameItem, detailName) => {
      createNameCard(detailName, nameItem, w3d, componentManager);
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(compIns, 'hoverOut', (e, info) => {
    compIns.hoverOut(info.object, (data, podItem, selected) => {
      if (data) {
        w3d.agent.wrapper.children[0].style.cursor = 'initial';
        componentManager.cardManager.removeCard(data, 'pod');
        if (!selected) {
          componentManager.events.hoverOutObject(podItem);
        }
      }
    }, () => {
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
    }, (nameItem, detailName) => {
      removeNameCard(detailName, nameItem, w3d, componentManager);
    });
    w3d.agent.renderOnce();
    return true;
  });
};

const bindSiteClickEvents = (compIns, w3d, componentManager) => {
  A3D.DOM.bind(compIns, 'click', (e, info) => {
    compIns.select(info.object, (data, podItem) => {
      componentManager.events.showPanel({
        type: 'pod',
        id: data.id,
      });
      componentManager.cardManager.removeCard(data, 'pod');
      componentManager.events.selectObject(podItem);
    }, (data, siteItem, isOldSelected) => {
      if (!isOldSelected) {
        w3d.resetCamera(true, 300);
      }
      componentManager.events.showPanel({
        type: 'site',
        id: data.id,
      });
      componentManager.events.selectObject(null);
    });
    return true;
  });
};

export const bindSiteEvents = (compIns, w3d, componentManager) => {
  bindSiteHoverEvents(compIns, w3d, componentManager);
  bindSiteClickEvents(compIns, w3d, componentManager);
};
