import {getTextCanvas} from '../../../../components/utils/site';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {getPosititons} from './business';

const A3D = getA3D();

const control1 = new A3D.C.Vector3();
const control2 = new A3D.C.Vector3();

const getPointCount = function(count) {
  return Math.min(Math.max(64, count), 128);
};

export const getBezier1 = function(start, end) {
  control1.copy(start);
  control2.copy(end);
  const dis = start.distanceTo(end);
  control2.copy(start);
  let disx = end.x - start.x;
  let disz = end.z - start.z;
  let controlRatio1x = 0.25 * disx;
  let controlRatio1z = 0 * disz;
  let controlRatio2x = 0.3 * disx;
  let controlRatio2z = 0.9 * disz;
  control1.x += controlRatio1x;
  control1.z += controlRatio1z;
  control2.x += controlRatio2x;
  let ratio = 2.5;
  control2.z += (controlRatio2z - ratio);
  control2.y += ratio;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

export const getBezier2 = function(start, end) {
  control1.copy(start);
  control2.copy(end);
  const dis = start.distanceTo(end);
  control2.copy(start);
  let disx = end.x - start.x;
  let disy = end.y - start.y;
  let controlRatio1x = 0.6 * disx;
  let controlRatio1y = 0 * disy;
  let controlRatio2x = 0.65 * disx;
  let controlRatio2y = disy;
  control1.x += controlRatio1x;
  control1.y += controlRatio1y;
  control2.x += controlRatio2x;
  control2.y += controlRatio2y;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

export const getBezier3 = function(start, end, flag) {
  const dis = start.distanceTo(end);
  let disx = end.x - start.x;
  let disz = end.z - start.z;
  let disy = end.y - start.y;

  control1.copy(start);
  control2.copy(end);

  let controlRatio1x = 0.9 * disx;
  let controlRatio1y = flag ? Number(disy) : 0.65 * disy;
  let controlRatio1z = -3 * disz;
  control1.x += controlRatio1x;
  control1.y += controlRatio1y;
  control1.z -= controlRatio1z;

  let controlRatio2x = 0.22 * disx;
  let controlRatio2y = flag ? Number(disy) : 0.65 * disy;
  let controlRatio2z = -3 * disz;
  control2.x += controlRatio2x;
  control2.y += controlRatio2y;
  control2.z -= controlRatio2z;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

export const getBezier4 = function(start, end) {
  const dis = start.distanceTo(end);
  let disx = end.x - start.x;
  let disz = end.z - start.z;
  let disy = end.y - start.y;

  control1.copy(start);
  control2.copy(end);

  let controlRatio1x = 0.5 * disx;
  let controlRatio1y = -0.15 * disy;
  let controlRatio1z = -0.5 * Number(disz);
  control1.x += controlRatio1x;
  control1.y += controlRatio1y;
  control1.z -= controlRatio1z;

  let controlRatio2x = -0.25 * disx;
  let controlRatio2y = -0.15 * disy;
  let controlRatio2z = Number(disz);
  control2.x += controlRatio2x;
  control2.y += controlRatio2y;
  control2.z -= controlRatio2z;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

export const getBezier5 = function(start, end) {
  const dis = start.distanceTo(end);
  let disx = end.x - start.x;
  let disz = end.z - start.z;
  let disy = end.y - start.y;

  control1.copy(start);
  control2.copy(end);

  let controlRatio1x = 0.5 * disx;
  let controlRatio1y = -0.15 * disy;
  let controlRatio1z = -0.5 * Number(disz);
  control1.x += controlRatio1x;
  control1.y += controlRatio1y;
  control1.z -= controlRatio1z;

  let controlRatio2x = -0.25 * disx;
  let controlRatio2y = -0.15 * disy;
  let controlRatio2z = Number(disz);
  control2.x += controlRatio2x;
  control2.y += controlRatio2y;
  control2.z -= controlRatio2z;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

export const getBezier6 = function(start, end) {
  const dis = start.distanceTo(end);
  let disx = end.x - start.x;
  let disz = end.z - start.z;
  let disy = end.y - start.y;

  control1.copy(start);
  control2.copy(end);

  let controlRatio1x = 0.5 * disx;
  let controlRatio1y = 0.1 * disy;
  let controlRatio1z = Number(disz);
  control1.x += controlRatio1x;
  control1.y += controlRatio1y;
  control1.z -= controlRatio1z;

  let controlRatio2x = -0.25 * disx;
  let controlRatio2y = -0.15 * disy;
  let controlRatio2z = Number(disz);
  control2.x += controlRatio2x;
  control2.y += controlRatio2y;
  control2.z -= controlRatio2z;
  const curve = new A3D.C.CubicBezierCurve3(start, control1, control2, end);
  return curve.getPoints(getPointCount(dis * 8));
};

const setting = {
  pixelRatio: 4,
  'text-align': 'center',
  color: '#fff',
  'font-weight': 'normal',
  'font-size': 18,
  'line-height': 22.5,
  maxWidth: 190,
};

const drawRoundRect = function(ctx, canvas) {
  ctx.fillStyle = '#393939';
  ctx.roundRect(0, 0, canvas.width, canvas.height, 5);
  ctx.fill();
};

const getTPSTex = function(text) {
  setting.text = `TPS ${text}`;
  let _canvas = getTextCanvas(setting, null, true, false);
  const ctx = _canvas.getContext('2d');
  _canvas.width *= 1.5;
  _canvas.height *= 1.8;
  ctx.clearRect(0, 0, _canvas.width, _canvas.height);
  drawRoundRect(ctx, _canvas);
  getTextCanvas(setting, _canvas, false, true);
  return A3D.canvasToTex(_canvas, true);
};

const getTPSTex2 = function(name, text) {
  setting.text = `${name} ${text}`;
  setting['font-size'] = 22;
  let _canvas = getTextCanvas(setting, null, true, false);
  const ctx = _canvas.getContext('2d');
  _canvas.width *= 1.1;
  _canvas.height *= 1.8;
  ctx.clearRect(0, 0, _canvas.width, _canvas.height);
  drawRoundRect(ctx, _canvas);
  getTextCanvas(setting, _canvas, false, true);
  return A3D.canvasToTex(_canvas, true);
};

const setSpriteScale = function(sprite, canvas, scale) {
  let _scale = scale || sprite.userData.scale;
  if (!_scale) {
    _scale = sprite.scale.x;
  }
  sprite.scale.set(canvas.width / canvas.height * _scale, _scale);
};

export const createLineCard = function(item, line, position, w3d, scale = 1) {
  let tex = getTPSTex(item.indicatorValue);
  let canvas = tex.image;
  let material = new A3D.C.SpriteMaterial({
    map: tex,
    depthWrite: false,
  });
  const sprite = new A3D.C.Sprite(material);
  sprite.position.copy(position);
  sprite.position.x += 0.1;
  setSpriteScale(sprite, canvas, scale);
  sprite.center.set(0, 0.5);
  sprite.renderOrder = 4;
  sprite.name = 'tpsTip';
  sprite.userData.indicatorValue = item.indicatorValue;
  sprite.userData.scale = scale;

  let mat = item.indicatorStatus === false ? w3d.themeManager.referStyle('normal-circle') : w3d.themeManager.referStyle('alarm-circle');
  let circle = new A3D.C.Sprite(mat);
  circle.scale.set(0.2, 0.2);
  circle.position.copy(position);
  circle.renderOrder = 5;
  circle.name = 'circle';

  let group = A3D.createGroup();
  group.name = 'tpsGroup';
  group.userData.tpsTip = sprite;
  group.add(circle);
  line.add(group);

  if (item.indicatorValue !== undefined && item.indicatorValue !== null) {
    group.add(sprite);
  }
};

export const createLineThird2ChannelCard = function(data, line, position, w3d, scale = 1) {
  let text = data.value;
  let tex = getTPSTex2(data.name, text);
  let canvas = tex.image;
  let material = new A3D.C.SpriteMaterial({
    map: tex,
    depthWrite: false,
  });
  const sprite = new A3D.C.Sprite(material);
  sprite.position.copy(position);
  sprite.position.x += 0.1;
  setSpriteScale(sprite, canvas, scale);
  sprite.center.set(0, 0.5);
  sprite.renderOrder = 4;
  sprite.name = 'tpsTip';
  sprite.userData.value = text;
  sprite.userData.scale = scale;

  let mat = w3d.themeManager.referStyle(data.status === 'normal' ? 'normal-circle' : 'alarm-circle');
  let circle = new A3D.C.Sprite(mat);
  circle.scale.set(0.2, 0.2);
  circle.position.copy(position);
  circle.renderOrder = 5;
  circle.name = 'circle';

  let group = A3D.createGroup();
  group.name = 'tpsGroup';
  group.userData.tpsTip = sprite;
  group.add(circle);
  line.add(group);

  if (text !== undefined && text !== null) {
    group.add(sprite);
  }
};

export const createLineChannel2StripCard = function(data, line, position, w3d, scale = 1) {
  let text = data.value;
  let tex = getTPSTex2(data.name, text);
  let canvas = tex.image;
  let material = new A3D.C.SpriteMaterial({
    map: tex,
    depthWrite: false,
  });
  const sprite = new A3D.C.Sprite(material);
  sprite.position.copy(position);
  sprite.position.x += 0.1;
  setSpriteScale(sprite, canvas, scale);
  sprite.center.set(0, 0.5);
  sprite.renderOrder = 4;
  sprite.name = 'tpsTip';
  sprite.userData.value = text;
  sprite.userData.scale = scale;

  let mat = w3d.themeManager.referStyle(data.status === 'normal' ? 'normal-circle' : 'alarm-circle');
  let circle = new A3D.C.Sprite(mat);
  circle.scale.set(0.2, 0.2);
  circle.position.copy(position);
  circle.renderOrder = 5;
  circle.name = 'circle';

  let group = A3D.createGroup();
  group.name = 'tpsGroup';
  group.userData.tpsTip = sprite;
  group.add(circle);
  line.add(group);

  if (text !== undefined && text !== null) {
    group.add(sprite);
  }
};

export const bindLineEvents = function(line, componentManager, w3d) {
  A3D.DOM.bind(line, 'hoverIn', (e, info) => {
    line.hoverIn(info.object, (item) => {
      let tpsTip = item.getObjectByName('tpsTip');
      if (!tpsTip) {
        return false;
      }
      componentManager.events.hoverInLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      let pos = item.children[1].children[0].position;
      componentManager.cardManager.updateCard('tps', {id: item.name, position: pos});
      const card = componentManager.cardManager.cards.tps[item.name];
      const d2Object = card.children[0];
      const domContainer = d2Object.element.children[0];
      componentManager.events.showTPSCard({
        data: {
          siteId: item.userData.data.siteId,
          businessId: item.userData.data.businessId,
        },
        dom: domContainer,
      });
      return true;
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(line, 'hoverOut', (e, info) => {
    line.hoverIn(info.object, (item) => {
      componentManager.events.hoverOutLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.cardManager.removeCard(
        {id: item.name},
        'tps',
      );
    });
    w3d.agent.renderOnce();
    return true;
  });
};

export const bindChannelLineEvents = function(line, componentManager, w3d) {
  A3D.DOM.bind(line, 'hoverIn', (e, info) => {
    line.hoverIn(info.object, (item) => {
      let tpsTip = item.getObjectByName('tpsTip');
      if (!tpsTip) {
        return false;
      }
      componentManager.events.hoverInLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      let pos = item.children[1].children[0].position;
      componentManager.cardManager.updateCard('tps', {id: item.name, position: pos});
      const card = componentManager.cardManager.cards.tps[item.name];
      const d2Object = card.children[0];
      const domContainer = d2Object.element.children[0];
      componentManager.events.showTPSCard({
        data: {
          ...item.userData.data.channelData,
        },
        dom: domContainer,
      });
      return true;
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(line, 'hoverOut', (e, info) => {
    line.hoverIn(info.object, (item) => {
      componentManager.events.hoverOutLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.cardManager.removeCard(
        {id: item.name},
        'tps',
      );
    });
    w3d.agent.renderOnce();
    return true;
  });
};

export const bindStripLineEvents = function(line, componentManager, w3d) {
  A3D.DOM.bind(line, 'hoverIn', (e, info) => {
    line.hoverIn(info.object, (item) => {
      let tpsTip = item.getObjectByName('tpsTip');
      if (!tpsTip) {
        return false;
      }
      componentManager.events.hoverInLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'pointer';
      let pos = item.children[1].children[0].position;
      componentManager.cardManager.updateCard('tps', {id: item.name, position: pos});
      const card = componentManager.cardManager.cards.tps[item.name];
      const d2Object = card.children[0];
      const domContainer = d2Object.element.children[0];
      componentManager.events.showTPSCard({
        data: {
          ...item.userData.data.stripData,
        },
        dom: domContainer,
      });
      return true;
    });
    w3d.agent.renderOnce();
    return true;
  });
  A3D.DOM.bind(line, 'hoverOut', (e, info) => {
    line.hoverIn(info.object, (item) => {
      componentManager.events.hoverOutLine(item);
      w3d.agent.wrapper.children[0].style.cursor = 'initial';
      componentManager.cardManager.removeCard(
        {id: item.name},
        'tps',
      );
    });
    w3d.agent.renderOnce();
    return true;
  });
};


let color = {normal: '#2070F3', alarm: '#E54545'};
let flowColor = {normal: '#4bb2f4', alarm: '#f97f83'};

export const updateLine = function(data, lineMesh, flowMesh, w3d) {
  let tpsTip = lineMesh.getObjectByName('tpsTip');
  let indicator = data.indicatorValue;
  if (data.indicatorStatus === false) {
    lineMesh.material.color.set(color.normal);
    flowMesh.material.color.set(flowColor.normal);
    let circle = lineMesh.getObjectByName('circle');
    circle && (circle.material = w3d.themeManager.referStyle('normal-circle'));
  } else {
    lineMesh.material.color.set(color.alarm);
    flowMesh.material.color.set(flowColor.alarm);
    let circle = lineMesh.getObjectByName('circle');
    circle && (circle.material = w3d.themeManager.referStyle('alarm-circle'));
  }

  let tpsGroup = lineMesh.getObjectByName('tpsGroup');

  if (!tpsGroup) {
    return;
  }

  if (indicator === undefined || indicator === null) {
    tpsGroup.remove(tpsTip);
  } else {
    tpsTip = tpsGroup.userData.tpsTip;
    tpsGroup.add(tpsTip);
  }

  if (!tpsTip) {
    return;
  }

  tpsTip.userData.indicatorValue = indicator;
  let mat = tpsTip.material;
  mat.map.dispose();
  let tex = getTPSTex(indicator);
  mat.map = tex;
  setSpriteScale(tpsTip, mat.map.image);
};

export const updateMMLine = function(lineMesh, flowMesh) {
  lineMesh.material.color.set(color.normal);
  flowMesh.material.color.set(flowColor.normal);
};

const LINE_TYPE = 2;
export const createLine = function(componentManager, data, line, w3d) {
  let {northPos, sitePositions, southPos} = getPosititons(componentManager);
  let _line;
  let center;
  let businessPos;
  let sitePos;
  let cardPosPercent;
  let cardScale;
  let lineType;
  data.forEach((item) => {
    if (item.type === 'north') {
      businessPos = northPos;
      sitePos = sitePositions[item.siteId].backPosition;
      cardPosPercent = 0.8;
      cardScale = 0.9;
      lineType = 1;
    } else {
      businessPos = southPos;
      sitePos = sitePositions[item.siteId].fontPosition;
      cardPosPercent = 0.6;
      cardScale = 0.75;
      lineType = LINE_TYPE;
    }
    line.add({
      start: businessPos, end: sitePos, ...item, lineType, id: `${item.siteId}-${item.businessId}-${item.type}`,
    });
    _line = line.children[line.children.length - 1];
    center = line.getPointAt(_line, cardPosPercent);
    createLineCard(item, _line, center, w3d, cardScale);
  });
};


export const createLineThird2Channel = function(thirdPos, channelPositions, data, line, w3d, channelData) {
  let _line;
  let center;
  let cardPosPercent;
  let cardScale;
  let lineType;
  data.forEach((item, index) => {
    let channelPos = channelPositions[item.to];
    if (!channelPos) {
      return;
    }
    cardPosPercent = 0.6;
    cardScale = 0.9;
    lineType = 1;
    line.add({
      start: thirdPos, end: channelPos, ...item, lineType, id: `${item.to}`,
      channelData: (channelData || []).find((v) => String(v.id) === String(item.to)),
    });
    _line = line.children[line.children.length - 1];
    center = line.getPointAt(_line, cardPosPercent);
    createLineThird2ChannelCard(item, _line, center, w3d, cardScale);
  });
};


export const createLineChannel2Strip = function(channelPos, stripPositions, data, line, w3d, layoutData, stripData) {
  let _line;
  let center;
  let cardPosPercent;
  let cardScale;
  let lineType;

  let map = {};
  layoutData.level1.forEach((v, index) => {
    v.group.forEach((strip) => {
      map[strip.data.id] = index + 1;
    });
  });
  const firstRowLength = layoutData?.level1?.[0]?.group?.length || 0;
  // 第二层
  data.forEach((item, index) => {
    let stripPos = stripPositions[item.to];
    if (!stripPos) {
      return;
    }

    cardPosPercent = 0.65;
    if (map[item.to] !== 1) {
      cardPosPercent = 0.55;
    }

    if (Object.keys(map).length <= 10) {
      cardPosPercent = 0.5;
    }

    cardScale = 0.9;
    if (Object.keys(map).length <= 10) {
      lineType = 3;
    } else {
      lineType = map[item.to] === 1 ? 1 : 2;
    }
    line.add({
      start: channelPos,
      end: stripPos,
      ...item,
      lineType,
      id: `${item.to}`,
      stripData: (stripData || []).find((v) => String(v.id) === String(item.to)),
    });
    _line = line.children[line.children.length - 1];
    center = line.getPointAt(_line, cardPosPercent);
    createLineChannel2StripCard(item, _line, center, w3d, cardScale);
  });
};
export const updateLineThird2Channel = function(data, lineMesh, flowMesh, w3d) {
  let tpsTip = lineMesh.getObjectByName('tpsTip');
  let value = data.value;
  if (data.status === 'normal') {
    lineMesh.material.color.set(color.normal);
    flowMesh.material.color.set(flowColor.normal);
    let circle = lineMesh.getObjectByName('circle');
    circle && (circle.material = w3d.themeManager.referStyle('normal-circle'));
  } else {
    lineMesh.material.color.set(color.alarm);
    flowMesh.material.color.set(flowColor.alarm);
    let circle = lineMesh.getObjectByName('circle');
    circle && (circle.material = w3d.themeManager.referStyle('alarm-circle'));
  }

  let tpsGroup = lineMesh.getObjectByName('tpsGroup');

  if (!tpsGroup) {
    return;
  }

  if (value === undefined || value === null) {
    tpsGroup.remove(tpsTip);
  } else {
    tpsTip = tpsGroup.userData.tpsTip;
    tpsGroup.add(tpsTip);
  }

  if (!tpsTip) {
    return;
  }

  tpsTip.userData.value = value;
  let mat = tpsTip.material;
  mat.map.dispose();
  let tex = getTPSTex2(data.name, value);
  mat.map = tex;
  setSpriteScale(tpsTip, mat.map.image);
};
export const updateLineChannel2Strip = updateLineThird2Channel;
export const getLineData = function(componentManager, lineData, isSwitchBusiness) {
  let data = componentManager.store.data;
  let site = data.site;
  let northId = data.northBusiness.businessId;
  let southId = data.southBusiness.businessId;
  let lineNorth = {};
  let lineNorthShow = {};
  let lineSouth = {};
  let lineSouthShow = {};
  lineData.forEach((lineItem) => {
    if (lineItem.businessId === northId) {
      lineNorth[lineItem.siteId] = lineItem.indicatorValue;
      lineNorthShow[lineItem.siteId] = lineItem.indicatorStatus;
    }
    if (lineItem.businessId === southId) {
      lineSouth[lineItem.siteId] = lineItem.indicatorValue;
      lineSouthShow[lineItem.siteId] = lineItem.indicatorStatus;
    }
  });
  let line = [];
  let needAddNorth = !isSwitchBusiness || data.selectBusiness === 'north';
  let needAddSouth = !isSwitchBusiness || data.selectBusiness === 'south';
  site.forEach((item) => {
    item.siteList.forEach((siteData) => {
      if (needAddNorth) {
        line.push({
          siteId: siteData.siteId,
          businessId: northId,
          indicatorValue: lineNorth[siteData.siteId],
          indicatorStatus: lineNorthShow[siteData.siteId],
          type: 'north',
        });
      }
      if (needAddSouth) {
        line.push({
          siteId: siteData.siteId,
          businessId: southId,
          indicatorValue: lineSouth[siteData.siteId],
          indicatorStatus: lineSouthShow[siteData.siteId],
          type: 'south',
        });
      }
    });
  });
  return line;
};
