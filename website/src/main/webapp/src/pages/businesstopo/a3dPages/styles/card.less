.w3d-podDataBaseTip {
  background: #393939;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.50);
  border-radius: 0.5rem;
  padding: 1rem;
  .status {
    display: flex;
    font-size: 1rem;
    line-height: 1.25rem;
    .podId {
      font-weight: bold;
      color: #F5F5F5;
    }
    .podStatus {
      border:1px solid #00A874;
      border-radius: 0.125rem;
      padding: 0 0.25rem;
      margin-left: 0.5rem;

      color: #00A874;

      &.alarm {
        border-color: #E54545;
        color: #E54545;
      }

      &.asSwitchover {
        border-color: #ffbb33;
        color: #ffbb33;
      }

      &.arSwitchover {
        border-color: #E54545;
        color: #E54545;
      }
    }
  }
  .time {
    font-size: 0.875rem;
    color:#F5F5F5;
    line-height: 1rem;    
    margin-top: 1rem;  
  }
  .switchTime, .alarmCount {
    font-size: 0.875rem;
    color:#F5F5F5;
    line-height: 1rem;    
    margin-top: 0.5rem;  
      .alarm {
        color: #E54545;
      }
      .asSwitchover {
        color: #ffbb33;
      }
      .arSwitchover {
        color: #E54545;
      }
  }
}