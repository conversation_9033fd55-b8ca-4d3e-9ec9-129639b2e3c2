import { resetSetCameraByMesh } from '.';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import OrbitCtrlEx from '@a3d/a3d/lib/ex/OrbitCtrlEx.min.js';
import * as THREE from 'three';
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { GammaCorrectionShader } from 'three/examples/jsm/shaders/GammaCorrectionShader';

const A3D = getA3D();

const setAttr = (setting, key, value) => {
  if (value !== undefined) {
    setting[key] = value;
  }
};

export default class W3D {
  #themeSwitchedCallback;
  #meshThemeSwitchedCallback;
  #mapsUpdatedCallback;
  #cameraSetting;
  #cameraDistance;
  #orbitCtrlSetting;
  #loopAnimation;
  #renderSetting;
  #zoomTarget;
  #outlineStyle;

  constructor(wrapper, routeQuery, cameraSetting, orbitCtrlSetting, renderSetting) {
    this.wrapper = wrapper;
    this.#renderSetting = {
      loopRender: false,
      enableGammaCorrection: false,
      enableOutline: true,
      logarithmicDepthBuffer: false,
      ...renderSetting,
    };
    this.#outlineStyle = {
      visibleEdgeColor: new THREE.Color('#fff'), edgeGlow: 1.0, edgeThickness: 0.1, pulsePeriod: 0, edgeStrength: 2.0,
    };
    this.agent = this.#getAgent(
      routeQuery.msaa,
      routeQuery.hdp,
      this.#renderSetting.logarithmicDepthBuffer,
      this.#renderSetting.antialias,
      cameraSetting.cameraType,
    );
    this.resize();
    if (routeQuery.showGui) {
      this.stats = this.#initStats();
      this.gui = this.#initGui();
    }
    this.#themeSwitchedCallback = {};
    this.#meshThemeSwitchedCallback = {};
    this.#mapsUpdatedCallback = {};
    this.agent.on('beforeRender', () => {
      if (this.stats) {
        this.stats.update();
      }
      if (this.orbit) {
        this.orbit.update();
      }
    });
    if (cameraSetting.resetSetCameraByMesh) {
      resetSetCameraByMesh(this.agent);
    }
    this.#cameraSetting = {
      animation: null,
      target: new THREE.Object3D(),
      fov: 45,
      lat: 0,
      lon: 0,
      distance: 50,
      near: 1,
      far: 100,
      minDistance: 1,
      maxDistance: 100,
      animate: false,
    };
    this.#orbitCtrlSetting = { customize: false, ...orbitCtrlSetting };
    this.orbit = this.#initOrbitCtrl(this.#orbitCtrlSetting);
    this.setCameraDefaultSetting(cameraSetting);
    this.setCamera(cameraSetting);
    this.objLoader = new OBJLoader();
    this.textureLoader = new THREE.TextureLoader();
    this.agent.camera.updateProjectionMatrix();
    this.#setRenderSetting();
  }

  setCamera(cameraSetting) {
    const position = this.agent.camera.position;
    const target = this.agent.orbitCtrl.target;
    const { lon, lat, radius } = A3D.util.getLonLatAtSphere(position, target);
    const finalSetting = {
      lon,
      lat,
      distance: radius,
      duration: this.#cameraSetting.duration,
      easing: this.#cameraSetting.easing,
      ...cameraSetting,
    };
    this.#zoomTarget = this.#zoomTarget || new A3D.C.Object3D();
    if (cameraSetting.target) {
      this.#zoomTarget.position.copy(cameraSetting.target);
    } else {
      this.#zoomTarget.position.copy(this.agent.orbitCtrl.target);
    }
    if (this.#cameraSetting.animation) {
      this.#cameraSetting.animation.stop();
    }
    if (this.#cameraDistance) {
      this.#cameraDistance = null;
    }
    this.#cameraSetting.animation = this.agent.setCameraByMesh(this.#zoomTarget, finalSetting);
    return this.#cameraSetting.animation;
  }

  resetCamera(animate = true, duration = 1000) {
    this.setCamera({
      ...this.#cameraSetting,
      target: this.#cameraSetting.target.position,
      duration,
      animate,
    });
  }

  setCameraDefaultSetting({
    fov, lat, lon, target, distance, near, far, minDistance, maxDistance, duration, easing,
  } = {}) {
    let cameraSetting = this.#cameraSetting;

    setAttr(cameraSetting, 'fov', fov);
    setAttr(cameraSetting, 'lat', lat);
    setAttr(cameraSetting, 'lon', lon);
    setAttr(cameraSetting, 'distance', distance);
    setAttr(cameraSetting, 'near', near);
    setAttr(cameraSetting, 'far', far);
    setAttr(cameraSetting, 'minDistance', minDistance);
    setAttr(cameraSetting, 'maxDistance', maxDistance);
    setAttr(cameraSetting, 'duration', duration);
    setAttr(cameraSetting, 'easing', easing);
    if (target) {
      cameraSetting.target.position.copy(target);
    }

    this.agent.camera.fov = cameraSetting.fov;
    this.agent.camera.near = cameraSetting.near;
    this.agent.camera.far = cameraSetting.far;
    if (this.#orbitCtrlSetting.customize) {
      this.orbit.setting.zoom.distance.min = cameraSetting.minDistance;
      this.orbit.setting.zoom.distance.max = cameraSetting.maxDistance;
    } else {
      this.orbit.minDistance = cameraSetting.minDistance;
      this.orbit.maxDistance = cameraSetting.maxDistance;
    }
  }

  #getAgent(msaa = false, hdp = false, logarithmicDepthBuffer = false, antialias = true, cameraType = 'Perspective') {
    const agent = new A3D(this.wrapper, {
      enableMultiSampler: Boolean(msaa),
      pixelRatio: hdp ? Math.max(window.devicePixelRatio, 2) : window.devicePixelRatio,
      showCameraHelper: false,
      logarithmicDepthBuffer,
      antialias,
      cameraType,
    });

    agent.init({ click: true, keyboard: true, hover: true });
    agent.setIntersectsFilter((mesh) => {
      if (mesh.focusable === false || mesh.pointerEvents === 'none' || mesh.visible === false) {
        return false;
      }
      return true;
    });

    agent.on('click', () => {
      agent.focus();
      agent.renderOnce();
    });
    return agent;
  }

  #setLoopRender() {
    const loopRender = this.#renderSetting.loopRender;
    if (loopRender) {
      this.#loopAnimation = this.#loopAnimation || new A3D.Animation(this.agent);
      this.#loopAnimation
        .push({ duration: Infinity })
        .start();
    }
  }

  startLoopRender() {
    this.#loopAnimation = this.#loopAnimation || new A3D.Animation(this.agent);
    this.#loopAnimation.stop();
    this.#loopAnimation
      .push({ duration: Infinity })
      .start();
  }

  stopLoopRender() {
    if (this.#loopAnimation) {
      this.#loopAnimation.stop();
    }
  }

  #setGammaCorrectionShader() {
    const enableGammaCorrection = this.#renderSetting.enableGammaCorrection;
    if (enableGammaCorrection) {
      const composer = this.agent.env.Postprocessing.composer;
      composer.addPass(new ShaderPass(GammaCorrectionShader));
    }
  }

  #setOutline() {
    const enableOutline = this.#renderSetting.enableOutline;
    this.agent.env.Postprocessing.composer.passes[1].enabled = enableOutline;
  }

  #setRenderSetting() {
    this.#setLoopRender();
    this.#setGammaCorrectionShader();
    this.#setOutline();
    this.setOutline({});
  }

  #initOrbitCtrl({
    customize, enabled, enableDamping, enablePan, enableRotate, enableZoom, maxAzimuthAngle,
    maxDistance, maxPolarAngle = Math.PI * 2, maxZoom, minAzimuthAngle,
    minDistance, minPolarAngle = -Math.PI * 2, minZoom,
    panSpeed, rotateSpeed, zoomSpeed, screenSpacePanning,
  }) {
    this.#orbitCtrlSetting.customize = customize;
    let setting = {
      enableRotate: true,
      enableKeys: true,
    };
    if (customize) {
      setting.orbitCtrl = OrbitCtrlEx;
    }
    const orbit = this.agent.initOrbitControls(setting);
    if (customize) {
      orbit.setting.zoom.speed = zoomSpeed || 0.1;
      orbit.setting.translate.speed = panSpeed || 1;
      orbit.setting.rotate.speed = rotateSpeed || 1;
      orbit.setting.rotate.lat.max = maxPolarAngle;
      orbit.setting.rotate.lat.min = minPolarAngle;
    } else {
      setAttr(orbit, 'enabled', enabled);
      setAttr(orbit, 'enableDamping', enableDamping);
      setAttr(orbit, 'enablePan', enablePan);
      setAttr(orbit, 'enableRotate', enableRotate);
      setAttr(orbit, 'enableZoom', enableZoom);
      setAttr(orbit, 'maxAzimuthAngle', maxAzimuthAngle);
      setAttr(orbit, 'maxDistance', maxDistance);
      setAttr(orbit, 'maxPolarAngle', maxPolarAngle);
      setAttr(orbit, 'maxZoom', maxZoom);
      setAttr(orbit, 'minAzimuthAngle', minAzimuthAngle);
      setAttr(orbit, 'minDistance', minDistance);
      setAttr(orbit, 'minPolarAngle', minPolarAngle);
      setAttr(orbit, 'minZoom', minZoom);
      setAttr(orbit, 'panSpeed', panSpeed || 1);
      setAttr(orbit, 'rotateSpeed', rotateSpeed || 1);
      setAttr(orbit, 'zoomSpeed', zoomSpeed || 1);
      setAttr(orbit, 'screenSpacePanning', screenSpacePanning || false);
    }

    return orbit;
  }

  resize() {
    const p = this.agent.wrapper;
    const rect = p.getBoundingClientRect();
    this.agent.resize(Math.floor(rect.width), Math.floor(rect.height));
  }

  destroy() {
    this.stopLoopRender();
    if (this.#orbitCtrlSetting.customize && this.orbit) {
      this.orbit.destroy();
    }
    if (this.stats) {
      this.stats.dom.parentNode.removeChild(this.stats.dom);
    }
    if (this.gui) {
      this.gui.destroy();
    }
    if (this.themeManager) {
      this.themeManager.destroy();
    }
    this.agent.renderer.forceContextLoss();
    this.agent.scene.traverse((child) => {
      if (child.isMesh) {
        child.geometry.dispose();
        child.material?.dispose();
      } else if (child.isCSS2DObject) {
        child.parent.remove(child);
      }
    });
    this.agent.destroy();
    this.agent = null;
    this.wrapper = null;
    this.objLoader = null;
    this.#themeSwitchedCallback = null;
    this.#meshThemeSwitchedCallback = null;
    this.#mapsUpdatedCallback = null;
    this.#cameraSetting = null;
  }

  #initStats() {
    const stats = new Stats();
    this.wrapper.appendChild(stats.domElement);
    this.stats = stats;
    return stats;
  }

  #initGui() {
    this.gui = new GUI();
    return this.gui;
  }

  initThemeManager(defaultJson, darkJson) {
    let themeManager = new A3D.ThemeManager();
    themeManager.setAgent(this.agent);
    themeManager.defineStyle('default', defaultJson);
    themeManager.defineStyle('dark', defaultJson);
    themeManager.enableAuto();
    // 切换皮肤时，执行的回调函数（只会调用一次）
    themeManager.bind('themeSwitched', (option) => {
      Object.keys(this.#themeSwitchedCallback).forEach((item) => {
        this.#themeSwitchedCallback[item](option);
      });
    });
    // 切换皮肤时，更新被修改过的材质属性（每个材质会调用一次）
    themeManager.bind('meshThemeSwitched', (option) => {
      Object.keys(this.#meshThemeSwitchedCallback).forEach((item) => {
        this.#meshThemeSwitchedCallback[item](option);
      });
    });
    themeManager.bind('mapsUpdated', (option) => {
      Object.keys(this.#mapsUpdatedCallback).forEach((item) => {
        this.#mapsUpdatedCallback[item](option);
      });
    });
    this.themeManager = themeManager;
    return themeManager;
  }

  loadImage(url) {
    return new Promise((resolve) => {
      const img = A3D.util.createElement('img');
      img.onload = () => {
        resolve(img);
      };
      img.setAttribute('src', url);
    });
  }

  loadModel(url) {
    let objLoader = this.objLoader;
    return new Promise((resolve) => {
      objLoader.load(url, (gltf) => {
        resolve(gltf);
      });
    });
  }

  #updateTexture(texture, setting) {
    Object.keys(texture).forEach((key) => {
      if (setting[key] !== undefined) {
        texture[key] = setting[key];
      }
    });
    texture.encoding = this.agent.renderer.outputEncoding;
  }

  loadTexture(url = '', setting = {}) {
    return new Promise((resolve) => {
      this.textureLoader.loadAsync(url).then((texture) => {
        texture.colorSpace = A3D.C.SRGBColorSpace;
        this.#updateTexture(texture, setting);
        resolve(texture);
      });
    });
  }

  createCustomMat(name, fn) {
    A3D.Mat.plugin(name, (options) => fn(options));
  }

  addThemeSwitchedCallback(name, cb) {
    if (name && typeof cb === 'function') {
      this.#themeSwitchedCallback[name] = cb;
    }
  }

  removeThemeSwitchedCallback(name) {
    delete this.#themeSwitchedCallback[name];
  }

  addMeshThemeSwitchedCallback(name, cb) {
    if (name && typeof cb === 'function') {
      this.#meshThemeSwitchedCallback[name] = cb;
    }
  }

  removeMeshThemeSwitchedCallback(name) {
    delete this.#meshThemeSwitchedCallback[name];
  }

  addMapsUpdatedCallback(name, cb) {
    if (name && typeof cb === 'function') {
      this.#mapsUpdatedCallback[name] = cb;
    }
  }

  removeMapsUpdatedCallback(name) {
    delete this.#mapsUpdatedCallback[name];
  }

  setBackground(url, callback) {
    A3D.tex(url, (texture) => {
      texture.colorSpace = A3D.C.SRGBColorSpace;
      this.agent.scene.background = texture;
      if (callback) {
        callback(texture);
      }
    });
  }

  setHDR(url, callback, { background = true, environment = false } = {}) {
    if (!this.rgbeLoader) {
      this.rgbeLoader = new RGBELoader(this.loadManager);
    }
    this.rgbeLoader.load(url, (texture) => {
      texture.mapping = THREE.EquirectangularReflectionMapping;
      if (background) {
        texture.colorSpace = A3D.C.SRGBColorSpace;
        this.agent.scene.background = texture;
      }
      if (environment) {
        this.agent.scene.environment = texture;
      }
      this.agent.renderOnce();
      if (callback) {
        callback(texture);
      }
    });
  }

  setCubeTexture({
    posx, negx, posy, negy, posz, negz,
  }) {
    if (!this.cubeTextureLoader) {
      this.cubeTextureLoader = new THREE.CubeTextureLoader();
    }
    this.cubeTextureLoader.load(
      [
        posx,
        negx,
        posy,
        negy,
        posz,
        negz,
      ],
      (texture) => {
        texture.colorSpace = A3D.C.SRGBColorSpace;
        this.agent.scene.background = texture;
      },
    );
  }

  #setCameraByZoom(lon, lat) {
    let cameraSetting = this.#cameraSetting;
    const target = this.agent.orbitCtrl.target;
    if (cameraSetting.animation) {
      cameraSetting.animation.stop();
    }
    this.#zoomTarget = this.#zoomTarget || new A3D.C.Object3D();
    this.#zoomTarget.position.copy(target);
    cameraSetting.animation = this.agent.setCameraByMesh(this.#zoomTarget, {
      ...cameraSetting,
      lon,
      lat,
      distance: this.#cameraDistance,
      animate: true,
      onComplete: () => {
        this.#cameraDistance = null;
      },
    });
  }

  // 放大
  zoomIn(scale = 0.8) {
    const position = this.agent.camera.position;
    const target = this.agent.orbitCtrl.target;
    const { lon, lat, radius } = A3D.util.getLonLatAtSphere(position, target);

    let cameraSetting = this.#cameraSetting;

    if (radius <= cameraSetting.minDistance) {
      return;
    }

    if (typeof this.#cameraDistance === 'number') {
      this.#cameraDistance = Math.max(
        this.#cameraDistance * scale,
        cameraSetting.minDistance,
      );
    } else {
      this.#cameraDistance = Math.max(radius * scale, cameraSetting.minDistance);
    }

    this.#setCameraByZoom(lon, lat);
  }

  // 缩小
  zoomOut(scale = 0.8) {
    const position = this.agent.camera.position;
    const target = this.agent.orbitCtrl.target;
    const { lon, lat, radius } = A3D.util.getLonLatAtSphere(position, target);

    let cameraSetting = this.#cameraSetting;

    if (radius >= cameraSetting.maxDistance) {
      return;
    }

    if (typeof this.#cameraDistance === 'number') {
      this.#cameraDistance = Math.min(
        this.#cameraDistance / scale,
        cameraSetting.maxDistance,
      );
    } else {
      this.#cameraDistance = Math.min(radius / scale, cameraSetting.maxDistance);
    }

    this.#setCameraByZoom(lon, lat);
  }

  setOutline({
    edgeStrength, edgeGlow, edgeThickness, pulsePeriod, visibleEdgeColor, currentAgent = 'selectedAgent',
  }) {
    // 强度
    setAttr(this.#outlineStyle, 'edgeStrength', edgeStrength);
    // 明暗度
    setAttr(this.#outlineStyle, 'edgeGlow', edgeGlow);
    // 雾化度
    setAttr(this.#outlineStyle, 'edgeThickness', edgeThickness);
    // 闪烁，值越大频率越低
    setAttr(this.#outlineStyle, 'pulsePeriod', pulsePeriod);
    setAttr(this.#outlineStyle, 'visibleEdgeColor', visibleEdgeColor);
    this.agent.env.Postprocessing[currentAgent].attr({
      edgeStrength: this.#outlineStyle.edgeStrength,
      edgeGlow: this.#outlineStyle.edgeGlow,
      edgeThickness: this.#outlineStyle.edgeThickness,
      pulsePeriod: this.#outlineStyle.pulsePeriod,
      visibleEdgeColor: this.#outlineStyle.visibleEdgeColor,
    });
  }

  openShadow(flag) {
    this.agent.scene.traverse((item) => {
      if (item.isGroup || item.isMesh) {
        item.receiveShadow = flag;
        item.castShadow = flag;
      }
    });
  }
}
