import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

let defaultModel;

export const getDefaultModel = function() {
  if (defaultModel) {
    return defaultModel;
  }
  let geo = A3D.Geo.createBox({ width: 1, height: 1, depth: 1 });
  let mat = A3D.mat('Basic', { color: '#ddd' });
  defaultModel = A3D.mesh(geo, mat);
  return defaultModel;
};

export const ATTRS = {
  position: { defaultValue: new A3D.C.Vector3(0, 0, 0), value: new A3D.C.Vector3(0, 0, 0) },
  rotation: { defaultValue: new A3D.C.Euler(0, 0, 0), value: new A3D.C.Euler(0, 0, 0) },
  scale: { defaultValue: new A3D.C.Vector3(1, 1, 1), value: new A3D.C.Vector3(1, 1, 1) },
  color: { defaultValue: new A3D.C.Color('#fff'), value: new A3D.C.Color('#fff') },
  opacity: { defaultValue: 1 },
  textureIndex: { defaultValue: null },
  id: { newKey: 'name', defaultValue: undefined, value: undefined },
};

export const MESH_ATTRS = ['position', 'rotation', 'scale'];

export const INSTANCEDMESH_ATTRS = [
  ['position', 'rotation', 'scale', 'color'],
  ['id', 'opacity', 'textureIndex'],
];

export const DIRECTION_ATTRS = {
  quaternion: new A3D.C.Quaternion(),
  defaultDir: new A3D.C.Vector3(0, 0, 1),
  curDir: new A3D.C.Vector3(),
  rotation: new A3D.C.Euler(0, 0, 0),
};

export const LAYOUT_MODES = {
  dataDrivenMode: 'dataDriven',
  arrayMode: 'array',
  layerMode: 'layer',
  carouselMode: 'carousel',
};

export const getParameters = function(props, layoutGroup = {}) {
  let layoutMode = props.layoutMode || LAYOUT_MODES.dataDrivenMode;
  let setting;
  if (layoutMode === LAYOUT_MODES.arrayMode) {
    setting = {
      dir: 'row',
      wrap: 'positive',
      justifyContent: 'center',
      alignItems: 'center',
      count: { x: 10, z: 1 },
    };
  } else if (layoutMode === LAYOUT_MODES.layerMode) {
    setting = { space: { x: 2, y: 2, z: 2 } };
  } else if (layoutMode === LAYOUT_MODES.carouselMode) {
    setting = {
      selectScale: 1,
      unselectScale: 1,
    };
  }
  return A3D.util.extend(true, {
    onChangeCallback: (modelIns) => modelIns,
    onSelectCallback: (modelIns) => modelIns,
    layoutMode: LAYOUT_MODES.dataDrivenMode,
    layoutGroup,
    animate: false,
    space: { x: 2, y: 2, z: 2 },
    ...setting,
  }, props);
};

