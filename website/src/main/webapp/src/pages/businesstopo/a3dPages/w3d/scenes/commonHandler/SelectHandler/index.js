import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import Mask from '@pages/businesstopo/a3dPages/w3d/components/Mask';

const A3D = getA3D();

const scale = 1.2;

const defaultStartScale = new A3D.C.Vector3(1, 1, 1);
const duration = 500;

const matColor = '#226eeb';

// select 永远只有一个，unselect可能有多个，所以select只负责增加动画；
// unselect 之前必须有select，所以unselect时停掉上一次select
// unselect 可以多个执行，这里当发现重复 in-out 时，会直接上上一次 unselect 回到初始状态，再执行当前一次 unselect
export default class SelectHandler {
  #startSelectVec = new A3D.C.Vector3();
  #endSelectVec = new A3D.C.Vector3();
  #startUnselectVec = new A3D.C.Vector3();
  #endUnselectVec = new A3D.C.Vector3();
  constructor(agent) {
    this.selectObject = null;
    this.unselectObject = null;

    this.selectAnimation = new A3D.Animation(agent);
    this.unselectAnimation = new A3D.Animation(agent);

    this.mask = new Mask({ opacity: 0.8, depth: 0.5 });
    agent.add(this.mask);
  }

  select(object) {
    if (object) {
      if (this.unselectObject === object) {
        this.unselectAnimation.stop();
      }
      if (this.selectObject && this.selectObject !== object) {
        this.unselect(this.selectObject);
      }
      this.selectObject = object;
      if (!object.userData.startScale) {
        object.userData.startScale = object.scale.clone();
      }
      this.mask.select(object, matColor);
      this.#startSelectVec.copy(object.scale);
      this.#endSelectVec.copy(object.userData.startScale).multiplyScalar(scale);
      this.selectAnimation.push({
        duration,
        onUpdate: (e) => {
          object.scale.lerpVectors(this.#startSelectVec, this.#endSelectVec, e.value);
        },
      }).start();
    } else {
      if (this.selectObject) {
        this.unselect(this.selectObject);
      }
    }
  }

  unselect(object) {
    this.mask.select(null);
    this.#stopSelect(object);
    this.#stopUnselect(object);
    this.#startUnselect(object);
  }

  #stopSelect(object) {
    if (this.selectObject) {
      this.selectAnimation.stop();
      if (this.selectObject !== object) {
        const startScale = this.selectObject.userData.startScale || defaultStartScale;
        this.selectObject.scale.copy(startScale).multiplyScalar(scale);
      }
      this.selectObject = null;
    }
  }

  #stopUnselect(object) {
    if (this.unselectObject) {
      this.unselectAnimation.stop();
      if (this.unselectObject !== object) {
        const startScale = this.unselectObject.userData.startScale || defaultStartScale;
        this.unselectObject.scale.copy(startScale);
      }
      this.unselectObject = null;
    }
  }

  #startUnselect(object) {
    if (object) {
      this.unselectObject = object;
      this.#startUnselectVec.copy(object.scale);
      this.#endUnselectVec.copy(object.userData.startScale || defaultStartScale);
      if (this.#startUnselectVec.equals(this.#endUnselectVec)) {
        this.unselectObject = null;
        return;
      }
      this.unselectAnimation.push({
        duration,
        onUpdate: (e) => {
          object.scale.lerpVectors(this.#startUnselectVec, this.#endUnselectVec, e.value);
        },
        onComplete: () => {
          this.unselectObject = null;
        },
      }).start();
    }
  }
}
