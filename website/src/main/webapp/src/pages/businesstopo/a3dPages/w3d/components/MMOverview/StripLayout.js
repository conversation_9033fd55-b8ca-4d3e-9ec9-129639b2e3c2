const fixNum = (num, pow = 2) => {
  const ratio = Math.pow(10, pow);
  return Math.round(num * ratio) / ratio;
};

export default class StripLayout {
  static initLayout(layoutData, size, nameSize, space, position) {
    const podDepth = this.#getPodDepth(layoutData, size, nameSize, space);
    const podWidth = this.#getPodWidth(layoutData, size, space);
    const podSize = { podWidth, podDepth };
    this.#setSize(layoutData, size, nameSize, space, podSize);
    this.#fixSizeToFull(layoutData);
    this.#setPosition(layoutData, nameSize, space);
    layoutData.podWidth = podWidth;
    layoutData.podDepth = podDepth;
    layoutData.position = position;
  }

  static #getPodDepth(layoutData, size, nameSize, space) {
    const l1LayerZ = layoutData.level1.length;
    let leastDepth = size.depth - (nameSize + space * 3) * l1LayerZ - space * (l1LayerZ - 1);
    const podDepth = leastDepth / layoutData.row;
    return podDepth;
  }

  static #getPodWidth(layoutData, size, space) {
    let podWidth = size.width;
    layoutData.level1.forEach((businessGroup) => {
      const l1LayerX = businessGroup.group.length;
      let leastWidth = size.width - space * (l1LayerX - 1 + l1LayerX * 2);
      podWidth = Math.min(leastWidth / businessGroup.col, podWidth);
    });
    return podWidth;
  }

  static #setSize(layoutData, size, nameSize, space, podSize) {
    const { podWidth, podDepth } = podSize;
    layoutData.size = size;

    layoutData.level1.forEach((businessGroup) => {
      let lastBusinessX = -space;
      const row = businessGroup.row;
      const businessDepth = podDepth * row + space * 3 + nameSize;
      businessGroup.group.forEach((business) => {
        const col = business.col;
        const businessWidth = podWidth * col + space * 2;
        lastBusinessX += businessWidth + space;

        business.size = { width: businessWidth, depth: businessDepth };
      });

      businessGroup.size = { width: lastBusinessX, depth: businessDepth };
    });
  }

  static #fixSizeToFull(layoutData) {
    layoutData.level1.forEach((businessGroup) => {
      const businessGroupMoreWidth = fixNum(layoutData.size.width - businessGroup.size.width, 6);
      const businessMoreWidth = businessGroupMoreWidth / businessGroup.group.length;
      businessGroup.size.width = layoutData.size.width;
      businessGroup.group.forEach((business) => {
        business.size.width += businessMoreWidth;
      });
    });
  }

  static #setPosition(layoutData, nameSize, space) {
    let businessGroupPosZ = -layoutData.size.depth / 2;
    layoutData.level1.forEach((businessGroup) => {
      businessGroup.position = { x: 0, y: 0, z: businessGroupPosZ };
      businessGroupPosZ += businessGroup.size.depth + space;

      let businessPosX = -businessGroup.size.width / 2;
      businessGroup.group.forEach((business) => {
        business.position = { x: businessPosX, y: 0, z: 0 };
        businessPosX += business.size.width + space;
      });
    });
  }
}
