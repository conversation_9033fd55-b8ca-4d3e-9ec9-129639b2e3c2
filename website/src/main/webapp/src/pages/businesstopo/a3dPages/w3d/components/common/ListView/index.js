import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {
  getDefaultModel, getParameters, LAYOUT_MODES, MESH_ATTRS,
} from '../const';
import ListItem from '../ListItem';
import {
  arrayLayout, carouselInitLayout, carouselSelectLayout, getInitBoundingBox,
  getLayoutData, getObjectSize,
  getStartLayout,
  layerLayout, layerSelectLayout, onChange, runAnimation, toFixed,
} from '../utils';

const A3D = getA3D();

export default class ListView extends A3D.C.Group {
  /**
   * 组件构造方法
   * @param {JSON} props
   * @example
   * const props = {
   *   models: Group[]|Mesh[],     // models中的item可以是不同的模型，与data数组一一对应，否则取最后一个3D对象
   *   onChangeCallback: Function,  // 可选项，更新listItem时，会触发此回调函数，必须返回模型，eg: (modelIns, index) => { return modelIns }
   *   onBeforeSelectCallback: Function,  // 可选项，选中模型时对模型做额外处理，eg: (modelIns, index) => { return modelIns }
   *   onSelectCallback: Function,  // 可选项，选中模型时对模型做额外处理，eg: (modelIns, index) => { return modelIns }
   *   layoutGroup: Object, // 可选项，定义布局模式以及对应的布局算法，布局算法返回一个数组，存放每个listItem的位置，缩放，旋转信息
   *   // eg: { layoutMode2: Function, layoutMode2: Function, }
   *   // eg: (length, listViewIns) => { return new Array(length).fill({ position: {x: 0, y: 0, z: 0} }) }
   *   layoutMode: String,           // 可选项，布局类型，选择使用哪种布局算法
   *   animationOption: {           // 可选项，动画相关参数
   *     animation: A3D.Animation, // A3D动画实例，若有此配置，则点击实例会执行过渡动画
   *     duration: Number,        //  可选项，默认值为 400
   *     onStart: Function,      // 可选项，动画开始时的回调
   *     onComplete: Function,  // 可选项，动画开始结束时的回调
   *   },
   * }
   */
  #geoSize = [];
  #needsUpdate;
  #prevSelectIndex;
  #defaultClickFn;

  /**
   * 层级布局/轮播布局点击事件
   * @param {Event} e 点击/键盘事件
   * @param {Object} info 点击位置射线捕获到的 3D 信息
   * @returns {Boolean} 是否取消事件向上冒泡
   */
  #defaultClick(e, info) {
    const index = this.#getListItemIndex(info);
    this.select(index);
    return false;
  }

  constructor(props) {
    super();
    if (props) {
      // 事件绑定与解绑需要保证使用同一方法；可以使用箭头函数实现，保证 function 内部 this 的正常调用；也可以 .bind(this) 实现
      this.#defaultClickFn = (...rest) => this.#defaultClick(...rest);

      this.parameters = getParameters(props, {
        [LAYOUT_MODES.arrayMode]: this.#arrayLayout,
        [LAYOUT_MODES.layerMode]: this.#layerLayout,
        [LAYOUT_MODES.carouselMode]: this.#carouselLayout,
      });
      this.#needsUpdate = true;
      this.count = 0;
      this.selectIndex;
      this.boundingBox = getInitBoundingBox();
      let listItemGroup = A3D.createGroup();
      this.add(listItemGroup);
      this.setLayoutMode(this.parameters.layoutMode, true);
    }
  }

  /**
   * 重写 Object3D 的 copy 方法
   *
   * @param {ListView} source
   */
  copy(source) {
    super.copy(source, true);
    this.parameters = A3D.util.extend(true, {}, source.parameters);
    this.#needsUpdate = true;
    this.count = source.count;
    this.selectIndex = undefined;
    this.boundingBox = getInitBoundingBox();
    return this;
  }

  /**
   *
   * @param {JSON} data { name/id , position, rotation/direction, scale }
   * @param  {...Object3D} rest 模型
   * @example
   * const data = [{           // 组件数据，eg: [{id: 'id-1'}, {id: 'id-2'}, ...]
   *   position: Vector3,     // 位置，可选项，默认值{ x:0, y:0, z:0 }
   *   rotation: Euler,      // 旋转，可选项，默认值{ x:0, y:0, z:0 }
   *   direction: Vector3,  // 朝向，可选项，默认值{ x:0, y:0, z:0 }，可以代替rotation，组件内自动转化为旋转量。适用于只在某一平面如 xz 平面旋转的情况。
   *   scale: Vector3,     // 缩放，可选项，默认值{ x:1, y:1, z:1 }
   *   id: String,        // 可选项
   * }],
   * @returns {ListView}
   */
  add(data = {}, ...rest) {
    this.#needsUpdate = true;
    if (data.isObject3D) {
      super.add(data, ...rest);
      return this;
    }
    this.#addListItem(data, rest[0]);
    this.#onChange();
    return this;
  }

  /**
   * 删除第 index 个listItem
   *
   * @param {Number} index
   * @param  {...Object3D} rest
   * @returns
   */
  remove(index, ...rest) {
    if (index.isObject3D) {
      super.remove(index, ...rest);
      return this;
    }
    let listItem;
    rest.unshift(index);
    rest.sort((a, b) => b - a);
    rest.forEach((item) => {
      listItem = this.getListItem(index);
      if (listItem) {
        listItem.remove();
        this.#geoSize.splice(item, 1);
        this.count--;
      }
    });
    for (let i = rest.at(-1), len = this.count; i < len; i++) {
      this.getListItem(i).index = i;
    }
    this.#onChange();
    return this;
  }

  /**
   * 通过函数返回设置数据
   *
   * @param {Function} fn function(index) { return { id, position, rotation, scale, ... }; };
   * @param {Number} start 开始下标
   * @param {Number} count 数量
   */
  set(fn, start = 0, count = 0) {
    let N = this.count;
    if (count !== undefined) {
      N = Math.min(this.count || 0, count);
    }
    for (let i = start; i < N; i++) {
      const ret = fn ? fn(i) : undefined;
      this.setIndex(i, ret);
    }
  }

  /**
   * 设置第 index 个 listItem
   *
   * @param {Number} index 索引
   * @param {JSON} data listItem 数据，位置，缩放，旋转，名称等
   * @returns listItem 实例
   */
  setIndex(index, data) {
    let listItem = this.getListItem(index);
    listItem?.set(data);
    this.#needsUpdate = true;
    return listItem;
  }

  /**
   * 获取从 start 开始的 count 个 listItem 数据
   *
   * @param {Number} start 开始下标
   * @param {Number} count 数量
   * @returns {Array<JSON>} listItem 数组实例数据
   */
  get(start = 0, count = 0) {
    const N = count === undefined ? this.count : Math.min(this.count || 0, count);
    const res = [];
    for (let i = start; i < N; i++) {
      res.push(this.getIndex(i));
    }
    return res;
  }

  /**
   * 获取第 index 个 listItem 数据
   *
   * @param {Number} index 索引
   * @returns {JSON} listItem 实例数据
   */
  getIndex(index) {
    return this.getListItem(index)?.get();
  }

  /**
   * 获取第 index 个 listItem
   *
   * @param {Number} index 索引
   * @returns {ListItem} listItem 实例
   */
  getListItem(index) {
    return this.children[0].children[index];
  }

  removeListItem(listItem, ...rest) {
    rest.unshift(listItem);
    let arr = [];
    let index;
    let listView = this.children[0].children;
    rest.forEach((item) => {
      index = listView.indexOf(item);
      if (index !== -1) {
        arr.push(index);
      }
    });
    this.remove(...arr);
  }

  /**
   * 更新
   */
  update() {
    this.#onChange();
  }

  select(index) {
    const onBeforeSelectCallback = this.parameters.onBeforeSelectCallback;
    if (onBeforeSelectCallback && typeof this.selectIndex === 'number') {
      const listItem = this.children[0].children[this.selectIndex];
      onBeforeSelectCallback(listItem, this.selectIndex);
    }

    const mode = this.parameters.layoutMode;
    if (mode === LAYOUT_MODES.layerMode) {
      this.#layerSelectIndex(index);
    }
    if (mode === LAYOUT_MODES.carouselMode) {
      this.#carouselSelectIndex(index);
    }
    if (mode === LAYOUT_MODES.arrayMode) {
      this.#arraySelectIndex(index);
    }

    const onSelectCallback = this.parameters.onSelectCallback;
    if (onSelectCallback) {
      const listItem = this.children[0].children[index];
      onSelectCallback(listItem, index);
    }
  }

  prev() {
    const oldSelectIndex = this.selectIndex || 0;
    let newSelectIndex = oldSelectIndex - 1;
    if (newSelectIndex < 0) {
      newSelectIndex += this.count;
    }
    this.select(newSelectIndex);
  }

  next() {
    const oldSelectIndex = this.selectIndex || 0;
    let newSelectIndex = oldSelectIndex + 1;
    if (newSelectIndex >= this.count) {
      newSelectIndex -= this.count;
    }
    this.select(newSelectIndex);
  }

  #isCloser(startPoint, endPoint, dirVec3, closerFlag) {
    const newDir = endPoint.clone().sub(startPoint);
    const distance = newDir.length();
    // 距离为零的，跳过
    if (distance === 0) {
      return false;
    }
    const radian = toFixed(Math.acos(newDir.dot(dirVec3) / distance), 6);
    // 与按键方向夹脚超过90度，跳过
    if (radian > Math.PI * 0.25) {
      return false;
    }
    const cross = dirVec3.clone().cross(newDir);
    const yDistance = Math.abs(newDir.y);
    // 不是按上下按钮时
    if (dirVec3.y === 0) {
      // 严格按照垂直距离越小，优先级越高
      if (yDistance > closerFlag.minYDistance) {
        return false;
      } else if (yDistance < closerFlag.minYDistance) {
        closerFlag.minYDistance = yDistance;
        closerFlag.minRadian = radian;
        closerFlag.minDistance = distance;
        closerFlag.crossY = cross.y;
        return true;
      }
    }
    // 垂直距离一致时，判断夹角和距离的的关系。如果夹脚及距离关系一致，可能存在对称关系，则按顺时针确定优先级
    if (
      (closerFlag.minRadian === 0 && radian === 0 && distance < closerFlag.minDistance) ||
      radian * distance < closerFlag.minRadian * closerFlag.minDistance ||
      (radian * distance === closerFlag.minRadian * closerFlag.minDistance && cross.y > closerFlag.crossY)
    ) {
      closerFlag.minRadian = radian;
      closerFlag.minDistance = distance;
      closerFlag.crossY = cross.y;
      return true;
    }
    return false;
  }

  #getSpaceNext(camera, dir = [1, 0, 0]) {
    const oldSelectIndex = this.selectIndex || 0;
    const quaternion = new A3D.C.Quaternion();
    const defaultDir = new A3D.C.Vector3(0, 0, 1);
    const cameraDir = camera.getWorldDirection(new A3D.C.Vector3());
    cameraDir.negate().setY(0).normalize();
    quaternion.setFromUnitVectors(cameraDir, defaultDir);

    const points = this.children[0].children.map((listItem) => {
      const vector = new A3D.C.Vector3().copy(listItem.position)
        .applyQuaternion(quaternion);
      return vector;
    });
    const startPoint = points[oldSelectIndex];
    const dirVec3 = new A3D.C.Vector3(...dir);
    let nextIndex = -1;
    const closerFlag = {
      minDistance: Infinity,
      minYDistance: Infinity,
      minRadian: Math.PI / 2,
      crossY: Infinity,
    };
    points.forEach((endPoint, index) => {
      if (oldSelectIndex === index) {
        return;
      }
      if (this.#isCloser(startPoint, endPoint, dirVec3, closerFlag)) {
        nextIndex = index;
      }
    });
    if (nextIndex >= 0) {
      this.select(nextIndex);
    }
  }

  turnLeft(camera) {
    const dir = [-1, 0, 0];
    this.#getSpaceNext(camera, dir);
  }

  turnRight(camera) {
    const dir = [1, 0, 0];
    this.#getSpaceNext(camera, dir);
  }

  turnFront(camera) {
    const dir = [0, 0, 1];
    this.#getSpaceNext(camera, dir);
  }

  turnBack(camera) {
    const dir = [0, 0, -1];
    this.#getSpaceNext(camera, dir);
  }

  turnUp(camera) {
    const dir = [0, 1, 0];
    this.#getSpaceNext(camera, dir);
  }

  turnDown(camera) {
    const dir = [0, -1, 0];
    this.#getSpaceNext(camera, dir);
  }

  /**
   * 获取 listView 包围盒
   *
   * @returns {JSON} e.g. {max, min, width, height, depth}
   */
  getBoundingBox() {
    if (!this.#needsUpdate) {
      return this.boundingBox;
    }
    this.#needsUpdate = false;
    this.boundingBox = getObjectSize(this);
    return this.boundingBox;
  }

  /**
   * 是否开启动画
   *
   * @param {Boolean} enabled
   */
  enableAnimation(enabled) {
    this.parameters.animate = enabled;
  }

  /**
   * 设置布局类型
   *
   * @param {String} mode 布局类型
   * @param {Boolean} focus 是否强制设置
   */
  setLayoutMode(mode, focus = false) {
    if (this.parameters.layoutMode === mode && !focus) {
      return;
    }
    this.parameters.layoutMode = mode || this.parameters.layoutMode;
    this.selectIndex = undefined;
    this.#prevSelectIndex = undefined;
    let _mode = this.parameters.layoutMode;

    this.#unbind('click', this.#defaultClickFn);
    if (_mode === LAYOUT_MODES.layerMode || _mode === LAYOUT_MODES.carouselMode || _mode === LAYOUT_MODES.arrayMode) {
      this.#bind('click', this.#defaultClickFn);
    }
  }

  /**
   * 获取当前事件的 index
   * @param {Object} info
   * @returns
   */
  #getListItemIndex(info) {
    let listItem = info.object;
    let listItemGroup = this.children[0];
    while (listItem.parent && listItem.parent !== this && listItem.parent !== listItemGroup) {
      listItem = listItem.parent;
    }
    let index = listItemGroup.children.indexOf(listItem);
    return index;
  }

  /**
   * 层级选中
   * @param {Number} index
   * @returns
   */
  #layerSelectIndex(index) {
    this.#prevSelectIndex = this.selectIndex;
    if (index === this.selectIndex) {
      this.selectIndex = undefined;
    } else {
      this.selectIndex = index;
    }
    this.#runAnimation();
  }

  /**
   * 轮播选中
   * @param {Number} index
   * @returns
   */
  #carouselSelectIndex(index) {
    if (index === this.selectIndex) {
      this.#prevSelectIndex = this.selectIndex;
      return;
    }
    this.#prevSelectIndex = this.selectIndex || 0;
    this.selectIndex = index;
    this.#runAnimation();
  }

  /**
   * 阵列选中
   * @param {Number} index
   * @returns
   */
  #arraySelectIndex(index) {
    if (index === this.selectIndex) {
      this.#prevSelectIndex = this.selectIndex;
      return;
    }
    this.#prevSelectIndex = this.selectIndex || 0;
    this.selectIndex = index;
  }

  /**
   * 绑定事件
   *
   * @param {String} event 事件类型
   * @param {Function} handler 事件回调
   */
  #bind(event, handler) {
    A3D.DOM.bind(this, event, handler);
  }

  /**
   * 解绑事件
   *
   * @param {String} event 事件类型
   * @param {Function} handler 事件回调
   */
  #unbind(event, handler) {
    A3D.DOM.unbind(this, event, handler);
  }

  /**
   * 布局变更回调
   */
  #onChange() {
    this.#needsUpdate = true;
    onChange(this, () => this.#runAnimation(), (data, percent) => this.#onLayout(data, percent));
  }

  /**
   * 根据布局类型，获取布局状态
   */
  #getAnimationState() {
    let parameters = this.parameters;
    let endLayout = parameters.layoutGroup[parameters.layoutMode](this.count, this);
    if (Array.isArray(endLayout[0])) {
      return endLayout;
    }
    let startLayout = getStartLayout(endLayout, this.count, this, MESH_ATTRS);
    return [startLayout, endLayout];
  }

  /**
   * 当布局发生变化时自动计算布局
   *
   * @param {Array<JSON>} states 布局状态
   * @param {Number} percent 进度
   * @returns
   */
  #onLayout(states, percent) {
    let layoutData = getLayoutData(states, percent, this);
    if (layoutData) {
      this.set((i) => layoutData[i], 0);
    }
  }

  /**
   * 阵列布局
   *
   * @param {Number} length
   * @param {this} self
   * @returns {Array<JSON>}
   */
  #arrayLayout(length, self) {
    let geoSize = self.#getGeoSize();
    return arrayLayout(length, self.parameters, geoSize);
  }

  /**
   * 层级布局
   *
   * @param {Number} length
   * @param {this} self
   * @returns {Array<JSON>}
   */
  #layerLayout(length, self) {
    let geoSize = self.#getGeoSize();
    if (!isNaN(self.#prevSelectIndex)) {
      let temp = geoSize[self.#prevSelectIndex].height;
      geoSize[self.#prevSelectIndex].height = geoSize[self.#prevSelectIndex].depth;
      geoSize[self.#prevSelectIndex].depth = temp;
    }
    if (isNaN(self.selectIndex)) {
      return layerLayout(length, self.parameters, geoSize);
    }
    let res = layerSelectLayout(length, self.parameters, geoSize, self.selectIndex);
    self.#prevSelectIndex = self.selectIndex;
    return res;
  }

  /**
   * 轮播布局
   *
   * @param {Number} length
   * @param {this} self
   * @returns {Array<JSON>}
   */
  #carouselLayout(length, self) {
    let parameters = self.parameters;
    let listItemGroup = self.children[0];
    if (isNaN(self.selectIndex) || self.#prevSelectIndex === self.selectIndex) {
      let selectIndex = self.#prevSelectIndex || 0;
      return carouselInitLayout(length, parameters, selectIndex, listItemGroup);
    }
    let res = carouselSelectLayout(length, parameters, self.selectIndex, self.#prevSelectIndex, listItemGroup);
    self.#prevSelectIndex = self.selectIndex;
    return res;
  }

  /**
   * 当布局发生变化时自动计算布局并触发动画
   */
  #runAnimation() {
    let states = this.#getAnimationState();
    runAnimation(this, this.parameters.animationOption, states, (percent) => this.#onLayout(states, percent));
  }

  /**
   * 添加单个节点
   *
   * @param {JSON} data
   * @param {Object3D} model
   */
  #addListItem(data, model) {
    let _model;
    if (model) {
      _model = model;
    } else {
      let models = this.parameters.models;
      if (models) {
        _model = models[this.count]
        || models[this.count - 1]?.clone()
        || this.getListItem(this.count - 1)?.children[0].clone() || getDefaultModel();
      } else {
        _model = this.getListItem(this.count - 1)?.children[0].clone() || getDefaultModel();
      }
    }
    let listItem = new ListItem({
      index: this.count++,
      data,
      onChangeCallback: this.parameters.onChangeCallback,
      model: _model,
    });
    this.children[0].add(listItem);
  }

  /**
   * 计算每个 listItem 的包围盒大小
   */
  #getGeoSize() {
    if (!this.#geoSize) {
      this.#geoSize = [];
    }
    let listItemGroup = this.children[0];
    listItemGroup.children.forEach((item, index) => {
      this.#geoSize[index] = item.getBoundingBox();
    });
    return this.#geoSize;
  }
}
