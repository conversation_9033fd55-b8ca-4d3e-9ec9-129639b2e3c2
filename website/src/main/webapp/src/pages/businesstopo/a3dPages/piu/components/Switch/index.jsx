import React, {useEffect, useState, useRef} from 'react';
import { Tooltip } from 'eview-ui';

const Switch = (props) => {
  const {
    solutionDataList = [],
    selectIndex = 0,
    onClick = (index) => {
      // 空方法
    },
  } = props;

  const switchRef = useRef(null); // 用来引用模态框
  const [show, setShow] = useState(false);

  useEffect(() => {
    // 定义事件处理函数
    const handleClick = (event) => {
      if (switchRef.current && !switchRef.current.contains(event.target)) {
        // 如果点击不在模态框内，关闭模态框
        setShow(false);
      }
    };

    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []); // 空依赖数组确保该效果只在组件挂载和卸载时执行

  return (
    <div className="switch" ref={switchRef}>
      <div className="switch_icon" onClick={() => setShow(!show)} />
      {show && (
        <div className="switch_content" style={{height: `${40 * solutionDataList.length}px`}}>
          {solutionDataList.map((child, index) => (
            <div
              key={index}
              className={index === selectIndex ? 'switch_content_item_selected' : 'switch_content_item'}
              onClick={() => {
                setShow(false);
                onClick?.(index);
              }}
            >
              <Tooltip content={child.solutionName} trigger={['hover', 'focus']}>
                  <span
                    style={{
                      display: 'inline-block',
                      maxWidth: '100px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {child.solutionName}
                  </span>
              </Tooltip>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};


export default Switch;
