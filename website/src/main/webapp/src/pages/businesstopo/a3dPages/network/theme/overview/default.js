import { baseUrl } from '@pages/businesstopo/a3dPages/global';

export default {
  ':scene': {
    matType: 'Basic',
    setting: { color: '#171719' },
  },

  '4G-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/4G/normal/4G-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/4G/normal/4G-emissiveMap.png`,
      },
    },
  },
  '4G-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/4G/alarm/4G-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/4G/alarm/4G-emissiveMap.png`,
      },
    },
  },
  '5G-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/5G/normal/5G-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/5G/normal/5G-emissiveMap.png`,
      },
    },
  },
  '5G-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/5G/alarm/5G-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/5G/alarm/5G-emissiveMap.png`,
      },
    },
  },
  'smpp-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/smpp/normal/smpp-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/smpp/normal/smpp-emissiveMap.png`,
      },
    },
  },
  'smpp-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/smpp/alarm/smpp-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/smpp/alarm/smpp-emissiveMap.png`,
      },
    },
  },
  'universal-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-emissiveMap.png`,
      },
    },
  },
  'universal-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-emissiveMap.png`,
      },
    },
  },
  ring: {
    matType: 'Lambert',
    setting: {
      color: '#303030', transparent: true, opacity: 0.8, depthWrite: false,
    },
  },
  'normal-circle': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/normal-circle.png`,
      },
    },
  },
  'alarm-circle': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/alarm-circle.png`,
      },
    },
  },

  'sites-ground-line': {
    matType: 'Basic',
    setting: {
      color: '#215ea2',
      opacity: 0.5,
      transparent: true,
      depthWrite: false,
      side: 2,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 2,
        repeatY: 1,
      },
    },
  },
  'sites-ground': {
    matType: 'Basic',
    setting: {
      color: '#2a2a2c',
      transparent: true,
      depthWrite: false,
    },
  },
  'site-ground': {
    matType: 'Lambert',
    setting: { color: '#404044' },
  },
  'site-ground-hover': {
    matType: 'Lambert',
    setting: { color: '#505054' },
  },
  'site-ground-select': {
    matType: 'Basic',
    setting: { color: '#2177ce' },
  },
  'site-group-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/normal/group-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/normal/group-emissiveMap.png`,
      },
    },
  },
  'site-group-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/alarm/group-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/alarm/group-emissiveMap.png`,
      },
    },
  },
  'site-group-gray': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/gray/group-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/gray/group-emissiveMap.png`,
      },
    },
  },
};
