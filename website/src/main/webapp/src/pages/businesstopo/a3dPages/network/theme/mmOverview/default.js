import { baseUrl } from '@pages/businesstopo/a3dPages/global';
import { getA3D } from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';

const A3D = getA3D();

export default {
  ':scene': {
    matType: 'Basic',
    setting: { color: '#171719' },
  },

  ground: {
    matType: 'Lambert',
    setting: {
      color: '#202123',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'block-ground': {
    matType: 'Basic',
    setting: {
      color: '#282b2e',
      opacity: 1,
      transparent: true,
      depthWrite: false,
    },
  },
  'business-ground': {
    matType: 'Basic',
    setting: {
      color: '#383d46',
      opacity: 0.6,
      transparent: true,
      depthWrite: false,
    },
  },
  'business-ground-select': {
    matType: 'Basic',
    setting: {
      color: '#3a6eab',
      opacity: 0.2,
      transparent: true,
      depthWrite: false,
      blending: A3D.C.AdditiveBlending,
    },
  },
  'block-ground-select': {
    matType: 'Basic',
    setting: {
      color: '#20293b',
      opacity: 0.4,
      transparent: true,
      depthWrite: false,
      blending: A3D.C.AdditiveBlending,
    },
  },
  'block-ground-line': {
    matType: 'Basic',
    setting: {
      color: '#4b7eb4',
      transparent: true,
      depthWrite: false,
      opacity: 0.4,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 3,
        repeatY: 1,
      },
    },
  },
  'block-ground-line-select': {
    matType: 'Basic',
    setting: {
      color: '#4b7eb4',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 3,
        repeatY: 1,
      },
    },
  },

  'universal-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-emissiveMap.png`,
      },
    },
  },

  'universal-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-emissiveMap.png`,
      },
    },
  },

  'universal-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      opacity: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/alarm/universal-emissiveMap.png`,
      },
    },
  },


  'universal-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      opacity: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/universal/normal/universal-emissiveMap.png`,
      },
    },
  },

  'app-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-emissiveMap.png`,
      },
    },
  },
  'app-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-emissiveMap.png`,
      },
    },
  },
  'normal-circle': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/normal-circle.png`,
      },
    },
  },
  'alarm-circle': {
    matType: 'Sprite',
    setting: {
      color: '#fff',
      transparent: true,
      depthWrite: false,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/alarm-circle.png`,
      },
    },
  },
  'app-normal-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/normal/app-emissiveMap.png`,
      },
    },
  },
  'app-alarm-unselect': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0,
      transparent: true,
      opacity: 0.4,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/app/alarm/app-emissiveMap.png`,
      },
    },
  },
  'site-group-normal': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/normal/group-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/normal/group-emissiveMap.png`,
      },
    },
  },
  'ground-line': {
    matType: 'Basic',
    setting: {
      color: '#215ea2',
      opacity: 0.5,
      transparent: true,
      depthWrite: false,
      side: 2,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/lineColorMap.png`,
        repeatX: 2,
        repeatY: 1,
      },
    },
  },
  'ground-click': {
    matType: 'Lambert',
    setting: { color: '#215ea2' },
  },

  'site-group-alarm': {
    matType: 'Standard',
    setting: {
      color: '#ffffff',
      metalness: 0.4,
      transparent: true,
      emissive: '#FFFFFF',
      roughness: 1,
      map: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/alarm/group-color.png`,
      },
      emissiveMap: {
        isMap: true,
        url: `${baseUrl}w3d/theme/images/group/alarm/group-emissiveMap.png`,
      },
    },
  },
};
