import React, { useEffect, useState } from 'react';
import './index.less';
import { alarmData, statusEnum, eventData } from './Data';
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/statusCard';
registerResource(i18n, 'control2D');

function StatusCard() {
  const [statusData, selectedStatusData] = useState([]);
  const [selectedStatus, setIndex] = useState('all');
  const [type, setType] = useState('alarm');
  const list = type === 'alarm' ? alarmData : eventData;
  useEffect(() => {
    let newData = list;
    if (selectedStatus !== 'all') {
      newData = list.filter((item) => item.status === selectedStatus);
    }
    selectedStatusData(newData);
  }, [selectedStatus]);
  const seachLength = (status) => {
    if (status !== 'all') {
      const newData = list.filter((item) => item.status === status);
      return newData?.length || 0;
    }
    return list?.length || 0;
  };
  useEffect(() => {
    setIndex('all');
    if (type === 'alarm') {
      selectedStatusData(alarmData);
    } else {
      selectedStatusData(eventData);
    }
  }, [type]);
  return (
    <div className="statusCard">
      <div className="statusCard_tabs">
        <span onClick={() => setType('alarm')} className={type === 'alarm' ? 'tip active' : 'tip'}>
          {getMessage('control2D.tabs.alarm')}
          <span className='line' />
        </span>
        <span onClick={() => setType('events')} className={type === 'events' ? 'tip active' : 'tip'}>
          {getMessage('control2D.tabs.event')}
          <span className='line' />
        </span>
      </div>
      <div className="statusCard_tag">
        {
          statusEnum.map((item, index) => (
            <span key={index}
              className={`status_tag ${item.en === selectedStatus ? 'active' : ''}`}
              onClick={() => {
                setIndex(item.en);
              }}
            >
              {item.zh}
                (
              {seachLength(item.en)}
                )
            </span>
          ))
        }
      </div>
      {
        statusData?.map((item, index) => (
          <div className={`statusCard_item ${item.status}`} key={index}>
            <span className="tag">
              {statusEnum.filter((ele) => ele.en === item.status)[0].zh}
            </span>
            <div className="title">
              {' '}
              <span className="icon" />
              <span className="text">
                {item.title}
              </span>
            </div>
            <div className="desc">
              {item.describe}
            </div>
            <div className="time">
              <span />
              {item.time}
            </div>
          </div>
        ))
      }
    </div>
  );
}
export default StatusCard;
