import './index.less';
import database from '@pages/businesstopo/a3dPages/assets/image/svg/database.svg';
import databaseActive from '@pages/businesstopo/a3dPages/assets/image/svg/database_active.svg';
import overview from '@pages/businesstopo/a3dPages/assets/image/svg/overview.svg';
import overviewActive from '@pages/businesstopo/a3dPages/assets/image/svg/overview_active.svg';
import React, { useState, useEffect } from 'react';
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/floatPanel';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import { toDatabasePage, toOverviewPage } from '../../utils';

registerResource(i18n, 'control3D');
const data = [
  {
    name: getMessage('float.overview'), value: 'overview', icon: overview, iconAction: overviewActive,
  },
  {
    name: getMessage('float.database'),
    value: 'database',
    icon: database,
    iconAction: databaseActive,
    children: [
      { name: getMessage('float.memory'), value: 'memory' },
      { name: getMessage('float.physical'), value: 'physic' },
    ],
  }];

const switchPage = (paths, setPath, value) => {
  if (paths === value) {
    return;
  }
  if (value && value !== paths) {
    if (value === 'overview') {
      toOverviewPage();
    } else {
      toDatabasePage();
    }
    setPath(value);
  }
};

function FloatPanel() {
  const [databaseIndex, setDatabaseIndex] = useState(0);
  const [paths, setPath] = useState('overview');
  useEffect(() => {
    const hasDatabase = window.location.hash.includes('database');
    const path = hasDatabase ? 'database' : 'overview';
    setPath(path);
  }, [window.location.href]);
  const switchDatabase = (index, type) => {
    setDatabaseIndex(index);
    eventBus.emit('to3d_changeLib', type);
  };
  return (
    <div className="floatPanel">
      <div className="floatPanel_left floatPanel_card">
        {data.map((item, index) => (
          <div
            id={item.value}
            className={paths === item.value ? 'floatPanel_item active' : 'floatPanel_item'}
            onClick={() => switchPage(paths, setPath, item.value)}
            key={index}
          >
            <span className="icon">
              <img src={paths === item.value ? item.iconAction : item.icon} />
            </span>
            <span className={paths === item.value ? 'active' : ''}>
              {item.name}
            </span>
          </div>
        ))}
      </div>
      {paths === 'database' ? (
        <div className='floatPanel_right floatPanel_card'>
          {data[1].children.map((item, index) => (
            <span
              className={databaseIndex === index ? 'active' : ''}
              onClick={() => {
                switchDatabase(index, item.value);
              }}
              key={index}
            >
              {' '}
              {item.name}
            </span>
          ))}
        </div>
      ) : null}
    </div>
  );
}
export default FloatPanel;
