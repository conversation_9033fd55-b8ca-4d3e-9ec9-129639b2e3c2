import {baseUrl} from '@pages/businesstopo/a3dPages/global';
import defaultTheme from '@pages/businesstopo/a3dPages/network/theme/incidentView/default';
import {resetTheme} from '@pages/businesstopo/a3dPages/w3d/scenes/incidentView/handler/theme';
import {
  ComponentManager, createGui, offEvent, onEvent, W3D,
} from '@pages/businesstopo/a3dPages/w3d/utils';
import {getA3D} from '@pages/businesstopo/a3dPages/w3d/utils/attachCore';
import {initDefaultView4Scene} from '@pages/businesstopo/a3dPages/w3d/utils/manager/component/helper/incidentView';
import HoverHandler from '@pages/businesstopo/a3dPages/w3d/scenes/commonHandler/HoverHandler';
import SelectHandler from '@pages/businesstopo/a3dPages/w3d/scenes/commonHandler/SelectHandler';

const A3D = getA3D();

const cameraSetting = {
  fov: 30, // 相机广角，常规值为0 ~ 90
  lat: Math.PI * 0.2, // 相机相对焦点的维度，常规值为 -0.5*Pi ~ 0.5*Pi
  lon: 0, // 相机相对焦点的经度，常规值为 0 ~ 2*Pi
  distance: 108, // 相机相对焦点的距离，配合经纬度方便定义相机位置
  minDistance: 10, // 缩放过程中相机与焦点的最小距离，决定了最大的放大程度
  maxDistance: 120, // 缩放过程中相机与焦点的最大距离，决定了最大的缩小程度
  near: 0.1, // 能被相机看到的最近距离
  far: 500, // 能被相机看到的最远距离
  target: { // 相机焦点
    x: 8,
    y: 2,
    z: -8,
  },
  animate: false,
  resetSetCameraByMesh: true,
};

const resource = {
  modelsUrl: {
    site: `${baseUrl}w3d/models/group.obj`,
    app: `${baseUrl}w3d/models/app.obj`,
    database: `${baseUrl}w3d/models/database.obj`,
    vm: `${baseUrl}w3d/models/vm.obj`,
    pod: `${baseUrl}w3d/models/pod.obj`,
  },
  textureUrl: {
    alarmLayerBg: `${baseUrl}images/incident/alarm_layer_bg.png`,
    alarmSelectedBg: `${baseUrl}images/incident/alarm_selected_bg.png`,
    alarmItemSelected: `${baseUrl}images/incident/alarm_selected.png`,
    alarmItemUnSelected: `${baseUrl}images/incident/alarm_unSelected.png`,
  },
};

export default class IncidentViewScene {
  resize = null;

  constructor(eventBusHandler) {
    this.eventBusHandler = eventBusHandler;
    this.w3d = null;
    this.componentManager = null;
    this.#initEventHandlers();
    this.#initEvents();
  }

  async init(wrapper, routeQuery, data) {
    if (this.w3d) {
      this.w3d.destroy();
    }
    // 初始化w3d
    this.w3d = this.#initW3d(wrapper, routeQuery);
    this.w3d.agent.env.raycaster.params.Line2 = {threshold: 0.2};
    // 监听事件
    onEvent(this.eventBusHandler, this.eventHandlers);

    this.selectHandler1 = new SelectHandler(this.w3d.agent);
    this.selectHandler2 = new SelectHandler(this.w3d.agent);
    this.hoverHandler = new HoverHandler(this.w3d.agent, this.selectHandler1, this.selectHandler2);

    // 创建组件管理器
    this.componentManager = new ComponentManager(this.w3d, resource, this.events);
    // 设置卡片告警态。无配置show情况下，卡片告警态显示，非告警态隐藏
    this.componentManager.cardManager.setWarnStatus({status: ['alarm']});
    // 加载资源
    await this.componentManager.loadResource();
    // resize
    this.resize = () => {
      this.w3d.resize();
    };
    // 初始化灯光
    this.#initLights();
    // 初始化材质管理器
    await this.#initTheme(data);
    // 根据静态数据，创建组件
    this.update(data);
    this.resize();
    // 显示GUI
    if (routeQuery.showGui) {
      createGui(this.w3d.gui, this.w3d);
    }

    window.addEventListener('resize', this.resize);
    this.#bindAgentEventListener();
  }

  update(updateData) {
    Object.keys(updateData).forEach(sceneKey => {
      this.componentManager.updateComp(sceneKey, updateData[sceneKey]);
    });
  }

  destroy() {
    window.removeEventListener('resize', this.resize);
    offEvent(this.eventBusHandler, this.eventHandlers);
    this.w3d.destroy();
  }

  // 初始化监听事件
  #initEventHandlers() {
    const events1 = this.#getEventsHandlers1();
    this.eventHandlers = [
      {name: 'to3d_zoomIn', callback: () => this.w3d.zoomIn()},
      {name: 'to3d_zoomOut', callback: () => this.w3d.zoomOut()},
      {name: 'to3d_resetCamera', callback: () => this.w3d.resetCamera()},
      {name: 'to3d_switchTheme', callback: (e) => this.w3d.themeManager.switch(e)},
      ...events1,
    ];
  }

  #getEventsHandlers1() {
    return [
      {
        name: 'to3d_updatePodDrillDownData',
        callback: (podData) => {
          const incidentView = this.componentManager.comps.incidentView;
          if (incidentView) {
            incidentView.updateStatus(podData);
            initDefaultView4Scene(incidentView, this.w3d, this.componentManager);
            this.w3d.agent.renderOnce();
          }
        },
      },
      {
        name: 'to3d_updateSelectedRelation',
        callback: (selectedRelation) => {
          const incidentView = this.componentManager.comps.incidentView;
          if (incidentView) {
            incidentView.updateSelected(selectedRelation);
          }
        },
      },
    ];
  }

  // 初始化模型、卡片的点击交互事件
  #initEvents() {
    this.events = {
      // 点击网元实例之外的位置时，取消选中状态
      selectNull: () => {
        const incidentView = this.componentManager.comps.incidentView;
        if (incidentView) {
          incidentView.select(null, null, null, null, null, () => {
            this.componentManager.events.showPanel({
              type: '',
              id: '',
            });
            // 只取消非站点实例的选中状态
            this.componentManager.events.selectObject(null);
          });
          this.w3d.agent.renderOnce();
        }
      },
      showPanel: (setting) => {
        this.eventBusHandler.emit('from3d_showPanel', setting);
      },
      // 鼠标放到拓扑模型上，出现卡片提示
      showCommonTip: (setting) => {
        this.eventBusHandler.emit('from3d_showCommonTip', setting);
      },
      // 通过 componentManager.events.showIncidentPanelInfo 主动调用
      showIncidentPanelInfo: (setting) => {
        this.eventBusHandler.emit('from3d_showIncidentPanelInfo', setting);
      },
      ...this.#getHoverEvents(),
    };
  }

  #getHoverEvents() {
    const events = {
      hoverInObject: (object) => {
        this.hoverHandler.hoverIn(object);
      },
      hoverOutObject: (object) => {
        this.hoverHandler.hoverOut(object);
      },
      // app、pod、vm只能选择一个
      selectObject: (object) => {
        this.selectHandler1.select(object);
      },
      // site只能选择一个，但不影响底下app、pod、vm的选择
      selectSite: (object) => {
        this.selectHandler2.select(object);
      },
    };
    return events;
  }

  #initW3d(wrapper, routeQuery) {
    if (routeQuery.isMM) {
      cameraSetting.distance = 70;
      cameraSetting.target.x = 3;
    }
    const w3d = new W3D(
      wrapper,
      routeQuery,
      { ...cameraSetting },
      {
        customize: false,
        maxPolarAngle: Math.PI * 0.99,
        enableRotate: false,
        screenSpacePanning: false,
      },
      {
        logarithmicDepthBuffer: true,
        // 若不使用outline，关闭后期处理可减少锯齿影响
        enableOutline: false,
        loopRender: true,
      },
    );
    const {MOUSE, TOUCH} = A3D.C;
    w3d.orbit.mouseButtons = {LEFT: MOUSE.PAN, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.ROTATE};
    w3d.orbit.touches = {ONE: TOUCH.PAN, TWO: TOUCH.DOLLY_ROTATE};
    return w3d;
  }

  #initLights() {
    let lightGroup = A3D.createGroup();
    lightGroup.name = 'lightGroup';
    this.w3d.agent.add(lightGroup);
    let dis = 50;
    this.w3d.agent.addLight('ambient', {parent: lightGroup, color: '#fff', intensity: 0.3});
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 1.2,
      x: 0,
      y: 0,
      z: 30,
    });
    this.w3d.agent.addLight('dir', {
      parent: lightGroup,
      color: '#ffffff',
      intensity: 1,
      x: 0,
      y: 50,
      z: 30,
      mapSize: 1024,
      near: 0.5,
      far: 500,
      left: -dis / 10,
      right: dis / 10,
      top: dis / 10,
      bottom: -dis / 10,
    });
  }

  async #initTheme(data) {
    delete defaultTheme[':scene'];
    const darkTheme = JSON.parse(JSON.stringify(defaultTheme));

    // 初始化材质管理器
    await resetTheme(defaultTheme, darkTheme, data, this.w3d);
    this.w3d.initThemeManager(defaultTheme, darkTheme);
    this.w3d.themeManager.switch('default');
  }

  #bindAgentEventListener = () => {
    this.w3d.agent.on('click', () => {
      this.events.selectNull();
    });
  };
}
