import {
  getDrillDownSiteData,
  getDrillDownGrayBasicInfo,
  getDrillDownSitePositionSetting,
} from '@pages/businesstopo/a3dPages/network/request';
import '@pages/businesstopo/a3dPages/styles/split.less';
import React, {
  useEffect, useRef, useState, useReducer,
} from 'react';
import Control3DPanel from '../common/Control3DPanel';
import {
  destroyW3D,
  initW3D,
  updateGray,
  updateSiteData,
} from './SiteDrillDown3D';
import { getQuery, toOverviewPage, toSiteDrillDownPage } from '../utils';

let _setSiteName;

const init = async(w3dContainerRef, siteId, groupId) => {
  const siteData = await getDrillDownSiteData(siteId);
  const positionSetting = await getDrillDownSitePositionSetting();
  _setSiteName(siteData.data.siteName);
  await initW3D(w3dContainerRef, {
    siteData: siteData.data,
    positionSetting,
    groupId,
    changeSite: async(data) => {
      toSiteDrillDownPage(data.siteId);

      // 合并代码时注释
      const _siteData = await getDrillDownSiteData(data.siteId);
      _setSiteName(_siteData.data.siteName);
      updateSiteData(_siteData.data);
      const _grayBasicInfo = await getDrillDownGrayBasicInfo(data.siteId);
      updateGray(_grayBasicInfo.data);
    },
  });
  const grayBasicInfo = await getDrillDownGrayBasicInfo(siteId);
  updateGray(grayBasicInfo.data);
};

function SiteDrillDown() {
  const [siteName, setSiteName] = useState('');
  _setSiteName = setSiteName;
  const w3dContainerRef = useRef();
  const query = getQuery();
  const siteId = query.siteId;
  const groupId = query.selectMold;
  useEffect(() => {
    init(w3dContainerRef, siteId, groupId);
    return () => destroyW3D();
  }, []);

  const onClick = () => {
    toOverviewPage();
  };
  return (
    <div className='rightPanelTest'>
      <div className='split_left split_content'>
        <div className='backTo' onClick={onClick}>
          {siteName}
        </div>
        <div className="topo">
          <div ref={w3dContainerRef} className="w3d_container" />
          <Control3DPanel />
        </div>
        <div className="timeLine">
          时间轴
        </div>
      </div>
    </div>
  );
}
export default SiteDrillDown;
