
import { registerResource, getMessage } from '@pages/businesstopo/a3dPages/commonUtil/intl.js';
import i18n from '@pages/businesstopo/a3dPages/locales/podDetails';
registerResource(i18n, 'control2D');
const getTime = (timestamp, type) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = addZero(date.getMonth() + 1);
  const day = addZero(date.getDate());
  const hours = addZero(date.getHours());
  const minutes = addZero(date.getMinutes());
  const seconds = addZero(date.getSeconds());
  if (type === 'year') {
    return `${year}-${month}-${day}`;
  } else if (type === 'time') {
    return `${hours}:${minutes}`;
  }
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

};
function addZero(value) {
  return value < 10 ? `0${value}` : value;
}
const today = '2024-01-17';
const getChartsData = (data) => {
  const dateJSON = JSON.parse(JSON.stringify(data));
  const newDate = [];
  let weekData = [];
  let dayData = [];
  let yesterdayData = [];
  for (const key in dateJSON) {
    if (Object.hasOwnProperty.call(dateJSON, key)) {
      const element = dateJSON[key];
      if (key === 'Other') {
        weekData = mapDataTime(element);
      } else {
        const day = getTime(Number(key), 'year');
        if (day === today) {
          dayData = mapDataTime(element);
        } else {
          yesterdayData = mapDataTime(element);
        }
      }
    }
  }
  for (let index = 0; index < dayData.length; index++) {
    const element = dayData[index];
    const time = element.timestampStr;
    const yesterday = yesterdayData.filter((item) => item.timestampStr === time)[0];
    const week = weekData.filter((item) => item.timestampStr === time)[0];
    newDate.push({
      time,
      [getMessage('control2D.charts.today')]: element.indexValue,
      [getMessage('control2D.charts.beforeDay')]: yesterday?.indexValue || undefined,
      [getMessage('control2D.charts.beforeWeek')]: week?.indexValue || undefined,
    });
  }
  return newDate;
};
const mapDataTime = (list) => {
  const defaultTime = list[0].timestampStr;
  list.map((item) => item.timestampStr = getTime(Number(item.timestampStr - defaultTime), 'time'));
  return list;
};
export { getTime, getChartsData };
