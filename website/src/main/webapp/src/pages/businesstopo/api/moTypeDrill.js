/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import {request} from '@util';
import {requestConfig} from '@pages/businesstopo/api/index';

const getMoTypeDrillDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/querymotypeviewgrid';

const getPodDeployDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/querypoddeploydata';

const getVMDeployDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/queryvmdeploydata';

const getPodIndicatorDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/querypodindicator';

const getAlarmEventDataUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryalarmdata';

const getVMIndicatorDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/queryvmindicator';

const queryTimeTracDataUrl = '/rest/dvtopowebsite/v1/business/topo/queryoverviewgridhistory';

const queryAppDataUrl = '/rest/dvtopowebsite/v1/business/topo/motype/queryclusterdeploydata';

const queryHostviewUrl = '/rest/dvtopowebsite/v1/business/topo/motype/queryhostview';

// 查询网元类型下钻数据
export const queryHostview = (setting, success, error) => {
  request.post(queryHostviewUrl, setting, success, error, false, requestConfig);
};

// 查询网元类型下钻数据
export const getMoTypeDrillData = (setting, success, error) => {
  request.post(getMoTypeDrillDataUrl, setting, success, error, false, requestConfig);
};

// 查询pod指标数据
export const getPodIndicatorData = (setting, success, error) => {
  request.post(getPodIndicatorDataUrl, setting, success, error, false);
};

// 查询告警/事件数据
export const getAlarmEventData = (setting, success, error) => {
  request.post(getAlarmEventDataUrl, setting, success, error, false);
};

// 查询pod的部署详情
export const getPodDeployData = (setting, success, error) => {
  request.post(getPodDeployDataUrl, setting, success, error, false);
};

// 查询VM部署
export const getVMDeployData = (setting, success, error) => {
  request.post(getVMDeployDataUrl, setting, success, error, false);
};

// VM指标数据和基础信息
export const getVMIndicatorData = (setting, success, error) => {
  request.post(getVMIndicatorDataUrl, setting, success, error, false);
};

// 时间回溯
export const queryTimeTrackData = (setting, success, error) => {
  request.post(queryTimeTracDataUrl, setting, success, error, false);
};

// 时间回溯
export const getAppData = (setting, success, error) => {
  request.post(queryAppDataUrl, setting, success, error, false);
};
