/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { request } from '@util';
import { requestConfig } from '@pages/businesstopo/api/index';

const getTimeUrl = '/rest/dvtopowebsite/v1/business/topo/common/querytimeline';

const getEventListUrl = '/rest/dvtopowebsite/v1/business/topo/common/querytimelinealarmdata';

const userauthenticateUrl = '/rest/dvtopowebsite/v1/business/topo/common/userauthenticate';

// 查询时间轴初始化数据
export const getTimeLineData = (param, success, error) => request.post(getTimeUrl, param, success, error, false, requestConfig);

// 查询事件列表
export const getEventList = (setting, success, error) => request.post(getEventListUrl, setting, success, error, false, requestConfig);


// 权限刷新
export const refreshUerAuthenticate = (success, error) => request.get(userauthenticateUrl, success, error, false, requestConfig);