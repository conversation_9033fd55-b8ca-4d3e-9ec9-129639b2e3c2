import { request } from '@util';

const solutionUrl = '/rest/dvtopowebsite/v1/business/topo/querysolution';
const businessUrl = '/rest/dvtopowebsite/v1/business/topo/querybusiness';
const siteDistritionUrl = '/rest/dvtopowebsite/v1/business/topo/queryindicatordistribution';
const siteHistoryUrl = '/rest/dvtopowebsite/v1/business/topo/queryindicatorhistory';
const alarmUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryalarmdata';
const alarmLevelUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryalarmofonelevel';
const alarmDetailUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryalarmdetail';
const overviewgridUrl = '/rest/dvtopowebsite/v1/business/topo/queryoverviewgrid';
const getGrayScaleUrl = '/rest/dvtopowebsite/v1/business/topo/motype/pipeline';
const getMoreAlarmUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryalarmbycsn';
const querySiteGridUrl = '/rest/dvtopowebsite/v1/business/topo/siteview/querysitedetailviewinfo';
const querysitealarmlinkinfoUrl = '/rest/dvtopowebsite/v1/business/topo/siteview/querysitealarmlinkinfo';
const queryDatabaseGridUrl = '/rest/dvtopowebsite/v1/business/topo/dbview/querydbviewinfo';
const queryDatabaseTipsUrl = '/rest/dvtopowebsite/v1/business/topo/dbview/queryswitchtime';
const queryLightNodeUrl = '/rest/dvtopowebsite/v1/business/topo/motype/grayscale';
const queryCurrentIdsUrl = '/rest/dvtopowebsite/v1/business/topo/common/queryinsofonemoment';
const saveConfigurationUrl = '/rest/dvtopowebsite/v1/business/topo/homePageConfiguration';
const querymmtopnconfigUrl = '/rest/dvtopowebsite/v1/business/topo/querymmtopnconfig';
const editmmtopnconfigUrl = '/rest/dvtopowebsite/v1/business/topo/editmmtopnconfig';
// 设置请求配置
export const requestConfig = {
  isScheduledTask: true, // 定时任务的标记
};


export const querymmtopnconfig = ( success, error) => request.get(`${querymmtopnconfigUrl}`, success, error, false, requestConfig);

export const editmmtopnconfig = (params, success, error) => request.post(editmmtopnconfigUrl, params, success, error, false);

// 根据业务id查询告警和事件
export const queryCurrentIds = (params, success, error) => request.post(queryCurrentIdsUrl, params, success, error, false);

// 查询所有业务数据
export const getSolutionData = (params, success, error) => request.get(`${solutionUrl}?timestamp=${params.timestamp}`, success, error, false, requestConfig);

// 查询所有业务数据
export const getAllBussinessData = (params, success, error) => request.get(`${businessUrl}?solutionId=${params.solutionId}&timestamp=${params.timestamp}`, success, error, false, requestConfig);

// 根据站点分布
export const getSiteDistritionData = (params, success, error) => request.post(siteDistritionUrl, params, success, error, false, requestConfig);

// 根据业务id和指标id查询折线图数据
export const getHistoryData = (params, success, error) => request.post(siteHistoryUrl, params, success, error, false, requestConfig);

// 根据业务id查询告警和事件
export const getAlarmData = (params, success, error) => request.post(alarmUrl, params, success, error, false, requestConfig);

// 根据modelType查询告警和事件
export const getAlarmLevelData = (params, success, error) => request.post(alarmLevelUrl, params, success, error, false, requestConfig);

// 根据业务id查询告警和事件
export const getAlarmDeatilData = (params, success, error) => request.post(alarmDetailUrl, params, success, error, false);

export const getOverViewGrid = (params, success, error) => request.post(overviewgridUrl, params, success, error, false, requestConfig);

// 获取总览视图容连接线数据
export const queryOverviewSiteLinkData = (params, success, error) => request.post(siteHistoryUrl, params, success, error, false);

export const getGrayScaleData = ( params, success, error) => request.post(`${getGrayScaleUrl}`, params, success, error, false, requestConfig);

export const updateGrayScaleData = (params, success, error) => request.post(`${getGrayScaleUrl}`, params, success, error, false, requestConfig);

export const getLightNode = (params, success, error) => request.post(`${queryLightNodeUrl}`, params, success, error, false, requestConfig);

export const getMoreAlarmData = (params, success, error) => request.post(getMoreAlarmUrl, params, success, error, false);

export const querySiteGridData = (params, success, error) => request.post(querySiteGridUrl, params, success, error, false, requestConfig);

export const querysitealarmlinkinfo = (params, success, error) => request.post(querysitealarmlinkinfoUrl, params, success, error, requestConfig);

export const queryDataBaseGridData = (params, success, error) => request.post(queryDatabaseGridUrl, params, success, error, false, requestConfig);

export const queryDataBaseTipsData = (params, success, error) => request.post(queryDatabaseTipsUrl, params, success, error, false);

export const saveConfiguration = (params, success, error) => request.post(saveConfigurationUrl, params, success, error, false);
