export const initState = {
  showRightPanel: true,
  showKPIPanel: true,
  showCharts: true,

  businessData: [],
  currentBusinessData: {},
  currentBusinessId: -1,
  goldIndicator: '',
  indicatorData: [],
  currentIndicator: {},
  siteDistributionData: [],
  siteHistoryData: [],
  kpiChartsShow: true,
  alarmShowIndex: 0,
  currentEvent: 'all',
  currentIndicatorIndex: 0,
  alarmEventData: [],
  siteAlarmCount: {
    critical: 0,
    major: 0,
    minor: 0,
    info: 0,
    total: 0,
  },
  sitePannelIsOpen: false,
  showSiteRightPanel: false,
  siteVMKpiCount: [],
  siteHistoryList: [],
  isTimeTrack: false,
  selectedTime: 0,
  topoData: {},
  historicalSnapshot: [],
  currentSiteName: '',

  currentSiteId: -1,
  currentMoData: {id: -1, name: '', podCount: 0, podErrorCount: 0},
  moHistoryData: [],
  moIndicatorData: [],
  moIndicatorIndex: 0,
  solutionId: '',
  stripId: '', // MM条带页面使用
  dataShow: true,
  dataDaseShow: false,
  forceDataBaseHide: false, // piu使用，强行隐藏数据库入口
  dataBasePannelIsOpen: false,
  showDataBaseRightPanel: false,
  podData: {id: -1},
  siteGroup: [],
  editAddPannelShow: false,
  editAddPannelStatus:1,
  editAddPannelKey:'',
  editRightPannelOpen: false,
  editRightPannelShow: true,
  editRightPanelSiteName: false,
  editRightPannelContent: {},
  netWorkTreeParam: {},
  unitTree: {
    measureType: 'all',
    measureUnit: '',
    IndicatorId: '',
  },
  objectTable: {
    isObjectNoData:true,
  },
  indicatorButtonDisable: true,
  solutionName:'',
  hasFiltered:false,
  podIdToSiteNameMap:[],
  isImporting:false,
  showDeleteInfo:false,
  solutionData:[],
  selectSolutionIndex:-1,
  showMessage:false,
  mmStripTimeLineFlag:0,
};

export const reducer = (state, action) => ({...state, ...action});
