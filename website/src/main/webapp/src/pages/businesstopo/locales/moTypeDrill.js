export default {
  'zh-cn': {
    'moType.drill.rightPanel.state': '运行状态',
    'moType.drill.rightPanel.host': '所属主机名',
    'moType.drill.rightPanel.site': '站点信息',
    'moType.drill.rightPanel.stripName': '条带名称',
    'moType.drill.rightPanel.appType': '应用类型',
    'moType.drill.rightPanel.ipAddress': 'IP地址',
    'moType.drill.rightPanel.ip': '容器IP',
    'moType.drill.rightPanel.hostIP': '所属主机IP',
    'moType.drill.rightPanel.time': '接入时间',
    'moType.drill.rightPanel.dockerName': 'Docker名称',
    'moType.drill.rightPanel.appName': '程序名称',
    'moType.drill.vm.tooltip.vmIp': '主机IP',
    'moType.drill.leftText.app': '管理业务',
    'moType.drill.leftText.mm.app': '应用集群',
    'moType.drill.leftText.pod': '业务pod',
    'moType.drill.leftText.pod.instance': '实例数',
    'moType.drill.leftText.unit': '个',
    'moType.drill.leftText.product': '生产区占比',
    'moType.drill.leftText.gray': '灰度占比',
    'moType.drill.pod.product': '生产区',
    'moType.drill.pod.gray': '灰度区',
    'moType.drill.pod.status.normal': '正常',
    'moType.drill.pod.status.abnormal': '异常',
    'moType.drill.pod.status.unknown': '未知',
    'moType.drill.vm.normal': '健康',
    'moType.drill.vm.num': '个',
    'moType.drill.vm.abnormal': '不健康',
    'moType.drill.vm.status': '运行状态',
    'moType.drill.status': '状态',
    'moType.drill.status.normal': '正常',
    'moType.drill.status.abnormal': '异常',
    'moType.drill.vm.healthstatus': '健康状态',
    'moType.drill.vm.memoryTotal': '内存总量',
    'moType.drill.vm.hostname': '主机名',
    'moType.drill.vm.host': '主机',
    'moType.drill.vm.memoryUsage': '内存使用率',
    'moType.drill.vm.podNum': '关联POD数',
    'moType.drill.vm.resourceUsage': '资源使用情况',
    'moType.drill.app.version': '版本',
    overViewName:'总览视图',
    'w3d.version':'版本',
    filtered_info:'用户授权导致部分数据无权查看',
  }, 'en-us': {
    'moType.drill.rightPanel.state': 'Running Status',
    'moType.drill.rightPanel.host': 'VM Name',
    'moType.drill.rightPanel.site': 'Site Name',
    'moType.drill.rightPanel.stripName': 'Strip Name',
    'moType.drill.rightPanel.appType': 'Application Type',

    'moType.drill.rightPanel.ipAddress': 'IP Address',
    'moType.drill.rightPanel.ip': 'Container IP',
    'moType.drill.rightPanel.hostIP': 'HOST IP',
    'moType.drill.rightPanel.time': 'Access Time',
    'moType.drill.rightPanel.dockerName': 'Docker Name',
    'moType.drill.rightPanel.appName': 'Application Name',
    'moType.drill.vm.tooltip.vmIp': 'HOST IP',
    'moType.drill.leftText.app': 'Manage Business',
    'moType.drill.leftText.mm.app': 'Application Cluster',
    'moType.drill.leftText.pod': 'Service Pod',
    'moType.drill.leftText.pod.instance': 'Instance Number',
    'moType.drill.leftText.unit': ' ',
    'moType.drill.leftText.product': 'Percentage of production areas',
    'moType.drill.leftText.gray': 'Percentage of gray areas',
    'moType.drill.pod.product': 'Production area',
    'moType.drill.pod.gray': 'Gray area',
    'moType.drill.pod.status.normal': 'Enabled',
    'moType.drill.pod.status.abnormal': 'Disabled',
    'moType.drill.pod.status.unknown': 'Unknown',
    'moType.drill.vm.normal': 'Healthy',
    'moType.drill.vm.num': 'num',
    'moType.drill.vm.abnormal': 'Unhealthy',
    'moType.drill.vm.status': 'Running Status',
    'moType.drill.status': 'Status',
    'moType.drill.status.normal': 'Normal',
    'moType.drill.status.abnormal': 'Abnormal',
    'moType.drill.vm.healthstatus': 'Health Status',
    'moType.drill.vm.memoryTotal': 'Total Memory',
    'moType.drill.vm.hostname': 'Hostname',
    'moType.drill.vm.host': 'Host',
    'moType.drill.vm.memoryUsage': 'Memory Usage',
    'moType.drill.vm.podNum': 'Number Of Associated PODs',
    'moType.drill.vm.resourceUsage': 'Resource Usage',
    'moType.drill.app.version': 'version',
    overViewName:'Over View',
    'w3d.version':'Version',
    filtered_info:'Some data cannot be viewed due to user authorization.',
  },
};
