import { $t, registerResource } from '@util';
import i18n from '../locales/moTypeDrill';

registerResource(i18n, 'moTypeDrillI18n');

export const STATUS = {
  normal: 'Enabled',
  failed: 'Disabled',
};

export const HEALTH_STATUS = {
  normal: 'Healthy',
  failed: 'Unhealthy',
};

export const STATUS_TEXT = {
  [STATUS.normal]: $t('moType.drill.pod.status.normal'),
  [STATUS.failed]: $t('moType.drill.pod.status.abnormal'),
};

export const STATUS_NEW_TEXT = {
  [STATUS.normal]: $t('moType.drill.status.normal'),
  [STATUS.failed]: $t('moType.drill.status.abnormal'),
};