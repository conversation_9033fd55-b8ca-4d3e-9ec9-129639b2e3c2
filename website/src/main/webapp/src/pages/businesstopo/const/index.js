/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext} from 'react';
import * as echarts from 'echarts';
import {$t} from '@util';

export const BUSINESS_TOPO_CONTEXT = React.createContext(null);

export const LINE_CHART_COLOR = [
  ['rgba(241,110,51)', 'rgba(241,110,51, 0.5)', 'rgba(241,110,51, 0.1)'],
  ['rgba(106,64,246)', 'rgba(106,64,246, 0.5)', 'rgba(106,64,246, 0.1)'],
  ['rgba(58,132,255)', 'rgba(58,132,255, 0.5)', 'rgba(58,132,255, 0.1)'],
  ['rgba(54,221,242)', 'rgba(54,221,242, 0.5)', 'rgba(54,221,242, 0.1)'],
  ['rgba(25,216,149)', 'rgba(25,216,149, 0.5)', 'rgba(25,216,149, 0.1)'],
  ['rgba(255,194,153)', 'rgba(255,194,153, 0.5)', 'rgba(255,194,153, 0.1)'],
  ['rgba(153,75,44)', 'rgba(153,75,44, 0.5)', 'rgba(153,75,44, 0.1)'],
  ['rgba(246,202,104)', 'rgba(246,202,104, 0.5)', 'rgba(246,202,104, 0.1)'],
  ['rgba(255,187,187)', 'rgba(255,187,187, 0.5)', 'rgba(255,187,187, 0.1)'],
  ['rgba(255, 0, 0)', 'rgba(255, 0, 0, 0.5)', 'rgba(255, 0, 0, 0.1)'],
  ['rgba(0, 255, 0)', 'rgba(0, 255, 0, 0.5)', 'rgba(0, 255, 0, 0.1)'],
  ['rgba(255, 255, 0)', 'rgba(255, 255, 0, 0.5)', 'rgba(255, 255, 0, 0.1)'],
  ['rgba(255, 0, 255)', 'rgba(255, 0, 255, 0.5)', 'rgba(255, 0, 255, 0.1)'],
  ['rgba(0, 128, 0)', 'rgba(0, 128, 0, 0.5)', 'rgba(0, 128, 0, 0.1)'],
  ['rgba(0, 0, 128)', 'rgba(0, 0, 128, 0.5)', 'rgba(0, 0, 128, 0.1)'],
  ['rgba(128, 128, 0)', 'rgba(128, 128, 0, 0.5)', 'rgba(128, 128, 0, 0.1)'],
  ['rgba(128, 0, 128)', 'rgba(128, 0, 128, 0.5)', 'rgba(128, 0, 128, 0.1)'],
  ['rgba(0, 128, 128)', 'rgba(0, 128, 128, 0.5)', 'rgba(0, 128, 128, 0.1)'],
  ['rgba(192, 192, 192)', 'rgba(192, 192, 192, 0.5)', 'rgba(192, 192, 192, 0.1)'],
  ['rgba(255, 20, 147)', 'rgba(255, 20, 147, 0.5)', 'rgba(255, 20, 147, 0.1)'],
  ['rgba(75, 0, 130)', 'rgba(75, 0, 130, 0.5)', 'rgba(75, 0, 130, 0.1)'],
  ['rgba(0, 0, 0)', 'rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 0.1)'],
];

export const refeshTime = 60000;

export const getHistorySeries = (siteHistoryData, isGoldInd, isVm) => {
  let tempData = getLineChartData(siteHistoryData, isGoldInd, isVm);

  let res = [];
  for (let i = 0; i < tempData.length; i++) {
    res.push({
      lineStyle: {
        normal: {
          width: 1,
        },
      },
      symbol: 'none',
      legendHoverLink: true,
      name: tempData[i].name,
      data: tempData[i].data,
      type: 'line',
      smooth: true,
      itemStyle: {
        normal: {
          color: LINE_CHART_COLOR[i][0],
        },
      },
      areaStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: LINE_CHART_COLOR[i][1],
              },
              {
                offset: 1,
                color: LINE_CHART_COLOR[i][2],
              },
            ],
            false,
          ),
        },
      },
    });
  }
  return res;
};

const getLineChartData = (siteHistoryData, isGoldInd, isVm) => {
  let tempData = [];
  if (!siteHistoryData || siteHistoryData.length === 0) {
    return tempData;
  }

  const timeObj = {
    Today: 0,
    Yesterday: 86400000,
    'Last week': 604800000,
  };
  const todayObj = {
    Today: 0,
  };
  let obj;

  if (siteHistoryData.length > 1) {
    obj = todayObj;
  } else {
    obj = timeObj;
  }
  siteHistoryData.map(item => {
    for (let propName in obj) {
      if (item.comparativeValueMap[propName]) {
        let name;
        if (isVm) {
          name = item.indexName;
        } else if (isGoldInd) {
          name = getName(propName);
        } else {
          if (siteHistoryData.length > 1) {
            name = `${item.siteName}`;
          } else {
            name = `${item.siteName}_${getName(propName)}`;
          }
        }
        if (item.moName) {
          name = `${item.moName}_${name}`;
        }
        if (item.podName) {
          if (siteHistoryData.length > 1) {
            name = `${item.podName}`;
          } else {
            name = `${item.podName}_${name}`;
          }
        }
        tempData.push({
          name,
          data: getHistoryIndexValueList(item.comparativeValueMap[propName], timeObj[propName]),
        });
      }
    }
  });
  return tempData;
};

export const getName = propName => {
  if (propName === 'Today') {
    return $t('today');
  } else if (propName === 'Yesterday') {
    return $t('yesterday');
  } else {
    return $t('lastweek');
  }
};
export const getHistoryIndexValueList = (list, time) =>
  list.map(item => ({
    value: [parseInt(item.timestampStr) + time, item.indexValue],
  }));

export const template = {
  assets: [

    {
      type: 2,
      name: 'site_node_red_mtl',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: 'site_node_color_red_img',
        emissive: '0xffffff',
        emissiveMap: 'site_node_alpha_red_img',
        roughness: 1,
        metalness: 0,
      },
    },
    {
      name: 'site_node_alpha_red_img',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/site_red_light.png',
      },
    },
    {
      name: 'site_node_color_red_img',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/site_red.png',
      },
    },
    {
      type: 4,
      name: 'site_node_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/site.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: 'north_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/north.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: '5G_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/5G.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: '4G_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/4G.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: '4G_voice_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/4G_voice.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: 'smpp_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/smpp.obj',
        public: true,
      },
    },
    {
      type: 4,
      name: 'smpp+_obj',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/smpp+.obj',
        public: true,
      },
    },
    {
      name: 'site_node_alpha_img',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/site_alpha.png',
      },
    },
    {
      name: 'site_node_color_img',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/site_color_1.png',
      },
    },
    {
      name: 'north_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/north_alpha.png',
      },
    },
    {
      name: '4G_voice_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/4G_voice_alpha.png',
      },
    },
    {
      name: '5G_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/5G_alpha.png',
      },
    },
    {
      name: '4G_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/4G_alpha.png',
      },
    },
    {
      name: 'smpp_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/smpp_alpha.png',
      },
    },
    {
      name: 'smpp+_alpha',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/smpp+_alpha.png',
      },
    },
    {
      name: 'north_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/north_color.png',
      },
    },
    {
      name: '5G_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/5G_color.png',
      },
    },
    {
      name: '4G_voice_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/4G_voice_color.png',
      },
    },
    {
      name: '4G_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/4G_color.png',
      },
    },
    {
      name: 'smpp_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/smpp_color.png',
      },
    },
    {
      name: 'smpp+_color',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/smpp+_color.png',
      },
    },
    {
      type: 2,
      name: 'site_node_mtl',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: 'site_node_color_img',
        emissive: '0xffffff',
        emissiveMap: 'site_node_alpha_img',
        metalness: 0.35,
        roughness: 1.0,
      },
    },
    {
      type: 2,
      name: 'north_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: 'north_color',
        emissive: '0xffffff',
        emissiveMap: 'north_alpha',
        transparent: true,
      },
    },
    {
      type: 2,
      name: '4G_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: '4G_color',
        emissive: '0xffffff',
        emissiveMap: '4G_alpha',
        transparent: true,
      },
    },
    {
      type: 2,
      name: '5G_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: '5G_color',
        emissive: '0xffffff',
        emissiveMap: '5G_alpha',
        transparent: true,
      },
    },
    {
      type: 2,
      name: '4G_voice_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: '4G_voice_color',
        emissive: '0xffffff',
        emissiveMap: '4G_voice_alpha',
        transparent: true,
      },
    },
    {
      type: 2,
      name: 'smpp_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: 'smpp_color',
        emissive: '0xffffff',
        emissiveMap: 'smpp_alpha',
        transparent: true,
      },
    },
    {
      type: 2,
      name: 'smpp+_mat',
      data: {
        type: 'MeshStandardMaterial',
        color: '0xffffff',
        map: 'smpp+_color',
        emissive: '0xffffff',
        emissiveMap: 'smpp+_alpha',
        transparent: true,
      },
    },
    {
      type: 1,
      name: 'plate_color',
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/plate_color.jpg',
        flipY: true,
      },
    },
    {
      type: 2,
      name: 'plate_mtl',
      data: {
        type: 'MeshStandardMaterial',
        transparent: false,
        side: 0,
        color: '0x555555',
        map: 'plate_color',
        metalness: 0.0,
        roughness: 1.0,
      },
    },
    {
      type: 4,
      name: 'plate_geo',
      data: {
        url: '/dvtopowebsite/businesstopo/assets/models/dvView/plate.obj',
        public: true,
      },
    },
    {
      type: 3,
      name: 'top_plane_geo',
      data: {
        type: 'CylinderBufferGeometry',
        radiusTop: 150,
        radiusBottom: 150,
        height: 0.15,
        radialSegments: 128,
        heightSegments: 1,
      },
    },
    {
      type: 3,
      name: 'plane_geo',
      data: {
        type: 'CylinderBufferGeometry',
        radiusTop: 175,
        radiusBottom: 175,
        height: 0.15,
        radialSegments: 128,
        heightSegments: 1,
      },
    },
    {
      type: 3,
      name: 'site_group_geo',
      data: {
        type: 'PlaneBufferGeometry',
        width: 550,
        height: 280,
        widthSegments: 1,
        heightSegments: 1,
      },
    },
    {
      type: 3,
      name: 'site_outline_geo',
      data: {
        type: 'PlaneBufferGeometry',
        width: 175,
        height: 125,
        widthSegments: 1,
        heightSegments: 1,
      },
    },
    {
      name: 'site_outline_tex',
      type: 1,
      data: {
        image: '/dvtopowebsite/businesstopo/assets/images/dvView/plate_blue.png',
      },
    },
    {
      type: 2,
      name: 'site_outline_mtl',
      data: {
        type: 'MeshStandardMaterial',
        map: 'site_outline_tex',
        color: '0x2774CE',
        transparent: true,
        opacity: 0,
      },
    },
    {
      type: 2,
      name: 'top_plane_mat',
      data: {
        type: 'DvColorGradientMaterial',
        innerColor: '#171717',
        outerColor: '#686F69',
        opacity: 0.8,
        innerRadius: 0,
        radius: 150,
      },
    },
    {
      type: 2,
      name: 'plane_mat',
      data: {
        type: 'DvColorGradientMaterial',
        innerColor: '#232323',
        outerColor: '#585F69',
        opacity: 0.8,
        innerRadius: 0,
        radius: 175,
        transparent: true,
      },
    },
    {
      type: 2,
      name: 'site_group_mtl',
      data: {
        type: 'MeshStandardMaterial',
        color: '0x262626',
        roughness: 1.0,
        metalness: 0.5,
        transparent: false,
      },
    },
    {
      type: 0,
      name: 'plane',
      data: {
        type: 'Group',
        properties: {
          name: 'Group',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            -0.41,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'CircleRotationLayer',
              properties: {
                enable: true,
                radius: 190,
                keepPosition: true,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'plane',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  0,
                  35,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                scale: [
                  1.15,
                  1,
                  1.15,
                ],
                geometry: 'plane_geo',
                material: 'plane_mat',
              },
            },
            {
              type: 'PlaneText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  -248,
                  -12,
                  45,
                ],
                rotation: [
                  -0.01,
                  0,
                  0,
                ],
                scale: [
                  0.17,
                  0.17,
                  0.17,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'Microsoft YaHei, arial, tahoma, helvetica, sans-serif',
                fillColor: 16119285,
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'topPlane',
      data: {
        type: 'Group',
        properties: {
          name: 'Group',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'CircleRotationLayer',
              properties: {
                enable: true,
                radius: 155,
                keepPosition: true,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'plane',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  0,
                  50,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                scale: [
                  1.0,
                  1.0,
                  1.0,
                ],
                geometry: 'top_plane_geo',
                material: 'top_plane_mat',
              },
            },
            {
              type: 'PlaneText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  -190,
                  0,
                  70,
                ],
                rotation: [
                  -0.4,
                  0,
                  0,
                ],
                scale: [
                  0.17,
                  0.17,
                  0.17,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'Microsoft YaHei, arial, tahoma, helvetica, sans-serif',
                fillColor: 16119285,
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      name: 'centerLayer',
      type: 0,
      data: {
        type: 'Group',
        properties: {
          name: 'Group',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0.5,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1.0,
            1.0,
            1.0,
          ],
          components: [],
          attachments: [],
        },
      },
    },
    {
      name: 'siteGroup',
      type: 0,
      data: {
        type: 'Group',
        properties: {
          name: 'siteGroup',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1.0,
            1.0,
            1.0,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'site_group_plane',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  0,
                  0,
                ],
                rotation: [
                  -1.57,
                  0,
                  0,
                  'XYZ',
                ],
                scale: [
                  1,
                  1,
                  1,
                ],
                material: 'site_group_mtl',
                geometry: 'site_group_geo',
                components: [],
              },
            },
            {
              type: 'PlaneText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  -295,
                  0,
                  0,
                ],
                rotation: [
                  -0.8,
                  0,
                  0,
                ],
                scale: [
                  0.18,
                  0.18,
                  0.18,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'Microsoft YaHei, arial, tahoma, helvetica, sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'site',
      data: {
        type: 'Group',
        properties: {
          name: 'site',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'group_floor_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  3,
                  0,
                ],
                rotation: [
                  0,
                  3.1415926,
                  0,
                  'XYZ',
                ],
                scale: [
                  0.175,
                  0.15,
                  0.125,
                ],
                material: 'plate_mtl',
                geometry: 'plate_geo@geo@???',
                components: [
                  {
                    type: 'BorderEffect',
                    properties: {
                      enabled: true,
                      width: 2,
                      color: '#2774CE',
                    },
                  },
                  {
                    type: 'HighlightEffect',
                    properties: {
                      enabled: true,
                      color: '#616166',
                    },
                  },
                ],
              },
            },
            {
              type: 'Mesh',
              properties: {
                name: 'site_outline_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  5,
                  0,
                ],
                rotation: [
                  -1.57,
                  0,
                  0,
                  'XYZ',
                ],
                scale: [
                  1,
                  1,
                  1,
                ],
                material: 'site_outline_mtl',
                geometry: 'site_outline_geo',
              },
            },
            {
              type: 'PlaneText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  -78,
                  -1,
                  -55,
                ],
                rotation: [
                  -1.3,
                  0,
                  0,
                ],
                scale: [
                  0.2,
                  0.2,
                  0.2,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 40,
                fontFamily: 'Microsoft YaHei, arial, tahoma, helvetica, sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'MutualLink',
      data: {
        type: 'Line',
        properties: {
          name: 'MutualLink',
          enabled: true,
          interactive: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'TrailingCurveLink',
              properties: {
                enabled: true,
                lineWidth: 0.3,
                lineOpacity: 0.35,
                lineColor: 39167,
                pointsNum: 40,
                tailSize: 12,
                tailColor: '#46B6F9',
              },
            }, {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.01,
                delay: 400,
              },
            },
          ],
          renderOrder: 0,
          transparent: true,
          visibility: 0,
          sizeAttenuation: false,
          depthTest: false,
        },
      },
    },
    {
      type: 0,
      name: 'MutualLowLink',
      data: {
        type: 'Line',
        properties: {
          name: 'MutualLowLink',
          enabled: true,
          interactive: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'TrailingCurveLink',
              properties: {
                enabled: true,
                lineWidth: 0.3,
                lineOpacity: 0.35,
                lineColor: 39167,
                pointsNum: 40,
                tailSize: 12,
                tailColor: '#46B6F9',
                controlPoints: [
                  [
                    0.35,
                    0.05,
                    0.2,
                  ],
                  [
                    0.7,
                    0.7,
                    0.6,
                  ],
                ],
              },
            }, {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.01,
                delay: 400,
              },
            },
          ],
          renderOrder: 0,
          transparent: true,
          visibility: 0,
          sizeAttenuation: false,
          depthTest: false,
        },
      },
    },
    {
      type: 0,
      name: 'siteNode',
      data: {
        type: 'Group',
        properties: {
          name: 'siteNode',
          enabled: true,
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            }, {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'site_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  5,
                  0,
                ],
                scale: [
                  12,
                  15,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: 'site_node_obj@geo@??v2.2',
                material: 'site_node_mtl',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  5,
                  16,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 256,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'north',
      data: {
        type: 'Group',
        properties: {
          name: 'north',
          enabled: true,
          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  5,
                  0,
                ],
                scale: [
                  13,
                  13,
                  13,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: 'north_obj@geo@Push_Apart.1',
                material: 'north_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  3,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: '5G',
      data: {
        type: 'Group',
        properties: {
          name: '5G',
          enabled: true,
          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  -2,
                  0,
                ],
                scale: [
                  12,
                  12,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: '5G_obj@geo@5G.1',
                material: '5G_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  1,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: '4G',
      data: {
        type: 'Group',
        properties: {
          name: '4G',
          enabled: true,
          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  -2,
                  0,
                ],
                scale: [
                  12,
                  12,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: '4G_obj@geo@4G_data',
                material: '4G_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  1,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'smpp',
      data: {
        type: 'Group',
        properties: {
          name: 'smpp',
          enabled: true,
          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  -2,
                  0,
                ],
                scale: [
                  12,
                  12,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: 'smpp_obj@geo@smpp',
                material: 'smpp_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  1,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: 'smpp+',
      data: {
        type: 'Group',
        properties: {
          name: 'smpp+',
          enabled: true,
          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  -2,
                  0,
                ],
                scale: [
                  12,
                  12,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: 'smpp+_obj@geo@smpp+.1',
                material: 'smpp+_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  1,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
    {
      type: 0,
      name: '4G_voice',
      data: {
        type: 'Group',
        properties: {
          name: '4G_voice',
          enabled: true,

          padding: [10, 0, 0],
          position: [
            0,
            0,
            0,
          ],
          rotation: [
            0,
            0,
            0,
            'XYZ',
          ],
          scale: [
            1,
            1,
            1,
          ],
          components: [
            {
              type: 'Label',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'MutualLinkCreator',
              properties: {
                enabled: true,
              },
            },
            {
              type: 'SelectedEffect',
              properties: {
                enabled: true,
                delay: 850,
                bringFront: true,
              },
            },
            {
              type: 'ZoomInEffect',
              properties: {
                enabled: true,
                rate: 1.05,
                delay: 400,
              },
            },
          ],
          attachments: [
            {
              type: 'Mesh',
              properties: {
                name: 'business_mesh',
                enabled: true,
                interactive: true,
                position: [
                  0,
                  -2,
                  0,
                ],
                scale: [
                  12,
                  12,
                  12,
                ],
                rotation: [
                  0,
                  0,
                  0,
                  'XYZ',
                ],
                geometry: '4G_voice_obj@geo@4G_voice',
                material: '4G_voice_mat',
              },
            },
            {
              type: 'SpriteText',
              properties: {
                name: 'label',
                enabled: true,
                interactive: false,
                adelomorphic: true,
                position: [
                  0,
                  1,
                  30,
                ],
                rotation: [
                  0,
                  0,
                  0,
                ],
                scale: [
                  6,
                  6,
                  6,
                ],
                anchor: [
                  0.5,
                  0,
                ],
                width: 512,
                height: 64,
                text: '',
                fontSize: 32,
                fontFamily: 'sans-serif',
                fillColor: '#eeeeee',
                shadowColor: 0,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowBlur: 1,
                textAlign: 'center',
                lineBreak: false,
                lineNumber: 1,
                minCanvasWidth: 2,
                maxCanvasWidth: 512,
                minCanvasHeight: 2,
                maxCanvasHeight: 64,
                textAbbreviationMode: 'firsttail',
              },
            },
          ],
        },
      },
    },
  ],
  root: {
    type: 'Group',
    properties: {
      uuid: '@ENT-AEAD9F61-2A3E-4254-AF04-F4226DEF41C1',
      name: 'ROOT',
      enabled: true,
      publish: false,
      interactive: false,
      adelomorphic: false,
      position: [
        0,
        0,
        0,
      ],
      rotation: [
        0,
        0,
        0,
        'XYZ',
      ],
      scale: [
        1,
        1,
        1,
      ],
      renderOrder: 0,
      components: [
        {
          type: 'Interaction',
          properties: {
            enabled: true,
          },
        },
        {
          type: 'CameraInit',
          properties: {
            enabled: true,
            zoom: 1,
            fov: 30,
            near: 0.1,
            far: 1000,
            center: [
              0,
              0,
              0,
            ],
            radius: 600,
            latitude: 0.349065850398866,
            longitude: 0,
          },
        },
      ],
      attachments: [
        {
          type: 'SimpleSky',
          properties: {
            uuid: '@ENT-79EC97F9-B186-4732-B1F5-B65208E365E4',
            name: 'SimpleSky',
            enabled: true,
            publish: true,
            interactive: false,
            position: [
              -1.3123676998890383e-12,
              136.80805733026753,
              375.8770483143633,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: null,
            radius: 1000,
            widthSegments: 55,
            heightSegments: 55,
            offset: 400,
            exponent: 0.6,
            topColor: 921102,
            bottomColor: 2302755,
          },
        },
        {
          type: 'AmbientLight',
          properties: {
            uuid: '@ENT-EC266F9E-EE4D-4ACD-9CCB-30FDC74B9BD4',
            name: 'New AmbientLight',
            enabled: true,
            publish: false,
            interactive: false,
            adelomorphic: false,
            position: [
              -400,
              400,
              -400,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: 0,
            color: 16777215,
            intensity: 0.8,
          },
        },
        {
          type: 'SpotLight',
          properties: {
            uuid: '@ENT-AA4C607C-01EA-4B1D-8E2D-5A868FFCF3DC',
            name: 'New SpotLight',
            enabled: true,
            publish: false,
            interactive: false,
            adelomorphic: false,
            position: [
              600,
              500,
              400,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: 0,
            color: 14740479,
            intensity: 0.8,
            distance: 1400,
            angle: 0.5235987755982988,
            decay: 0.8,
            penumbra: 0.8,
          },
        },
        {
          type: 'SpotLight',
          properties: {
            uuid: '@ENT-A18D1E32-7FE4-4C92-8DD5-EB06B96485C9',
            name: 'New SpotLight',
            enabled: true,
            publish: false,
            interactive: false,
            adelomorphic: false,
            position: [
              -600,
              500,
              400,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: 0,
            color: 14740479,
            intensity: 0.8,
            distance: 1400,
            angle: 0.5235987755982988,
            decay: 0.8,
            penumbra: 0.8,
          },
        },
        {
          type: 'SpotLight',
          properties: {
            uuid: '@ENT-A18D1E32-7FE4-4C92-8DD5-EB06B96485C9',
            name: 'New SpotLight',
            enabled: true,
            publish: false,
            interactive: false,
            adelomorphic: false,
            position: [
              0,
              500,
              0,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: 0,
            color: 14740479,
            intensity: 0.8,
            distance: 1400,
            angle: 0.78539815,
            decay: 0.1,
            penumbra: 0.8,
          },
        },
        {
          type: 'SpotLight',
          properties: {
            uuid: '@ENT-A18D1E32-7FE4-4C92-8DD5-EB06B96485C9',
            name: 'New SpotLight',
            enabled: true,
            publish: false,
            interactive: false,
            adelomorphic: false,
            position: [
              0,
              -500,
              1000,
            ],
            rotation: [
              0,
              0,
              0,
              'XYZ',
            ],
            scale: [
              1,
              1,
              1,
            ],
            renderOrder: 0,
            color: 16777215,
            intensity: 0.8,
            distance: 900,
            angle: 0.78539815,
            decay: 0,
            penumbra: 0.9,
          },
        },
      ],
      children: [],
    },
  },
};

export const IMAGE_NAME = ['smpp', 'common', '4G', '5G'];

export const OPERATE_SUCCESS_CODE = 0;
