/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import i18n from '../locales/timeLine';
import { $t, registerResource } from '@util';

registerResource(i18n, 'timeline');

export const TIME_LINE_COLOR = '#9b9b9b';

export const SCATTER_COLOR = {
  2: '#D3D3D3',
  1: '#E54545',
  3: '#2E94FF',
  10: '#4B0082',
};
export const ALARM_LEVEL_CLASS = {
  1: 'alarm_level_1_icon',
  2: 'alarm_level_2_icon',
  3: 'alarm_level_3_icon',
  4: 'alarm_level_4_icon',
};

export const ALARM_TAG_CLASS = {
  1: 'alarm_level_tag_critical',
  2: 'alarm_level_tag_major',
  3: 'alarm_level_tag_minor',
  4: 'alarm_level_tag_warning',
};

export const ALARM_LEVEL_TEXT = {
  1: $t('timeline.common.alarm.critical'),
  2: $t('timeline.common.alarm.major'),
  3: $t('timeline.common.alarm.minor'),
  4: $t('timeline.common.alarm.warning'),
};

export const DETAIL_TYPE = {
  alarm: 1,
  analysis: 10,
  clear:2,
  event: 3,
};

export const DETAIL_VIEW_TYPE = {
  single: '0',
  multiple: '2',
};

export const SINGLE_MAX_WIDTH = 438;

export const CAROUSEL_MAX = 3;

export const CAROUSEL_WIDTH = {
  1: 426,
  2: 865,
  3: 1295,
};
export const OVER_VIEW = 'overView';
export const STRIP_VIEW = 'stripView';
export const SITE_HOME = 'siteHome';
export const MO_TYPE_HOME = 'moType';
export const HMS_TYPE_HOME = 'hmsType';
export const DATABASE_TYPE_HOME = 'databaseType';
export const HOST_TYPE_HOME = 'hostType';