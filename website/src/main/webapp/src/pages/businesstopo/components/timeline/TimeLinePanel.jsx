/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, { useContext, useEffect, useRef, useState } from 'react';
import TimeLine from './TimeLine';
import { OVER_VIEW } from '../../const/timeLine';
function TimeLinePanel(props) {
  const intervalId = useRef(0);
  const [refreshFlag, setRefreshFlag] = useState(0);
  useEffect(() => {
    startRefresh();
    return () => {
      clearInterval(intervalId.current);
    };
  }, []);

  const startRefresh = () => {
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, 60000);
    intervalId.current = id;
  };

  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };

  return (
    <TimeLine
      pageType={props.pageType}
      renderTopology={props.renderTopology}
      refreshFlag={refreshFlag}
      startRefresh={startRefresh}
      stopRefresh={stopRefresh}
      backTimeTrack={props.backTimeTrack}
    />
  );
}

export default TimeLinePanel;
