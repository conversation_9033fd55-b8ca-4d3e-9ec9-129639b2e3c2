/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import {Button, SelectPro} from 'eview-ui';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {BUSINESS_TOPO_CONTEXT} from '../const';
import {$t} from '@util';

import downIcon from '../../../apps/businesstopo/assets/ic_chevron_up_lined.svg';
import upIcon from '../../../apps/businesstopo/assets/ic_chevron_down_lined.svg';
import downWhiteIcon from '../../../apps/businesstopo/assets/ic_chevron_up_lined_white.svg';
import upWhiteIcon from '../../../apps/businesstopo/assets/ic_chevron_down_lined_white.svg';
import SiteDistributionPieChart from './sitedrilldown/SiteDistributionPieChart';
import ServiceLineChart from './ServiceLineChart';
import AlarmEventPanel from './AlarmEventPanel';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {
  getAlarmData,
  getHistoryData,
  getSiteDistritionData,
} from '../api';
import {setSessionData, updateStorage} from '../util';
import PropTypes from 'prop-types';

function updateRightPannel(value, dispatch, currentBussiness, selectedTime) {
  const {selectSiteId, solutionId} = JSON.parse(sessionStorage.getItem('topoSession'));
  let distritionParam = {
    instanceId: value,
  };

  let historyDParam = {

    businessId: currentBussiness.businessId,
  };

  if ( currentBussiness.indicatorList.length > 0 ) {
    historyDParam.indicatorId = currentBussiness.indicatorList[0].indicatorId;
  }

  let alarmParam = {
    instanceId: solutionId,
  };

  if (selectedTime !== 0) {
    distritionParam.currentTime = selectedTime;
    historyDParam.currentTime = selectedTime;
    alarmParam.timestamp = selectedTime;
  }

  // 饼图数据
  getSiteDistritionData(
    distritionParam,
    ({data, resultCode}) => {
      if (resultCode === -1) {
        return;
      }
      dispatch({
        siteDistributionData: data.siteDataList,
      });
    },
  );

  if (currentBussiness.indicatorList.length > 0) {
    // 折线图数据
    getHistoryData(
      historyDParam,
      ({data, resultCode}) => {
        dispatch({
          siteHistoryData: data.siteHistoryList,
        });
      },
    );
    setSessionData({
      indicatorId: currentBussiness.indicatorList[0].indicatorId,
    });
  } else {
    dispatch({
      siteHistoryData: [],
      indicatorData:[],
    });
  }

  if (selectSiteId && selectSiteId !== -1) {
    alarmParam.instanceId = selectSiteId;
  }

  // 告警数据
  getAlarmData(
    alarmParam,
    res => {
      let alarmDatas = res.data;
      dispatch({
        alarmEventData: alarmDatas.alarmDataList,
      });
    },
  );
}

function KPIOverviewPanel(props) {
  const {refreshFlag} = props;
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    showCharts,
    currentBusinessData,
    businessData,
    currentBusinessId,
    isTimeTrack,
    selectedTime,
  } = state;
  const [options, setOptions] = useState({});
  let userNeedShowRef = useRef(true);
  let businessDataResult;
  const handleChartsShow = () => {
    userNeedShowRef.current = !showCharts;
    dispatch({
      showCharts: !showCharts,
    });
  };
  useEffect(() => {
    // 不处理回溯模式的刷新
    if (!state.solutionId) {
      return;
    }
    if (!state.businessData || state.businessData.length === 0) {
      return;
    }

    let refreshForce = false;
    sortBussinessData(state.businessData);
    businessDataResult = state.businessData;

    // 查询默认业务数据
    let currentBussiness = businessDataResult.find(
      item => {
        if (parseInt(currentBusinessId) === -1) {
          return item.businessType === 'south' && item.isMain;
        } else {
          refreshForce = true;
          return item.businessId === parseInt(currentBusinessId);
        }
      },
    );
    if (!currentBussiness) {
      currentBussiness = businessDataResult.find(
        item => {
          return item.businessType === 'south' && item.isMain;
        });
    }

    if (parseInt(currentBusinessId) === -1) {
      let currentNorthBussiness = businessDataResult.find(
        item => item.businessType === 'north' && item.isMain,
      );
      setSessionData({
        mainBussiness: {
          north: currentNorthBussiness,
          south: currentBussiness,
        },
      },
      );
    }

    businessDataResult.sort((a, b) => {
      if (a.businessType === 'north' && b.businessType !== 'north') {
        return 1;
      } else if (a.businessType !== 'north' && b.businessType === 'north') {
        return -1;
      } else {
        return 0;
      }
    });

    let optionData = businessDataResult.map(item => ({
      text: item.businessName,
      value: item.businessId,
    }));
    setOptions(optionData);
    let goldIndicator = -1;
    if (currentBussiness.indicatorList.length > 0) {
      goldIndicator = currentBussiness.indicatorList.find(
        value => value.indicatorDisplayType === 1,
      ).indicatorId;
    }
    let indicator = -1;
    if (currentBussiness.indicatorList.length > 0) {
      indicator = currentBussiness.indicatorList[0];
    }
    setSessionData({
      currentBusinessId: currentBussiness.businessId,
      indicatorId: goldIndicator,
      businessData: businessDataResult,
    });
    dispatch({
      businessData: businessDataResult,
      currentBusinessData: currentBussiness,
      currentBusinessId: currentBussiness.businessId,
      goldIndicator,
      indicatorData: currentBussiness.indicatorList,
      currentIndicator: indicator,
    });
  }, [state.isTimeTrack, state.solutionId, state.businessData, state.selectedTime]);
  useEffect(() => {
    if (currentBusinessId === -1) {
      return;
    }
    updateStorage(currentBusinessId);
    handleBusinessChange(parseInt(currentBusinessId));
  }, [parseInt(currentBusinessId), isTimeTrack, selectedTime]);

  const handleBusinessChange = value => {
    let currentBussiness = businessData.find(
      item => item.businessId === value,
    );

    updateRightPannel(value, dispatch, currentBussiness, selectedTime);
    dispatch({
      // 当前业务
      currentBusinessId: value,
      currentBusinessData: currentBussiness,

      // 当前业务的指标数据
      indicatorData: currentBussiness.indicatorList,
      currentIndicator: currentBussiness.indicatorList[0],
      indicatorId: currentBussiness.indicatorList[0].indicatorId,
      goldIndicator: currentBussiness.indicatorList.find(
        indicator => indicator.indicatorDisplayType === 1,
      ).indicatorId,
      currentEvent: 'all',
      alarmShow: true,
      currentIndicatorIndex: 0,

    });
  };

  const sortBussinessData = data => {
    for (let business of data) {
      if (!business.indicatorList) {
        continue;
      }
      business.indicatorList.sort((a, b) => {
        if (a.isGoldInd === b.isGoldInd) {
          return 0;
        } else if (a.isGoldInd) {
          return -1;
        } else {
          return 1;
        }
      });
    }
  };

  useEffect(() => {
    const func = e => {
      if (!userNeedShowRef.current) {
        return;
      }

      const {top} = $('.alarm_event_panel').offset();
      if (top <= 78) {
        $('#dummySelect').css('top', '-62px');
        $('#dummySelect').css('padding-top', '12px');
        $('#dummySelect').css('padding-bottom', '10px');
        $('#dummySelect').css('display', 'inline-block');

        $('#alarmEventDiv').css('top', '26px');
        $('#alarmEventDiv').css('padding-top', '10px');
        $('#alarmEventDiv').css('padding-bottom', '8px');
        $('.alarm_event_panel .eui_tab').css('top', '-10px');
      } else {
        $('#dummySelect').css('top', '');
        $('#dummySelect').css('padding-top', '');
        $('#dummySelect').css('padding-bottom', '');
        $('#dummySelect').css('display', 'none');
        $('#alarmEventDiv').css('top', '');
        $('#alarmEventDiv').css('padding-top', '');
        $('#alarmEventDiv').css('padding-bottom', '');

        $('.alarm_event_panel .eui_tab').css('top', '');
      }
    };

    const rightDom = document.querySelector('.right-panel');

    rightDom.addEventListener('scroll', func);
    return () => {
      rightDom.removeEventListener('scroll', func);
    };
  }, []);

  return (
    <div id="topo_right_container">
      <div
        className="kpi_title"
        style={{display: 'line-block', marginTop: '-3rem'}}
      >
        <text className="kpi_title_text">{state.currentSiteName}{$t('kpi.title')}</text>
        <SelectPro
          value={currentBusinessId}
          onChange={value => {
            let nowBusiness = businessData.find(item => item.businessId === parseInt(value));
            dispatch({
              // 当前业务
              currentBusinessId: value,
              currentBusinessData: nowBusiness,
            });
            eventBus.emit('to3d_updateBusiness', value);
          }}
          options={options}
          className="service_select"
          style={{outline: 'none', border: 'none', background: '#272727'}}
          selectStyle={{outline: 'none'}}
          optionStyle={{outline: 'none'}}
          dropdownStyle={{outline: 'none', border: '1px solid #676767'}}
          selectClassName="service_select_select"
          dropdownClassName="service_select_dropdown"
          optionClassName="service_select_option"
          placeholder=""
        />
        <Button
          onClick={handleChartsShow}
          leftIconProps={{
            leftHoverIcon: showCharts ? downWhiteIcon : upWhiteIcon,
          }}
          leftIcon={showCharts ? downIcon : upIcon}
          style={{
            color: '#FFFFFF',
            background: '#272727',
            border: 'none',
            float: 'right',
            outline: 'none',

            // marginRight:'-10px'
          }}
        />
      </div>
      <div style={{display: showCharts ? 'block' : 'none', position: 'relative', zIndex: 11}}>
        <div className="piechart_panel">
          <SiteDistributionPieChart showCharts={showCharts} refreshFlag={refreshFlag} />
        </div>
        <div className="chart_panel">
          <ServiceLineChart showCharts={showCharts} refreshFlag={refreshFlag} />
        </div>
      </div>

      <div style={{
        position: 'relative',
        display: 'inline-block',
        width: '100%',
        zIndex: 10,
      }}
      >
        <div
          id="dummySelect"
          className="kpi_title"
          style={{
            position: 'sticky',
            width: '100%',
            background: '#272727',
            display: 'none',
            zIndex: 2,
          }}
        >
          <text className="kpi_title_text">{state.currentSiteName}{$t('kpi.title')}</text>
          <SelectPro
            value={currentBusinessData.businessId}
            onChange={value => {
              dispatch({
                // 当前业务
                currentBusinessId: value,
              });
            }}
            options={options}
            className="service_select"
            style={{outline: 'none', border: 'none'}}
            selectStyle={{outline: 'none'}}
            optionStyle={{outline: 'none'}}
            dropdownStyle={{outline: 'none'}}
            selectClassName="service_select_select"
            dropdownClassName="service_select_dropdown"
            optionClassName="service_select_option"
            placeholder=""
          />
          <Button
            onClick={e => {
              document.querySelector('.right-panel').scroll(0, 0);
            }}
            leftIconProps={{
              leftHoverIcon: upWhiteIcon,
            }}
            leftIcon={upIcon}
            style={{
              color: '#FFFFFF',
              background: '#272727',
              border: 'none',
              float: 'right',
              outline: 'none',

              // marginRight:'-10px'
            }}
          />
        </div>
        {/* style={{ paddingTop:showCharts ? '40px' : '10px', marginTop: showCharts ? '40px' : 0 }}*/}
        <div style={{
          marginLeft: '16px',
          paddingTop: showCharts ? '40px' : '10px',
          marginTop: showCharts ? '40px' : 0,
        }}
        >
          <AlarmEventPanel refreshFlag={refreshFlag} isOverview={true} />
        </div>
      </div>
    </div>
  );
}

KPIOverviewPanel.prototype = {
  refreshFlag: PropTypes.number,
};

export default KPIOverviewPanel;
