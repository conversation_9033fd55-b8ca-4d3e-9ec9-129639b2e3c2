/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, { useContext, useEffect, useState } from 'react';
import { BUSINESS_TOPO_CONTEXT } from '../const';
import { $t } from '../../../commonUtil';

function ViewToggle({ show, setShow }) {
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const databaseClick = () => {
    setShow(!show);
  };
  return (
    <div className="view_toggle">
      <div className="over_view_select ">
        <div className="over_view_icon" />
      </div>
      <p className="toggle_text" style={{ color: '#5CACFF' }}>{$t('overview')}</p>

      <div className="database_notselect" onClick={databaseClick}>
        <div className="database_icon" />

      </div>
      <p className="toggle_text">{$t('database')}</p>

    </div>
  );
}

export default ViewToggle;
