import React, {useState, useEffect, useReducer, useRef} from 'react';
import {ConfigProvider, TextField, DivMessage, MessageDialog} from 'eview-ui';
import {$t, registerResource, validate} from '@util';
import {BUSINESS_TOPO_CONTEXT as TopoContext} from '@pages/businesstopo/const';
import {initState, reducer} from '@pages/businesstopo/reducer';
import {BUSINESS_ICON_LIST} from './const';
import {getSolutionData, querymmtopnconfig, editmmtopnconfig} from '../../../api';
import DataCheck from '../../../../../commonUtil/DataCheck';
import {editMMbusiness, getIniteditpage} from '../api';
import pageCss from './css/index.css';
import i18n from './locale/index';
import DroppableArea from './DroppableArea';
import StripsArea from './StripsArea';
import {MMRightPanel} from './rightPanel';
import CustomSelectTree from '../customSelectTree';
import {deepClone, sorter} from '@digitalview/fe-utils';
import {sortListByName} from '../../../a3dPages/components/utils';
import {setHelpId} from '@util/index';

/**
 * 业务拓扑编辑页面 For MM
 */

let mmIconIndex = 0;
const EditMMComponent = () => {
  registerResource(i18n, 'editComponent');
  setHelpId('com.huawei.dvtopo.edit');
  const [state, dispatch] = useReducer(reducer, initState);
  const [solutionName, setSolutionName] = useState('');
  const [solutionId, setSolutionId] = useState('');
  const [areaIcons, setAreaIcons] = useState({thirdPart: [], app: []});
  const [allStripsData, setAllStripsData] = useState([]);
  useEffect(() => {
    initPageData();
  }, []);

  const initPageData = () => {
    new Promise((resolve, reject) => {
      getSolutionData({timestamp: 0}, resp => {
        if (resp?.data?.length === 0) {
          return;
        }
        setSolutionName(resp.data[0].solutionName);
        setSolutionId(resp.data[0].solutionId);
        resolve(resp.data[0]);
      });
    }).then(solution => {
      getIniteditpage(
        {solutionId: solution.solutionId},
        resp => {
          if (resp.resultCode !== 0) {
            return;
          }

          const result = {
            thirdPart: [],
            app: [],
          };
          sortListByName(resp.data.stripeTeamList, 'stripeTeamName');
          sortListByName(resp.data.businessList, 'businessName');
          sortListByName(resp.data.channelList, 'channelName');
          resp.data.businessList.sort((a, b) => a.sortOrder - b.sortOrder);
          resp.data.businessList.forEach(item => {
            const goldList = item.indicatorList.filter(v => v.indicatorDisplayType === 1);
            const otherList = item.indicatorList.filter(v => v.indicatorDisplayType === 0);
            goldList.sort((a, b) => sorter(a.indexName, b.indexName));
            goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
            otherList.sort((a, b) => sorter(a.indexName, b.indexName));
            otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
            const updatedData = [...goldList, ...otherList].map(data1 => ({
              ...data1,
              key: `${data1.moType}${data1.measUnitKey}${data1.indicatorId}${data1.originalValue}`,
            }));
            const newItem = {
              ...item,
              flag: 0,
              rightPannelData: updatedData || [],
              icon: BUSINESS_ICON_LIST[0].icon,
              iconType: item.iconType,
              toggled: !item.isDisplay,
              checkError: false,
            };

            result.thirdPart.push(newItem);
          });
          resp.data.channelList.sort((a, b) => a.sortOrder - b.sortOrder);
          resp.data.channelList.forEach(item => {
            const goldList = item.indicatorList.filter(v => v.indicatorDisplayType === 1);
            const otherList = item.indicatorList.filter(v => v.indicatorDisplayType === 0);
            goldList.sort((a, b) => sorter(a.indexName, b.indexName));
            goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
            otherList.sort((a, b) => sorter(a.indexName, b.indexName));
            otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
            item.indicatorList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
            const updatedData = [...goldList, ...otherList].map(data1 => ({
              ...data1,
              key: `${data1.moType}${data1.measUnitKey}${data1.indicatorId}${data1.originalValue}`,
            }));
            const newItem = {
              ...item,
              flag: 0,
              rightPannelData: updatedData || [],
              iconType: item.iconType,
              icon: BUSINESS_ICON_LIST[1].icon,
              toggled: !item.isDisplay,
              checkError: false,
              businessName: item.channelName,
            };
            result.app.push(newItem);
          });
          setAreaIcons(result);

          setAllStripsData(resp?.data?.stripeTeamList ?? []);
        },
      );
    });
  };

  const handleBusinessIconDrag = (event, icon, index) => {
    event.dataTransfer.setData('icon', icon);
    event.dataTransfer.setData('iconIndex', index);
    mmIconIndex = index;
  };

  return (
    <TopoContext.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div id="dvtopommedit">
          <div id="topocontainer" className={pageCss.topocontainer}>
            <div id="leftPanel" className={pageCss.leftPanelCls}>
              <TextField
                required={true}
                hideRequiredMark={true}
                value={solutionName}
                readOnly={true}
                hintType="tip"
                autoComplete="off"
              />
              <div className={pageCss.left_panel_separator} />
              <div className={pageCss.left_panel_title}>{$t('edit.left.panel.title')}</div>
              <div className={pageCss.left_panel_icon_area}>
                {BUSINESS_ICON_LIST.map((item, index) => (
                  <div
                    draggable
                    key={index}
                    className={pageCss.left_panel_icon}
                    onDragStart={event => handleBusinessIconDrag(event, item.icon, index)}
                  >
                    <img alt="" key={index} src={item.icon} draggable="true" width="64px" />
                    <div className={pageCss.left_panel_icon_name}>{item.name}</div>
                  </div>
                ))}
              </div>
            </div>
            <div id="body" className={pageCss.bodyCls}>
              <MainComponent areaIcons={areaIcons}
                setAreaIcons={setAreaIcons}
                allStripsData={allStripsData}
                setAllStripsData={setAllStripsData}
                solutionId={solutionId}
              />
              <div id="rightPanel" />
            </div>
          </div>
        </div>
      </ConfigProvider>
    </TopoContext.Provider>
  );
};

const MainComponent = ({allStripsData, areaIcons, setAreaIcons, setAllStripsData, solutionId}) => {
  const [isShowRightPanel, setIsShowRightPanel] = useState(false);
  const [editRightPanelShow, setEditRightPanelShow] = useState(true);
  const [topNCount, setTopNCount] = useState({
    thrid: 0,
    app: 0,
    strip: 0,
  });

  useEffect(() => {
    querymmtopnconfig((data) => {
      if (data && data.resultCode === 0) {
        setTopNCount({
          thrid: data.data.businessTopNConfig,
          app: data.data.channelTopNConfig,
          strip: data.data.stripeTopNConfig,
        });
      }
    });
  }, []);
  const rightRef = useRef();
  const [highlightedAreaId, setHighlightedAreaId] = useState('');
  const [selectedIcon, setSelectedIcon] = useState({areaId: '', iconId: ''});
  const [pannelData, setPannelData] = useState({});
  const [indictorError, setIndictorError] = useState(false);
  const [validateError, setValidateError] = useState({
    show: false,
    msg: '',
    type: 'error',
  });
  const [validateSuccess, setValidateSuccess] = useState({
    show: false,
    msg: '',
  });
  const setStripName = (index, newName) => {
    const updatedData = allStripsData.map((item, idx) =>
      idx === index ? {...item, stripeTeamName: newName} : item,
    );
    setAllStripsData(updatedData); // 更新状态
  };
  let style = {
    marginBottom: '20px', position: 'absolute', 'z-index': '99999', left: '40%', top: '0%', width: '400px',
  };
  const setErrorStatus = (area, index) => {
    let data = areaIcons[area];
    let updateData = data[index];
    updateData.checkError = true;
    setAreaIcons({
      ...areaIcons,
      [area]: data,
    },
    );
  };

  const checkLength = (data) => {
    if (!data) {
      return false;
    }

    if (data.length >= 1 && data.length <= 128) {
      return true;
    }
    return false;
  };

  function check(data, area, errorMsg, areaName) {
    // 使用 Set 来确保名称唯一
    let allHidden = true;
    // 用一个对象来存储每个名称出现的次数
    const nameSet = new Set([]);
    // 用一个对象来存储重复名称的索引
    data.forEach((item, index) => {
      if (item.isDisplay) {
        allHidden = false;
      }
      // 名称校验
      if (checkLength(item.businessName) && DataCheck.siteNameValidChar(item.businessName) && !nameSet.has(item.businessName)) {
        nameSet.add(item.businessName);
      } else {
        errorMsg.push({
          area,
          businessName: item.businessName,
          errMsg: $t('business.check.failed'),
        });
        setErrorStatus(area, index);
      }
      // 主指标校验
      if (item.rightPannelData.length > 0) {
        let primaryFlag = false;
        item.rightPannelData.forEach(item2 => {
          if (item2.indicatorDisplayType === 1) {
            primaryFlag = true;
          }
        });
        if (!primaryFlag) {
          errorMsg.push({
            area,
            businessName: item.businessName,
            errMsg: $t('mm.edit.no.primary'),
          });
          setErrorStatus(area, index);
        }
      } else {
        errorMsg.push({
          area,
          businessName: item.businessName,
          errMsg: $t('mm.edit.no.indicator'),
        });
        setErrorStatus(area, index);
      }
    });
    if (allHidden) {
      errorMsg.push({
        area,
        businessName: areaName,
        errMsg: $t('mm.edit.hidden.all'),
      });
    }
  }

  const editConfig = () => {
    editmmtopnconfig({
      businessTopNConfig: topNCount.thrid,
      channelTopNConfig: topNCount.app,
      stripeTopNConfig: topNCount.strip,
    });
  };

  const handleConfirm = () => {
    setIsShowRightPanel(false);
    editConfig();
    const errorMsg = [];
    setValidateError({
      show: false,
      msg: '',
      type: 'error',
    });
    setValidateSuccess({
      show: false,
      msg: '',
    });
    check(areaIcons.thirdPart, 'thirdPart', errorMsg, $t('mm.edit.thirdParty'));
    areaIcons.thirdPart.forEach((item, index) => {
      if (!item.associatedChannelList || item.associatedChannelList.length === 0) {
        errorMsg.push({
          area: 'thirdPart',
          businessName: item.businessName,
          errMsg: $t('mm.edit.channel.length'),
        });
        setErrorStatus('thirdPart', index);
      }
    });
    check(areaIcons.app, 'app', errorMsg, $t('mm.edit.channel'));
    if (errorMsg.length !== 0) {
      let msg = $t('business.check.failed');
      setValidateError({
        show: true,
        msg,
        type: 'error',
      });
      return;
    }
    let params = {};
    params.solutionId = solutionId;
    const idToModeIDMap = areaIcons.app.reduce((acc, {channelId, modelId}) => {
      acc[channelId] = modelId;
      return acc;
    }, {});
    params.businessList = areaIcons.thirdPart.map((item, index) => {
      let indicatorList = item.rightPannelData.map((indicator, index2) => ({
        ...indicator,
        indicatorSortNumber: index2,
      }));
      return {
        businessId: String(item.businessId).includes('random') ? '' : item.businessId,
        businessName: item.businessName,
        indicatorList,
        isDisplay: item.isDisplay,
        editType: item.editType,
        sortOrder: index,
        canDelete: item.canDelete,
        associatedChannelList: item.associatedChannelList.map(channel => {
          return {
            channelName: channel.channelName,
            channelId: String(channel.channelId).includes('random') ? '' : channel.channelId,
            modelId: idToModeIDMap[channel.channelId] || '',
          };
        }),
      };
    });
    params.channelList = areaIcons.app.map((item, index) => {
      let indicatorList = item.rightPannelData.map((indicator, index2) => ({
        ...indicator,
        indicatorSortNumber: index2,
      }));
      return {
        canDelete: item.canDelete,
        channelName: item.channelName,
        editType: item.editType,
        indicatorList,
        isDisplay: item.isDisplay,
        sortOrder: index,
        channelId: String(item.channelId).includes('random') ? '' : item.channelId,
      };
    });
    params.stripeTeamList = allStripsData.map(
      item => {
        return {
          stripeTeamId: item.stripeTeamId,
          stripeTeamName: item.stripeTeamName,
        };
      },
    );

    editMMbusiness(params, (data) => {
      if (data && data.resultCode === 0) {
        setValidateSuccess({
          show: true,
          msg: $t('save.success'),
        });
      } else {
        setValidateError(({
          show: true,
          msg: data.resultMessage,
          type: 'error',
        }));
      }
    });
  };

  const handleCancel = () => {
    location.replace('/eviewwebsite/index.html#path=/businesstopo');
  };
  const handleDrop = (event, id) => {
    if (id === 'thirdPart' && areaIcons[id].length >= 30) {
      setValidateError({
        show: true,
        msg: $t('third.max.length'),
        type: 'warn',
      });
      setHighlightedAreaId('');
      return;
    }
    const icon = event.dataTransfer.getData('icon');
    const iconIndex = event.dataTransfer.getData('iconIndex');
    let nodeData = {
      icon,
      flag: 1,
      businessName: '',
      rightPannelData: [],
      canDelete: true,
      editType: 1,
      isDisplay: true,
    };
    if (id === 'app') {
      nodeData.channelId = `random${iconIndex}`;
    } else {
      nodeData.businessId = `random${iconIndex}`;
    }
    if (id === 'app' && areaIcons[id].length >= 10) {
      setValidateError({
        show: true,
        msg: $t('channel.max.length'),
        type: 'warn',
      });
      setHighlightedAreaId('');
      return;
    }
    setSelectedIcon(null);
    const iconMap = {
      0: 'thirdPart',
      1: 'app',
    };
    if (iconMap[iconIndex] !== id) {
      return;
    }

    setAreaIcons(prev => ({
      ...prev,
      [id]: [
        ...prev[id],
        {
          ...nodeData,
        },
      ],
    }));
    let length = areaIcons[id].length;
    setSelectedIcon({areaId: id, iconId: length});
    setHighlightedAreaId('');
    // 打开右侧面板
    setIsShowRightPanel(true);
  };

  const handleDragOver = (event, id) => {
    const iconMap = {
      0: 'thirdPart',
      1: 'app',
    };
    if (iconMap[mmIconIndex] !== id) {
      return;
    }
    event.preventDefault();
    setHighlightedAreaId(id);
  };

  const handleDragLeave = () => {
    setHighlightedAreaId('');
  };
  const getRightPannelData = (areaId, iconId) => {
    const typeMap = {
      thirdPart: 1,
      app: 2,
    };
    if (areaId) {
      let data = areaIcons[areaId][iconId];
      let allChannelList = areaIcons.app.map(item => {
        return {
          text: item.channelName,
          value: item.channelId,
        };
      });
      const mainIndicator = data?.rightPannelData?.find(v => v.indicatorDisplayType === 1);
      return {
        name: data?.businessName || '',
        type: typeMap[areaId],
        channelList: data?.associatedChannelList?.[0]?.channelId || '',
        cardList: data?.rightPannelData || [],
        allChannelList,
        index: iconId,
        mainSelect: mainIndicator?.key || '',
        hidden: data?.toggled,
      };
    }
    return {};
  };
  const getDroppableAreaParams = (id, text) => {
    return {
      id,
      text,
      highlightedAreaId,
      selectedIcon,
      areaIcons,
      handleDrop,
      handleDragOver,
      handleDragLeave,
      setIsShowRightPanel,
      setEditRightPanelShow,
      setSelectedIcon: ({areaId, iconId}) => {
        if (id === null) {
          setSelectedIcon(null);
        } else {
          setSelectedIcon({areaId, iconId});
          setPannelData(getRightPannelData(areaId, iconId));
          setAreaIcons(prevState => {
            const updatedAreaIcons = {...prevState}; // 复制当前状态
            updatedAreaIcons[areaId][iconId].checkError = false;
            return updatedAreaIcons; // 返回更新后的状态
          });
        }
      },
      setNullSelectedIcon: () => {
        setSelectedIcon(null);
      },
      handleDelete: (delId, index, e) => {
        setSelectedIcon(null);
        let area = areaIcons[delId];
        let thirdPart = areaIcons.thirdPart;
        if (delId === 'app') {
          let channelId = area[index].channelId;
          thirdPart = thirdPart.map(item => ({
            ...item,
            associatedChannelList: item.associatedChannelList?.filter(
              channel => channel.channelId !== channelId,
            ) ?? [],
          }));
        }
        area.splice(index, 1);
        if (delId === 'app') {
          setAreaIcons(prevState => ({
            thirdPart,
            [delId]: area,
          }),
          );
        } else {
          setAreaIcons(prevState => ({
            ...prevState,
            [delId]: area,
          }),
          );
        }

        e.stopPropagation();
        setIsShowRightPanel(false);
      },
      setPannelData,
    };
  };
  const handleClick = (event) => {
    let target = event.target;
    while (target) {
      if (target.classList.contains('divMessage')) {
        return; // 如果找到，退出函数
      }
      target = target.parentElement; // 移动到父元素
    }

    if (event.target.id === 'confirmToolbar') {
      return;
    }

    // 点击右侧面板相关元素，则忽略此次点击
    let isIgnore = false;
    const ignoreDivIdArr = ['editRightPannel', 'editAddPannel', 'custom-select-tree', 'left-arrow-div',
      'middle-area-div', 'right-arrow-div'];
    for (let i = 0; i < ignoreDivIdArr.length; i++) {
      let tempDiv = document.getElementById(ignoreDivIdArr[i]);
      if (tempDiv && tempDiv.contains(event.target)) {
        isIgnore = true;
        break;
      }
    }
    if (isIgnore) {
      return;
    }
    if (!event.target.classList.contains('business-select')) {
      setSelectedIcon(null);
      setIsShowRightPanel(false);
    }
  };

  useEffect(() => {
    if (selectedIcon === null) {
      return;
    }
    setPannelData(getRightPannelData(selectedIcon?.areaId, selectedIcon?.iconId));
  }, [JSON.stringify(selectedIcon), JSON.stringify(areaIcons)]);
  return (
    <ConfigProvider version="aui3-1" theme="evening">
      <div>
        <div>
          <div className={pageCss.body_head_container}>
            <div>
              <div className={pageCss.body_head_title}>{$t('edit.body.title')}</div>
            </div>
            <div className={pageCss.edit_btn_container}
              style={{transform: (isShowRightPanel && editRightPanelShow) ? 'translateX(-400px)' : 'translateX(0)'}}
            >
              <div className={pageCss.edit_btn_cancel} onClick={handleCancel}>
                {$t('edit.body.button.cancel')}
              </div>
              <div className={pageCss.edit_btn_confirm} onClick={handleConfirm}>
                {$t('edit.body.button.save')}
              </div>
            </div>
          </div>
          <div className={pageCss.body_content_container}
            style={{transform: (isShowRightPanel && editRightPanelShow) ? 'translateX(-150px)' : 'translateX(0)'}}
            onClick={handleClick}
          >
            <DroppableArea {...getDroppableAreaParams('thirdPart', $t('edit.body.third.part'))} />
            <DroppableArea {...getDroppableAreaParams('app', $t('edit.body.app'))} />
            <DivMessage text={validateError.msg} type={validateError.type}
              style={{...style, backgroundColor: '#371f20'}} display={validateError.show}
              className="divMessage"
              disposeTimeOut={3000}
              onClose={() => {
                setValidateError({
                  show: false,
                  msg: '',
                });
              }}
            />
            <DivMessage text={validateSuccess.msg} type="success" style={style} display={validateSuccess.show}
              className="divMessage"
              disposeTimeOut={3000}
              onClose={() => {
                setValidateSuccess({
                  show: false,
                  msg: '',
                });
              }}
            />
            <StripsArea data={allStripsData} setPageData={(index, name) => {
              setIsShowRightPanel(true);
              setPannelData({
                type: 3,
                index,
                name,
              });
            }}
            />
          </div>
        </div>
        <MMRightPanel
          viewShow={isShowRightPanel}
          setViewShow={setIsShowRightPanel}
          editRightPanelShow={editRightPanelShow}
          setEditRightPanelShow={setEditRightPanelShow}
          ref={rightRef}
          data={pannelData}
          nameOnChange={(v) => {
            // v
          }}
          setStripName={setStripName}
          areaIcons={areaIcons}
          setAreaIcons={setAreaIcons}
          setSelectedIcon={setSelectedIcon}
          topNCount={topNCount}
          setTopNCount={setTopNCount}
        />
        <CustomSelectTree
          areaIcons={areaIcons}
          setAreaIcons={setAreaIcons}
          selectedIcon={{...selectedIcon, id: selectedIcon?.areaId}}
          setIndictorError={
            setIndictorError
          }
          pannelContent={deepClone((pannelData.type === 1 ? areaIcons.thirdPart : areaIcons.app)[pannelData.index])}
          setPannelContent={(value) => {
            // v
          }}
        />
        <MessageDialog type='error' isOpen={indictorError} onClose={() => {
          setIndictorError(false);
        }} modal={false}
        content={$t('mm.add.error')} detail={$t('mm.add.error.content')}
        buttons={{
          ok: {
            onClick: () => {
              setIndictorError(false);
            },
          },
        }}
        />
      </div>
    </ConfigProvider>
  );
};

export default EditMMComponent;
