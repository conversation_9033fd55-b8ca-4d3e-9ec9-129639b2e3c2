import React, {
  useEffect, useRef, useContext,
} from 'react';
import '@pages/businesstopo/a3dPages/styles/split.less';
import { destroyW3D, initW3D, resizeW3D } from './overview3D';
import RightPanel from '../RightPanel';
import { BUSINESS_TOPO_CONTEXT } from '../../const';

function OverviewPage() {
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const w3dContainerRef = useRef();
  const { showKPIPanel } = state;
  useEffect(() => {
    initW3D(w3dContainerRef, dispatch, state);
    return () => destroyW3D();
  }, []);

  useEffect(() => {
    resizeW3D();
  }, [showKPIPanel]);
  return (
    <>
      <div id="business_topo" style={{ height: '100%', width: '100%' }}>
        <RightPanel />
      </div>
  </>);
}

export default OverviewPage;
