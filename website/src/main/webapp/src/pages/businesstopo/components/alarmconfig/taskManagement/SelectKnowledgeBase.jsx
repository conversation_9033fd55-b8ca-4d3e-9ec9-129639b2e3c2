import React, { useContext, useEffect, useRef } from 'react';
import { Wizards, Select, Button } from 'eview-ui';
import {goToNewPage} from '@digitalview/fe-utils';
import styles from '../index.less';
import { $t, modal, validate } from '@util';
import { cancelCreateOrEditTask } from './NewOrEditCommonUtils';
import LabelHint from '../../common/LabelHint';
import { querySolution, queryKnowledgeList } from '../api';
import {
  TaskManageContext,
  TASK_PAGE_TYPE,
  CREATE_TASK_WIZARDS_PARAMS,
  OPERATE_SUCCESS_CODE,
  GlobalContext,
} from '../const';

const SelectKnowledgeBase = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const { theme } = useContext(GlobalContext);
  const { curTaskPage, solutionList, knowledgeBaseList, selectedSolution, selectedKnowledgeBase } = state;
  const solutionInputRef = useRef(null);
  const knowledgeBaseInputRef = useRef(null);  

  // 进入此页面时查询解决方案列表和知识库列表
  useEffect(() => {
    querySolution(res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error(
          $t('intelligent.incident.task.manage.solution.query.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
        return;
      }
      const solutionListOptions = res.data.map(ele => {
        return {
          text: ele.solutionName,
          value: ele.solutionName,
        };
      });
      dispatch({
        type: 'setSolutionList',
        solutionList: solutionListOptions,
      });
    }, () => {
      modal.error(
        $t('intelligent.incident.task.manage.solution.query.fail'),
        null,
        $t('intelligent.incident.task.manage.error.tip'),
        null,
        theme,
      );
    });
    queryKnowledgeList(2, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error(
          $t('intelligent.incident.task.manage.knowledge.query.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
        return;
      }
      const knowledgeBaseListOptions = res.data.map((ele, index) => {
        return {
          text: ele,
          value: ele,
        };
      });
      dispatch({
        type: 'setKnowledgeBaseList',
        knowledgeBaseList: knowledgeBaseListOptions,
      });
    }, () => {
      modal.error(
        $t('intelligent.incident.task.manage.knowledge.query.fail'),
        null,
        $t('intelligent.incident.task.manage.error.tip'),
        null,
        theme,
      );
    });
  }, []);

  // 选择解决方案下拉框回调
  const onSelectSolution = (value) => {
    dispatch({
      type: 'setSelectedSolution',
      selectedSolution: value,
    });
  };

  // 选择知识库下拉框回调
  const onSelectKnowledgeBase = (value) => {
    dispatch({
      type: 'setSelectedKnowledgeBase',
      selectedKnowledgeBase: value,
    });
  };

  const importKnowledgeClick = () => {
    const pageUrl = `${location.origin}/eviewwebsite/index.html#path=/aiOpsService&subMenu=neKnowledge`;
    goToNewPage(pageUrl, true);  
  };

  const backTaskList = () => {
    cancelCreateOrEditTask(dispatch);
  };

  const goNextStep = () => {
    if (!verifyData()) {
      return;
    }
    dispatch({
      type: 'setCurTaskPage',
      curTaskPage: TASK_PAGE_TYPE.CONFIGURE_ALGORITHM,
    });
  };

  // 校验页面输入项
  const verifyData = () => {
    if (!solutionInputRef.current.validate()) {
      modal.error(
        $t('intelligent.incident.task.manage.solution.please.select'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    return true;
  };

  return (
    <>
      <div className={styles.taskTopTitleContainer}>
        <div className={styles.backIcon} onClick={backTaskList} />
        <div>{$t('intelligent.incident.task.manage.create.task')}</div>
      </div>
      <div className={styles.bottomPanelContainer}>
        <Wizards data={CREATE_TASK_WIZARDS_PARAMS} currentStep={curTaskPage} />
        <table className={styles.inputContentContainer}>
          <tbody>
            <tr>
              <td>
                <LabelHint
                  label={$t('intelligent.incident.task.manage.solution')}
                  require={true}
                  theme={theme}
                />
              </td>
              <td>
                <Select
                  id='solutionInput'
                  selectStyle={{ width: '33.75rem' }}
                  options={solutionList}
                  value={selectedSolution}
                  onChange={onSelectSolution}
                  required
                  validator={(val, id) => validate(['required'], val, id)}   
                  ref={ele => solutionInputRef.current = ele}               
                />
              </td>
            </tr>
            <tr>
              <td>
                <LabelHint
                  label={$t(
                    'intelligent.incident.task.manage.table.knowledge.base'
                  )}
                  require={false}
                  theme={theme}
                  isHelp={true}
                  hint={$t('intelligent.incident.task.manage.knowledge.tip.select.appropriate')}
                />
              </td>
              <td>
                <div className={styles.knowledgeRow}>
                  <Select
                    id='knowledgeBaseInput'
                    selectStyle={{ width: '33.75rem' }} 
                    options={knowledgeBaseList}
                    value={selectedKnowledgeBase}
                    onChange={onSelectKnowledgeBase}
                    ref={ele => knowledgeBaseInputRef.current = ele}
                  />
                  <div className={styles.importKnowledge} onClick={importKnowledgeClick}>
                    {$t(
                      'intelligent.incident.task.manage.import.knowledge.base'
                    )}
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div className={styles.bottomButtonRow}>
          <Button
            text={$t('alarm.edit.cancel')}
            style={{ float: 'left', marginRight: '0.5rem' }}
            onClick={backTaskList}
          />
          <Button
            text={$t('intelligent.incident.task.manage.next.step')}
            status='primary'
            onClick={goNextStep}
          />
        </div>
      </div>
    </>
  );
};

export default SelectKnowledgeBase;
