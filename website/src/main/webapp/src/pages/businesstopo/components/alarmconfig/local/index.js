export default {
  'zh-cn': {
    'alarm.edit.config.rule': '规则配置',
    'alarm.edit.alarm.config.rule': '告警展示配置',
    'alarm.edit.full.alarm': '启用全量告警策略',
    'alarm.edit.fmOrIntelligent.alarm': '根据性能阈值告警过滤',
    'alarm.edit.fm.alarm': ' 性能阈值告警',
    'alarm.edit.alarm.policy.alarm': '根据告警级别过滤',
    'alarm.edit.alarm.policy.alarm.critical': '紧急',
    'alarm.edit.alarm.policy.alarm.major': '重要',
    'alarm.edit.alarm.policy.alarm.minor': '次要',
    'alarm.edit.alarm.policy.alarm.info': '提示',
    'alarm.edit.alarm.policy.clear.alarm': '根据告警清理策略过滤',
    'alarm.edit.alarm.policy.manual.clear.alarm': '手动清除',
    'alarm.edit.alarm.policy.auto.clear.alarm': '自动清除',
    'alarm.edit.alarm.trustlist': '启用告警白名单策略',
    'alarm.edit.alarm.blocklist': '启用告警/事件黑名单策略',
    'alarm.edit.cancel': '取消',
    'alarm.edit.confirm': '确认',
    'alarm.edit.add': '创建',
    'alarm.edit.delete': '删除',
    'alarm.edit.edit': '编辑',
    'alarm.edit.application': '保存',
    'alarm.edit.alarm.name': '规则名称',
    'alarm.edit.alarm.id': '告警ID/事件ID',
    'alarm.edit.operation': '操作',
    'alarm.range': '告警范围',
    'alarm.rule.name': '告警规则名称',
    'alarm.name': '名称',
    'iemp.eam.alarmselector.search.tips': '输入关键字。',
    'fm.severity.check.box.group.tip': '至少选择: 1',
    'iemp.fm.alarmselector.column.groupName': '分组名称',
    'iemp.fm.component.max.number': ' 最多. 允许:1,000',
    'iemp.fm.component.selected.number': '已选择: ',
    'iemp.fm.component.selected.alarm': '选择告警',
    'alarm.edit.operation.copy':'复制',
    'alarm.edit.operation.delete':'删除',
    'alarm.modify.person':'最后修改人',
    'alarm.modify.time':'最后修改时间',
    'alarm.range.tip': '选中为时间轴告警，关闭切换为健康状态渲染规则',
    'alarm.edit.refresh': '同步',
    'alarm.edit.alarm.policy.alarm.id': '根据告警/事件信息过滤',
    'alarm.edit.alarm.policy.alarm.source': '条件',
    'alarm.edit.alarm.policy.alarm.source.all': '所有告警源',
    'alarm.edit.alarm.policy.alarm.select': '请选择',
    'alarm.edit.alarm.policy.alarm.select.tips': '告警ID/事件ID只能输入数字,可以输入多个ID,每个ID之间用英文逗号分隔,ID不能为0,每个ID的长度不能超过10',
    'alarm.edit.alarm.policy.alarm.select.name1': '告警ID/事件ID',
    'alarm.edit.alarm.policy.alarm.select.relation1': '包含',
    'alarm.edit.alarm.add.sucess': '添加成功',
    'alarm.edit.alarm.enable.tips': '启用/禁用会清理掉当前告警并重新同步，且同步会耗费一定时间',
    'alarm.edit.alarm.modify.sucess': '修改成功',
    'alarm.edit.alarm.delete.sucess': '删除成功',
    'alarm.start.refresh':'刷新任务启动成功',
    'alarm.close':'关闭',
    sys_btn_cancel:'取消',
    sys_confirm_content:'刷新会清理掉当前告警并重新同步，且同步会耗费一定时间',
    sys_resource_valid_audit_char:
      '输入不能包含特殊字符！其中，特殊字符为#+&|<>\'";\\',
    site_name_valid_audit_char: '输入不能包含特殊字符！其中，特殊字符为#%()/+&|<>\'";\\',
    sys_valid_name_length_info: '字符长度不能超过{0}',
    sys_required_val: '必填项',
    sys_valid_positive_integer: '输入必须为0或正整数', 
    sys_format_valid_char_out_range: '输入值超出范围：{0}-{1}。',
    sys_valid_positive_decimal: '输入必须为0.0或正小数，且最多包含三位小数',
    sys_valid_no_zero_positive_decimal: '输入必须为正小数，且最多包含三位小数',
    sys_valid_positive_no_zero_integer: '输入必须为正整数',
    sys_valid_positive_all_decimal: '输入必须为小数，且最多包含三位小数',
    sys_valid_positive_all_integer: '输入必须为整数',
    sys_format_valid_char_out_range: '输入值超出范围：{0}-{1}。',
    sys_valid_numberAndComma: '输入只能包含数字和英文逗号',
    sys_valid_alarmId_max: '最多输入{0}个告警ID',
    sys_valid_audit_char: '输入不能包含特殊字符！其中，特殊字符为#%&+=|><\';?\"\\\/()',
    sys_valid_char_out_range: '字符长度超出范围：{0}-{1}。',
    sys_valid_name_less_length_info: '字符长度不能少于{0}',
    'alarm.edit.alarm.enable': '已启用',
    'alarm.edit.alarm.disabled': '未启用',
    'alarm.edit.rule.status':'规则状态',
    'alarm.preset.tips':'预置模型不支持修改',
    'intelligent.incident.task.manage.title': '智能事件任务管理',
    'intelligent.incident.task.manage.input.name': '请输入任务名称',
    'intelligent.incident.task.manage.table.task.name': '任务名称',
    'intelligent.incident.task.manage.table.algorithm': '算法',
    'intelligent.incident.task.manage.table.solution.type': '解决方案类型',
    'intelligent.incident.task.manage.table.knowledge.base': '知识库',
    'intelligent.incident.task.manage.table.train.status': '训练状态',
    'intelligent.incident.task.manage.table.last.modify.time': '最后一次修改时间',
    'intelligent.incident.task.manage.table.train.status.success': '训练成功',  
    'intelligent.incident.task.manage.table.train.status.fail': '训练失败', 
    'intelligent.incident.task.manage.table.train.status.running': '训练中',
    'intelligent.incident.task.manage.table.train.status.partial.success': '部分成功',
    'intelligent.incident.task.manage.table.operation': '操作',
    'intelligent.incident.task.manage.table.operation.train': '训练',   
    'intelligent.incident.task.manage.table.operation.modify': '修改',            
    'intelligent.incident.task.manage.table.operation.delete.tip': '是否确定删除该任务？',
    'intelligent.incident.task.manage.table.operation.train.tip': '是否立即训练模型？',
    'intelligent.incident.task.manage.table.operation.delete.failure': '删除任务失败',
    'intelligent.incident.task.manage.table.operation.train.failure': '训练模型失败',
    'intelligent.incident.task.manage.table.loading': '加载中...',
    'intelligent.incident.task.manage.table.no.data': '暂无数据',
    'intelligent.incident.task.manage.table.query.fail': '查询任务列表失败',        
    'intelligent.incident.task.manage.create.task': '创建任务',  
    'intelligent.incident.task.manage.select.knowledge.base': '选择知识库',  
    'intelligent.incident.task.manage.config.algorithm': '配置算法', 
    'intelligent.incident.task.manage.basic.information': '基本信息', 
    'intelligent.incident.task.manage.solution': '解决方案', 
    'intelligent.incident.task.manage.solution.query.fail': '查询解决方案列表失败',
    'intelligent.incident.task.manage.solution.please.select': '请选择解决方案',
    'intelligent.incident.task.manage.knowledge.query.fail': '查询知识库列表失败',
    'intelligent.incident.task.manage.knowledge.tip.select.appropriate': '建议根据解决方案选择合适的知识库',
    'intelligent.incident.task.manage.knowledge.please.select': '请选择知识库',
    'intelligent.incident.task.manage.algorithm.query.fail': '查询算法列表失败',
    'intelligent.incident.task.manage.algorithm.please.select': '请选择算法包',  
    'intelligent.incident.task.manage.algorithm.no.param.list': '未查询到算法参数列表',          
    'intelligent.incident.task.manage.algorithm.param.query.fail': '查询算法参数列表失败',         
    'intelligent.incident.task.manage.algorithm.param.decline.end.time.tip': '衰退时间必须小于强制结束时间',     
    'intelligent.incident.task.manage.import.knowledge.base': '导入知识库',    
    'intelligent.incident.task.manage.next.step': '下一步', 
    'intelligent.incident.task.manage.previous.step': '上一步', 
    'intelligent.incident.task.manage.parameter.name': '参数名称',      
    'intelligent.incident.task.manage.default.value': '默认值', 
    'intelligent.incident.task.manage.task.analysis.result.visible': '是否分析结果可见', 
    'intelligent.incident.task.manage.task.analysis.result.visible.yes': '是',
    'intelligent.incident.task.manage.task.analysis.result.visible.no': '否',  
    'intelligent.incident.task.manage.task.train.period': '任务训练周期', 
    'intelligent.incident.task.manage.train.cycle.please.select': '请选择任务训练周期',      
    'intelligent.incident.task.manage.day.one': '1天', 
    'intelligent.incident.task.manage.day.three': '3天', 
    'intelligent.incident.task.manage.day.seven': '7天', 
    'intelligent.incident.task.manage.task.description': '任务描述', 
    'intelligent.incident.task.manage.task.description.placeholder': '请输入',     
    'intelligent.incident.task.manage.completed': '完成', 
    'intelligent.incident.task.manage.save.fail.tip': '保存信息失败', 
    'intelligent.incident.task.manage.create.success': '保存任务成功',
    'intelligent.incident.task.manage.error.tip': '错误',  
    'intelligent.incident.task.manage.tip': '提示',      
    'intelligent.incident.task.manage.view.fail': '查看任务信息失败',  
    'intelligent.incident.task.manage.view.update.fail': '查询任务信息失败',  
    'intelligent.incident.task.manage.please.wait': '请等待...',    
    'intelligent.incident.task.manage.task.detail': '任务详情', 
    'intelligent.incident.task.manage.task.id': '任务ID',
    'intelligent.incident.task.manage.task.detail.confirm': '确定',       
    'intelligent.incident.task.manage.task.detail.no.support.modify': '无权限的任务不支持修改',  
  },
  'en-us': {
    'alarm.edit.config.rule': 'Rule configuration',
    'alarm.edit.alarm.config.rule': 'Alarm Rule configuration',
    'alarm.edit.full.alarm': 'Enable the Full Alarm Policy',
    'alarm.edit.fmOrIntelligent.alarm': 'Filter by performance threshold alarms',
    'alarm.edit.fm.alarm': ' Performance Threshold Alarm',
    'alarm.edit.alarm.policy.alarm': 'Filter by alarm level',
    'alarm.edit.alarm.policy.alarm.critical': 'Critical',
    'alarm.edit.alarm.policy.alarm.major': 'Major',
    'alarm.edit.alarm.policy.alarm.minor': 'Minor',
    'alarm.edit.alarm.policy.alarm.info': 'Info',
    'alarm.edit.alarm.policy.clear.alarm': 'Filter by alarm cleaning policy',
    'alarm.edit.alarm.policy.manual.clear.alarm': 'Manual Clear',
    'alarm.edit.alarm.policy.auto.clear.alarm': 'Auto Clear',
    'alarm.edit.alarm.trustlist': 'Enable the alarm whitelist policy',
    'alarm.edit.alarm.blocklist': 'Enable the alarm/event blacklist policy',
    'alarm.edit.cancel': 'Cancel',
    'alarm.edit.confirm': 'Confirm',
    'alarm.edit.add': 'Create',
    'alarm.edit.delete': 'Delete',
    'alarm.edit.edit': 'Edit',
    'alarm.edit.application': 'Save',
    'alarm.edit.alarm.name': 'Rule Name',
    'alarm.edit.alarm.enable': 'Enable',
    'alarm.edit.alarm.disabled': 'Disabled',
    'alarm.edit.alarm.id': 'Alarm ID/Event ID',
    'alarm.edit.operation': 'Operation',
    'iemp.eam.alarmselector.search.tips': 'Enter a keyword.',
    'fm.severity.check.box.group.tip': 'Min selected: 1',
    'iemp.fm.alarmselector.column.groupName': 'Group Name',
    'iemp.fm.component.max.number': ' Max. allowed:1,000',
    'iemp.fm.component.selected.number': 'Selected: ',
    'iemp.fm.component.selected.alarm': 'Select Alarms',
    'alarm.range': 'Alarm Range',
    'alarm.rule.name': 'Alarm Rule Name',
    'alarm.name': 'Name',
    'alarm.edit.rule.status':'Rule Status',
    'alarm.edit.operation.copy':'Copy',
    'alarm.edit.operation.delete':'Delete',
    'alarm.modify.person':'Last Modify Person',
    'alarm.modify.time':'Last Modify Time',
    'alarm.range.tip': 'If the alarm is selected as a timeline alarm, disable the rendering rule for switching to the health status',
    'alarm.edit.refresh': 'Sync',
    'alarm.edit.alarm.policy.alarm.id': 'Filter by alarm/event information',
    'alarm.edit.alarm.policy.alarm.source': 'Condition',
    'alarm.edit.alarm.policy.alarm.source.all': 'All alarm Source',
    'alarm.edit.alarm.policy.alarm.filter.conditions': 'Filter conditions',
    'alarm.edit.alarm.policy.alarm.select': 'Please select',
    'alarm.edit.alarm.policy.alarm.select.tips': 'The alarm ID/event ID can only contain numbers.Multiple IDs can be entered, each separated with a comma.  ID cannot be 0. The length of each ID cannot exceed 10',
    'alarm.edit.alarm.policy.alarm.select.name1': 'Alarm ID/Event ID',
    'alarm.edit.alarm.policy.alarm.select.relation1': 'IS IN',
    'alarm.edit.alarm.add.sucess': 'Add Sucess',
    'alarm.edit.alarm.modify.sucess': 'Modify Sucess',
    'alarm.edit.alarm.delete.sucess': 'Delete Sucess',
    'alarm.start.refresh': 'The refresh task was successfully started',
    'alarm.close':'Close',
    sys_btn_cancel:'Cancel',
    sys_confirm_content:'Refreshing will clear the current alarms and resynchronize, and synchronization takes some time',
    sys_resource_valid_audit_char: 'The value cannot contain the following special characters: #+&|<>\'";\\',
    site_name_valid_audit_char: 'The value cannot contain the following special characters: #%()/+&|<>\'";\\',
    sys_valid_name_length_info: 'The length cannot exceed {0} characters.',
    sys_required_val: 'Mandatory field',
    sys_valid_positive_integer: 'Enter 0 or a positive integer.',
    sys_format_valid_char_out_range: 'Input value Out of Range:{0}-{1}.',
    sys_valid_positive_decimal: 'The value must be 0.0 or a positive decimal, and contain a maximum of three decimal places.',
    sys_valid_no_zero_positive_decimal: 'The value must be a positive decimal, and contain a maximum of three decimal places.',
    sys_valid_positive_no_zero_integer: 'Enter a positive integer.',
    sys_valid_positive_all_decimal: 'The value must be a decimal, and contain a maximum of three decimal places.',
    sys_valid_positive_all_integer: 'Enter a integer.',
    sys_format_valid_char_out_range: 'Input value Out of Range:{0}-{1}.',
    sys_valid_numberAndComma: 'The value can contain only digits and commas (,).',
    sys_valid_alarmId_max: 'A maximum of {0} alarm IDs can be entered.',
    sys_valid_audit_char: 'The value cannot contain the following special characters: #%&+=|><\';?\"\\\/()',
    sys_valid_char_out_range: 'Character Length Out of Range:{0}-{1}.',
    sys_valid_name_less_length_info: 'Character length cannot be less than {0}.',
    'alarm.edit.alarm.enable.tips': 'Enable/Disabled will clear the current alarms and resynchronize, and synchronization takes some time',
    'alarm.preset.tips':'The preset model cannot be modified',
    'intelligent.incident.task.manage.title': 'Intelligent Incident Task Management',
    'intelligent.incident.task.manage.input.name': 'Enter the task name.',
    'intelligent.incident.task.manage.table.task.name': 'Task Name',
    'intelligent.incident.task.manage.table.algorithm': 'Algorithm',
    'intelligent.incident.task.manage.table.solution.type': 'Solution Type',
    'intelligent.incident.task.manage.table.knowledge.base': 'Knowledge Base',
    'intelligent.incident.task.manage.table.train.status': 'Train Status',
    'intelligent.incident.task.manage.table.last.modify.time': 'Last Modification Time',
    'intelligent.incident.task.manage.table.train.status.success': 'Successful',  
    'intelligent.incident.task.manage.table.train.status.fail': 'Failed', 
    'intelligent.incident.task.manage.table.train.status.running': 'In training',
    'intelligent.incident.task.manage.table.train.status.partial.success': 'Partially successful',
    'intelligent.incident.task.manage.table.operation': 'Operation',  
    'intelligent.incident.task.manage.table.operation.train': 'Train',  
    'intelligent.incident.task.manage.table.operation.modify': 'Modify', 
    'intelligent.incident.task.manage.table.operation.delete.tip': 'Are you sure to delete the task?',
    'intelligent.incident.task.manage.table.operation.train.tip': 'Do you want to train the model immediately?',
    'intelligent.incident.task.manage.table.operation.delete.failure': 'Failed to delete the task.',
    'intelligent.incident.task.manage.table.operation.train.failure': 'Failed to train the model.',
    'intelligent.incident.task.manage.table.loading': 'Loading...',
    'intelligent.incident.task.manage.table.no.data': 'No records found.',
    'intelligent.incident.task.manage.table.query.fail': 'Failed to query the task list.',  
    'intelligent.incident.task.manage.create.task': 'Create Task',  
    'intelligent.incident.task.manage.select.knowledge.base': 'Select Knowledge Base',  
    'intelligent.incident.task.manage.config.algorithm': 'Configuration Algorithm', 
    'intelligent.incident.task.manage.basic.information': 'Basic Information', 
    'intelligent.incident.task.manage.solution': 'Solution', 
    'intelligent.incident.task.manage.solution.query.fail': 'Failed to query the solution list.',
    'intelligent.incident.task.manage.solution.please.select': 'Please select a solution.',
    'intelligent.incident.task.manage.algorithm.no.param.list': 'No algorithm parameter list found.',  
    'intelligent.incident.task.manage.knowledge.query.fail': 'Failed to query the knowledge base list.', 
    'intelligent.incident.task.manage.knowledge.tip.select.appropriate': 'It is recommended to select an appropriate knowledge base based on the solution.', 
    'intelligent.incident.task.manage.algorithm.param.decline.end.time.tip': 'The decline time must be less than the maximum incident end time.', 
    'intelligent.incident.task.manage.knowledge.please.select': 'Please select the knowledge base.',
    'intelligent.incident.task.manage.algorithm.query.fail': 'Failed to query the algorithm list.', 
    'intelligent.incident.task.manage.algorithm.please.select': 'Please select the algorithm package.',   
    'intelligent.incident.task.manage.algorithm.param.query.fail': 'Failed to query the algorithm parameter list.',   
    'intelligent.incident.task.manage.import.knowledge.base': 'Import Knowledge Base',    
    'intelligent.incident.task.manage.next.step': 'Next step', 
    'intelligent.incident.task.manage.previous.step': 'Previous Step', 
    'intelligent.incident.task.manage.parameter.name': 'Parameter Name',      
    'intelligent.incident.task.manage.default.value': 'Default value', 
    'intelligent.incident.task.manage.task.analysis.result.visible': 'Is the analysis result visible', 
    'intelligent.incident.task.manage.task.analysis.result.visible.yes': 'Yes',
    'intelligent.incident.task.manage.task.analysis.result.visible.no': 'No',  
    'intelligent.incident.task.manage.task.train.period': 'Task Training Cycle', 
    'intelligent.incident.task.manage.train.cycle.please.select': 'Please select the task training cycle.',  
    'intelligent.incident.task.manage.day.one': '1 day', 
    'intelligent.incident.task.manage.day.three': '3 days', 
    'intelligent.incident.task.manage.day.seven': '7 days', 
    'intelligent.incident.task.manage.task.description': 'Task Description', 
    'intelligent.incident.task.manage.task.description.placeholder': 'Please enter',  
    'intelligent.incident.task.manage.completed': 'Completed',  
    'intelligent.incident.task.manage.save.fail.tip': 'Saving failed.', 
    'intelligent.incident.task.manage.create.success': 'The task is saved successfully.',
    'intelligent.incident.task.manage.error.tip': 'Error',  
    'intelligent.incident.task.manage.tip': 'Tip',  
    'intelligent.incident.task.manage.view.fail': 'Failed to view the task information.',  
    'intelligent.incident.task.manage.view.update.fail': 'Failed to query task information.',  
    'intelligent.incident.task.manage.please.wait': 'Please wait...',  
    'intelligent.incident.task.manage.task.detail': 'Task Details', 
    'intelligent.incident.task.manage.task.id': 'Task ID',
    'intelligent.incident.task.manage.task.detail.confirm': 'Confirm',  
    'intelligent.incident.task.manage.task.detail.no.support.modify': 'Tasks without permissions do not support modification.', 
  },
};
