/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext} from 'react';
import {BUSINESS_TOPO_CONTEXT} from '../const';
import {Tooltip} from 'eview-ui';
import {dateTimeFormat, jumpAlarmLogPage, jumpEventLogPage} from '../../../commonUtil/tools';
import PropTypes from 'prop-types';
import orangePng from '@images/timeLine/orange2.png';
import ringPng from '@images/timeLine/ring.png';
import {$t} from '@util';

function AnalysisDetail(props) {
  const {record} = props;

  const analysisJump = () => {
    let win = window.open(`/eviewwebsite/index.html#path=/aiOpsService&subMenu=associationAnalysisPage?refr-flags=mH&theme=dark&indicatorId=${
      encodeURIComponent(record.indicatorId)
    }&selectedAssociationTask=${
      encodeURIComponent(record.selectedAssociationTask)
    }&executionTime=${
      encodeURIComponent(record.executionTime)
    }`);
    if (win) {
      win.opener = null;
    }
  };

  return (
    <div className="detail_panel" onClick={analysisJump}>
      <div className="detail_content">
        <div className={`alarm_level_${record.severity}_icon`} />
        <Tooltip content={record.alarmName} placement='topLeft' color='#393939' overlayStyle={{color: '#FFF'}}>
          <div className="detail_title" style={{color: 'white', display: 'inline-block', width: '300px'}}>
            {record.alarmName}
          </div>
        </Tooltip>
        <Tooltip content={record.additionalInformation} placement='topLeft' color='#393939'
          overlayStyle={{color: '#FFF'}}
        >
          <p className="detail_sub_title">
            {record.additionalInformation}
          </p>
        </Tooltip>
        <div style={{marginTop: '10px', marginBottom: '10px'}}>
          <div className='time_icon' />
          <p className='detail_alarm_value'>{dateTimeFormat(record.occurUtc)}</p>
        </div>
      </div>
      <div style={{position: 'absolute', bottom: '30px', right: '150px'}}>
        {record.analysisStatus !== null && record.analysisStatus !== undefined &&
          <div
            className="detailsAlarmLabel2"
            style={{
              backgroundImage: `url(${orangePng})`,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <img src={ringPng} alt="" />
            <span>
              <Tooltip content={record.alarmName} placement='topLeft' color='#393939' overlayStyle={{color: '#FFF'}} />
              {$t(
                `${
                  'timeline.details.intelligent.analysis2'
                }`,
              )}
            </span>
          </div>}

      </div>
    </div>
  );
}

AnalysisDetail.propTypes = {
  record: PropTypes.shape({
    alarmName: PropTypes.string,
    occurUtc: PropTypes.string,
    additionalInformation: PropTypes.string,
    csn: PropTypes.string,
  }).isRequired,
};

export default AnalysisDetail;
