import '@pages/businesstopo/css/tooltip/errConnection.less';
import React, {useEffect, useRef, useState} from 'react';
import dataBaseSvg from '../../../../apps/businesstopo/assets/database_icon.svg';
import * as echarts from 'echarts';
import closeIcon from '../../../../apps/businesstopo/assets/closeicon.svg';
import {querysitealarmlinkinfo} from '../../api';
import {$t, registerResource} from '@util';
import i18n from '../../locales/tooltip';
import PropTypes from 'prop-types';
import {isZh} from '../../../../commonUtil';

function getXposition(links, linkPosiotn, charts, isDownMoreFalg) {
  let xPos = 80;
  for (let j = 0; j < links.length; j++) {
    let nodeName = links[j].modelName;
    let protocol = links[j].protocol;
    let node = {
      nodeName,
      protocol,
      value: [xPos, isDownMoreFalg ? 0 : 500],
      symbolSize: 60,
      symbol: `image://${dataBaseSvg}`,
      itemStyle: {
        fontSize: 12,
        textAlign: 'center',
      },
    };

    let link = {
      coords: [
        [linkPosiotn, isDownMoreFalg ? 125 : 375],
        [xPos, isDownMoreFalg ? 125 : 375],
        [xPos, isDownMoreFalg ? 50 : 490],
      ],
    };
    xPos += 400;
    charts.nodes.push(node);
    charts.linesData.push(link);
  }
  return xPos;
}

function setMinXposition(links, charts, isDownMoreFalg) {
  if (links.length === 0) {
    return;
  }
  let interval = 400;
  let centerXPostion = charts.nodes[0].value[0];
  let length = ((links.length - 1) / 2).toFixed(1);
  let xPos = centerXPostion - length * 400;
  let linkPosiotn = xPos;
  if (links.length === 1) {
    xPos = centerXPostion;
  }
  for (let j = 0; j < links.length; j++) {
    let nodeName = links[j].modelName;
    let protocol = links[j].protocol;
    let node = {
      nodeName,
      protocol,
      value: [xPos, isDownMoreFalg ? 0 : 500],
      symbolSize: 60,
      symbol: `image://${dataBaseSvg}`,
      itemStyle: {
        fontSize: 12,
        textAlign: 'center',
      },
    };
    let link = {
      coords: [
        [linkPosiotn, isDownMoreFalg ? 125 : 375],
        [xPos, isDownMoreFalg ? 125 : 375],
        [xPos, isDownMoreFalg ? 50 : 490],
      ],
    };
    xPos += interval;
    charts.nodes.push(node);
    if (links.length > 1) {
      charts.linesData.push(link);
    }
  }
}

function getFormatter() {
  return function(item) {
    const paramStr = item.data.nodeName;
    const strArr = paramStr.split('');
    let totalLength = 0;
    let shortLength = 0;
    let reStr = '';
    for (let i = 0; i < strArr.length; i++) {
      if (strArr[i].charCodeAt() >= 97 && strArr[i].charCodeAt() <= 122) {
        totalLength += 0.075;
      } else {
        totalLength += 0.1;
      }
    }
    if (totalLength > 1) {
      for (let i = 0; i < strArr.length; i++) {
        if (strArr[i].charCodeAt() >= 97 && strArr[i].charCodeAt() <= 122) {
          shortLength += 0.075;
        } else {
          shortLength += 0.1;
        }

        if (shortLength >= 0.75) {
          reStr = `${reStr}...`;
          return reStr;
        } else {
          reStr = reStr + strArr[i];
        }
      }
    } else {
      reStr = paramStr;
    }
    return reStr;
  };
}

function pushNode(charts, moName, length, data) {
  charts.nodes.push({
    nodeName: moName,
    value: [80 + length * 400, 250],
    symbolSize: 60,
    symbol: `image://${dataBaseSvg}`,
    itemStyle: {
      fontSize: 12,
      textAlign: 'center',
    },
  });
  if (data.toLinks.length > 0) {
    charts.linesData.push({
      coords: [
        [80 + length * 400, 200],
        [80 + length * 400, 125],
      ],
    });
  }
  if (data.fromLinks.length > 0) {
    charts.linesData.push({
      coords: [
        [80 + length * 400, 250],
        [80 + length * 400, 380],
      ],
    });
  }
}

function ErrConnection(props) {
  registerResource(i18n, 'tooltip');
  let chartDomRef = useRef();
  const [chooseAlarm, setChooseAlarm] = useState(null);
  const [alarmData, setAlarmData] = useState({
    dataList: [],
  });

  const [show, setShow] = useState('none');
  const [isErr, setIsErr] = useState(false);
  useEffect(() => {
    if (!chartDomRef.current) {
      return;
    }

    let selectedTime = 0;

    // 请求数据
    if (props.errorData.display) {
      // 如果是快照
      if (props.errorData.isTimeTrack) {
        selectedTime = JSON.parse(sessionStorage.getItem('topoSession') || '{}');
      }
      querysitealarmlinkinfo({
        ...props.errorData.params,
        timeStamp: selectedTime,
      }, ({data, resultCode}) => {
        setAlarmData({
          dataList: data.alarms,
        });

        if (data.toLinks.length === 0 && data.fromLinks.length === 0) {
          setShow('none');
          return;
        } else {
          setShow('block');
        }

        let element = document.querySelector('.chart');
        let linksLength = Math.max(data.toLinks.length, data.fromLinks.length);
        if (linksLength > 4) {
          let percent = ((linksLength) / 4) * 100;
          element.style.width = `${percent}%`;
        }

        let chartObj = echarts.init(chartDomRef.current);
        window.chartObj = chartObj;

        if (data.alarms.length === 0) {
          setIsErr(false);
          chartObj.setOption(getOption(props.errorData.moName, props.errorData.params.instanceId, data, false));
        } else {
          setIsErr(true);
          if (data.toLinks.length > 0 || data.fromLinks.length > 0) {
            chartObj.setOption(getOption(props.errorData.moName, props.errorData.params.instanceId, data, true));
          }
        }
      });
    } else {
      // 清空
      setAlarmData({
        total: 0,
        dataList: [],
        moTypeList: [],
        jump: '',
      });
    }
    return () => {
      chartObj.dispose();
    };
  }, []);
  let grid = {
    left: 20,
    right: 10,
    top: 30,
    bottom: 60,
  };

  const getXAxis = xPos => ({
    min: -50,
    max: xPos - 250,
    show: false,
    type: 'value',
  });

  const getYAxis = (data) => {
    if (data.toLinks.length > 0 && data.fromLinks.length > 0) {
      return {
        min: 0,
        max: 550,
        show: false,
        type: 'value',
      };
    }

    if (data.toLinks.length === 0 && data.fromLinks.length > 0) {
      return {
        min: 250,
        max: 550,
        show: false,
        type: 'value',
      };
    }

    if (data.toLinks.length > 0 && data.fromLinks.length === 0) {
      return {
        min: 0,
        max: 250,
        show: false,
        type: 'value',
      };
    }
    return {};
  };
  const getOption = (moName, id, data, iserr) => {
    let linksLength = Math.max(data.toLinks.length, data.fromLinks.length);
    let isDownMoreFalg = data.toLinks.length >= data.fromLinks.length;
    let length = ((linksLength - 1) / 2).toFixed(1);

    let linkPosiotn = linksLength > 4 ? 680 : length * 400;
    if (linksLength === 1) {
      linkPosiotn = 80;
    }
    let charts = {
      nodes: [],
      linesData: [],
    };
    pushNode(charts, moName, length, data);
    let xPostion;
    if (data.toLinks.length >= data.fromLinks.length) {
      xPostion = getXposition(data.toLinks, linkPosiotn, charts, isDownMoreFalg);
      setMinXposition(data.fromLinks, charts, !isDownMoreFalg);
    } else {
      xPostion = getXposition(data.fromLinks, linkPosiotn, charts, isDownMoreFalg);
      setMinXposition(data.toLinks, charts, !isDownMoreFalg);
    }
    return {
      backgroundColor: '#303030',
      grid,
      xAxis: getXAxis(xPostion),
      yAxis: getYAxis(data),
      tooltip: {
        trigger: 'item', // 触发方式是鼠标悬浮
        backgroundColor: '#303030',
        textStyle: {
          color: '#fff', // 设置文字颜色为白色
          fontSize: 14, // 设置字体大小
        },
        appendTo: document.body,
        extraCssText: 'max-width: 300px; word-wrap: break-word; text-overflow: ellipsis;',
        formatter(params) {
          if (!params.data.nodeName) {
            return {};
          }
          if (isZh) {
            if (params.data.nodeName && params.data.protocol) {
              return `名称: ${params.data.nodeName
              }<br/>协议: ${params.data.protocol}`;
            } else {
              return `名称: ${params.data.nodeName}`;
            }
          } else {
            if (params.data.nodeName && params.data.protocol) {
              return `Name: ${params.data.nodeName
              }<br/>Protocol: ${params.data.protocol}`;
            } else {
              return `Name: ${params.data.nodeName}`;
            }
          }
        },
        enterable: false,
        hideDelay: 100,
      },
      series: [
        {
          type: 'graph',
          coordinateSystem: 'cartesian2d',
          label: {
            show: true,
            position: 'insideBottom',
            distance: -10,
            color: '#F5F5F5',
            formatter: getFormatter(),
          },
          data: charts.nodes,
        },
        {
          type: 'lines',
          polyline: true,
          coordinateSystem: 'cartesian2d',
          lineStyle: {
            type: 'solid',
            width: 3,
            color: '#2E94FF',
          },

          label: {
            show: true, // 显示标签
            position: 'middle', // 文字显示在线条中间
            formatter: '{b}', // 使用 'b' 显示节点名称
            fontSize: 14, // 字体大小
            color: '#ff0000', // 标签文字颜色
          },

          data: charts.linesData,
        },
      ],
    };
  };

  const Dom = (
    <div id="errConnection" className={isErr ? 'connectionErrHeight' : ''} style={{display: show}}>
      <div className='triangle' />

      <div className="header">
        <div
          className='title'
        >
          {isZh && `${props.errorData.moName}${$t('error.and')}${props.errorData.name}${$t('error.connect.info')}`}
          {!isZh && `Connection between ${props.errorData.moName} and ${props.errorData.name}`}
        </div>
        <div className="close" style={{
          backgroundImage: `url(${closeIcon})`,
        }}
        onClick={e => {
          setShow('none');
          setChooseAlarm(null);
        }}
        />
      </div>
      <div className="chartScroll">
        <div ref={chartDomRef} id='errConnectionChart' className='chart' />

      </div>
    </div>
  );
  return Dom;
}

ErrConnection.prototype = {
  errorData: PropTypes.shape({
    moName: PropTypes.string,
  }).isRequired,
};

export default ErrConnection;
