/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useRef, useImperativeHandle, useReducer} from 'react';
import {Table, TextField} from 'eview-ui';
import {modal, $t, validate} from '@util';
import {BUSINESS_TOPO_CONTEXT, OPERATE_SUCCESS_CODE} from '@pages/businesstopo/const';

let measureObjectTable;
let measureSearch;
const DataSourceObjectTable = ({
  setObjectTable, parentDataHandle,
  getCreateTaskAllInfo, cRef,
}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {objectTable} = state;

  // 表格查询
  const queryObjTable = (selectedKeys, queryFn, params, callback) => {
    queryFn(params, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setObjectTable({isObjectNoData: true});
        modal.error($t('kpi.task.common.error.tip'), $t('kpi.view.task.query.object.table.fail'));
        return;
      }

      if (!res.data || res.data.length) {
        setObjectTable({isObjectNoData: true});
        return;
      }
      let columns = res.data.indexes ? res.data.indexes.map(item => ({title: item, id: item, allowSort: false})) :
        [];
      let rows = [];
      if (res.data.mos && res.data.mos.length) {
        res.data.mos.forEach((item, index) => {
          rows.push([
            `${parentDataHandle.getMeasureData().indexId}_${item.displayValue}`, `${item.displayValue}`,
            `${item.originalValue}`, item.indexValues, ...item.indexValues,
          ]);
        });
      }

      if (callback) {
        callback(columns, rows);
      } else {
        // 如果存在测量对象表格禁选
        let checkAllData = [];
        dispatch({
          objectTable: getObjectTableData(rows, columns, checkAllData, []),
        });
      }
    }, err => {
      setObjectTable({isObjectNoData: true});
      dispatch({type: 'setIsObjTableLoading', isObjTableLoading: false});
    });
  };

  // 表格单选
  const onCheck = (row, measureObjectCheckedRow, event) => {
    if (row.checked && event.shiftKey) {// 屏蔽shift单击多选表格
      setObjectTable({measureObjectCheckedRow: parentDataHandle.getCheckedMeasureIndexTable()});
      return;
    }

    if (row.checked) {// 选中
      parentDataHandle.setMeasureData({
        id: `${parentDataHandle.getMeasureData().rowId}_${row.id}`,
        originalValue: row._org[2],
        displayValue: row._org[1],
      });
      parentDataHandle.setCheckedMeasureIndexTable([...measureObjectCheckedRow]);
    }

    dispatch({
      objectTable: {
        ...getCreateTaskAllInfo().objectTable,
        measureObjectCheckedRow,
      },
    });
  };

  // 表格分页
  const handlePageChange = ({pageIndex, pageSize}) => {
    setObjectTable({
      pageIndex,
      pageSize,
      measureObjectPageResults: objectTable.objectTableList.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
    });
  };

  // 获取测量对象表格数据
  const getObjectTableData = (rows, columns, checkAllData, measureObjectCheckedRow) => ({
    ...getCreateTaskAllInfo().objectTable,
    measureObjectPageResults: rows.slice(0, 50),
    pageIndex: 1,
    pageSize: 50,
    objectTableList: rows,
    initialObjectTableList: rows,
    measureObject: '',
    measureObjectTableColumns: [
      {title: '', display: false, id: 'keyId'},
      {title: '', display: false, id: 'displayValue'},
      {title: '', display: false, id: 'originalValue'},
      {title: '', display: false, id: 'indexValues'},
      ...columns,
    ],
    measureTableKey: new Date().getTime(),
    measureObjectCheckedRow,
    isObjectNoData: rows.length ? false : true,
    disableRowIdsObj: checkAllData,
    disableHeader: true,
  });

  const getClearObject = () => {
    return {
      measureObjectPageResults: [],
      measureObjectTableColumns: [],
      measureTableKey: new Date().getTime(),
      measureObjectCheckedRow: [],
      isObjectNoData: true,
      disableRowIdsObj: [],
      disableHeader: false,
      measureObject: '',
      pageIndex: 1,
      pageSize: 50,
      objectTableList: [],
      initialObjectTableList: [],
    };
  };

  // 传给父组件的方法
  useImperativeHandle(cRef, () => ({
    getObjectTableData,
    queryObjTable,
    getClearObject,
  }));

  // 表格过滤
  const onFilter = (event) => {
    if (event.keyCode !== 13 && event.type !== 'click') {
      return;
    }
    let indexRows = objectTable.initialObjectTableList;
    let filterRows = indexRows.filter(item => {
      let filterList = item[3].filter(ele => (ele.toLowerCase().indexOf(objectTable.measureObject.toLowerCase()) > -1));
      return filterList.length;
    });
    setObjectTable({
      measureObjectPageResults: filterRows.slice(0, 50),
      objectTableList: filterRows,
      pageIndex: 1,
      pageSize: 50,
    });
  };

  return (
    <>
      <div className="df-input-frame" style={{position: 'relative'}}>
        <TextField
          inputStyle={{paddingRight: '28px', width: '100%'}} hintType='tip'
          value={objectTable.measureObject} id="measureObjectInput"
          placeholder={$t('kpi.new.task.resource.measure.object')}
          onChange={val => setObjectTable({measureObject: val})} tipDuration={6000}
          autoComplete="off" style={{width: '100%'}}
          validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 128)}
          ref={(ele) => measureSearch = ele}
          onKeyDown={event => onFilter(event)}
        />
        <div className="df-icon-search" onClick={event => onFilter(event)} />
      </div>
      <div className='measure_border' id='measureObject' style={{overflow: 'hidden' }}>
        {(objectTable.isObjectNoData) ?
          <div style={{padding: '10px', textAlign: 'center', color:'#f5f5f5'}}>{$t('kpi.new.task.resource.no.data')}</div> :
          <Table key={objectTable.measureTableKey}
            columns={objectTable.measureObjectTableColumns}
            enableCheckBox={true} keyIndex={0} pageSizeOptions={[50]} id="measureObjTable"
            disableRowIds={objectTable.disableRowIdsObj}
            pageSize={objectTable.pageSize}
            currentPage={objectTable.pageIndex}
            checkType="single"
            pagingType="select"
            onPageChange={pageIndex => handlePageChange({pageIndex, pageSize: objectTable.pageSize})}
            onPageSizeChange={pageSize => handlePageChange({pageSize, pageIndex: 1})}
            enablePagination={true} enableScrollPagination={true}
            disableHeaderCheckbox={objectTable.disableHeader}
            recordCount={objectTable.objectTableList.length} splitPagination={true}
            checkedRows={objectTable.measureObjectCheckedRow}
            onRowCheck={onCheck} dataset={objectTable.measureObjectPageResults}
            ref={table => {
              measureObjectTable = table;
            }}
          />}
      </div>
    </>
  );
};

export default DataSourceObjectTable;
