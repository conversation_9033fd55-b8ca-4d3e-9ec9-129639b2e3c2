/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useImperativeHandle} from 'react';
import {Select, Tree, TextField} from 'eview-ui';
import {modal, $t, validate} from '@util';
import * as api from '../api';
import {BUSINESS_TOPO_CONTEXT, OPERATE_SUCCESS_CODE} from '@pages/businesstopo/const';

let measureUnitTree;
let unitEleSearch;
const DataSourceUnitTree = ({
  setUnitTree,
  getCreateTaskAllInfo,
  getClearObject,
  cRef,
  queryObjTable,
  parentDataHandle,
}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {unitTree} = state;
  const MEASURE_TYPE = [
    {
      text: $t('kpi.new.task.resource.all'),
      value: 'all',
    },
    {
      text: $t('kpi.new.task.resource.measure.unit'),
      value: 'group',
    },
    {
      text: $t('kpi.new.task.resource.measure.index'),
      value: 'unit',
    },
  ];

  const MEASURE_TYPE_MAPS = {
    unit: $t('kpi.new.task.resource.measure.index'),
    group: $t('kpi.new.task.resource.measure.unit'),
    all: $t('task.filter.measure.keyword'),
  };

  // 传给父组件的方法
  useImperativeHandle(cRef, () => ({
    query: queryUnitTree,
    selectedUnitNodes: selectedUnitNodesHandler,
    getClearUnitTreeInfo,
  }));

  // 递归生成树节点
  const readNodes = (nodes, arr = []) => {
    let measureData = parentDataHandle.getMeasureData();
    return nodes.map(item => {
      let preItem = {
        ...item,
        text: item.name,
        id: item.chkDisabled ? `${measureData.checkedNetType}${item.measUnitKey}` :
          `${item.resourceTypeKey}${item.indexGroupName}${item.measUnitKey}${item.measTypeKey}`,
        measUnitKey: item.measUnitKey,
        indexGroupName: item.indexGroupName,
        disabled: item.chkDisabled,
      };
      if (item.children) {
        preItem.children = readNodes(item.children, arr);
      }
      return preItem;
    });
  };

  // 树查询
  const queryUnitTree = (apiFn, params, callback) => {
    if (unitEleSearch && !unitEleSearch.validate()) { // 校验测量单元名称
      unitEleSearch.focus();
      return;
    }
    let measureData = parentDataHandle.getMeasureData();
    dispatch({unitTree: {...unitTree, isUnitLoad: true}});
    apiFn(params, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        queryFail();
        modal.error($t('kpi.task.common.error.tip'), $t('kpi.view.task.query.unit.tree.fail'));
        return;
      }

      let unitTreeData = readNodes(res.data);
      let unitAllTreeData = [...unitTree.measureUnitTreeData];
      unitAllTreeData[0].children = unitTreeData;
      if (callback) {
        callback(unitAllTreeData);
      } else {// 过滤
        dispatch({
          type: 'setResourceLists',
          isRefreshByResource: false,
          measureIndexList: getCreateTaskAllInfo().measureIndexList,
          netWorkTreeParam: getCreateTaskAllInfo().netWorkTreeParam,
          unitTree: {
            ...getCreateTaskAllInfo().unitTree,
            isUnitDisable: false,
            isSelectDisable: false,
            isTreeDisable: false,
            isUnitLoad: false,
            measureUnitTreeData: unitAllTreeData,
            checkedKeysByUnit: selectedUnitNodesHandler(measureData.checkedNetId),
            expandedKeysByMeasureUnit: selectedUnitNodesHandler(measureData.checkedNetId, true),
          },
          objectTable: getClearObject(),
        });
      }
    }, err => {
      queryFail();
    });
  };

  // 树查询失败
  const queryFail = () => {
    dispatch({type: 'setIsUnitLoading', isUnitLoading: false});
    setUnitTree({
      measureUnitTreeData: [{text: 'All', id: '/', children: [], disabled: true, isLeaf: false}],
      isUnitLoad: false,
    });
  };

  // 树展开
  const onExpand = (expandedKeys) => {
    setUnitTree({expandedKeysByMeasureUnit: expandedKeys});
  };

  // 树勾选
  const onCheck = (selectedKeys, node) => {
    parentDataHandle.setMeasureData({
      id: node.props.id,
      rowId: node.props.id,
      indexGroupName: node.props.indexGroupName,
      measureIndex: node.props.text,
      measureUnitId: node.props.id,
      measUnitKey: node.props.measUnitKey,
      measTypeKey: node.props.measTypeKey,
      unit: node.props.unit,
      hasMeasObj: node.props.hasMeasObj,
      indexId: node.props.indexId,
      resourceTypeKey: node.props.resourceTypeKey,
      displayValue: '',
      originalValue: '',
    });
    // 判断是否有测量对象,有则查询无则添加数据到测量总列表
    if (node.props.hasMeasObj) {
      checkByType(selectedKeys, node);
    }
    dispatch({
      indicatorButtonDisable:false,
    });
  };

  // 树勾选：按网元类型
  const checkByType = (selectedKeys, node) => {
    let apiFn = api.queryObjectTreeByType;
    let measureData = parentDataHandle.getMeasureData();
    queryObjTable(selectedKeys, apiFn, {
      type: measureData.checkedNetType,
      measUnitKey: node.props.measUnitKey,
      checkedNetId: measureData.checkedNetId,
      eamNodeType: measureData.eamNodeType,
    }, false);
  };

  // 树过滤
  const onFilter = (event) => {
    if (event.keyCode !== 13 && event.type !== 'click') {
      return;
    }
    dispatch({
      indicatorButtonDisable:true,
    });
    let measureData = parentDataHandle.getMeasureData();
    // 校验测量单元名称
    if (unitEleSearch && !unitEleSearch.validate()) {
      unitEleSearch.focus();
      return;
    }
    queryUnitTree(api.getMeasureUnitTree, {
      type: measureData.checkedNetType,
      dn: measureData.checkedDn || '',
      searchKey: unitTree.measureUnit,
      searchScope: unitTree.measureType,
    });
  };

  // 根据指标列表和网元勾选节点反推测量单元树
  const selectedUnitNodesHandler = (checkedNeNode, isExpand) => {
    let selectedNodes = [];
    let selectedUnitNodes = [];
    if (isExpand) {
      selectedNodes = [...new Set([...selectedNodes, ...selectedUnitNodes])];
    } else {
      selectedNodes = [...new Set(selectedNodes)];
    }
    return isExpand ? ['/', ...selectedNodes] : selectedNodes;
  };

  const getClearUnitTreeInfo = () => ({
    isUnitLoad: false,
    isUnitDisable: false,
    measureType: 'all',
    measureUnit: '',
    isSelectDisable: false,
    isTreeDisable: false,
    measureUnitTreeData: [{text: 'All', id: '/', children: [], disabled: true, isLeaf: false}],
    expandedKeysByMeasureUnit: ['/'],
    checkedKeysByUnit: [],
  });

  return (
    <>
      <div style={{overflow: 'hidden'}}>
        <Select options={MEASURE_TYPE} style={{float: 'left', marginRight: '10px'}}
          selectStyle={{width: '120px'}}
          value={unitTree.measureType} disabled={unitTree.isSelectDisable}
          onChange={val => setUnitTree({measureType: val})}
        />
        <div style={{float: 'left', width: 'calc(100% - 130px)'}}>
          <div className="df-input-frame" style={{position: 'relative'}}>
            <TextField
              inputStyle={{paddingRight: '28px', width: '100%'}} hintType='tip'
              value={unitTree.measureUnit} id="unitInput"
              placeholder={MEASURE_TYPE_MAPS[unitTree.measureType]}
              onChange={val => setUnitTree({measureUnit: val})} tipDuration={6000}
              autoComplete="off" style={{width: '100%'}} disabled={unitTree.isTreeDisable}
              validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 600)}
              ref={(ele) => unitEleSearch = ele}
              onKeyDown={event => onFilter(event)}
            />
            <div className="df-icon-search" onClick={event => onFilter(event)} />
          </div>
        </div>
      </div>
      <div className='measure_border'>
        {
          unitTree.isUnitLoad ?
            <div className='loading'><span />{$t('kpi.new.task.resource.loading')}</div> :
            <Tree data={unitTree.measureUnitTreeData} nodeKey="id"
              selectBoxType="radio"
              checkable={true} enableCheckbox
              onCheck={(selectedKeys, node, event) => onCheck(selectedKeys, node, event)}
              onExpand={onExpand}
              checkedKeys={unitTree.checkedKeysByUnit}
              expandedKeys={unitTree.expandedKeysByMeasureUnit} className="netWorkEleTree"
              checkWhenSelect={false} enableScroll={true}
              disabled={unitTree.isUnitDisable} ref={tree => {
                measureUnitTree = tree;
              }}
            />
        }
      </div>
    </>
  );
};

export default DataSourceUnitTree;
