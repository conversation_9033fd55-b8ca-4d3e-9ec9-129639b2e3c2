import React, { useContext, useEffect } from 'react';
import { Wizards, Button, TextField, SelectCard, TextArea, Toggle } from 'eview-ui';
import styles from '../index.less';
import { $t, validate, modal } from '@util';
import { cancelCreateOrEditTask } from './NewOrEditCommonUtils';
import LabelHint from '../../common/LabelHint';
import {
  TaskManageContext,
  TASK_PAGE_TYPE,
  CREATE_TASK_WIZARDS_PARAMS,
  TRAIN_EXECUTE_CYCLE,
  OPERATE_SUCCESS_CODE,
  GlobalContext,
} from '../const';
import { newOrEditTask } from '../api';

let taskNameInput;
let trainCycle;
let taskDescTextArea;
let basicInfo = {};

const BasicInfo = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const {theme} = useContext(GlobalContext);
  const { curTaskPage, algorithmParamsList, basicInfoList, selectedSolution, selectedKnowledgeBase } = state;

  useEffect(() => {
    basicInfo = { ...basicInfoList };
  }, [basicInfoList]);

  const setBasicInfoList = (param) => {
    dispatch({
      type: 'setBasicInfoList',
      basicInfoList: { ...basicInfoList, ...param },
    });
  };

  const backTaskList = () => {
    cancelCreateOrEditTask(dispatch);
  };

  const goPreviousStep = () => {
    dispatch({
      type: 'setCurTaskPage',
      curTaskPage: TASK_PAGE_TYPE.CONFIGURE_ALGORITHM,
    });    
  };

  // 完成创建任务流程
  const goNextStep = () => {
    if (!verifyData()) {
      return;
    }
    createTask();  
  };

  // 校验页面输入项
  const verifyData = () => {
    if (!taskNameInput.validate()) {
      return false;
    }
    if (!basicInfoList.trainCycle) {
      modal.error(
        $t('intelligent.incident.task.manage.train.cycle.please.select'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    if (!taskDescTextArea.validate()) {
      taskDescTextArea.focus();
      return false;
    }
    return true;
  };

  // 使用用户输入的各项配置信息进行创建任务请求
  const createTask = () => {
    const param = {
      taskId: '',
      taskType: 10,
      periodicType: 1,
      datasourceId: 9,
      taskName: basicInfoList.taskName,
      displayResult: basicInfoList.displayResult,
      taskDesc: basicInfoList.taskDescription,
      algorithmModelId: algorithmParamsList.algorithmModelId,
      algorithmModelName: algorithmParamsList.algorithmModelName_Version,
      trainCron: basicInfoList.trainCycle,
      solutionName: selectedSolution,
      solutionTypeForRecommendation: selectedKnowledgeBase,
      algorithmParam: JSON.stringify(handleAlgorithmParam(algorithmParamsList.algorithmParam)),
    };
    newOrEditTask(param, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        let msg = res.resultMessage ? res.resultMessage : $t('intelligent.incident.task.manage.save.fail.tip');
        modal.error(msg, null, $t('intelligent.incident.task.manage.error.tip'), null, theme);
        return;
      }
      modal.success(
        $t('intelligent.incident.task.manage.create.success'), 
        null, 
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      dispatch({
        type: 'setCurTaskPage',
        curTaskPage: TASK_PAGE_TYPE.TASK_LIST,
      });  
    }, err => {
      let msg = $t('intelligent.incident.task.manage.save.fail.tip');
      modal.error(msg, null, $t('intelligent.incident.task.manage.error.tip'), null, theme);
    });
  };

  const handleAlgorithmParam = (param) => {
    let temp = JSON.parse(JSON.stringify(param));
    temp.map(item => {
      if (item.type === 'int' || item.type === 'float') {
        item.parameterDefaultValue = `${item.parameterDefaultValue}`;
      }
    });
    return temp;
  };

  return (
    <>
      <div className={styles.taskTopTitleContainer}>
        <div className={styles.backIcon} onClick={backTaskList} />
        <div>{$t('intelligent.incident.task.manage.create.task')}</div>
      </div>
      <div className={styles.bottomPanelContainer}>
        <Wizards data={CREATE_TASK_WIZARDS_PARAMS} currentStep={curTaskPage} />
        <table className={styles.inputContentContainer}>
          <tbody>
            <tr>
              <td>
                <LabelHint
                  label={$t('intelligent.incident.task.manage.table.task.name')}
                  require={true}
                  theme={theme}
                />
              </td>
              <td>
                <TextField placeholder={$t('intelligent.incident.task.manage.input.name')} id='taskName'
                  inputStyle={{ width: '33.75rem' }} hintType='tip' 
                  value={basicInfoList.taskName}
                  validator={(val, id) => validate(['required', 'cmpValidChar', 'checkLength'],
                    val, id, null, 200)}
                  ref={textField => taskNameInput = textField} autoComplete='off'
                  onChange={(val) => setBasicInfoList({ taskName: val })}
                />
              </td>
            </tr>
            <tr>
              <td style={{ verticalAlign: 'baseline' }}>
                <LabelHint
                  label={$t('intelligent.incident.task.manage.task.analysis.result.visible')}
                  require={true}
                  theme={theme}
                />
              </td>
              <td>
                <Toggle 
                  data={[false, true]}
                  toggled={basicInfoList.displayResult}
                  onToggle={(val) => setBasicInfoList({ displayResult: val })}
                />
              </td>
            </tr>
            <tr>
              <td>
                <LabelHint
                  label={$t('intelligent.incident.task.manage.task.train.period')}
                  require={true}
                  theme={theme}
                />
              </td>
              <td>
                <SelectCard data={TRAIN_EXECUTE_CYCLE} 
                  itemStyle={{ width: '11.25rem' }}
                  style={{ width: '33.75rem' }}
                  value={basicInfoList.trainCycle}
                  onChange={(val) => setBasicInfoList({ trainCycle: val })}
                  ref={ele => trainCycle = ele}
                />
              </td>
            </tr>
            <tr>
              <td style={{ verticalAlign: 'baseline' }}>
                <LabelHint
                  label={$t('intelligent.incident.task.manage.task.description')}
                  theme={theme}
                />
              </td>
              <td>
                <TextArea rows={5} cols={32} inputStyle={{ resize: 'both', width: '33.75rem' }}
                  value={basicInfoList.taskDescription}
                  id='taskDesc' placeholder={$t('intelligent.incident.task.manage.task.description.placeholder')}
                  validator={(val, id) => validate(['cmpValidChar'], val, id)}
                  ref={textField => taskDescTextArea = textField} hintType='tip' maxLength={650}
                  onChange={(val) => setBasicInfoList({ taskDescription: val })}
                />
              </td>
            </tr>
          </tbody>
        </table>
        <div className={styles.bottomButtonRow}>
          <Button
            text={$t('alarm.edit.cancel')}
            style={{ float: 'left', marginRight: '0.5rem' }}
            onClick={backTaskList}
          />
          <Button
            text={$t('intelligent.incident.task.manage.previous.step')}
            style={{ marginRight: '0.5rem' }}
            onClick={goPreviousStep}
          />
          <Button
            text={$t('intelligent.incident.task.manage.completed')}
            status='primary'
            onClick={goNextStep}
          />
        </div>
      </div>
    </>
  );
};

export default BasicInfo;
