import '@pages/businesstopo/css/tooltip/message.less';
import React from 'react';
import { $t, registerResource } from '@util';
import i18n from '../../locales/tooltip';
import PropTypes from 'prop-types';

function MessageDeatil({ display, main }) {
  registerResource(i18n, 'tooltip');
  return (
    display &&
    <div id="message" style={{
      position: 'absolute',
      left: main ? '25%' : '40%',
      top: '40%',
    }}
    >
      {$t('tooltip.nodata')}
    </div>
  );
}

MessageDeatil.prototype = {
  display: PropTypes.bool,
  main: PropTypes.bool,
};

export default MessageDeatil;
