/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useEffect, useRef, useState, useContext, useMemo} from 'react';
import Tab, {TabItem} from 'eview-ui/Tab';
import {Button, CheckboxGroup} from 'eview-ui';
import * as echarts from 'echarts';
import {$t} from '@util';
import {getPodIndicatorData} from '../../api/moTypeDrill';
import {dateFormatToHour, jumpToPMHistoryDataPage} from '@util/tools';
import {BUSINESS_TOPO_CONTEXT, getHistorySeries, LINE_CHART_COLOR} from '../../const';
import edit from '../../../../apps/businesstopo/assets/line_chart_edit.svg';
import '../../css/moTypeDrill.less';
import PropTypes from 'prop-types';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {getUrlParam} from '../../util';
import SelectButtonGroup from '../common/SelectButtonGroup';
import {MoreLegend} from '../common/Legend';
import {sortHistoryListData} from '../../../../commonUtil/tools';

function PodPanelChart({podData, refresh}) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);

  const {id} = podData;

  const chartObjRef = useRef(null);
  const legendRef = useRef(null);
  const currentId = useRef(null);

  // 全量得指标头信息，定时更新
  const [rawIndicatorList, setRawIndicatorList] = useState({});

  const [currentIndex, setCurrenIndex] = useState(0);

  // 控制多指标下拉框是否展示
  const [indicatorSelectShow, setIndicatorSelectShow] = useState(false);
  // // 勾选了哪些指标

  // // 设置指标展示哪些 最多三个 当前页面展示得选中指标的index,indicatorSelectShow中对应的index
  const [selectValue, setSelectValue] = useState([]);

  const [seriesLegendData, setSeriesLegendData] = useState([]);
  const [selected, setSelected] = useState({});
  useEffect(() => {
    setSelected(seriesLegendData.reduce((curr, next) => {
      curr[next.id] = selected[next.id] === false ? false : true;
      return curr;
    }, {}));
  }, [JSON.stringify(seriesLegendData)]);

  const legendData = useMemo(() => {
    return seriesLegendData.map(v => {
      return {
        id: v.id,
        name: v.name,
        selected: selected[v.id],
      };
    });
  }, [selected]);

  useEffect(() => {
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('podPanelChartDiv');
      chartObjRef.current = echarts.init(chartDom);
    }
    if (currentId.current !== id) {
      setSelectValue([]);
      setCurrenIndex(0);
      setRawIndicatorList([]);
    }
    queryIndicatorData(true);
  }, [id]);

  useEffect(() => {
    if (selectValue.length === 0) {
      return;
    }
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('podPanelChartDiv');
      chartObjRef.current = echarts.init(chartDom);
    }
    queryIndicatorData();
  }, [currentIndex]);

  useEffect(() => {
    if (!chartObjRef.current) {
      return;
    }
    if (selectValue.length === 0) {
      return;
    }
    let params = {
      instanceId: id,
    };
    let paramsTitle = {
      instanceId: id,
      newIndicatorListFlag:1,
    };
    if (state.isTimeTrack && state.selectedTime > 0) {
      params.endTime = state.selectedTime;
      paramsTitle.endTime = state.selectedTime;
    }
    getPodIndicatorData(paramsTitle, respTitle => {
      setRawIndicatorList(respTitle.data.indicatorIdList);
      if (currentId.current === id) {
        let index = selectValue.indexOf(currentIndex);
        params.indicatorId = respTitle.data.indicatorIdList[selectValue[index]].indicatorId;
      } else {
        currentId.current = id;
      }
      if (getUrlParam('isMM')) {
        params.stripeUnit = ''; // 方便后台区分MM场景
      }

      // 刷新场景
      getPodIndicatorData(
        params,
        resp => {
          if (!resp || resp.resultCode !== 0) {
            return;
          }
          if (resp.data && resp.data.podIndicatorList && resp.data.podIndicatorList.length > 0) {
            resp.data.podIndicatorList = sortHistoryListData(resp.data.podIndicatorList || []);
            const oldOption = chartObjRef.current.getOption();
            let options = getOptions(resp.data);
            oldOption.xAxis = options.xAxis;
            oldOption.yAxis = options.yAxis;
            oldOption.series = options.series;
            options.legend = legendRef.current;
            chartObjRef.current.clear();
            chartObjRef.current.setOption(oldOption);
            setSeriesLegendData((oldOption.series || []).map(v => {
              return {
                name: v.name,
                id: v.name,
              };
            }));
            chartObjRef.current.getZr().off('click');
            chartObjRef.current.getZr().on('click', param => {
              const pointInPixel = [param.offsetX, param.offsetY];
              if (!chartObjRef.current.containPixel('grid', pointInPixel)) {
                return;
              }
              handleJump(resp.data.podIndicatorList);
            });
          }
        },
      );
    });
  }, [refresh]);

  const handleJump = indicatorArr => {
    jumpToPMHistoryDataPage(indicatorArr, indicatorArr[0].startTime, indicatorArr[0].endTime);
  };

  // 查询指标数据
  const queryIndicatorData = (idChange) => {
    let params = {
      instanceId: id,
    };
    if (chartObjRef.current) {
      chartObjRef.current.clear();
    }
    let paramsTitle = {
      instanceId: id,
      newIndicatorListFlag:1,
    };

    if (state.isTimeTrack) {
      params.endTime = state.selectedTime;
      paramsTitle.endTime = state.selectedTime;
    }
    getPodIndicatorData(paramsTitle, respTitle => {
      // 初始化场景
      setRawIndicatorList(respTitle.data.indicatorIdList);
      if (idChange) {
        if (respTitle.data.indicatorIdList.length >= 3) {
          setSelectValue([0, 1, 2]);
        } else {
          const array = [];
          for (let i = 0; i < respTitle.data.indicatorIdList.length; i++) {
            array.push(i);
          }
          setSelectValue(array);
        }
        setCurrenIndex(0);
      }
      if (currentId.current === id) {
        let index = selectValue.indexOf(currentIndex);
        params.indicatorId = respTitle.data.indicatorIdList[selectValue[index]].indicatorId;
      } else {
        currentId.current = id;
      }

      if (getUrlParam('isMM')) {
        params.stripeUnit = ''; // 方便后台区分MM场景
      }
      getPodIndicatorData(
        params,
        resp => {
          if (!resp || resp.resultCode !== 0) {
            return;
          }
          if (resp.data && resp.data.podIndicatorList && resp.data.podIndicatorList.length > 0) {
            resp.data.podIndicatorList = sortHistoryListData(resp.data.podIndicatorList || []);
            let options = getOptions(resp.data);
            chartObjRef.current.setOption(options);
            chartObjRef.current.getZr().off('click');
            chartObjRef.current.getZr().on('click', param => {
              const pointInPixel = [param.offsetX, param.offsetY];
              if (!chartObjRef.current.containPixel('grid', pointInPixel)) {
                return;
              }
              handleJump(resp.data.podIndicatorList);
            });
            setSeriesLegendData((options.series || []).map(v => {
              return {
                name: v.name,
                id: v.name,
              };
            }));
          }
        },
      );
    });
  };

  const getOptions = respData => ({
    title: {
      subtext: respData.podIndicatorList[0].indexUnit,
      subtextStyle: {
        align: 'left',
        color: '#BBBBBB',
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      borderColor: '#393939',
      backgroundColor: '#393939',
      appendToBody: true,
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      textStyle: {
        color: '#FFFFFF',
      },
      enterable: true,
      position: (point, params, dom, rect, size) => {
        let chartDom = document.getElementById('podPanelChartDiv');
        const TOOLTIP_OFFSET = 4;
        // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
        const canvasRect = chartDom.getBoundingClientRect();
        const {contentSize: tooltipSize, viewSize: canvasSize} = size;
        let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
        let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
        if (canvasRect) {
          // 校正tooltip的 top 定位，防止超出可视窗口
          const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
          const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
          if (toolTipExceedViewport || toolTipExceedCanvas) {
            top = -canvasRect.top;
          }
        }
        return [left, top];
      },
      formatter: (params) => {
        // 获取时间（假设 x 轴为时间）
        const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
        // 遍历每条线的数据，保留默认的值展示
        const values = params
          .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
          .join('<br>');
        // 返回自定义的提示框内容
        return `<strong>${time}</strong><br>${values}`;
      },
    },
    grid: {
      left: '0px',
      right: '3%',
      bottom: '10%',
      top: '14%',
      containLabel: true,
    },
    textStyle: {
      color: '#BBBBBB',
    },
    legend: {
      show: getUrlParam('isMM') ? false : true,
      orient: 'horizontal',
      bottom: '0%',
      itemGap: 50,
      type: 'scroll',
      icon: 'circle',
      itemWidth: 15,
      itemHeight: 10,
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      textStyle: {
        color: '#BBBBBB',
      },
      pageTextStyle: {
        color: '#FFFFFF',
      },
    },
    xAxis: {
      type: 'time',
      boundaryGap: true,
      axisLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
          type: 'solid',
          width: 3,
        },
      },
      axisTick: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
          type: 'solid',
          width: 2,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        interval: 0,
        borderColor: 'transparent',
        borderWidth: 10,
        fontSize: 10,
        margin: 10,
        hideOverlap: true,
        showMinLabel: false,
        showMaxLabel: false,
        formatter: value => {
          let startTime = parseInt(respData.podIndicatorList[0].startTime);
          let endTime = parseInt(respData.podIndicatorList[0].endTime);
          const unit = (endTime - startTime) / 12;
          if (value > startTime && value < endTime) {
            if (Math.abs(value - startTime) < unit || Math.abs(value - endTime) < unit) {
              return '';
            }
            return dateFormatToHour(value);
          }
          return '';
        },
      },
      splitLine: {
        show: false,
      },
      min: parseInt(respData.podIndicatorList[0].startTime),
      max: parseInt(respData.podIndicatorList[0].endTime),
    },
    yAxis: {
      type: 'value',
      boundaryGap: true,
      nameTextStyle: {
        color: '#BBBBBB',
      },
      splitLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        fontSize: 10,
        margin: 5,
        hideOverlap: true,
        align: 'right',
        overflow: 'break',
        width: 200,
      },
    },
    series: getHistorySeries(respData.podIndicatorList, true, false),
  });

  // 切换指标显示
  const onIndicatorChange = index => {
    setCurrenIndex(selectValue[index]);
  };

  // 右边下拉的切换指标
  const handleIndicatorSelect = event => {
    setIndicatorSelectShow(!indicatorSelectShow);

    let selectBtnTarget = document.getElementById('indicator_select_btn');
    let selectPanelTarget = document.getElementById('indicator_select_panel');
    document.addEventListener('click', current => {
      if (
        current.target.id.indexOf('indicator_select_checkbox') !== -1 ||
        current.target === selectBtnTarget ||
        current.target === selectPanelTarget ||
        selectBtnTarget.contains(current.target) ||
        selectPanelTarget.contains(current.target)
      ) {
        return;
      }
      setIndicatorSelectShow(false);
    });
  };

  const getSelectData = () => rawIndicatorList.map((item, index) => ({
    value: index,
    text: item.indexName,
    tipText: item.indexName,
  }));

  const handleIndicatorChange = (newValue, oldValue, event) => {
    // 修改选择值
    setSelectValue(newValue);
    if (newValue.indexOf(currentIndex) === -1) {
      setCurrenIndex(newValue[0]);
    }
  };

  return (
    <div className="podPanelChartContainer">
      {rawIndicatorList.length > 0 && (
        <div style={{width: '100%', height: '12%'}}>
          {
            getUrlParam('isMM') ?
              <SelectButtonGroup
                {
                ...{
                  data: rawIndicatorList.map((item, index) => {
                    return {
                      id: `${index}___${item.indexName}`,
                      name: String(item.indexName),
                      onClick: () => {
                        setCurrenIndex(index);
                      },
                    };
                  }),
                  editButtonGroupClick: (newValue) => {
                    handleIndicatorChange((newValue || []).map(v => {
                      return Number(String(v).split('___')[0]);
                    }));
                  },
                }
                }
              /> :
                <>
                  <Tab
                    style={{display: 'inline-block', width: '80%'}}
                    draggable={false}
                    onClick={onIndicatorChange}
                    selectedIndex={selectValue.indexOf(currentIndex)}
                    titleLength={2}
                  >
                    {selectValue.map((item) => (
                      <TabItem key={item} lazyLoad={true}
                        tabItemStyle={{outline: 'none'}}
                        title={rawIndicatorList[item].indexName}
                        titleLength={item === currentIndex ? 8 : 2}
                      />
                    ))}
                  </Tab>

                  <Button
                    className="indicator_select_btn"
                    id="indicator_select_btn"
                    onClick={handleIndicatorSelect}
                    leftIconProps={{leftHoverIcon: edit}}
                    leftIcon={edit}
                    style={{
                      display: 'inline-block',
                      color: '#BBBBBB',
                      background: '#272727',
                      border: 'none',
                      float: 'right',
                      outline: 'none',
                      opacity: '0.6',

                      marginTop: '4px',

                      marginRight: '-15px',
                    }}
                  />
                  <div id="indicator_select_panel" style={{visibility: indicatorSelectShow ? 'visible' : 'hidden'}}>
                    <p className="indicator_select_tip"> {$t('indicator.select.tip')}</p>
                    <CheckboxGroup
                      id="indicator_select_checkbox"
                      onChange={handleIndicatorChange}
                      data={getSelectData()}
                      value={selectValue}
                      validtor={{minSelect: 1, maxSelect: 3}}
                      style={{marginLeft: '16px'}}
                      tipData={{placement: 'topRight', overlayStyle: {color: '#f5f5f5'}, color: '#393939'}}
                    />
                  </div>
                </>
          }
        </div>
      )}
      <div id="podPanelChartDiv" className="podPanelChartDiv" />
      {
        getUrlParam('isMM') &&
          <div style={{padding: '0 30px'}}>
            <MoreLegend
              data={legendData}
              style={{marginTop: '-30px'}}
              iconStyle={{
                width: '10px',
                height: '10px',
                top: '5px',
              }}
              color={LINE_CHART_COLOR.map(v => v[0])}
              onClick={(clickId) => {
                if (!chartObjRef.current) {
                  return;
                }

                setSelected(prevSelected => {
                  const oldSelected = { ...prevSelected };
                  if (event.ctrlKey) {
                    // 点击的时候按住ctl键，只改变当前图例的勾选状态
                    oldSelected[clickId] = !oldSelected[clickId];
                  } else {
                    if (oldSelected[clickId]) {
                      // 点击的图例已勾选
                      if (Object.values(oldSelected).every(isSelect => isSelect)) {
                        // 当前为全选，单选该图例
                        for (const legendKey of Object.keys(oldSelected)) {
                          oldSelected[legendKey] = false;
                        }
                        oldSelected[clickId] = true;
                      } else {
                        // 当前为非全选，勾选全部图例
                        for (const legendKey of Object.keys(oldSelected)) {
                          oldSelected[legendKey] = true;
                        }
                      }
                    } else {
                      // 点击的图例未勾选，单选该图例
                      for (const legendKey of Object.keys(oldSelected)) {
                        oldSelected[legendKey] = false;
                      }
                      oldSelected[clickId] = true;
                    }
                  }

                  const selectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                    return value;
                  }).map(([key, value]) => {
                    return {
                      name: legendData.find(v => v.id === key)?.name,
                    };
                  });
                  const unSelectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                    return !value;
                  }).map(([key, value]) => {
                    return {
                      name: legendData.find(v => v.id === key)?.name,
                    };
                  });
                  chartObjRef.current.dispatchAction({
                    type: 'legendSelect',
                    batch: selectedNameList,
                  });

                  chartObjRef.current.dispatchAction({
                    type: 'legendUnSelect',
                    batch: unSelectedNameList,
                  });
                  return oldSelected;
                });
              }}
            />
          </div>
      }
    </div>
  );
}

PodPanelChart.propTypes = {
  podData: PropTypes.shape({
    id: PropTypes.number,
  }).isRequired,
};

export default PodPanelChart;
