import React, {useState, useEffect, useMemo} from 'react';
import styles from './index.less';


/**
 *
 *
 * @param props
 * props.data
 * [{
 *   id: string;
 *   name: string;
 *   onClick: (id: string) => void;
 * }],
 * props.style: {},
 */
const ButtonGroup = (props) => {
  const {
    data = [],
    style = {},
    itemStyle = {},
    clickItemStyle = {},
    activateIndex = 0,
  } = props;

  const [chooseIndex, setChooseIndex] = useState(0);
  useEffect(() => {
    setChooseIndex(activateIndex);
  }, [activateIndex]);

  return (
    <div className={styles.topoButtonGroup} style={style}>
      {
        data.map((v, i) => {
          const isChoose = chooseIndex === i;

          return (
            <div
              key={v.id}
              onClick={() => {
                if (isChoose) { // 已经选中了，不再触发
                  return;
                }
                setChooseIndex(i);
                v.onClick(v.id);
              }}
              className={styles.topoButtonGroupItem}
              style={{
                color: isChoose ? 'rgb(46, 148, 255)' : undefined,
                ...itemStyle,
                ...(isChoose ? clickItemStyle : {}),
              }}
              title={v.name}
            >
              <span
                style={{
                  borderBottom: isChoose ? '2px solid rgb(46, 148, 255)' : undefined,
                  paddingBottom: '4px',
                }}
              >
                {v.name}
              </span>
            </div>
          );
        })
      }
    </div>
  );
};

export default ButtonGroup;
