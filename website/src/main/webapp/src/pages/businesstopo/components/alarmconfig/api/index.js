import {request} from '@util';
const queryalarmrulesUrl = '/rest/dvtopowebsite/v1/business/topo/config/queryalarmrules';
const queryalarmruledetailUrl = '/rest/dvtopowebsite/v1/business/topo/config/queryalarmruledetail';
const addalarmruleUrl = '/rest/dvtopowebsite/v1/business/topo/config/addalarmrule';
const modifyalarmruleUrl = '/rest/dvtopowebsite/v1/business/topo/config/modifyalarmrule';
const deletealarmruleUrl = '/rest/dvtopowebsite/v1/business/topo/config/deletealarmrule';
const queryalarmmotypeUrl = '/rest/dvtopowebsite/v1/business/topo/config/queryalarmmotype';
const queryalarmstaticinfoUrl = '/rest/dvtopowebsite/v1/business/topo/config/queryalarmstaticinfo';
const refreshbusinesstopoalarmUrl = '/rest/dvtopowebsite/v1/business/topo/common/refreshbusinesstopoalarm';
const startalarmruleUrl = '/rest/dvtopowebsite/v1/business/topo/config/startalarmrule';
const queryTaskUrl = '/rest/dvanalysisenginewebsite/v1/taskmanage/tasklist';
const taskUrl = '/rest/dvanalysisenginewebsite/v1/taskmanage/task';
const deleteTaskUrl = '/rest/dvanalysisenginewebsite/v1/taskmanage/task';
const trainModelUrl = '/rest/dvanalysisenginewebsite/v1/taskmanage/trainmodel';
const querySolutionUrl = '/rest/dvtopowebsite/v1/business/topo/querysolution';
const queryKnowledgeListUrl = '/rest/dvanalysisenginewebsite/v1/knowledgereposity/getsolutiontype';
const queryAlgorithmListUrl = '/rest/dvanalysisenginewebsite/v1/library/models';
const getAlgorithmParamsUrl = '/rest/dvanalysisenginewebsite/v1/library/modeldefaultparameters';

// 非容量评估：按网元类型查询网元树
export const queryalarmrules = (params, success, error) => request.post(queryalarmrulesUrl, params, success, error, false);

export const queryalarmruledetail = (params, success, error) => request.post(queryalarmruledetailUrl, params, success, error, false);

export const addalarmrule = (params, success, error) => request.post(addalarmruleUrl, params, success, error, false);

export const modifyalarmrule = (params, success, error) => request.post(modifyalarmruleUrl, params, success, error, false);

export const deletealarmrule = (params, success, error) => request.post(deletealarmruleUrl, params, success, error, false);

export const queryalarmmotype = (params, success, error) => request.post(queryalarmmotypeUrl, params, success, error, false);

export const queryalarmstaticinfo = (params, success, error) => request.post(queryalarmstaticinfoUrl, params, success, error, false);

export const refreshbusinesstopoalarm = (success, error) => request.post(refreshbusinesstopoalarmUrl, {}, success, error, false);

export const startalarmrule = (params, success, error) => request.post(startalarmruleUrl, params, success, error, false);

// 查询任务列表
export const queryTask = (params, succ, fail) => {
  return request.post(queryTaskUrl, params, succ, fail, true, {
    headers: {
      'x-non-renewal-session': true,
    },
  });
};

// 新建或修改任务
export const newOrEditTask = (params, succ, fail) => {
  return request.post(taskUrl, params, succ, fail);
};

// 获取单个任务详情
export const viewTask = ({ taskId }, succ, fail) => {
  return request.get(`${taskUrl}?taskId=${taskId}`, succ, fail);
};

// 删除任务
export const deleteTask = (params, succ, fail) => {
  return request.delete(deleteTaskUrl, succ, fail, true, { data: params });
};

// 训练任务
export const trainModel = (params, succ, fail) => {
  return request.post(trainModelUrl, params, succ, fail);
};

// 获取拓扑解决方案列表
export const querySolution = (succ, fail) => {
  return request.get(querySolutionUrl, succ, fail);
};

// 获取知识库列表
export const queryKnowledgeList = (params, succ, fail) => {
  return request.get(`${queryKnowledgeListUrl}?scene=${params}`, succ, fail);
};

// 获取算法列表
export const queryAlgorithmList = ({ taskType }, succ, fail) => {
  return request.get(`${queryAlgorithmListUrl}?taskType=${taskType}`, succ, fail);
};

// 获取算法参数列表
export const getAlgorithmParams = ({ modelName, interfaceVersion, feature }, succ, fail) => {
  return request.get(
    `${getAlgorithmParamsUrl}?modelName=${modelName}&interfaceVersion=${interfaceVersion}&feature=${feature}`,
    succ,
    fail,
  );
};