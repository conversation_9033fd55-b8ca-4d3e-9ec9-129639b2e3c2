import React, {useState, useEffect, useMemo} from 'react';
import Tab, {TabItem} from 'eview-ui/Tab';
import {Button, CheckboxGroup} from 'eview-ui';
import {$t} from '@util';
import styles from './index.less';
import edit from '../../../../../apps/businesstopo/assets/line_chart_edit.svg';

function hasParentWithId(node, targetId) {
  let currentNode = node;
  while (currentNode !== null && currentNode !== document && currentNode !== window) {
    // 检查当前节点是否有指定的ID
    if (currentNode?.id?.includes(targetId)) {
      return true;
    }
    // 移动到父节点
    currentNode = currentNode.parentNode;
  }
  return false;
}

/**
 *
 *
 * @param props
 * props.data
 * [{
 *   id: string;
 *   name: string;
 *   onClick: (id: string) => void;
 * }],
 * props.maxShowNum: number,
 */
const SelectButtonGroup = (props) => {
  const {
    data = [],
    maxShowNum = 3,
    editButtonGroupClick = () => {
      // 空方法
    },
    autoChangeChoose = (index) => {
      // 空方法
    },
    buttonStyle,
  } = props;

  // 用对象做state，是因为number是基本类型 比如0 更新0的时候不会触发渲染，使用对象来强制触发渲染
  const [chooseIndex, setChooseIndex] = useState({index: 0});

  const [showCheckBox, setShowCheckBox] = useState(false);
  const [checkBoxSelectValue, setCheckBoxSelectValue] = useState([]);
  useEffect(() => {
    setCheckBoxSelectValue(data.slice(0, maxShowNum).map(v => v.id));
    setChooseIndex({index: 0});
    autoChangeChoose?.(0);
  }, [JSON.stringify(data)]);

  const showData = useMemo(() => {
    return data.filter(v => checkBoxSelectValue.includes(v.id));
  }, [checkBoxSelectValue]);

  useEffect(() => {
    const func = current => {
      let selectBtnTarget = document.getElementById('indicator_select_btn');
      let selectPanelTarget = document.getElementById('indicator_select_panel');

      const flag = (
        current.target.id.indexOf('indicator_select_checkbox') !== -1 ||
        current.target === selectBtnTarget ||
        current.target === selectPanelTarget
      );
      if (
        flag ||
        selectBtnTarget?.contains(current.target) ||
        selectPanelTarget?.contains(current.target)
      ) {
        return;
      }

      if (hasParentWithId(current.target, 'indicator_select_checkbox_')) {
        return;
      }

      setShowCheckBox(false);
    };

    document.addEventListener('click', func);
    return () => {
      document.removeEventListener('click', func);
    };
  }, []);

  return (
    <div className='line_chart_tab'>
      {showData.length > 0 && (
        <div style={{width: '100%'}}>
          <div className='topoSelectTab'>
            {
              showData.map((item, index) => (
                <div
                  className='topoSelectTabItem'
                  key={`${item.name}_${index}`}
                  title={item.name}
                  style={{
                    maxWidth: `calc((100% - 70px) / ${showData.length})`,
                  }}
                  onClick={() => {
                    if (chooseIndex.index === index) {
                      return;
                    }
                    setChooseIndex({index});
                    showData[index]?.onClick(showData[index]?.id);
                  }}
                >
                  <span
                    className={`topoSelectTabItemSpan ${chooseIndex.index === index ? 'topoSelectTabActive' : ''}`}
                    style={{
                      display: 'inline-block',
                      height: '25px',
                      width: '100%',
                    }}
                  >
                    {item.name}
                  </span>
                </div>
              ))
            }
            <Button
              className="indicator_select_btn"
              id="indicator_select_btn"
              onClick={() => {
                setShowCheckBox(!showCheckBox);
              }}
              leftIconProps={{leftHoverIcon: edit}}
              leftIcon={edit}
              style={{
                display: 'inline-block',
                color: '#BBBBBB',
                background: '#272727',
                border: 'none',
                outline: 'none',
                opacity: '0.6',
                marginTop: '-4px',
                position: 'absolute',
                right: '0px',
                padding: '0',
                marginRight:'0px',
                minWidth: '0rem',
                ...buttonStyle,
              }}
            />
          </div>
          <div
            id="indicator_select_panel"
            style={{visibility: showCheckBox ? 'visible' : 'hidden'}}
          >
            <p className="indicator_select_tip"> {$t('indicator.select.tip')}</p>
            <CheckboxGroup
              id="indicator_select_checkbox"
              onChange={(newValue, oldValue, event) => {
                const oldData = showData[chooseIndex.index];
                // 如果newValue中不包含当前的索引的, 索引第一个
                if (!newValue.includes(oldData?.id)) {
                  setChooseIndex({index: 0});
                  data.find(v => v.id === newValue[0])?.onClick();
                } else {
                  setChooseIndex({index: newValue.findIndex((v) => v === oldData?.id)});
                }
                setCheckBoxSelectValue(newValue);
                editButtonGroupClick?.(newValue, oldValue, event);
              }}
              data={data.map(v => {
                return {
                  value: v.id,
                  text: v.name,
                  check: checkBoxSelectValue.includes(v.id),
                  tipText: v.name,
                };
              })}
              value={checkBoxSelectValue}
              validtor={{minSelect: 1, maxSelect: 3}}
              style={{marginLeft: '16px'}}
              tipData={{placement: 'topRight', overlayStyle: {color: '#f5f5f5'}, color: '#393939'}}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectButtonGroup;
