/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, {useContext, useEffect, useRef, useImperativeHandle} from 'react';
import {Tree, TextField} from 'eview-ui';
import {modal, $t, validate} from '@util';
import * as api from '../api';
import {BUSINESS_TOPO_CONTEXT, OPERATE_SUCCESS_CODE} from '@pages/businesstopo/const';
import {queryNetWorkTreeByType, getMeasureUnitTree} from '../api';

let netWorkTree;
let networkEleSearch;
const DataSourceNETree = ({
  selectedUnitNodes,
  getClearObject,
  getClearUnitTreeInfo,
  getCreateTaskAllInfo,
  queryUnitTree,
  parentDataHandle,
}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {netWorkTreeParam, unitTree} = state;
  const setNetWorkTreeParam = (param) => {
    dispatch({
      netWorkTreeParam: {
        ...netWorkTreeParam,
        ...param,
      },
    },
    );
  };
  useEffect(() => {
    queryByType(queryNetWorkTreeByType, '');
  }, []);

  const readNetNodesByType = (nodes, isExpand, arr, preKeyVal) => {
    return nodes.map(item => {
      let keyId = `${preKeyVal}(_)${item.id}`;
      let preItem = {
        ...item,
        keyId,
        text: item.name,
        type: item.id,
        id: item.id,
      };
      if (item.children) {
        preItem.children = readNetNodesByType(item.children, isExpand, arr, keyId);
      }
      return preItem;
    });
  };
  // 树初始化
  const initTree = () => {
    let treeData = [{
      text: 'Root',
      id: '/',
      children: [],
      disabled: true,
      keyId: '/',
    }];
    return treeData;
  };

  // 树查询：按网元类型
  const queryByType = (queryFn, queryParam) => {
    setNetWorkTreeParam({isNetWorkLoad: true});
    queryFn(queryParam, res => {
      // // 查询失败
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        queryFail();
        modal.error($t('kpi.task.common.error.tip'), $t('kpi.vie.task.query.NE.fail'));
        return;
      }

      let treeData = initTree();
      let unitTreeData = [];
      if (res.data && res.data.length) {
        let nodes = res.data[0].children || [];
        unitTreeData = readNetNodesByType(nodes, false, [], res.data[0].id);
      }
      treeData[0].children = unitTreeData;
      restoreTreeData(treeData);
    }, err => {
      queryFail('type');
    });
  };

  // 树过滤
  const OnFilter = (event) => {
    if (event.keyCode !== 13 && event.type !== 'click') {
      return;
    }
    dispatch({
      indicatorButtonDisable:true,
    });
    // 校验网元名称
    if (networkEleSearch && !networkEleSearch.validate()) {
      networkEleSearch.focus();
      return;
    }
    queryByType(queryNetWorkTreeByType, netWorkTreeParam.networkEle);
  };

  // 树展开
  const onExpand = (expandedKeys, node) => {
    setNetWorkTreeParam({expandedKeysByNetWork: expandedKeys});
  };

  // 树勾选
  const onCheck = (selectedKeys, node) => {
    checkByType(selectedKeys, node, () => checkCallback(selectedKeys, node));
    dispatch({
      indicatorButtonDisable:true,
    });
  };

  // 树勾选： 网元类型树
  const checkByType = (selectedKeys, node, callback) => {
    parentDataHandle.setMeasureData({
      checkedNetName: node.props.text,
      checkedNetType: node.props.id,
      checkedNetId: node.props.keyId,
      checkedDn: '',
      eamNodeType: node.props.eamNodeType,
    });

    queryUnitTree(api.getMeasureUnitTree, {
      type: node.props.id,
      dn: '',
      searchKey: unitTree.measureUnit,
      searchScope: unitTree.measureType,
    }, callback);
  };

  // 树勾选回调
  const checkCallback = (selectedKeys, node) => {
    dispatch(
      {
        unitTree: {
          ...getCreateTaskAllInfo().unitTree,
          isSelectDisable: false,
          isTreeDisable: false,
          isUnitDisable: false,
          expandedKeysByMeasureUnit: selectedUnitNodes(node.props.keyId, true),
          isUnitLoad: false,
          checkedKeysByUnit: selectedUnitNodes(node.props.keyId),
        },
        objectTable: getClearObject(),
      },
    );
  };

  // 查询树失败
  const queryFail = (type) => {
    dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
  };

  // 新建任务时根据指标列表反推网元勾选节点的dn和标识
  const selectedNeNodes = (isExpand) => {
    let defaultRootId = getDefaultNeRootId();
    let defaultSelected = isExpand ? [defaultRootId] : [];
    return {selectedNodes: [], selectedNodeIds: defaultSelected};
  };

  // 指标预测下均根据解决方案过滤树，其余都是网元实例下根据解决方案过滤树
  const getDefaultNeRootId = () => {
    return '/';
  };

  // 树查询更新状态
  const restoreTreeData = (treeData) => {
    let expandedList = selectedNeNodes(true).selectedNodeIds;
    dispatch({
      netWorkTreeParam: {
        ...netWorkTreeParam,
        netWorkTreeData: treeData,
        isNetWorkLoad: false,
        expandedKeysByNetWork: expandedList,
        checkedKeysByNetWork: selectedNeNodes().selectedNodeIds,
        isFilterSuccess: false,
      },
      unitTree: getClearUnitTreeInfo(),
      objectTable: getClearObject(),
    });
  };

  return (
    <>
      <div className="df-input-frame" style={{position: 'relative'}}>
        <TextField
          inputStyle={{paddingRight: '28px', width: '100%'}} hintType='tip'
          value={netWorkTreeParam.networkEle} id="netWorkInput"
          placeholder={$t('kpi.new.task.resource.network')}
          onChange={val => setNetWorkTreeParam({networkEle: val})} tipDuration={6000}
          autoComplete="off" style={{width: '100%'}}
          validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 128)}
          ref={(ele) => networkEleSearch = ele}
          onKeyDown={event => OnFilter(event)}
        />
        <div className="df-icon-search" onClick={event => OnFilter(event)} />
      </div>
      <div className='measure_border'>
        {netWorkTreeParam.isNetWorkLoad ?
          <div className='loading'><span />{$t('kpi.new.task.resource.loading')}</div> :
          <Tree data={netWorkTreeParam.netWorkTreeData}
            nodeKey="keyId"
            enableCheckbox
            selectBoxType="radio"
            checkWhenSelect={false}
            checkable={true}
            onCheck={onCheck} onExpand={onExpand}
            checkedKeys={netWorkTreeParam.checkedKeysByNetWork}
            expandedKeys={netWorkTreeParam.expandedKeysByNetWork} className="netWorkEleTree"
            ref={tree => {
              netWorkTree = tree;
            }}
            enableScroll={true}
          />}
      </div>
    </>
  );
};

export default DataSourceNETree;
