/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, {useContext, useEffect} from 'react';
import {Tooltip} from 'eview-ui';
import '../../css/moTypeDrill.less';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import AlarmEventPanel from '../AlarmEventPanel';
import {getAlarmEventData} from '../../api/moTypeDrill';
import PropTypes from 'prop-types';
import {getUrlParam} from '../../util';

function HSMRightPanel(props) {
  const { detailsInfoArr, appData, isOpenPanel, setIsOpenPanel } = props;
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    isTimeTrack,
    selectedTime,
  } = state;
  useEffect(() => {
    queryAlarmData();
  }, [appData.id, selectedTime, props.refresh]);

  // 查询告警/事件数据
  const queryAlarmData = () => {
    let params = {
      instanceId: appData.id,
    };
    if (isTimeTrack) {
      params.timestamp = selectedTime;
    }
    getAlarmEventData(
      params,
      res => {
        if (!res || res.resultCode !== 0) {
          return;
        }
        dispatch({ alarmEventData: res.data.alarmDataList });
      },
    );
  };

  return (
    <>
      {isOpenPanel && (
        <div className="moType_right_panel">
          <div>
            <div className={getUrlParam('isMM') ? 'moType_right_panel_title_mm' : 'moType_right_panel_title'}>
              <Tooltip content={appData.name} id='title_tooltip' color='#393939'
                overlayStyle={{ color: '#FFF' }}
              >
                <div className="moType_right_panel_title_text">{`${appData.name}`}</div>
              </Tooltip>
            </div>
            <div style={{ marginTop: '32px' }}>
              <div className="pod-detail-right-panel-container">
                {detailsInfoArr.map((item, index) => (
                  <div style={{ paddingBottom: '16px', width: '100px' }} key={index}>
                    <Tooltip content={index === 0 ? item.content : item.value} placement='right' color='#393939'
                      overlayStyle={{ color: '#FFF' }}
                    >
                      <div
                        className="pod-detail-title"
                        style={{ color: '#f5f5f5' }}
                      >
                        <span
                          style={{ marginRight: '5px' }}
                        > {item.value}
                        </span>
                      </div>
                    </Tooltip>
                    <div className="pod-detail-value">{item.title}</div>
                  </div>
                ))}
              </div>
            </div>
            <div id="moType_alarm_event" style={{ marginTop: '32px' }}>
              <AlarmEventPanel />
            </div>
          </div>
        </div>
      )}
      <div
        className={isOpenPanel ? 'moType_drill_right_close_div' : 'moType_drill_right_open_div'}
        onClick={() => setIsOpenPanel(!isOpenPanel)}
      >
        <div className={isOpenPanel ? 'moType_drill_right_close' : 'moType_drill_right_open'} />
      </div>
    </>
  );
}

HSMRightPanel.propTypes = {
  detailsInfoArr: PropTypes.array,
  isOpenPanel: PropTypes.bool,
  setIsOpenPanel: PropTypes.func,
  appData: PropTypes.shape({
    name: PropTypes.string,
    id: PropTypes.number,
  }).isRequired,
  refresh: PropTypes.number,
};
export default HSMRightPanel;
