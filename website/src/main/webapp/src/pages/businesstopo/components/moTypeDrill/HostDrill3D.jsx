/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

/**
 * 网元类型下钻视图
 */
import React, {useEffect, useReducer, useRef, useState} from 'react';
import {ConfigProvider} from 'eview-ui';
import Crumbs from 'eview-ui/Crumbs';
import {$t, registerResource} from '@util';
import {STATUS} from '../../const/moTypeDrill';
import {getPodDeployData, getVMDeployData, queryHostview} from '../../api/moTypeDrill';
import {initState, reducer} from '../../reducer';
import i18n from '../../locales/moTypeDrill';
import '../../css/index.less';
import {getUrlParam, setSessionData} from '../../util';
import {HOST_TYPE_HOME, MO_TYPE_HOME} from '../../const/timeLine';
import PodRightPanel from './PodRightPanel';
import VMRightPanel from './VMRightPanel';
import TimeLine from '../timeline/TimeLine';
import {eventBusHandler, getQuery} from '../../a3dPages/components/utils';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import MessageDeatil from '@pages/businesstopo/components/tooltip/messageDeatil';
import {getSolutionData, queryCurrentIds} from '../../api';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {isZh, setHelpId} from '../../../../commonUtil';
import HostDrillDown from '@pages/businesstopo/a3dPages/components/HostDrillDownPage';
import {BUSINESS_TOPO_CONTEXT as TopoContext} from '../../const';

function useStateAndRef(initialValue) {
  const [state, setState] = useState(initialValue);
  const ref = useRef(initialValue);

  const setStateAndRef = (updater) => {
    let newState;
    if (typeof updater === 'function') {
      newState = updater(state);
    } else {
      newState = updater;
    }
    setState(newState);
    ref.current = newState;
  };

  return [state, ref, setStateAndRef];
}


function HostDrill3D() {
  registerResource(i18n, 'moTypeDrillI18n');
  setHelpId('com.huawei.dvtopo.business');
  const [state, dispatch] = useReducer(reducer, initState);
  const [showPodPanel, showPodPanelRef, setShowPodPanel] = useStateAndRef({
    isShow: false, isOpen: false, detailsInfoArr: [], podData: {}, isTimeTrack: false, trackAlarmData: {},
  });
    // 存储点击的pod的id, 确保消亡的时候能关闭
  const showPodIdRef = useRef(null);

  const [showVMPanel, setShowVMPanel] = useState({
    isShow: false, isOpen: false, vmData: {}, isTimeTrack: false, timeTrackVMData: {},
  });

  const [pageParams, setPageParams] = useState({
    vmId: getUrlParam('vmId') || 289,
    moTypeId: getUrlParam('moTypeId'),
    siteId: getUrlParam('siteId'),
    solutionId: getUrlParam('solutionId'),
    siteName: decodeURIComponent(getUrlParam('siteName')),
    moTypeName: getUrlParam('moTypeName'),
    hostName: getUrlParam('hostName'),
    isMM: Boolean(getUrlParam('isMM')),
    stripId: getUrlParam('stripId'),
    stripUnit: getUrlParam('stripUnit'),
    isSearch: getUrlParam('isSearch'),
    applicationType:getUrlParam('applicationType'),
  });

  const [drillData, setDrillData] = useState({isInit: true, data: null, events: [], isMM: false});
  const allPodData = useRef({});
  const allVMData = useRef({});
  const isTimeTrack = useRef(false);
  const [data, setData] = useState([]);
  let moSelectedTime = useRef(0);
  let isVMScenario = useRef(false);
  const [refresh, setRefresh] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const initFlagRef = useRef(0);

  function showTips(setting) {
    let tipStr;
    setting.dom.innerHTML = '';
    if (setting.type === 'pod') {
      let podId = setting.data.dnId;
      let podData = allPodData.current[podId];
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (podData.availableStatus === STATUS.normal && podData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      tipStr = `<div class="moType_tip_container">
                        <div class="moType_tipTitle">${setting.data.name}</div>
                        <div class=${className}>${status}</div>
                        <div class="moType_tipValue">${$t('moType.drill.rightPanel.time')} : ${formatDate2SysSetting(parseInt(podData.createTime))}</div>
                      </div>`;
    }

    if (setting.type === 'vm') {
      let vmId = setting.data.dnId;
      let vmData = allVMData.current[vmId];
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (vmData.availableStatus === STATUS.normal && vmData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      tipStr = `<div class="moType_tip_container">
                        <div class="moType_tipTitle">${setting.data.name}</div><div class="${className}">${status}</div>
                        <div class="moType_tipValue">${$t('moType.drill.vm.tooltip.vmIp')} : ${setting.data.ip || 'NA'}</div>
                      </div>`;
    }
    if (tipStr) {
      let tipParser = new DOMParser();
      let tipElement = tipParser.parseFromString(tipStr, 'text/html');
      setting.dom.append(tipElement.body.firstChild);
    }
  }

  // 查询钻取数据后处理
  const handleQueryPageData = pageData => {
    pageData.podDataList.map(pod => {
      allPodData.current[pod.dnId] = pod;
    });
    pageData.vmDataList.map(vm => {
      allVMData.current[vm.dnId] = vm;
    });
  };

  useEffect(() => {
    handleQueryPageData(drillData.data);
  }, [drillData]);

  const getA3dEvents = () => {
    return [{
      // 点击事件，联动右侧面板
      eventName: 'from3d_showPanel', async fn(setting) {
        if (setting.type === '') {
          closePodPanel();
          closeVMPanel();
        }
        if (setting.type === 'pod') {
          closeVMPanel(setting);
          let podDeployData = await queryPodDeployData(setting.id);
          podDeployData.dnId = setting.id;
          handlePodDetailsInfo(podDeployData.respData, setting.id, setting.name);
          eventBusHandler.emit('to3d_updateSelectedRelation', podDeployData);
          return;
        }
        if (setting.type === 'vm') {
          closePodPanel(setting);
          let vmDeployData = await queryVmDeployData(setting.id);
          handleVMDetailsInfo(vmDeployData.respData, setting.id, setting.name);
          eventBusHandler.emit('to3d_updateSelectedRelation', vmDeployData);
        }
      },
    }, {
      // 悬浮事件，显示tooltip
      eventName: 'from3d_showCommonTip', fn(setting) {
        showTips(setting);
      },
    }, {
      // 显示左侧详情
      eventName: 'from3d_showPodDrillDownDetail', fn(setting) {
        if (setting.type === 'vm') {
          const dom = document.createElement('div');
          dom.classList.add('w3d-podDetail-block');
          dom.innerText = 'HOST';
          setting.dom.append(dom);
        } else if (setting.type === 'pod') {
          const dom = document.createElement('div');
          dom.innerHTML = `
                 <div class='w3d-podDetail-block'>${isVMScenario.current ? $t('moType.drill.leftText.pod.instance') : $t('moType.drill.leftText.pod')}</div>`;
          if (pageParams.isMM) { // MM 标签左对齐。在2k差不多，分辨率不同可能有差异，只能尽量
            dom.style = `transform: translate(${isZh ? -40 : -10}px, 0);`;
          }
          setting.dom.append(dom);
        }
      },
    }];
  };

  useEffect(async() => {
    if (refresh !== 0) {
      if (showPodPanelRef.isOpen) {
        let podDeployData = await queryPodDeployData(showPodPanel.podData.id);
        handlePodDetailsInfo(podDeployData.respData, showPodPanel.podData.id, showPodPanel.podData.name);
      }

      if (showPodPanelRef.isOpen) {
        let vmDeployData = await queryVmDeployData(showVMPanel.vmData.id);
        handleVMDetailsInfo(vmDeployData.respData, showVMPanel.vmData.id, showVMPanel.vmData.name);
      }
    }
  }, [refresh]);

  useEffect(() => {
    let param = {timestamp: state.selectedTime || 0};
    getSolutionData(param, res => {
      // CBS才展示
      const solutionId = getQuery().solutionId;
      if (solutionId) {
        const needShow = (res.data || [])
          .find(v => String(v.solutionId) === getQuery().solutionId)?.solutionType === 1;
      }
    });
  }, [state.selectedTime]);

  useEffect(() => {
    setData([{
      title: $t('overViewName'), url: '/eviewwebsite/index.html#path=/businesstopo',
    }, {
      title: !pageParams.isMM ? `${decodeURIComponent(pageParams.siteName)}` : `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})`,
      url: pageParams.isMM ? `/eviewwebsite/index.html#path=/businesstopo/mmStripDrillDown&stripId=${pageParams.siteId}&stripName=${pageParams.siteName}&solutionId=${pageParams.solutionId}${pageParams.stripUnit ? `&stripUnit=${pageParams.stripUnit}` : ''}` : `/eviewwebsite/index.html#path=/businesstopo/sitehome&siteId=${pageParams.siteId}&solutionId=${pageParams.solutionId}`,
    }, {
      title: decodeURIComponent(pageParams.moTypeName),
      url:  `/eviewwebsite/index.html#path=/businesstopo/mohome&siteId=${pageParams.siteId}&moTypeId=${pageParams.moTypeId}&siteName=${pageParams.siteName}&moTypeName=${pageParams.moTypeName}&solutionId=${pageParams.solutionId}&applicationType=${pageParams.applicationType}`,
    }, {
      title: decodeURIComponent(pageParams.hostName),
    },
    ]);
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    let currentSelectSolutionIndex = 0;
    dispatch({
      selectSolutionIndex: currentSelectSolutionIndex,
    });
    if (topoSession.isTimeTrack) {
      return ()=> {
        // 空返回
      };
    }
    queryDrillData(initFlagRef.current === 0);
    initFlagRef.current = 1;

    const intervalId = setInterval(() => {
      if (!isTimeTrack.current) {
        setRefresh(new Date().getTime());
        queryDrillData(false);
      }
    }, 60 * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  const isPodDestroyed = (podData) => {
    const dnIdSet = new Set();
    (podData.podDataList || []).forEach(pod => {
      if (pod.dnId) {
        dnIdSet.add(pod.dnId);
      }
    });
    if (!dnIdSet.has(showPodIdRef.current)) {
      closePodPanel();
    }
  };

  // 查询钻取数据
  const queryDrillData = initFlag => {
    let params = {
      instanceId: pageParams.vmId,
      timestamp: 0,
    };
    queryHostview(params, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      if (resp.data && resp.data.vmDataList && resp.data.vmDataList.length > 0) {
        // 判断是否是pod消亡场景
        isPodDestroyed(resp.data);
        setDrillData({
          isInit: initFlag,
          data: resp.data,
          events: getA3dEvents(),
        });
      } else {
        closePodPanel();
        closeVMPanel();
      }
    });
  };

  // 查询pod部署关系
  const queryPodDeployData = podId => new Promise((resolve, reject) => {
    let param = {instanceId: podId};
    if (moSelectedTime.current !== 0) {
      param.endTime = moSelectedTime.current;
    }
    getPodDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      resolve({
        respData: resp.data, dnId: resp.data.dnId, relation: resp.data.vmDataList.map(vm => vm.dnId),
      });
    });
  });

  // 查询VM部署关系
  const queryVmDeployData = vmId => new Promise((resolve, reject) => {
    let param = {instanceId: vmId};
    if (moSelectedTime.current !== 0) {
      param.endTime = moSelectedTime.current;
    }
    getVMDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      resolve({
        respData: resp.data,
        dnId: resp.data.dnId,
        relation: resp.data.podInstanceIdList.map(num => num.toString()),
      });
    });
  });

  // 关闭pod详情面板
  const closePodPanel = setting => {
    showPodIdRef.current = null;
    setShowPodPanel({
      isShow: false, isOpen: false, detailsInfoArr: [], podData: {}, isTimeTrack: false, trackAlarmData: {},
    });
  };

  // 关闭VM详情面板
  const closeVMPanel = () => {
    setShowVMPanel({isShow: false, isOpen: false, vmData: {}, isTimeTrack: false, timeTrackVMData: {}});
  };

  // 处理pod右侧面板初始化数据
  const handlePodDetailsInfo = (respData, podId, podName, timeTrackPod) => {
    let vmData = {};
    if (respData.vmDataList && respData.vmDataList.length > 0) {
      vmData = respData.vmDataList[0];
    }
    let content = (
      <div style={{padding: '5px'}}>
        <p>
          {$t('moType.drill.vm.healthstatus')} :{' '}
          {$t(respData.csnState === 0 ? 'moType.drill.vm.normal' : 'moType.drill.vm.abnormal')}
        </p>
        <p>
          {$t('moType.drill.vm.status')} :{' '}
          {$t(
            respData.availableStatus === STATUS.normal ?
              'moType.drill.pod.status.normal' :
              'moType.drill.pod.status.abnormal',
          )}
        </p>
      </div>
    );

    let detailsInfoArr = [
      {
        value: respData.csnState === 0 && respData.availableStatus === STATUS.normal ? STATUS.normal : STATUS.failed,
        title: $t('moType.drill.status'),
        content,
      },
      {value: vmData.vmName, title: $t('moType.drill.rightPanel.host')},
      {
        value: pageParams.isMM ? `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})` : `${decodeURIComponent(pageParams.siteName)}`,
        title: pageParams.isMM ? $t('moType.drill.rightPanel.stripName') : $t('moType.drill.rightPanel.site'),
      },
      {value: respData.podIp ? respData.podIp : 'NA', title: $t('moType.drill.rightPanel.ip')},
      {
        value: vmData.vmIp ? vmData.vmIp : 'NA',
        title: $t('moType.drill.rightPanel.hostIP'),
      },
      {value: formatDate2SysSetting(parseInt(respData.createTime)), title: $t('moType.drill.rightPanel.time')},
      {
        value: respData.dockerName,
        title: isVMScenario.current ? $t('moType.drill.rightPanel.appName') : $t('moType.drill.rightPanel.dockerName'),
      },
    ];

    if (isVMScenario.current) {
      detailsInfoArr = detailsInfoArr.filter(item => item.title !== $t('moType.drill.rightPanel.ip'));
    }

    showPodIdRef.current = podId;
    setShowPodPanel({
      isShow: true,
      isOpen: true,
      detailsInfoArr,
      podData: {
        name: podName,
        id: podId,
        indicatorList: allPodData.current[podId].podIndicatorList ? allPodData.current[podId].podIndicatorList : [],
      },
      isTimeTrack: Boolean(timeTrackPod),
      trackAlarmData: timeTrackPod ? timeTrackPod.alarmInfo.alarmDataList : {},
    });
  };

  // 处理VM右侧面板初始化数据
  const handleVMDetailsInfo = (respData, vmId, vmName, timeTrackVM) => {
    let normal = 0;
    let abnormal = 0;

    for (let pod of respData.podInstanceIdList) {
      let podData = allPodData.current[pod];
      if (!podData) {
        continue;
      }
      if (podData.csnState === 1) {
        abnormal++;
      } else {
        normal++;
      }
    }

    setShowVMPanel({
      isShow: true, isOpen: true, vmData: {
        id: vmId, name: vmName, normalPods: normal, abnormalPods: abnormal,
      }, isTimeTrack: Boolean(timeTrackVM), timeTrackVMData: timeTrackVM,
    });
  };

  // 时间回溯回调事件
  const timeTrack = timeStamp => {
    closeVMPanel();
    closePodPanel();
    dispatch({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    moSelectedTime.current = timeStamp;
    setSessionData({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    isTimeTrack.current = true;

    setRefresh(timeStamp);
    let param = {
      instanceIdList: [],
      timestamp: 0,
      targetTimestamp: timeStamp,
    };
    param.instanceIdList.push(pageParams.vmId);
    queryCurrentIds(param, async({data: idMap}) => {
      if (!idMap || Object.entries(idMap).length === 0) {
        setShowMessage(true);
        let nullData = {
          businessClusterList: [],
          podDataList: [],
          vmDataList: [],
        };
        setDrillData({
          isInit: false, data: nullData, events: getA3dEvents([]),
        });
        return;
      }

      let params = {
        instanceId: idMap[pageParams.vmId],
        timestamp: timeStamp,
      };
      queryHostview(params, resp => {
        if (!resp || resp.resultCode !== 0) {
          return;
        }
        if (resp.data) {
          if (!resp.data || resp.data.vmDataList.length === 0) {
            setShowMessage(true);
          } else {
            setShowMessage(false);
          }
          setDrillData({
            isInit: initFlagRef.current === 0,
            data: resp.data,
            events: getA3dEvents(),
          });
          initFlagRef.current = 1;
        }
      });
    });
  };

  // 退出时间回溯回调事件
  const exitTimeTrack = () => {
    closeVMPanel();
    closePodPanel();
    dispatch({
      selectedTime: 0, isTimeTrack: false,
    });
    setShowMessage(false);
    isTimeTrack.current = false;
    moSelectedTime.current = 0;
    setSessionData({
      selectedTime: 0, isTimeTrack: false,
    });
    setRefresh(new Date().getTime());
    queryDrillData(false);
  };

  return (
    <TopoContext.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div style={{background: '#191919'}} id="motypeDrillTopo">
          <HostDrillDown pageParams={drillData} />
          <MessageDeatil display={showMessage} main={false} />
          {
            !pageParams.isSearch &&
                        <div className="moType_back_btn">
                          <Crumbs data={data} seprator="/" />
                        </div>
          }
          {showPodPanel.isShow && (
            <PodRightPanel
              closePanel={closePodPanel}
              detailsInfoArr={showPodPanel.detailsInfoArr}
              podData={showPodPanel.podData}
              moTypeId={pageParams.moTypeId}
              siteId={pageParams.siteId}
              isTimeTrack={showPodPanel.isTimeTrack}
              trackAlarmData={showPodPanel.trackAlarmData}
              isOpenPanel={showPodPanel.isOpen}
              setIsOpenPanel={flag => setShowPodPanel({...showPodPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          {showVMPanel.isShow && (
            <VMRightPanel
              vmData={showVMPanel.vmData}
              closePanel={closeVMPanel}
              moTypeId={pageParams.moTypeId}
              siteId={pageParams.siteId}
              siteName={pageParams.siteName}
              isTimeTrack={showVMPanel.isTimeTrack}
              timeTrackVMData={showVMPanel.timeTrackVMData}
              isOpenPanel={showVMPanel.isOpen}
              setIsOpenPanel={flag => setShowVMPanel({...showVMPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          <TimeLine
            renderTopology={timeTrack}
            refreshFlag={refresh}
            backTimeTrack={exitTimeTrack}
            pageType={HOST_TYPE_HOME}
          />
          <Toolbar isMotype={true} showMotypePanel={showPodPanel.isOpen || showVMPanel.isOpen}
            isMM={pageParams.isMM}
          />
        </div>
      </ConfigProvider>
    </TopoContext.Provider>
  );
}

export default HostDrill3D;
