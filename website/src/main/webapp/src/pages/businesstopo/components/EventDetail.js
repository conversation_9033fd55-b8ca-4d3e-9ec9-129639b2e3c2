/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, { useContext } from 'react';
import { BUSINESS_TOPO_CONTEXT } from '../const';
import { Tooltip } from 'eview-ui';
import { dateTimeFormat, jumpAlarmLogPage, jumpEventLogPage } from '../../../commonUtil/tools';
import PropTypes from 'prop-types';

function EventDetail(props) {
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const { record } = props;

  const eventJump = () => {
    jumpEventLogPage(record.csn, record.occurUtc);
  };

  return (
    <div className="detail_panel" onClick={eventJump}>
      <div className="detail_content">
        <div className='event_icon' />
        <Tooltip content={record.alarmName} placement='topLeft' color='#393939' overlayStyle={{ color: '#FFF' }}>
          <div className="detail_title" style={{ color: 'white', display: 'inline-block', width: '300px' }}>
            {record.alarmName}
          </div>
        </Tooltip>
        <Tooltip content={record.additionalInformation} placement='topLeft' color='#393939'
          overlayStyle={{ color: '#FFF' }}
        >
          <p className="detail_sub_title">
            {record.additionalInformation}
          </p>
        </Tooltip>
        <div style={{ marginTop: '10px', marginBottom: '10px' }}>
          <div className='time_icon' />
          <p className='detail_alarm_value'>{dateTimeFormat(record.occurUtc)}</p>
        </div>
      </div>
    </div>
  );
}

EventDetail.propTypes = {
  record: PropTypes.shape({
    alarmName: PropTypes.string,
    occurUtc: PropTypes.string,
    additionalInformation: PropTypes.string,
    csn: PropTypes.string,
  }).isRequired,
};

export default EventDetail;
