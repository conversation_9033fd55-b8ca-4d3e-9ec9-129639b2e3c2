import React, {useContext, useEffect, useRef} from 'react';
import DataSourceNETree from '@pages/businesstopo/components/edit/customSelectTree/DataSourceNETree';
import '@pages/businesstopo/css/edit/customSelectTree/index.css';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
import DataSourceUnitTree from '@pages/businesstopo/components/edit/customSelectTree/DataSourceUnitTree';
import DataSourceObjectTable from '@pages/businesstopo/components/edit/customSelectTree/DataSourceObjectTable';
import ConfigProvider from 'eview-ui/ConfigProvider';
import {$t} from '@util';

const CustomSelectTree = ({
  areaIcons,
  setAreaIcons,
  selectedIcon,
  pannelContent,
  setPannelContent,
  setIndictorError,
}) => {
  const NEChildRef = useRef(); // 网元树组件Ref
  const UnitTreeChildRef = useRef(); // 测量单元树组件Ref
  const objTableChildRef = useRef(); // 测量对象表格组件Ref
  const createTaskAllInfo = useRef({});
  const measureData = useRef({});
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {objectTable, netWorkTreeParam, unitTree} = state;
  const checkedMeasureIndexTable = useRef();
  const setUnitTree = (param) => {
    dispatch({type: 'setUnitTree', unitTree: {...createTaskAllInfo.current.unitTree, ...param}});
  };
  const getCreateTaskAllInfo = () => {
    return createTaskAllInfo.current;
  };
  useEffect(() => {
    createTaskAllInfo.current = {objectTable, netWorkTreeParam, unitTree};
  }, [objectTable, netWorkTreeParam, unitTree]);
  // 父组件相关数据的获取和更新
  const parentDataHandle = {
    setMeasureData: (param) => {
      measureData.current = {
        ...measureData.current,
        ...param,
      };
    },
    getMeasureData: () => measureData.current,
    getCheckedMeasureIndexTable: () => checkedMeasureIndexTable.current,
    setCheckedMeasureIndexTable: (item) => checkedMeasureIndexTable.current = item,
  };

  const setObjectTable = (param) => {
    dispatch({objectTable: {...createTaskAllInfo.current.objectTable, ...param}});
  };
  const queryUnitTree = (apiFn, params, callback) => UnitTreeChildRef.current.query(apiFn, params, callback);
  const queryObjTable = (selectedKeys, queryFn, params, callback) =>
    objTableChildRef.current.queryObjTable(selectedKeys, queryFn, params, callback);

  const getClearObject = () => {
    return {
      isObjectNoData: true,
      measureObject: '',
    };
  };
  const handleCancel = () => {
    dispatch({
      editAddPannelShow: false,
      netWorkTreeParam: {},
      unitTree: {
        measureType: 'all',
        measureUnit: '',
        IndicatorId: '',
      },
      objectTable: {
        isObjectNoData: true,
      },
    });
  };

  const handleConfirm = () => {
    let measureData1 = parentDataHandle.getMeasureData();
    let area = areaIcons[selectedIcon.id];
    let data = area[selectedIcon.iconId];
    if (data.flag === 0) {
      data.flag = 2;
    }
    data.editType = 1;
    let key = measureData1.checkedNetType + measureData1.measUnitKey + measureData1.indexId + measureData1.originalValue;
    if (state.editAddPannelStatus === 2) {
      data.rightPannelData = data.rightPannelData.map(item => {
        if (item.key === state.editAddPannelKey) {
          // 返回一个新对象，修改 'flag' 属性
          return {
            moName: measureData1.checkedNetName,
            measUnitName: measureData1.indexGroupName,
            indexName: measureData1.measureIndex,
            displayValue: measureData1.displayValue,
            moType: measureData1.checkedNetType,
            measUnitKey: measureData1.measUnitKey,
            measTypeKey: measureData1.measTypeKey,
            indexId: measureData1.indexId,
            originalValue: measureData1.originalValue,
            indicatorDisplayType: item.indicatorDisplayType,
            key,
          };
        }
        // 不满足条件的对象保持不变
        return item;
      });
    } else {
      let existsWithKey = data.rightPannelData.some(item => 'key' in item && item.key === key);
      if (!existsWithKey) {
        if (data.rightPannelData.length >= 5) {
          dispatch({
            netWorkTreeParam: {},
            unitTree: {
              measureType: 'all',
              measureUnit: '',
              IndicatorId: '',
            },
            objectTable: {
              isObjectNoData: true,
            },
          });
          setIndictorError(true);
          return;
        }
        data.editType = 1;
        data.rightPannelData.push({
          moName: measureData1.checkedNetName,
          measUnitName: measureData1.indexGroupName,
          indexName: measureData1.measureIndex,
          displayValue: measureData1.displayValue,
          indicatorDisplayType:0,
          moType: measureData1.checkedNetType,
          measUnitKey: measureData1.measUnitKey,
          measTypeKey: measureData1.measTypeKey,
          indexId: measureData1.indexId,
          originalValue: measureData1.originalValue,
          key,
        });
      }
    }

    setAreaIcons(prevState => ({
      ...prevState,
      [selectedIcon.id]: area,
    }));
    setPannelContent({
      ...pannelContent,
      rightPannelData: data.rightPannelData,
      editType:1,
    });

    dispatch({
      editAddPannelShow: false,
    });
  };

  return (
    state.editAddPannelShow &&
      <div id="custom-select-tree">
        <div>
          <ul id='source_measure'>
            <li style={{marginRight: '1%'}}>
              <DataSourceNETree
                getCreateTaskAllInfo={getCreateTaskAllInfo}
                getClearUnitTreeInfo={() => UnitTreeChildRef.current.getClearUnitTreeInfo()}
                selectedUnitNodes={(checkedNeNode, isExpand) => UnitTreeChildRef.current.selectedUnitNodes(checkedNeNode, isExpand)}
                getClearObject={getClearObject}
                queryUnitTree={queryUnitTree}
                parentDataHandle={parentDataHandle}
                cRef={NEChildRef}
              />
            </li>
            <li style={{width: '34%', marginRight: '1%'}}>
              <DataSourceUnitTree setUnitTree={setUnitTree}
                getClearObject={getClearObject}
                cRef={UnitTreeChildRef}
                parentDataHandle={parentDataHandle}
                getCreateTaskAllInfo={getCreateTaskAllInfo}
                queryObjTable={queryObjTable}
              />
            </li>
            <li style={{width: '34%'}}>
              <DataSourceObjectTable
                cRef={objTableChildRef}
                parentDataHandle={parentDataHandle}
                setObjectTable={setObjectTable}
                getCreateTaskAllInfo={getCreateTaskAllInfo}
              />
            </li>
          </ul>
        </div>
        <div className="action-buttons" style={{marginTop: '30px', right: '50px'}}>
          <button className="cancel-button" onClick={handleCancel}>{$t('button.cancel')}</button>
          <button className={state.indicatorButtonDisable ? 'confirm-button-disable' : 'confirm-button'}
            onClick={handleConfirm} id="confirmToolbar"
          >{$t('button.confirm')}
          </button>
        </div>
      </div>
  );
};

export default CustomSelectTree;