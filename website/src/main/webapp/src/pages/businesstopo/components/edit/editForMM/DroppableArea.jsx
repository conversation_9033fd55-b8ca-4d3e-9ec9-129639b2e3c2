import React, {useState} from 'react';
import {Tooltip} from 'eview-ui';
import pageCss from './css/index.css';
import {$t} from '@util';

const DroppableArea = props => {
  const {
    id,
    text,
    highlightedAreaId,
    areaIcons,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    selectedIcon,
    setSelectedIcon,
    handleDelete,
    setIsShowRightPanel,
    setEditRightPanelShow,
    setPannelData,
    setNullSelectedIcon,
  } = props;
  const [currentIndex, setCurrentIndex] = useState(0);
  const handleIconSelect = (id1, iconId, e) => {
    if (selectedIcon === iconId) {
      setSelectedIcon(null);
      setIsShowRightPanel(false);
    } else {
      setSelectedIcon({
        areaId: id1,
        iconId,
      });
      setIsShowRightPanel(true);
      setEditRightPanelShow(true);
      e.stopPropagation();
    }
  };

  const getAreaIcons = () => {
    let iconsToShow = (currentIndex + 1) * 10;
    if (iconsToShow >= areaIcons[id].length) {
      iconsToShow = areaIcons[id].length;
    }
    const result = areaIcons[id].slice(currentIndex * 10, iconsToShow); // 截取对应条数的数据
    return result;
  };
  const onClickNextPage = () => {
    if (currentIndex === areaIcons[id].length - 1) {
      return;
    }
    setCurrentIndex(currentIndex + 1);
  };

  const onClickNext2Page = () => {
    if (currentIndex === areaIcons[id].length - 1) {
      return;
    }
    setCurrentIndex(currentIndex + 2);
  };

  const onClickBeforePage = () => {
    if (currentIndex === 0) {
      return;
    }
    setCurrentIndex(currentIndex - 1);
  };

  function getName(iconData) {
    let businessName = '';
    if (iconData.businessName) {
      businessName = iconData.businessName.length > 5 ?
        `${iconData.businessName.substring(0, 5)}...` :
        iconData.businessName;
    }

    return businessName;
  }

  return (
    <div className={pageCss.body_content_drop_area_container}>
      {currentIndex !== 0 &&
        <div id="left-arrow-div" className="left-arrow-div" onClick={onClickBeforePage} style={{left: '326px'}} />}
      <div className={pageCss.body_content_drop_area_text}>{text}</div>
      <div
        style={{marginTop: id === 'app' ? '64px' : 'unset'}}
        className={`${pageCss.body_content_drop_area} ${highlightedAreaId === id ? 'highlight' : ''}`}
        onDrop={e => {
          if ((currentIndex + 1) * 10 - 1 < areaIcons[id].length && id !== 'app') {
            if (areaIcons[id].length < 30) {
              if ((currentIndex + 1) * 10 + 9 >= areaIcons[id].length) {
                onClickNextPage();
              } else {
                onClickNext2Page();
              }
            }
          }
          handleDrop(e, id);
        }}
        onDragOver={e => handleDragOver(e, id)}
        onDragLeave={handleDragLeave}
        onClick={(e) => {
          setIsShowRightPanel(true);
          setEditRightPanelShow(true);
          setPannelData({
            type: id === 'thirdPart' ? 4 : 5,
            name: id === 'thirdPart' ? $t('edit.body.third.part') : $t('edit.body.app'),
          });
          setNullSelectedIcon();
          e.stopPropagation();
        }}
      >
        {getAreaIcons().map((iconData, index) => (
          <div
            key={index}
            className={`${pageCss.body_content_drop_area_icon} ${selectedIcon?.iconId === (currentIndex * 10 + index) && selectedIcon.areaId === id ? 'selected' : ''} ${iconData.checkError ? 'editerror' : ''}`}
            onClick={(e) => handleIconSelect(id, currentIndex * 10 + index, e)}
          >
            <img alt="" src={iconData.icon} draggable={false} width="70px" height="70px" />
            <div
              className={`${iconData.canDelete && selectedIcon && selectedIcon.iconId === (currentIndex * 10 + index) && id === selectedIcon.areaId ? 'delete-icon-mm' : ''}`}
              onClick={(e) => handleDelete(id, currentIndex * 10 + index, e)}
            />
            <Tooltip placement="top" content={iconData.businessName} trigger={['hover', 'focus']}>
              <div className={pageCss.body_content_drop_area_icon_text}>
                {getName(iconData)}
              </div>
            </Tooltip>
          </div>
        ))}
        <div className="line1" style={{top: 'calc(102%)'}} />
      </div>
      {currentIndex < (areaIcons[id].length / 10) - 1 &&
        <div className="right-arrow-div" style={{right: '150px'}} onClick={onClickNextPage} />}
    </div>
  );
};

export default DroppableArea;
