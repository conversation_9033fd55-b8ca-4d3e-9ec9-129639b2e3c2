/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';
import * as echarts from 'echarts';
import {getPodIndicatorData} from '../../api/moTypeDrill';
import {dateFormatToHour, jumpToPMHistoryDataPage} from '@util/tools';
import {BUSINESS_TOPO_CONTEXT, getHistorySeries, LINE_CHART_COLOR} from '../../const';
import '../../css/moTypeDrill.less';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {getUrlParam} from '../../util';
import SelectButtonGroup from '../common/SelectButtonGroup';
import {MoreLegend} from '../common/Legend';
import {sortHistoryListData} from '../../../../commonUtil/tools';

function HSMPanelChart({id, refresh, showKPIPanel}) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const chartObjRef = useRef(null);
  const legendRef = useRef(null);
  const currentId = useRef(null);

  // 全量得指标头信息，定时更新
  const [rawIndicatorList, setRawIndicatorList] = useState({});

  const [currentIndex, setCurrenIndex] = useState(0);

  // // 设置指标展示哪些 最多三个 当前页面展示得选中指标的index,indicatorSelectShow中对应的index
  const [selectValue, setSelectValue] = useState([]);

  const [seriesLegendData, setSeriesLegendData] = useState([]);
  const [selected, setSelected] = useState({});
  useEffect(() => {
    setSelected(seriesLegendData.reduce((curr, next) => {
      curr[next.id] = selected[next.id] === false ? false : true;
      return curr;
    }, {}));
  }, [JSON.stringify(seriesLegendData)]);

  let initFalg = useRef(false);

  const legendData = useMemo(() => {
    return seriesLegendData.map(v => {
      return {
        id: v.id,
        name: v.name,
        selected: selected[v.id],
      };
    });
  }, [selected]);

  useEffect(() => {
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('hsmPanelChart');
      chartObjRef.current = echarts.init(chartDom);
    }
    if (currentId.current !== id) {
      setSelectValue([]);
      setCurrenIndex(0);
      setRawIndicatorList([]);
    }
    queryIndicatorData(true);
  }, [id]);

  useEffect(() => {
    if (selectValue.length === 0) {
      return;
    }
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('hsmPanelChart');
      chartObjRef.current = echarts.init(chartDom);
    }
    queryIndicatorData();
  }, [currentIndex]);

  useEffect(() => {
    if (!initFalg.current) {
      return;
    }
    if (!chartObjRef.current) {
      return;
    }
    let params = {
      instanceId: id,
    };
    let paramsTitle = {
      instanceId: id,
      newIndicatorListFlag: 1,
    };
    if (getUrlParam('moTypeId') === paramsTitle.instanceId) {
      paramsTitle.stripeUnit = getUrlParam('stripUnit');
    }
    if (state.isTimeTrack && state.selectedTime > 0) {
      params.endTime = state.selectedTime;
      paramsTitle.endTime = state.selectedTime;
    }
    getPodIndicatorData(paramsTitle, respTitle => {
      setRawIndicatorList(respTitle.data.indicatorIdList);
      if (respTitle.data.indicatorIdList.length === 0) {
        chartObjRef.current.clear();
      }
      if (currentId.current === id) {
        let index = selectValue.indexOf(currentIndex);
        params.indicatorId = respTitle.data.indicatorIdList[selectValue[index]].indicatorId;
      } else {
        currentId.current = id;
      }
      if (getUrlParam('isMM')) {
        params.stripeUnit = ''; // 方便后台区分MM场景
      }
      if (getUrlParam('moTypeId') === params.instanceId) {
        params.stripeUnit = getUrlParam('stripUnit');
        params.stripeId = getUrlParam('siteId');
      }

      // 刷新场景
      getPodIndicatorData(
        params,
        resp => {
          if (!resp || resp.resultCode !== 0) {
            return;
          }
          if (resp.data && resp.data.podIndicatorList && resp.data.podIndicatorList.length > 0) {
            resp.data.podIndicatorList = sortHistoryListData(resp.data.podIndicatorList || []);
            const oldOption = chartObjRef.current.getOption();
            let options = getOptions(resp.data);
            if (oldOption.series.length !== 0) {
              oldOption.xAxis = options.xAxis;
              oldOption.yAxis = options.yAxis;
              oldOption.series = options.series;
              options.legend = legendRef.current;
              chartObjRef.current.clear();
              chartObjRef.current.setOption(oldOption);
              setSeriesLegendData((oldOption.series || []).map(v => {
                return {
                  name: v.name,
                  id: v.name,
                };
              }));
            } else {
              chartObjRef.current.setOption(options);
              setSeriesLegendData((options.series || []).map(v => {
                return {
                  name: v.name,
                  id: v.name,
                };
              }));
            }

            chartObjRef.current.getZr().off('click');
            chartObjRef.current.getZr().on('click', param => {
              const pointInPixel = [param.offsetX, param.offsetY];
              if (!chartObjRef.current.containPixel('grid', pointInPixel)) {
                return;
              }
              handleJump(resp.data.podIndicatorList);
            });
          }
        },
      );
    });
  }, [refresh]);

  const handleJump = indicatorArr => {
    jumpToPMHistoryDataPage(indicatorArr, indicatorArr[0].startTime, indicatorArr[0].endTime);
  };

  // 查询指标数据
  const queryIndicatorData = (idChange) => {
    let params = {
      instanceId: id,
    };
    if (chartObjRef.current) {
      chartObjRef.current.clear();
    }
    let paramsTitle = {
      instanceId: id,
      newIndicatorListFlag: 1,
    };
    if (getUrlParam('moTypeId') === paramsTitle.instanceId) {
      paramsTitle.stripeUnit = getUrlParam('stripUnit');
    }
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    if (topoSession.isTimeTrack) {
      params.endTime = topoSession.selectedTime;
      paramsTitle.endTime = topoSession.selectedTime;
    }
    getPodIndicatorData(paramsTitle, respTitle => {
      initFalg.current = true;
      // 初始化场景
      setRawIndicatorList(respTitle.data.indicatorIdList);
      if (idChange) {
        if (respTitle.data.indicatorIdList.length >= 3) {
          setSelectValue([0, 1, 2]);
        } else {
          const array = [];
          for (let i = 0; i < respTitle.data.indicatorIdList.length; i++) {
            array.push(i);
          }
          setSelectValue(array);
        }
        setCurrenIndex(0);
      }
      if (currentId.current === id) {
        let index = selectValue.indexOf(currentIndex);
        params.indicatorId = respTitle.data.indicatorIdList[selectValue[index]].indicatorId;
      } else {
        currentId.current = id;
      }

      if (getUrlParam('isMM')) {
        params.stripeUnit = ''; // 方便后台区分MM场景
      }
      if (getUrlParam('moTypeId') === params.instanceId) {
        params.stripeUnit = getUrlParam('stripUnit');
        params.stripeId = getUrlParam('siteId');
      }
      getPodIndicatorData(
        params,
        resp => {
          if (!resp || resp.resultCode !== 0) {
            return;
          }
          if (resp.data && resp.data.podIndicatorList && resp.data.podIndicatorList.length > 0) {
            resp.data.podIndicatorList = sortHistoryListData(resp.data.podIndicatorList || []);
            let options = getOptions(resp.data);
            chartObjRef.current.setOption(options);
            chartObjRef.current.getZr().off('click');
            chartObjRef.current.getZr().on('click', param => {
              const pointInPixel = [param.offsetX, param.offsetY];
              if (!chartObjRef.current.containPixel('grid', pointInPixel)) {
                return;
              }
              handleJump(resp.data.podIndicatorList);
            });
            setSeriesLegendData((options.series || []).map(v => {
              return {
                name: v.name,
                id: v.name,
              };
            }));
          }
        },
      );
    });
  };

  const getOptions = respData => ({
    title: {
      subtext: respData.podIndicatorList[0].indexUnit,
      subtextStyle: {
        align: 'left',
        color: '#BBBBBB',
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      borderColor: '#393939',
      backgroundColor: '#393939',
      appendToBody: true,
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      textStyle: {
        color: '#FFFFFF',
      },
      enterable: true,
      position: (point, params, dom, rect, size) => {
        let chartDom = document.getElementById('hsmPanelChart');
        const TOOLTIP_OFFSET = 4;
        // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
        const canvasRect = chartDom.getBoundingClientRect();
        const {contentSize: tooltipSize, viewSize: canvasSize} = size;
        let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
        let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
        // 校正tooltip的 left 定位，防止超出画布的左边界
        if (left < 0) {
          left = 0;
        }
        if (canvasRect) {
          // 校正tooltip的 top 定位，防止超出可视窗口
          const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
          const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
          if (toolTipExceedViewport || toolTipExceedCanvas) {
            top = -canvasRect.top;
          }
        }
        return [left, top];
      },
      formatter: (params) => {
        // 获取时间（假设 x 轴为时间）
        const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
        // 遍历每条线的数据，保留默认的值展示
        const values = params
          .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
          .join('<br>');
        // 返回自定义的提示框内容
        return `<strong>${time}</strong><br>${values}`;
      },
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '10%',
      top: '15%',
      containLabel: true,
    },
    textStyle: {
      color: '#BBBBBB',
    },
    legend: {
      show: false,
      orient: 'horizontal',
      bottom: '0%',
      itemGap: 50,
      type: 'scroll',
      icon: 'circle',
      itemWidth: 15,
      itemHeight: 10,
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      textStyle: {
        color: '#BBBBBB',
      },
      pageTextStyle: {
        color: '#FFFFFF',
      },
    },
    xAxis: {
      type: 'time',
      boundaryGap: true,
      axisLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
          type: 'solid',
          width: 3,
        },
      },
      axisTick: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
          type: 'solid',
          width: 2,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        interval: 0,
        borderColor: 'transparent',
        borderWidth: 10,
        fontSize: 10,
        margin: 10,
        hideOverlap: true,
        showMinLabel: false,
        showMaxLabel: false,
        formatter: value => {
          let startTime = parseInt(respData.podIndicatorList[0].startTime);
          let endTime = parseInt(respData.podIndicatorList[0].endTime);
          const unit = (endTime - startTime) / 12;
          if (value > startTime && value < endTime) {
            if (Math.abs(value - startTime) < unit || Math.abs(value - endTime) < unit) {
              return '';
            }
            return dateFormatToHour(value);
          }
          return '';
        },
      },
      splitLine: {
        show: false,
      },
      min: parseInt(respData.podIndicatorList[0].startTime),
      max: parseInt(respData.podIndicatorList[0].endTime),
    },
    yAxis: {
      type: 'value',
      boundaryGap: true,
      nameTextStyle: {
        color: '#BBBBBB',
      },
      splitLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        fontSize: 10,
        margin: 5,
        hideOverlap: true,
        align: 'right',
        overflow: 'break',
        width: 200,
      },
    },
    series: getHistorySeries(respData.podIndicatorList, true, false),
  });

  const handleIndicatorChange = (newValue, oldValue, event) => {
    // 修改选择值
    setSelectValue(newValue);
    if (newValue.indexOf(currentIndex) === -1) {
      setCurrenIndex(newValue[0]);
    }
  };
  let width = window.innerWidth - 600;
  return (
    <div className="podPanelChartContainer" style={{
      width,
      height: '22rem',
      marginBottom: '65px',
      padding: '20px',
      'z-index': '9999',
      position: 'absolute',
      bottom: '7rem',
      marginLeft: showKPIPanel ? `${80}px` : `${250}px`,
      zIndex: 9999,
    }}
    >
      {rawIndicatorList.length > 0 && (
        <div style={{width: '100%', height: '12%'}}>
          <SelectButtonGroup
            {
            ...{
              data: rawIndicatorList.map((item, index) => {
                return {
                  id: `${index}___${item.indexName}`,
                  name: String(item.indexName),
                  onClick: () => {
                    setCurrenIndex(index);
                  },
                };
              }),
              editButtonGroupClick: (newValue) => {
                handleIndicatorChange((newValue || []).map(v => {
                  return Number(String(v).split('___')[0]);
                }));
              },
            }
            }
            buttonStyle={{
              marginRight: '16px',
              marginTop: '0px',
            }}
          />
        </div>
      )}
      <div id="hsmPanelChart" className="hsmPanelChartDiv" />
      {
        <div style={{padding: '0 30px'}}>
          <MoreLegend
            data={rawIndicatorList.length === 0 ? [] : legendData}
            style={{marginTop: '-30px'}}
            iconStyle={{
              width: '10px',
              height: '10px',
              top: '5px',
            }}
            maxWidth={800}
            color={LINE_CHART_COLOR.map(v => v[0])}
            onClick={(clickId) => {
              if (!chartObjRef.current) {
                return;
              }

              setSelected(prevSelected => {
                const oldSelected = {...prevSelected};
                if (event.ctrlKey) {
                  // 点击的时候按住ctl键，只改变当前图例的勾选状态
                  oldSelected[clickId] = !oldSelected[clickId];
                } else {
                  if (oldSelected[clickId]) {
                    // 点击的图例已勾选
                    if (Object.values(oldSelected).every(isSelect => isSelect)) {
                      // 当前为全选，单选该图例
                      for (const legendKey of Object.keys(oldSelected)) {
                        oldSelected[legendKey] = false;
                      }
                      oldSelected[clickId] = true;
                    } else {
                      // 当前为非全选，勾选全部图例
                      for (const legendKey of Object.keys(oldSelected)) {
                        oldSelected[legendKey] = true;
                      }
                    }
                  } else {
                    // 点击的图例未勾选，单选该图例
                    for (const legendKey of Object.keys(oldSelected)) {
                      oldSelected[legendKey] = false;
                    }
                    oldSelected[clickId] = true;
                  }
                }

                const selectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                  return value;
                }).map(([key, value]) => {
                  return {
                    name: legendData.find(v => v.id === key)?.name,
                  };
                });
                const unSelectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                  return !value;
                }).map(([key, value]) => {
                  return {
                    name: legendData.find(v => v.id === key)?.name,
                  };
                });
                chartObjRef.current.dispatchAction({
                  type: 'legendSelect',
                  batch: selectedNameList,
                });

                chartObjRef.current.dispatchAction({
                  type: 'legendUnSelect',
                  batch: unSelectedNameList,
                });
                return oldSelected;
              });
            }}
          />
        </div>
      }
    </div>
  );
}

export default HSMPanelChart;
