import React, { useContext, useState, useEffect } from 'react';
import { Row, Col, Tooltip, Button, TextArea, SelectCard, Toggle } from 'eview-ui';
import { $t, modal, validate } from '@util';
import {goToNewPage} from '@digitalview/fe-utils';
import styles from '../../index.less';
import LabelHint from '../../../common/LabelHint';
import { modifyTaskCommon } from '../NewOrEditCommonUtils/index';
import {
  TaskManageContext,
  IS_CAN_MODIFY_TASK,
  TRAIN_EXECUTE_CYCLE,
  TRAIN_CRON_MAP,
  DISPLAY_RESULT_MAP,
  GlobalContext,
} from '../../const';
import { cancelCreateOrEditTask } from '../NewOrEditCommonUtils';

let trainCycle;
let taskDescTextArea;
let basicInfo = {};

const BasicInfoDetail = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const { theme } = useContext(GlobalContext);
  const { viewDetailInfo, basicInfoList } = state;
  const [modifyTask, setModifyTask] = useState(false);

  useEffect(() => {
    if (!modifyTask) {
      return;
    }
    setBasicInfoList({
      displayResult: viewDetailInfo.displayResult ?? false,
      trainCycle: viewDetailInfo.trainCron,
      taskDescription: viewDetailInfo.taskDesc,
    });
  }, [modifyTask]);

  useEffect(() => {
    basicInfo = { ...basicInfoList };
  }, [basicInfoList]);

  const setBasicInfoList = (param) => {
    dispatch({
      type: 'setBasicInfoList',
      basicInfoList: { ...basicInfoList, ...param },
    });
  };

  // 通过分析结果是否可见值获取展示
  const getResultVisibility = (displayResult) => {
    return DISPLAY_RESULT_MAP[displayResult];
  };

  // 通过训练周期值获取训练周期展示
  const getTrainPeriodShow = (trainCron) => {
    return TRAIN_CRON_MAP[trainCron];
  };

  // 是否有修改任务权限
  const isCanModifyPermission = () => {
    return viewDetailInfo.canModify === IS_CAN_MODIFY_TASK.YES;
  };

  const switchModifyTask = () => {
    setModifyTask(!modifyTask);
  };

  // 校验页面输入项
  const verifyData = () => {
    if (!basicInfoList.trainCycle) {
      modal.error(
        $t('intelligent.incident.task.manage.train.cycle.please.select'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    if (!taskDescTextArea.validate()) {
      taskDescTextArea.focus();
      return false;
    }
    return true;
  };

  const confirmModifyTask = () => {
    if (!verifyData()) {
      return;
    }
    const param = {
      ...viewDetailInfo,
      trainCron: basicInfoList.trainCycle,
      taskDesc: basicInfoList.taskDescription,
      displayResult: basicInfoList.displayResult,
    };
    modifyTaskCommon(param, switchModifyTask, viewDetailInfo.taskId, dispatch, theme);
  };

  return (
    modifyTask ? (
      <>
        <div className={styles.detailContentContainer}>
          <table>
            <tbody>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.id')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.taskId}
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.table.task.name')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.taskName}
                </td>
              </tr>
              <tr>
                <td style={{ verticalAlign: 'baseline' }}>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.analysis.result.visible')}
                    require={true}
                    theme={theme}
                  />
                </td>
                <td>
                  <Toggle 
                    data={[false, true]}
                    toggled={basicInfoList.displayResult}
                    onToggle={(val) => setBasicInfoList({ displayResult: val })}
                  />
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.train.period')}
                    require={true}
                    theme={theme}
                  />
                </td>
                <td>
                  <SelectCard data={TRAIN_EXECUTE_CYCLE} 
                    itemStyle={{ width: '11.25rem' }}
                    style={{ width: '33.75rem' }}
                    value={basicInfoList.trainCycle}
                    onChange={(val) => setBasicInfoList({ trainCycle: val })}
                    ref={ele => trainCycle = ele}
                  />
                </td>
              </tr>
              <tr>
                <td style={{ verticalAlign: 'baseline' }}>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.description')}
                    theme={theme}
                  />
                </td>
                <td>
                  <TextArea rows={5} cols={32} inputStyle={{ resize: 'both', width: '33.75rem' }}
                    value={basicInfoList.taskDescription}
                    id='taskDesc' placeholder={$t('intelligent.incident.task.manage.task.description.placeholder')}
                    validator={(val, id) => validate(['cmpValidChar'], val, id)}
                    ref={textField => taskDescTextArea = textField} hintType='tip' maxLength={650}
                    onChange={(val) => setBasicInfoList({ taskDescription: val })}
                  />
                </td>
              </tr>              
            </tbody>
          </table>
        </div>
        <div className={styles.detailBottomButtonRow}>
          <Button
            text={$t('alarm.edit.cancel')}
            style={{ float: 'left', marginRight: '1.25rem' }}
            onClick={switchModifyTask}
          />
          <Button
            text={$t('intelligent.incident.task.manage.task.detail.confirm')}
            status='primary'
            onClick={confirmModifyTask}
          />
        </div>      
      </>
      ) : (
      <Row className={styles.detailContentContainer}> 
        <Col cols={20}>
          <table>
            <tbody>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.id')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.taskId}
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.table.task.name')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.taskName}
                </td>
              </tr>
              <tr>
                <td style={{ verticalAlign: 'baseline' }}>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.task.analysis.result.visible')}
                    theme={theme}
                  />
                </td>
                <td>
                  {getResultVisibility(viewDetailInfo.displayResult ?? false)}
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t(
                      'intelligent.incident.task.manage.task.train.period'
                    )}
                    theme={theme}
                  />
                </td>
                <td>
                  {getTrainPeriodShow(viewDetailInfo.trainCron)}
                </td>
              </tr>
              <tr> 
                <td> 
                  <LabelHint
                    label={$t(
                      'intelligent.incident.task.manage.task.description'
                    )}
                    theme={theme}
                  />
                </td>
                <td className={styles.overflowWrap}> 
                  {viewDetailInfo.taskDetail}
                </td>
              </tr>
            </tbody>
          </table>
        </Col>
        <Col cols={2}>
          {isCanModifyPermission() ?
            <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' 
              onClick={switchModifyTask}
            /> :    
            <Tooltip content={$t('intelligent.incident.task.manage.task.detail.no.support.modify')}>
                <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' disabled />
            </Tooltip>}
        </Col>
      </Row> )
  );
};

export default BasicInfoDetail;
