/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import eventBus from '@pages/businesstopo/a3dPages/bus';

const addZero = function(value) {
  return value < 10 ? `0${value}` : value;
};

const getTime = timestamp => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = addZero(date.getMonth() + 1);
  const day = addZero(date.getDate());
  const hours = addZero(date.getHours());
  const minutes = addZero(date.getMinutes());
  const seconds = addZero(date.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const eventBusHandler = {
  on: (eventName, fn) => {
    eventBus.addListener(eventName, fn);
  },
  off: (eventName, fn) => {
    eventBus.removeListener(eventName, fn);
  },
  emit: (eventName, ...rest) => {
    eventBus.emit(eventName, ...rest);
  },
};

export { eventBusHandler, getTime };

