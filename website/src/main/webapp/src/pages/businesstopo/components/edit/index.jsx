/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React, { useState, useEffect, useReducer, useRef } from 'react';
import { ConfigProvider } from 'eview-ui';
import MessageDialog from 'eview-ui/MessageDialog';
import DivMessage from 'eview-ui/DivMessage';
import '@pages/businesstopo/css/edit/index.css';
import DroppableArea from './DroppableArea';
import MiddleArea from './MiddleArea';
import Toolbar from './Toolbar';
import EditRightPannel from './EditRightPannel';
import smpp from '../../../../apps/businesstopo/assets/edit/smpp.png';
import manager from '../../../../apps/businesstopo/assets/edit/manager.png';
import service_4g from '../../../../apps/businesstopo/assets/edit/service_4g.png';
import service_5g from '../../../../apps/businesstopo/assets/edit/service_5g.png';
import {BUSINESS_TOPO_CONTEXT as TopoContext, IMAGE_NAME} from '@pages/businesstopo/const';
import {$t, registerResource, validate} from '@util';
import i18n from '../../locales/edit';
import CustomSelectTree from '@pages/businesstopo/components/edit/customSelectTree';
import business from '@pages/businesstopo/components/tooltip/business';
import {initState, reducer} from '@pages/businesstopo/reducer';
import {editbusiness, getIniteditpage} from '@pages/businesstopo/components/edit/api';
import {getSolutionData} from '@pages/businesstopo/api';
import DataCheck from '@util/DataCheck';
import TextField from 'eview-ui/TextField';
import {isZh, setHelpId} from '@util/index';

registerResource(i18n, 'tooltip');

function getBusinessName(type, area) {
  if (type === 'businessType') {
    if (area === 'north') {
      return 'south';
    } else {
      return 'north';
    }
  } else {
    return area;
  }
}

const EditComponent = () => {
  const [state, dispatch] = useReducer(reducer, initState);
  const [icons] = useState([smpp, manager, service_4g, service_5g]);
  const [highlightedArea, setHighlightedArea] = useState(null);
  const [droppedIcon, setDroppedIcon] = useState(null);
  const [areaIcons, setAreaIcons] = useState({north: [], south: []});
  const [allGroupData, setAllGroupData] = useState([]);
  const [selectedIcon, setSelectedIcon] = useState(null);
  const [pannelContent, setPannelContent] = useState(null);
  const [indictorError, setIndictorError] = useState(false);
  const [validateError, setValidateError] = useState({
    show: false,
    msg: '',
    type: 'error',
  });
  const solutionId = new useRef(-1);
  setHelpId('com.huawei.dvtopo.edit');
  const [validateSuccess, setValidateSuccess] = useState({
    show: false,
    msg: '',
  });

  // 从后台查询初始的Icons数据
  //  convert
  // flag
  // 0 初始化未修改
  // 1新增
  // 2修改
  const iconName = {
    0: 'smpp',
    1: 'common',
    2: '4G',
    3: '5G',
  };
  let style = {marginBottom: '20px', position: 'absolute', 'z-index': '99999', left: '32%', top: '38%', width: '500px'};
  let businessStyle = {
    marginBottom: '20px',
    position: 'absolute',
    'z-index': '99999',
    left: '16px',
    top: '2%',
    width: '18%',
  };

  let style2 = {
    marginBottom: '20px',
    position: 'absolute',
    'z-index': '99999',
    right: '1%',
    top: '8%',
    width: '316px',
  };

  useEffect(async() => {
    let param = { timestamp: 0 };
    let selectSolutionIndex = 0;
    let solutionData = await getSolutionData(param, res => res);
    if (!solutionData && solutionData.data.length === 0) {
      return;
    }
    solutionId.current = solutionData.data[selectSolutionIndex].solutionId;
    dispatch({
      solutionName: solutionData.data[selectSolutionIndex].solutionName,
    });
    getIniteditpage(
      {
        solutionId: solutionId.current,
      },
      ({ data, resultCode }) => {
        const result = {
          north: [],
          south: [],
        };
        data.businessList.sort((a, b) => a.sortOrder - b.sortOrder);
        data.businessList.forEach(item => {
          const updatedData = item.indicatorList.map(data1 => ({
            ...data1,
            key: `${data1.moType}${data1.measUnitKey}${data1.indicatorId}${data1.originalValue}`,
          }));
          const newItem = {
            ...item,
            flag: 0,
            rightPannelData: updatedData || [],
            icon: item.iconType ? icons[item.iconType - 1] : icons[1],
            iconType: item.iconType,
            toggled: !item.isDisplay,
            checkError: false,
          };
          if (item.businessType === 'north') {
            result.north.push(newItem);
          } else if (item.businessType === 'south') {
            result.south.push(newItem);
          }
        });
        setAreaIcons(result);

        setAllGroupData(data.siteList);
      }
    );
  }, []);

  const closeError = () => {
    setIndictorError(false);
  };
  const moveItem = (fromArea, toArea, index) => {
    // 检查索引是否在有效范围内
    if (index < 0 || index >= areaIcons[fromArea].length) {
      return;
    }
    // 获取要移动的项
    const itemToMove = areaIcons[fromArea][index];

    let newData1 = areaIcons[fromArea].filter((item, idx) => idx !== index);

    areaIcons[toArea].push(itemToMove);
    setAreaIcons({
      [fromArea]: newData1,
      [toArea]: areaIcons[toArea],
    });
    setSelectedIcon({id: toArea, iconId: areaIcons[toArea].length - 1});
  };
  const selectedHandel = ({id, iconId}) => {
    setIndictorError(
      false,
    );
    if (id === null) {
      setSelectedIcon(null);
    } else {
      setSelectedIcon({id, iconId});
      let area = areaIcons[id];
      let data = area[iconId];
      if (!data) {
        dispatch({
          editRightPannelOpen: false,
        });
        return;
      }
      clearBusinessErrorStatus(id, iconId);
      setPannelContent({
        businessName: data.businessName,
        business: id,
        iconType: data.iconType - 1,
        rightPannelData: data.rightPannelData,
        primaryIndicator: data.primaryIndicator || '',
        toggled: data.toggled,
        isMain: data.isMain,
        canDelete: data.canDelete,
      });
    }
  };

  const deleteCardHandele = (index, primaryIndicator) => {
    let area = areaIcons[selectedIcon.id];
    let data = area[selectedIcon.iconId];
    let updatedData = data.rightPannelData.filter(item => item.key !== primaryIndicator);
    data.rightPannelData = updatedData;
    if (data.primaryIndicator === primaryIndicator) {
      data.primaryIndicator = '';
    }
    if (data.flag === 0) {
      data.flag = 2;
    }
    setAreaIcons(prevState => ({
      ...prevState,
      [selectedIcon.id]: area,
    }));
    setPannelContent({
      ...pannelContent,
      rightPannelData: data.rightPannelData,
      primaryIndicator: data.primaryIndicator || '',
    });
  };

  const editPannel = (area, type, value) => {
    let data = areaIcons[area];
    let updateData = data[selectedIcon.iconId];
    clearBusinessErrorStatus(area, selectedIcon.iconId);
    const isMainConditionMet = type === 'isMain' && value === 0;
    const isToggledConditionMet = updateData.isMain && type === 'toggled' && value === 1;
    if (isMainConditionMet || isToggledConditionMet) {
      setValidateError({
        show: true,
        msg: $t('main.business.error'),
        type: 'warn',
      });
      return;
    }
    if (type === 'businessType' && updateData.isMain) {
      setPannelContent({
        ...pannelContent,
        business: area,
      });
      setValidateError({
        show: true,
        msg: $t('main.business.error'),
        type: 'warn',
      });
      return;
    }

    if (updateData.flag === 0) {
      updateData.flag = 2;
    }
    if (type === 'businessName') {
      updateData.businessName = value;
    }

    if (type === 'businessIcon') {
      updateData.iconType = parseInt(value) + 1;
      updateData.icon = icons[parseInt(value)];
    }

    if (type === 'primaryIndicator') {
      updateData.rightPannelData = updateData.rightPannelData.map(item => ({
        ...item,
        indicatorDisplayType: 0,
      }));
      updateData.rightPannelData[value].indicatorDisplayType = 1;
    }
    if (type === 'toggled') {
      updateData.toggled = value === 1 ? true : false;
    }
    if (type === 'isMain') {
      data = data.map(item => {
        if (item.isMain) {
          return {...item, isMain: false, flag: 2}; // 如果 isMain 为 true，则改为 false
        }
        return item; // 保持不变
      });
      updateData.isMain = value === 1 ? true : false;
      updateData.toggled = false;
    }

    if (type === 'businessType') {
      let toArea = area === 'north' ? 'south' : 'north';
      moveItem(area, toArea, selectedIcon.iconId);
    } else {
      setAreaIcons({...areaIcons, [area]: data});
    }
    let businessName = getBusinessName(type, area);
    setPannelContent({
      toggled: updateData.toggled,
      businessName: updateData.businessName,
      business: businessName,
      iconType: updateData.iconType - 1,
      rightPannelData: updateData.rightPannelData,
      isMain: updateData.isMain,
    });
  };

  // 处理删除操作
  const handleDelete = (id, index) => {
    let area = areaIcons[id];
    area.splice(index, 1);
    setAreaIcons(prevState => ({
      ...prevState,
      [id]: area,
    }),
    );
    dispatch({
      editRightPannelOpen: false,
    });
  };

  const handleDragStart = (e, icon, index) => {
    e.dataTransfer.setData('icon', icon);
    e.dataTransfer.setData('iconIndex', index);
  };

  const handleDrop = (e, id) => {
    if (areaIcons[id].length >= 10) {
      setValidateError({
        show: true,
        msg: $t('over.max.length'),
        type: 'warn',
      });
      setDroppedIcon(null);
      setHighlightedArea(null);
      return;
    }
    setSelectedIcon(null);
    const icon = e.dataTransfer.getData('icon');
    const iconIndex = e.dataTransfer.getData('iconIndex');
    setDroppedIcon({icon, iconIndex});

    dispatch({
      editRightPannelOpen: true,
      editRightPannelShow: true,
      editRightPanelSiteName: false,
    });
    setPannelContent({
      business: id,
      iconType: iconIndex,
    });
    setAreaIcons((prev) => ({
      ...prev,
      [id]: [...prev[id], {
        icon, iconType: parseInt(iconIndex) + 1, flag: 1, businessName: '',
        rightPannelData: [],
        canDelete: true,
      }],
    }));
    let length = areaIcons[id].length;
    setSelectedIcon({
      id, iconId: length,
    });
    setDroppedIcon(null);
    setHighlightedArea(null);
  };

  const handleDragOver = (e, id) => {
    e.preventDefault();
    setHighlightedArea(id);
  };

  const handleDragLeave = () => {
    setHighlightedArea(null);
  };

  const setBusinessErrorStatus = (area, index) => {
    let data = areaIcons[area];
    let updateData = data[index];
    updateData.checkError = true;
    setAreaIcons({
      ...areaIcons,
      [area]: data,
    },
    );
  };

  const clearBusinessErrorStatus = (area, index) => {
    let data = areaIcons[area];
    let updateData = data[index];
    if (updateData) {
      updateData.checkError = false;
      setAreaIcons({
        ...areaIcons,
        [area]: data,
      },
      );
    }
  };

  function validateAreaIcons(nameSet) {
    const errorMsg = [];
    let mainBusinessCount = 0;
    if (state.solutionName.length === 0 || state.solutionName.length > 20 || !DataCheck.resourceValidChar(state.solutionName)) {
      errorMsg.push({
        area: 'all',
        msg: $t('business.name.msg'),
      });
    }

    // 校验单个 item 的函数
    function validateItem(index, item, region) {
      // Check for main business count
      if (item.isMain) {
        if (mainBusinessCount > 0) {
          errorMsg.push({
            area: region,
            businessName: item.businessName,
            icon: iconName[item.iconType],
          });
          setBusinessErrorStatus(region, index);
        }
        mainBusinessCount++;
      }
      if (!item.businessName || item.businessName.length === 0 || !DataCheck.resourceValidChar(item.businessName)) {
        errorMsg.push({
          area: region,
          businessName: '',
          icon: iconName[item.iconType],
        });
        setBusinessErrorStatus(region, index);
        return;
      }
      const isBusinessNameInvalid = item.businessName.length > 128 || nameSet.has(item.businessName);
      const isRightPannelDataInvalid = item.rightPannelData.length === 0 || item.rightPannelData.length > 5;
      if (isBusinessNameInvalid || isRightPannelDataInvalid) {
        errorMsg.push({
          area: region,
          businessName: item.businessName,
          icon: iconName[item.iconType],
        });
        setBusinessErrorStatus(region, index);
      } else {
        nameSet.add(item.businessName);
      }
      if (item.rightPannelData.length > 0) {
        let primaryFlag = false;
        item.rightPannelData.forEach(item2 => {
          if (item2.indicatorDisplayType === 1) {
            primaryFlag = true;
          }
        });
        if (!primaryFlag) {
          errorMsg.push({
            area: region,
            businessName: item.businessName,
            icon: iconName[item.iconType],
          });
          setBusinessErrorStatus(region, index);
        }
      }
    }

    let regionFlag = false;
    let mainFlag = false;
    // 遍历所有区域
    for (const [region, items] of Object.entries(areaIcons)) {
      if (items.length === 0 && !regionFlag) {
        regionFlag = true;
        errorMsg.push({
          area: 'all',
          msg: $t('business.service.msg'),
        });
        continue;
      }
      mainBusinessCount = 0;
      items.forEach((item, index) => validateItem(index, item, region));
      if (mainBusinessCount === 0 && !mainFlag) {
        mainFlag = true;
        errorMsg.push({area: 'all', msg: $t('business.main.msg')});
      }
    }
    return errorMsg;
  }

  function processBusinessData(items, area) {
    return items.map((item, index) => {
      const newData = {
        businessName: item.businessName,
        businessType: area, // 添加业务类型（区域）
        isMain: item.isMain || false,
        businessId: item.businessId,
        isDisplay: !item.toggled,
        editType: item.flag !== 0 ? 1 : 0,
        iconType: item.iconType,
        sortOrder: index,
      };

      // 如果 rightPannelData 存在，则处理指标列表
      if (item.rightPannelData && item.rightPannelData.length > 0) {
        newData.indicatorList = item.rightPannelData.map(data => ({
          moType: data.moType,
          measUnitKey: data.measUnitKey,
          measTypeKey: data.measTypeKey,
          indicatorDisplayType: data.indicatorDisplayType || 0,
          aggrType: data.aggrType || null, // 包含 aggrType 如果存在
          originalValue: data.originalValue,
        }));
      }
      return newData;
    });
  }

  function processAllAreas() {
    const businessList = [];
    for (const [area, items] of Object.entries(areaIcons)) {
      const processedData = processBusinessData(items, area);
      businessList.push(...processedData);
    }
    return {businessList};
  }

  const handleConfirm = () => {
    setValidateError({
      show: false,
      msg: '',
      type: 'error',
    });
    setValidateSuccess({
      show: false,
      msg: '',
    });
    // 用一个对象来存储每个名称出现的次数
    const nameSet = new Set([]);
    const errors = validateAreaIcons(nameSet);
    if (errors.length > 0) {
      let msg = $t('business.check.failed');
      setValidateError({
        show: true,
        msg,
        type: 'error',
      });
      return;
    }
    let params = {};
    params.solutionId = solutionId.current;
    params.solutionName = state.solutionName;
    params.businessList = processAllAreas().businessList;
    params.siteList = allGroupData;

    editbusiness(params, (data) => {
      if (data && data.resultCode === 0) {
        setValidateSuccess({
          show: true,
          msg: $t('save.success'),
        });
      } else {
        setValidateError(({
          show: true,
          msg: data.resultMessage,
          type: 'error',
        }));
      }
    });
  };

  const handleCancel = () => {
    location.replace('/eviewwebsite/index.html#path=/businesstopo');
  };

  const handleClick = (event) => {
    let target = event.target;
    while (target) {
      if (target.classList.contains('divMessage')) {
        return; // 如果找到，退出函数
      }
      target = target.parentElement; // 移动到父元素
    }
    if (state.editAddPannelShow) {
      return;
    }
    if (highlightedArea !== null) {
      return;
    }
    if (event.target.id === 'confirmToolbar') {
      return;
    }

    // 点击右侧面板相关元素，则忽略此次点击
    let isIgnore = false;
    const ignoreDivIdArr = ['editRightPannel', 'editAddPannel', 'custom-select-tree', 'left-arrow-div',
      'middle-area-div', 'right-arrow-div'];
    for (let i = 0; i < ignoreDivIdArr.length; i++) {
      let tempDiv = document.getElementById(ignoreDivIdArr[i]);
      if (tempDiv && tempDiv.contains(event.target)) {
        isIgnore = true;
        break;
      }
    }
    if (isIgnore) {
      return;
    }

    if (!event.target.classList.contains('business-select')) {
      setSelectedIcon(null);
      dispatch({
        editRightPannelOpen: false,
      });
    }
  };
  let labelStyle = {display: 'block', paddingLeft: '0px'};
  return (
    <TopoContext.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening" locale={isZh ? 'zh' : 'en'}>
        <div id="dvtopoedit">
          <div className="topocontainer" onClick={handleClick}>
            <div className="child1">
              <div>
                <TextField
                  labelStyle={labelStyle}
                  required={true}
                  hideRequiredMark={true}
                  hintType="tip"
                  validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 20)}
                  autoComplete="off"
                  inputStyle={businessStyle}
                  value={state.solutionName}
                  onChange={(value) => {
                    dispatch({
                      solutionName: value,
                    });
                  }}
                />
              </div>
              <div className="left_separator" />
              <div className="text-area" style={{marginBottom: '24px', marginTop: '15px'}}>{$t('business')}</div>
              <div className="icon-area-container">
                <div className="icon-area">
                  {icons.map((icon, index) => (
                    <div
                      key={index}
                      className="icon"
                      draggable
                      onDragStart={(e) => handleDragStart(e, icon, index)}
                    >
                      <img key={index} src={icon} draggable="true" />
                      <div className="icon_name"> {IMAGE_NAME[index]}</div>
                    </div>
                  ))}
                </div>

              </div>
            </div>
            <div className="child2">

              <DivMessage text={validateSuccess.msg} type="success" style={style2} display={validateSuccess.show}
                className="divMessage"
                disposeTimeOut={3000}
                onClose={() => {
                  setValidateSuccess({
                    show: false,
                    msg: '',
                  });
                }}
              />

              <Toolbar handleConfirm={handleConfirm} handleCancel={handleCancel} />
              <div className="child2-content"
                style={{transform: state.editRightPannelOpen && state.editRightPannelShow ? 'translateX(0px)' : 'translateX(100px)'}}
              >
                <DivMessage text={validateError.msg} type={validateError.type} style={style} display={validateError.show}
                  className="divMessage"
                  disposeTimeOut={3000}
                  onClose={() => {
                    setValidateError({
                      show: false,
                      msg: '',
                    });
                  }}
                />
                <div>
                  <DroppableArea
                    id="north"
                    highlightedArea={highlightedArea}
                    areaIcons={areaIcons}
                    handleDrop={handleDrop}
                    handleDragOver={handleDragOver}
                    handleDragLeave={handleDragLeave}
                    text={$t('northbound.service')}
                    selectedIcon={selectedIcon}
                    setSelectedIcon={selectedHandel}
                    handleDelete={handleDelete}
                  />
                </div>
                <div style={{position: 'relative'}}>
                  <MiddleArea data={allGroupData} setPanelContent={setPannelContent} />
                </div>
                <div>
                  <DroppableArea
                    id="south"
                    highlightedArea={highlightedArea}
                    areaIcons={areaIcons}
                    handleDrop={handleDrop}
                    handleDragOver={handleDragOver}
                    handleDragLeave={handleDragLeave}
                    text={$t('southbound.service')}
                    selectedIcon={selectedIcon}
                    setSelectedIcon={selectedHandel}
                    handleDelete={handleDelete}
                  />
                </div>
              </div>
              <EditRightPannel editPannel={editPannel} pannelContent={pannelContent}
                deleteCardHandele={deleteCardHandele} allGroupData={allGroupData} setAllGroupData={setAllGroupData}
                areaIcons={areaIcons}
              />
              <CustomSelectTree areaIcons={areaIcons} setAreaIcons={setAreaIcons} selectedIcon={selectedIcon}
                setIndictorError={setIndictorError}
                pannelContent={pannelContent} setPannelContent={setPannelContent}
              />
            </div>
          </div>
          <MessageDialog type='error' isOpen={indictorError} onClose={() => {
            closeError();
          }} modal={false}
          content={$t('cbs.add.error')} detail={$t('cbs.add.error.content')}
          buttons={{
            ok: {
              onClick: () => {
                closeError();
              },
            },
          }}
          />

        </div>
      </ConfigProvider>
    </TopoContext.Provider>
  );
};

export default EditComponent;
