/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useRef, useState} from 'react';
import * as echarts from 'echarts';
import {dateFormatToHour, jumpAlarmLogPage, jumpAnalysisPage, jumpEventLogPage, dateTimeFormat} from '@util/tools';
import {$t, registerResource} from '@util';
import {getTimeLineData, refreshUerAuthenticate} from '../../api/timeLine';
import {
  DATABASE_TYPE_HOME,
  DETAIL_TYPE,
  MO_TYPE_HOME,
  HMS_TYPE_HOME,
  OVER_VIEW,
  SCATTER_COLOR,
  SITE_HOME, STRIP_VIEW,
  TIME_LINE_COLOR, HOST_TYPE_HOME,
} from '../../const/timeLine';
import i18n from '../../locales/timeLine';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import '../../css/timeLine.less';
import DetailsDialog from './DetailsDialog';
import {getUrlParam, setSessionData} from '../../util';
import {getSolutionData} from '../../api';
import {formatDate2SysSetting} from '@digitalview/fe-utils';

function getPosition(size, point) {
  let $timeLine = document.getElementById('adminConsoleContainer');
  let width = $timeLine.offsetWidth;

  let element = document.getElementById('dvtopo_btn_back_time');
  if (element) {
    width = width - 96;
  }
  let contentWidth = size.contentSize[0];
  let x = point[0] - contentWidth / 2;
  if (x <= 0) {
    x = 10;
  }
  if (point[0] + contentWidth / 2 + 20 > width) {
    x = width - contentWidth - 20;
  }
  return [x, point[1] - 100];
}

function formatter(param, scatterDataCache) {
  let time = param[0].axisValue;
  let alarmArr = scatterDataCache.current.filter(
    item => parseInt(item.occurTime) <= time && parseInt(item.occurTime) + 20 * 60 * 1000 > time,
  );
  if (alarmArr.length > 0) {
    let alarmObj = alarmArr[0];
    let endTime = parseInt(alarmObj.occurTime) + 20 * 60 * 1000;
    return `<div class="tooltipTimeLineDiv">
              <div class="tooltip_time_icon"></div>
              <span style="margin-left: 4px">${formatDate2SysSetting(parseInt(alarmObj.occurTime))} ~ ${formatDate2SysSetting(endTime)}</span>
              <div style="margin-top: 12px">
                <div class="tipCircle" style="background-color: ${SCATTER_COLOR[alarmObj.category]}"></div>
                <span style="color: #BBBBBB">
     ${alarmObj.category === DETAIL_TYPE.alarm || alarmObj.category === DETAIL_TYPE.analysis || alarmObj.category === DETAIL_TYPE.clear ? $t('timeline.details.tooltip.alarm') : $t('timeline.details.tooltip.event')}
                </span>
                <span style="margin-left: 8px">${alarmObj.alarmCount}</span>
              </div>
            </div>`;
  }
}

function setMarkLine(topoSession, chartObjRef, getMarkLineItem) {
  let timeStamp = topoSession.selectedTime;
  chartObjRef.current.setOption({
    series: [
      {
        id: 'line',
        markLine: getMarkLineItem(timeStamp, topoSession.isTimeTrack),
      },
      {
        id: 'markScatter',
        data: topoSession.isTimeTrack ?
          [
            {
              value: [timeStamp, 0.2],
              itemStyle: {
                color: '#F5F5F5',
              },
              symbolSize: 8,
              label: {
                show: false,
              },
            },
            {
              value: [timeStamp, 0.2],
              itemStyle: {
                color: 'rgba(245,245,245,0.2)',
              },
              symbolSize: 18,
              label: {
                show: false,
              },
            },
          ] :
          [],
      },
    ],
  });
}

function setParam(pageType, param, solutionId) {
  if (pageType === SITE_HOME) {
    param.instanceId = getUrlParam('siteId');
  }
  if (pageType === MO_TYPE_HOME || pageType === HMS_TYPE_HOME) {
    param.instanceId = getUrlParam('moTypeId');
  }
  if (pageType === HOST_TYPE_HOME) {
    param.instanceId = getUrlParam('vmId');
  }
  if (pageType === DATABASE_TYPE_HOME) {
    param.instanceId = solutionId;
  }
}

function TimeLine({renderTopology, refreshFlag, startRefresh, stopRefresh, backTimeTrack, pageType, resizeFlag}) {
  registerResource(i18n, 'timeline');
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {showKPIPanel, curretnSiteGroupName, solutionData} = state;
  let initFlag = useRef(0);

  const [detailsInitData, setDetailsInitData] = useState({
    isShow: false,
    eventTime: 0,
    offsetX: 0,
    idList: [],
  });
  const [isShowBackTime, setIsShowBackTime] = useState(false);
  const [currentTime, setCurrentTime] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);

  const chartObjRef = useRef(null);
  const paramRef = useRef(null);
  const scatterDataCache = useRef(null);

  useEffect(() => {
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('time_line_chart');
      chartObjRef.current = echarts.init(chartDom);
    }
    if (initFlag.current !== 0) {
      getInitData(state.isTimeTrack);
    }
  }, [refreshFlag, state.solutionId]);

  useEffect(() => {
    if (state.mmStripTimeLineFlag === 0) {
      return;
    }
    if (!chartObjRef.current) {
      let chartDom = document.getElementById('time_line_chart');
      chartObjRef.current = echarts.init(chartDom);
    }
    initFlag.current = 0;
    getInitData(state.isTimeTrack);
  }, [state.mmStripTimeLineFlag]);

  useEffect(() => {
    chartObjRef.current.resize();
  }, [showKPIPanel, resizeFlag]);

  useEffect(() => {
    if (Number.isNaN(selectedTime)) {
      return;
    }
    setIsShowBackTime(selectedTime !== currentTime);
    if (selectedTime !== currentTime) {
      renderTopology(selectedTime);
    }
  }, [selectedTime]);

  useEffect(() => {
    chartObjRef.current.resize();
  }, [isShowBackTime]);

  useEffect(() => {
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    dispatch({
      isTimeTrack:topoSession.isTimeTrack,
    });
    getInitData(topoSession.isTimeTrack);
  }, [curretnSiteGroupName, pageType, state.selectSolutionIndex]);

  const sineWave = (value, frequency, amplitude) => amplitude * Math.sin(2 * Math.PI * frequency * value) + 0.75;

  const createSineWave = (startTime, endTime) => {
    let temp = startTime;
    let interval = (endTime - startTime) / 1000;
    let chartValueArr = [];
    let sinValue = 0;
    while (temp <= endTime) {
      chartValueArr.push({
        value: [temp, sineWave(sinValue, 2, 0.12)],
      });
      sinValue += 0.001;
      temp += interval;
    }
    return chartValueArr;
  };

  const getInitData = async(isTimeTrack) => {
    let solutionparam = {timestamp: 0};
    let realTimeSolutionData = await getSolutionData(solutionparam, res => res);
    let solutionId = realTimeSolutionData.data[0].solutionId;
    let param = {};
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    if (topoSession.isTimeTrack) {
      setSelectedTime(topoSession.selectedTime);
    }
    param.instanceId = solutionId;

    if (getUrlParam('stripUnit')) {
      param.stripeUnit = getUrlParam('stripUnit');
    }
    if (pageType === STRIP_VIEW) {
      if (state.mmStripTimeLineFlag === 0) {
        return;
      }
      param.instanceId = getUrlParam('stripId');
    }
    setParam(pageType, param, solutionId);

    if (param.instanceId === '') {
      return;
    }
    if (initFlag.current > 0 && isTimeTrack) {
      return;
    }
    paramRef.current = param;
    getTimeLineData(
      param,
      resp => {
        if (!resp || resp.resultCode !== 0) {
          return;
        }
        if (!resp.data.startTime || !resp.data.endTime) {
          return;
        }

        if (topoSession.isTimeTrack && parseInt(resp.data.startTime) > topoSession.selectedTime) {
          setSessionData({
            isTimeTrack: false,
            selectedTime: 0,
          });
          location.reload();
        }

        resp.data.alarmTimeCountList.forEach(obj => {
          const notEventAlarmAnalysis = obj.category !== DETAIL_TYPE.event && obj.category !== DETAIL_TYPE.clear && obj.category !== DETAIL_TYPE.analysis;
          // 检查每个对象的 category 属性值是否不等于 3
          if (notEventAlarmAnalysis) {
            // 如果不等于 3，则将其设置为 1
            obj.category = DETAIL_TYPE.alarm;
          }
        });
        scatterDataCache.current = resp.data.alarmTimeCountList;
        initTimeLineChart(chartObjRef.current, resp.data);

        setCurrentTime(parseInt(resp.data.endTime));
        if (!topoSession.isTimeTrack) {
          setSelectedTime(parseInt(resp.data.endTime));
        }
        if (detailsInitData.isShow && detailsInitData.eventTime > 0) {
          // 选中的散点图加描边
          chartObjRef.current.setOption({
            series: [
              {
                id: 'scatter',
                data: getScatterData(scatterDataCache.current, detailsInitData.eventTime),
              },
            ],
          });
        }
        if (topoSession.isTimeTrack) {
          setMarkLine(topoSession, chartObjRef, getMarkLineItem);
        }
        initFlag.current = 1;
      },
    );
  };

  const initTimeLineChart = (chartObj, respData) => {
    chartObj.off('click');

    let options = getOptions(respData);
    chartObj.setOption(options);

    chartObj.on('click', 'series.scatter', onClickScatter);
    chartObj.getZr().on('click', event => {
      onChangeTime(event, chartObj);
    });
    window.addEventListener('resize', () => {
      chartObjRef.current.resize();
    });
  };

  let axisTick = {
    show: true,
    inside: true,
    length: 8,
  };
  let minorTick = {
    show: true,
    length: 5,
    splitNumber: 6,
  };
  let timeXAxis = {
    type: 'time',
    boundaryGap: false,
    minInterval: 2 * 60 * 60 * 1000,
    maxInterval: 3 * 60 * 60 * 1000,
  };

  function getTimeAxis(startTime, endTime) {
    return [
      {
        ...timeXAxis,
        min: startTime,
        max: endTime,
        axisLabel: {
          margin: 2,
          color: '#BBBBBB',
          showMinLabel: false,
          showMaxLabel: false,
          formatter: (value) => {
            if (value > startTime && value < endTime) {
              if (Math.abs(value - startTime) < 1 * 60 * 60 * 1000 || Math.abs(value - endTime) < 1 * 60 * 60 * 1000) {
                return '';
              }
              return dateFormatToHour(value);
            }
            return '';
          },
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick,
        minorTick,
        axisPointer: {
          type: 'line',
          animation: false,
          snap: true,
          label: {
            show: true,
            backgroundColor: '#393939',
            formatter: param => dateFormatToHour(param.value),
          },
          lineStyle: {
            type: 'solid',
            color: new echarts.graphic.LinearGradient(
              0,
              1,
              1,
              0,
              [
                {offset: 0, color: 'rgba(253,253,253,0.1)'},
                {offset: 1, color: '#FDFDFD'},
              ],
              false,
            ),
            width: 1.5,
          },
        },
      },
    ];
  }

  let yAxis = [
    {
      name: '',
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      max: 1,
    },
  ];

  let grid = {
    top: '0%',
    right: '0%',
    bottom: 0,
    left: '0%',
    containLabel: true,
  };

  let series = {
    id: 'line',
    type: 'line',
    smooth: true,
    symbol: 'none',
    lineStyle: {
      width: 0.5,
      color: TIME_LINE_COLOR,
    },
    itemStyle: {
      color: TIME_LINE_COLOR,
    },
    areaStyle: {
      origin: 'end',
      color: '#313131',
    },
    silent: true,
    showSymbol: false,
  };
  const getOptions = respData => {
    let startTime = parseInt(respData.startTime);
    let endTime = parseInt(respData.endTime);
    let lineData = createSineWave(startTime, endTime);
    let scatterData = getScatterData(respData.alarmTimeCountList);
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#393939',
        transitionDuration: 0,
        appendToBody: true,
        borderColor: '#393939',
        position: (point, param, dom, rect, size) => getPosition(size, point),
        formatter: param => formatter(param, scatterDataCache),
      },
      xAxis: getTimeAxis(startTime, endTime),
      yAxis,
      series: [
        {
          ...series,
          data: lineData,
          markLine: getMarkLineItem(endTime, false),
          zlevel: 0,
          emphasis: {
            disabled: true,
          },
        },
        {
          id: 'scatter',
          type: 'scatter',
          symbolSize: 16,
          data: scatterData,
          zlevel: 1,
        },
        {
          id: 'markScatter',
          type: 'scatter',
          data: [],
          zlevel: 0,
        },
      ],
    };
  };

  const getMarkLineItem = (time, isTimeTrack) => ({
    lineStyle: {
      type: 'solid',
      color: new echarts.graphic.LinearGradient(
        0,
        1,
        1,
        0,
        [
          {offset: 0, color: 'rgba(253,253,253,0.1)'},
          {offset: 1, color: '#FDFDFD'},
        ],
        false,
      ),
      width: 1.5,
      cap: 'butt',
      join: 'miter',
    },
    label: {
      show: false,
    },
    data: isTimeTrack ? [{xAxis: time, name: 'currentTime'}] : [{xAxis: time - 2 * 60000, name: 'currentTime'}],
    symbol: [],
    animation: true,
  });

  const rerenderMarLine = (time, isTimeTrack) => {
    let timeStamp = parseInt(time);
    if (!isTimeTrack) {
      timeStamp += 20 * 60000;
    }
    setSelectedTime(parseInt(time));
    chartObjRef.current.setOption({
      series: [
        {
          id: 'line',
          markLine: getMarkLineItem(timeStamp, isTimeTrack),
        },
        {
          id: 'markScatter',
          data: isTimeTrack ?
            [
              {
                value: [timeStamp, 0.2],
                itemStyle: {
                  color: '#F5F5F5',
                },
                symbolSize: 8,
                label: {
                  show: false,
                },
              },
              {
                value: [timeStamp, 0.2],
                itemStyle: {
                  color: 'rgba(245,245,245,0.2)',
                },
                symbolSize: 18,
                label: {
                  show: false,
                },
              },
            ] :
            [],
        },
      ],
    });
  };

  const getScatterData = (dataList, selectedEventTime) =>
    dataList.map(data => {
      let dataItem = {
        value: [parseInt(data.occurTime) + 10 * 60 * 1000, 0.5, data.alarmCount, data.occurTime, data.category],
        itemStyle: {
          color: SCATTER_COLOR[data.category],
        },
        emphasis: {
          disabled: false,
          itemStyle: {
            shadowColor: SCATTER_COLOR[data.category],
            shadowBlur: 10,
          },
        },
        label: {
          show: true,
          color: '#FFFFFF',
          offset: [0, 1],
          formatter: param => {
            let number = param.data.value[2];
            if (number > 9) {
              return '9+';
            }
            return number;
          },
        },
      };
      if (selectedEventTime === parseInt(data.occurTime) + 10 * 60 * 1000) {
        dataItem.itemStyle.borderColor = '#FFFFFF';
      }
      return dataItem;
    });

  // 散点图点击事件
  const onClickScatter = param => {
    closeDetailsDialog();
    let eventTime = param.data.value[0];

    // 选中的散点图加描边
    chartObjRef.current.setOption({
      series: [
        {
          id: 'scatter',
          data: getScatterData(scatterDataCache.current, eventTime),
        },
      ],
    });
    setDetailsInitData({
      isShow: true,
      eventTime,
      idList: param.data.value[3],
      offsetX: param.event.offsetX,
    });
  };

  // 时间光标点击事件
  const onChangeTime = (event, chartObj) => {
    if (event.target) {
      // 点击元素不处理
      return;
    }
    if (stopRefresh) {
      stopRefresh();
    }

    let pointInPixel = [event.offsetX, event.offsetY];
    let pointInGrid = chartObj.convertFromPixel('grid', pointInPixel);
    if (chartObj.containPixel('grid', pointInPixel)) {
      let newTime = Math.trunc(pointInGrid[0]);
      rerenderMarLine(newTime, true);
    }
  };

  // 退出回放模式
  const onClickBackTime = () => {
    backTimeTrack();
    rerenderMarLine(currentTime, false);
    if (startRefresh) {
      startRefresh();
    }
    dispatch({
      isTimeTrack: false,
      selectedTime: 0,
    });
    setSessionData({
      isTimeTrack: false,
      selectedTime: 0,
    });
  };

  // 事件/告警详情按钮点击
  const timeTrack = (type, time, csn, analysisParam, needJumpAnalysis, selectedAssociationTask, executionTime, indicatorId) => {
    if (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) {
      if (needJumpAnalysis) {
        let win = window.open(`/eviewwebsite/index.html#path=/aiOpsService&subMenu=associationAnalysisPage?refr-flags=mH&theme=dark&indicatorId=${
          encodeURIComponent(indicatorId)
        }&selectedAssociationTask=${
          encodeURIComponent(selectedAssociationTask)
        }&executionTime=${
          encodeURIComponent(executionTime)
        }`);
        if (win) {
          win.opener = null;
        }
      } else {
        jumpAlarmLogPage(csn, time);
      }
    } else {
      jumpEventLogPage(csn, time);
    }
  };

  // 关闭详情弹窗
  const closeDetailsDialog = () => {
    // 选中的散点图加描边
    chartObjRef.current.setOption({
      series: [
        {
          id: 'scatter',
          data: getScatterData(scatterDataCache.current),
        },
      ],
    });
    setDetailsInitData({
      isShow: false,
      eventTime: 0,
      offsetX: 0,
      idList: [],
    });
  };

  return (
    <div style={{backgroundColor: '#FFF', height: '5rem'}}>
      <div
        id="timeLine"
        className="time_line"
        style={showKPIPanel && [OVER_VIEW, STRIP_VIEW, HMS_TYPE_HOME].includes(pageType) ? {width: 'calc(100% - 400px)'} : {width: '100%'}}
      >
        {selectedTime !== currentTime && (
          <div className="btn_back_time" onClick={onClickBackTime} id="dvtopo_btn_back_time">
            <div className="exit_track_icon" />
            <div style={{color: '#ffffff', fontSize: '14px', position: 'relative', top: '35%'}}>
              {$t('timeline.common.button.back')}
            </div>
          </div>
        )}
        <div
          className="time_line_chart"
          style={selectedTime !== currentTime ? {width: 'calc(100% - 96px)'} : {width: '100%'}}
        >
          <div id="time_line_chart" style={{height: '100%', overflowX: 'hidden'}} />
        </div>
      </div>
      {detailsInitData.isShow && (
        <DetailsDialog
          idList={detailsInitData.idList}
          occurTime={detailsInitData.eventTime}
          offsetX={detailsInitData.offsetX}
          timeTrack={timeTrack}
          closeDialog={closeDetailsDialog}
          paramRef={paramRef}
        />
      )}
    </div>
  );
}

export default TimeLine;
