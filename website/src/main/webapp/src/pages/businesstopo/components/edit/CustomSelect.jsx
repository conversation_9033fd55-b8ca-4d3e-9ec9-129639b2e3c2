import React, { useState } from 'react';
import '@pages/businesstopo/css/edit/CustomeSelect.css'; // 引入CSS文件
const CustomSelect = ({ options, value, onChange, style}) => {
  return (
    <div>
      <select value={value} onChange={(e) => onChange(e.target.value)} className="select-box" style={{...style}}>
        <option value="" disabled>Please Select</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default CustomSelect;