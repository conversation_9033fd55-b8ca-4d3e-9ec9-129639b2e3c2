import React, {useState, useEffect, useMemo, useRef} from 'react';
import styles from './index.less';
import i18n from './locale';
import {$t, registerResource} from '@util';
import SearchInput from 'eview-ui/SearchInput';
import ConfigProvider from 'eview-ui/ConfigProvider';

registerResource(i18n, 'common.legend');

/**
 *
 * @param props
 */
const Legend = (props) => {
  const {
    data = [],
    style = {},
    iconStyle = {},
    color = [],
    onClick,
    onMouseEnter,
    onMouseLeave,
  } = props;

  const colNumber = useMemo(() => {
    // 最大一行4列
    if (data.length <= 4) {
      return data.length;
    }
    return 4;
  }, [data.length]);

  const colWidth = useMemo(() => {
    switch (colNumber) {
      case 0:
      case 1:
        return 100;
      case 2:
        return 50;
      case 3:
        return 33.33;
      case 4:
        return 25;
      default:
        return Math.floor(100 / colNumber);
    }
  }, [colNumber]);

  const textWidth = useMemo(() => {
    const width = 400 - 46 * 2 - (colNumber - 1) * 20;
    return width / colNumber - 18;
  }, [colNumber]);
  return (
    <div className={styles.topoLegendDiv} style={{...style}}>
      {
        data.map((v, index) => {
          return (
            <div
              className={styles.topoLegendItem}
              key={v.id}
              style={{
                flexGrow: 0,
                flexShrink: 0,
                flexBasis: `calc(${colWidth}% - 20px)`,
                justifyContent: colNumber < 4 ? 'center' : 'left',
              }}
              onClick={() => {
                onClick?.(v.id);
              }}
              onMouseEnter={() => {
                onMouseEnter?.(v.id);
              }}
              onMouseLeave={() => {
                onMouseLeave?.(v.id);
              }}
            >
              <div
                className={styles.topoLegendIcon}
                style={{
                  backgroundColor: v.selected ? color[index % color.length] : '#ccc',
                  ...iconStyle,
                }}
              />
              <div
                className={styles.topoLegendItemText}
                style={{
                  maxWidth: textWidth,
                }}
                title={v.name}
              >
                {v.name}
              </div>
            </div>
          );
        })
      }
    </div>
  );
};

function hasParentWithClass(node, className) {
  let currentNode = node;
  while (currentNode !== null && currentNode !== document && currentNode !== window) {
    // 检查当前节点是否有指定的class
    if (currentNode.classList.contains(className)) {
      return true;
    }
    // 移动到父节点
    currentNode = currentNode.parentNode;
  }
  return false;
}

const getTextWidth = (text, font = 'normal 12px HarmonyOSHans') => {
  const canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement('canvas'));
  const context = canvas.getContext('2d');
  context.font = font;
  const metrics = context.measureText(text);
  return metrics.width;
};

const fillDom = (lastHtmlList, data, [maxWidth, color, iconStyle, varStyle, onClick, onMouseEnter, onMouseLeave, style, moreClick]) => {
  const dataLen = data.length;
  if (dataLen === 0) {
    return lastHtmlList;
  }

  let textWidth = getTextWidth(data[0].name || '');
  let iconTextWidth = textWidth + 18; // 18是前面圆的大小
  // 如果后面还有 需要将间隔算进去
  let width = iconTextWidth + (dataLen > 1 ? 20 : 0);

  let haveMore = true;
  let lastHaveEll = false;
  let tempMaxWidth = maxWidth - width;
  if (tempMaxWidth < 40) {
    if (maxWidth >= 100) {
      lastHaveEll = true; // 最后一个要展示省略号
      tempMaxWidth = 40;
    } else {
      haveMore = false;
    }
  }
  if (haveMore) {
    lastHtmlList.push(
      <div
        className='topoMoreLegendDivContainer'
        title={data[0].name}
        onClick={() => {
          onClick?.(data[0].id);
        }}
        onMouseEnter={() => {
          onMouseEnter?.(data[0].id);
        }}
        onMouseLeave={() => {
          onMouseLeave?.(data[0].id);
        }}
      >
        <div
          className={styles.topoMoreLegendIcon}
          style={{
            backgroundColor: data[0].selected ? color[lastHtmlList.length % color.length] : '#ccc',
            margin: '4px',
            ...iconStyle,
          }}
        />
        <div className='topoMoreLegendItemText' style={{width: lastHaveEll ? (maxWidth - (dataLen > 1 ? 40 : 0) - 38) : textWidth}}>{data[0].name}</div>
      </div>
    );
    varStyle.maxWidth = `calc((100% - ${(dataLen > 1 ? lastHtmlList.length : lastHtmlList.length - 1) * 20}px) / ${lastHtmlList.length})`;
  } else {
    varStyle.maxWidth = `calc((100% - 40px - ${(lastHtmlList.length) * 20}px) / ${lastHtmlList.length})`;
    lastHtmlList.push(
      <div
        className='topoMoreLegendItemMoreText'
        onClick={() => {
          moreClick?.();
        }}
      >{$t('mm.legend.more')}
      </div>
    );
  }

  return fillDom(
    lastHtmlList,
    haveMore ? data.slice(1) : [],
    [tempMaxWidth, color, iconStyle, varStyle, onClick, onMouseEnter, onMouseLeave, style, moreClick],
  );
};


const MoreLegend = (props) => {
  const {
    data = [],
    style = {},
    iconStyle = {},
    color = [],
    onClick,
    onMouseEnter,
    onMouseLeave,
    maxWidth = 304,
  } = props;
  const containerRef = useRef(null);

  const [dropdownVisibility, setDropdownVisibility] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [com, setCom] = useState([]);
  useEffect(() => {
    if (containerRef.current) {
      const varStyle = {};
      setCom(
        fillDom(
          [],
          data,
          [
            maxWidth,
            color,
            iconStyle,
            varStyle,
            onClick,
            onMouseEnter,
            onMouseLeave,
            style,
            () => {
              setDropdownVisibility(true);
            },
          ],
        ));
      containerRef.current.style.setProperty('--legendMaxWidth', varStyle.maxWidth);
    }
  }, [JSON.stringify(props)]);

  useEffect(() => {
    const func = (e) => {
      if (e.target) {
        if (hasParentWithClass(e.target, 'topoMoreLegendItemMoreText')) {
          // 点击更多按钮
          return;
        }

        if (!hasParentWithClass(e.target, 'topo-legend-dropdown-container')) {
          setDropdownVisibility(false);
          return;
        }
      }
    };

    document.body.addEventListener('click', func);
    return () => {
      document.body.removeEventListener('click', func);
    };
  }, []);

  return (
    <div className='topoMoreLegendDiv' ref={containerRef} style={style}>
      {com}
      <div className="topo-legend-dropdown-container" style={{visibility: dropdownVisibility ? 'visible' : 'hidden'}}>
        <ConfigProvider version="aui3-1" theme="evening">
          <SearchInput
            placeholder={$t('mm.legend.search')}
            onSearch={value => {
              setSearchValue(value);
            }}
            onChange={value => {
              setSearchValue(value);
            }}
          />
          <div className='topo-legend-dropdown'>
            {
              data.map((d, index) => {
                return (
                  <div
                    style={{
                      display: (d.name || '').toUpperCase().includes(searchValue.toUpperCase()) ? undefined : 'none',
                    }}
                    key={`${d.id}${index}`}
                    className='topo-legend-item-dropdown'
                    onClick={() => {
                      onClick?.(d.id);
                    }}
                    onMouseEnter={() => {
                      onMouseEnter?.(d.id);
                    }}
                    onMouseLeave={() => {
                      onMouseLeave?.(d.id);
                    }}
                  >
                    <div
                      className={styles.topoMoreLegendIcon}
                      style={{
                        backgroundColor: d.selected ? color[index % color.length] : '#ccc',
                        margin: '4px',
                        ...iconStyle,
                      }}
                    />
                    <div
                      className={styles.topoLegendItemText}
                      title={d.name}
                      style={{
                        maxWidth: '276px',
                      }}
                    >
                      {d.name}
                    </div>
                  </div>
                );
              })
            }

          </div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export {
  MoreLegend,
  Legend,
};
export default Legend;
