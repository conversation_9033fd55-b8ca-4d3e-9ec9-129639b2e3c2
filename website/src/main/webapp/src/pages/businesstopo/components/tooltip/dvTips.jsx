/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React, {useEffect, useRef} from 'react';
import '@pages/businesstopo/css/tooltip/pod.less';
import {$t, registerResource, isZh} from '@util';
import PropTypes from 'prop-types';

function DVTips(props) {
  return (
    <div id="dvtips">
      <div>
        <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%'}}>
          <div>{props.data.moTypeName}</div>
        </div>
      </div>
    </div>
  );
}

DVTips.propTypes = {
  data: PropTypes.shape({
    moTypeName: PropTypes.string,
  }).isRequired,
};
export default DVTips;
