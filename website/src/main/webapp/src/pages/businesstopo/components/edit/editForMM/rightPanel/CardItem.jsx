import React, {useContext} from 'react';
import Tooltip from 'eview-ui/Tooltip';
import '@pages/businesstopo/css/edit/Card.css';
import { $t } from '@util';

const CardItem = ({ titleName, moName, measUnitName, indexName, displayValue, editClick, deleteClick}) => {
  return (
    <div className="cardItem">
      <div className="cardItemHeader">
        <div>
          <span className="title-name">{titleName}</span>
        </div>
        <div>
          <span
            className="edit"
            onClick={() => {
              editClick?.();
            }}
          />
          <span
            className="delete"
            onClick={() => {
              deleteClick?.();
            }}
          />
        </div>
      </div>
      <div className="cardItemContent">
        <div className="cardItemContentRow">
          <Tooltip placement="top" content={$t('mm.edit.motype')} trigger={['hover', 'focus']}>
            <span className="row-name">{$t('mm.edit.motype')}</span>
          </Tooltip>
          <Tooltip placement="top" content={moName} trigger={['hover', 'focus']}>
            <span className="row-value">{moName}</span>
          </Tooltip>
        </div>

        <div className="cardItemContentRow">
          <Tooltip placement="top" content={$t('mm.edit.measUnitKey')} trigger={['hover', 'focus']}>
            <span className="row-name">{$t('mm.edit.measUnitKey')}</span>
          </Tooltip>
          <Tooltip placement="top" content={measUnitName} trigger={['hover', 'focus']}>
            <span className="row-value">{measUnitName}</span>
          </Tooltip>
        </div>

        <div className="cardItemContentRow">
          <Tooltip placement="top" content={$t('mm.edit.measUnit.indicator')} trigger={['hover', 'focus']}>
            <span className="row-name">{$t('mm.edit.measUnit.indicator')}</span>
          </Tooltip>
          <Tooltip placement="top" content={indexName} trigger={['hover', 'focus']}>
            <span className="row-value">{indexName}</span>
          </Tooltip>
        </div>

        <div className="cardItemContentRow">
          <Tooltip placement="top" content={$t('mm.edit.measUnit.originalValue')} trigger={['hover', 'focus']}>
            <span className="row-name">{$t('mm.edit.measUnit.originalValue')}</span>
          </Tooltip>
          <Tooltip placement="top" content={displayValue} trigger={['hover', 'focus']}>
            <span className="row-value">{displayValue}</span>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default CardItem;
