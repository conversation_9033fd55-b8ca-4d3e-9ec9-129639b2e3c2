// 算法参数整数校验
export const validateByInt = (valueRange) => {
  let rulePositive = getFloatOrIntValidKey(valueRange, false);
  let validParam = valueRange && valueRange.value ? {
    rules: ['required', rulePositive, 'range'],
    min: valueRange.value.min,
    max: valueRange.value.max,
  } : {
    rules: ['required', 'cmpPositiveInteger'],
  };
  return validParam;
};

// 算法参数浮点型校验
export const validFloat = (valueRange, isRequired) => {
  let ruleDecimals = getFloatOrIntValidKey(valueRange, true);
  let validParam = valueRange && valueRange.value ? {
    rules: [ruleDecimals, 'floatRange'],
    min: String(valueRange.value.min).indexOf('.') !== -1 ? valueRange.value.min : `${valueRange.value.min}.0`,
    max: String(valueRange.value.max).indexOf('.') !== -1 ? valueRange.value.max : `${valueRange.value.max}.0`,
  } : {
    rules: ['cmpDecimals'],
  };

  if (isRequired) {
    validParam.rules = ['required', ...validParam.rules];
  }
  return validParam;
};

// 获取浮点数/整数校验规则key
const getFloatOrIntValidKey = (valueRange, isFloat) => {
  if (valueRange && valueRange.value && (parseFloat(valueRange.value.min) === 0)) {
    return isFloat ? 'cmpDecimals' : 'cmpPositiveInteger';
  } else if (valueRange && valueRange.value && parseFloat(valueRange.value.min) > 0) {
    return isFloat ? 'cmpNoZeroDecimals' : 'cmpNoZeroPositiveInteger';
  } else {
    return isFloat ? 'cmpAllDecimals' : 'cmpAllPositiveInteger';
  }
};