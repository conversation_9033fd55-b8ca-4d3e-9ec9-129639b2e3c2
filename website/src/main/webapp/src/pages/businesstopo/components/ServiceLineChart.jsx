/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useState, useRef} from 'react';
import {BUSINESS_TOPO_CONTEXT, getHistorySeries} from '../const';
import Tab, {TabItem} from 'eview-ui/Tab';
import {Button, CheckboxGroup} from 'eview-ui';
import * as echarts from 'echarts';
import edit from '../../../apps/businesstopo/assets/line_chart_edit.svg';
import {getHistoryData} from '../api';
import {$t} from '@util';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {dateFormatToHour} from '@util/tools';
import { handleJumpPageData } from '../util';
import {sortHistoryListData} from '../../../commonUtil/tools';

let incrementId = 0;

function ServiceLineChart(props) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  let {
    currentBusinessData,
    indicatorData,
    currentIndicator,
    siteHistoryData,
    currentIndicatorIndex,
    currentSiteId,
  } = state;
  const [indicatorSelectShow, setIndicatorSelectShow] = useState(false);
  const [indicatorShowValue, setIndicatorShowValue] = useState([]);
  const [selectValue, setSelectValue] = useState([]);
  if (!currentIndicator) {
    currentIndicator = {};
  }
  const getOptions = respData => ({
    tooltip: {
      trigger: 'axis',
      borderColor: '#393939',
      backgroundColor: '#393939',
      appendToBody: true,
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      textStyle: {
        color: '#f5f5f5',
      },
      enterable: true,
      position: (point, params, dom, rect, size) => {
        let chartDom = document.getElementById('lineChartDiv');
        const TOOLTIP_OFFSET = 4;
        // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
        const canvasRect = chartDom.getBoundingClientRect();
        const {contentSize: tooltipSize, viewSize: canvasSize} = size;
        let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
        let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
        if (canvasRect) {
          // 校正tooltip的 top 定位，防止超出可视窗口
          const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
          const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
          if (toolTipExceedViewport || toolTipExceedCanvas) {
            top = -canvasRect.top;
          }
        }
        return [left, top];
      },
      formatter: (params) => {
        // 获取时间（假设 x 轴为时间）
        const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
        // 遍历每条线的数据，保留默认的值展示
        const values = params
          .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
          .join('<br>');

        // 返回自定义的提示框内容
        return `<strong>${time}</strong><br>${values}`;
      },
    },
    grid: {
      left: '0px',
      right: '3%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    textStyle: {
      color: '#BBBBBB',
    },
    legend: {
      show: true,
      orient: 'horizontal',
      bottom: '0%',

      // itemGap:50,
      type: 'scroll',
      icon: 'circle',
      itemWidth: 15,
      itemHeight: 10,
      textStyle: {
        color: '#BBBBBB',
      },
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      pageTextStyle: {
        color: '#FFFFFF',
      },
    },
    yAxis: {
      type: 'value',
      boundaryGap: true,
      nameTextStyle: {
        color: '#BBBBBB',
      },
      splitLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        fontSize: 10,
        margin: 5,
        hideOverlap: true,
        align: 'right',
        overflow: 'break',
        width: 200,
      },
    },
    xAxis: [
      {
        type: 'time',
        boundaryGap: true,
        axisLine: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 3,
          },
        },

        axisTick: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 2,

          },
        },

        axisLabel: {
          color: '#BBBBBB',
          interval: 0,
          borderColor: 'transparent',
          borderWidth: 10,
          fontSize: 10,
          margin: 10,
          hideOverlap: true,
          showMinLabel: false,
          showMaxLabel: false,
          formatter: value => {
            const unit = (respData[0].endTime - respData[0].startTime) / 12;
            if (value > parseInt(respData[0].startTime) && value < parseInt(respData[0].endTime)) {
              if (Math.abs(value - respData[0].startTime) < unit || Math.abs(value - respData[0].endTime) < unit) {
                return '';
              }
              return dateFormatToHour(value);
            }
            return '';
          },
        },

        splitLine: {
          show: false,
        },
        min: parseInt(respData[0].startTime),
        max: parseInt(respData[0].endTime),
      },
    ],
    series: getHistorySeries(respData, currentIndicator.indicatorDisplayType === 1, false),
  });
  const chartObj = useRef(null);

  useEffect(() => {
    if (!siteHistoryData || siteHistoryData.length === 0) {
      return;
    }
    let chartDom = document.getElementById('lineChartDiv');
    chartObj.current = echarts.getInstanceByDom(chartDom);
    if (!chartObj.current) {
      chartObj.current = echarts.init(chartDom);
      window.addEventListener('resize', () => {
        chartObj.current.resize();
      });

      // 注册图点击事件
      chartObj.current.getZr().off('click');
      chartObj.current.getZr().on('click', param => {
        const pointInPixel = [param.offsetX, param.offsetY];
        if (!chartObj.current.containPixel('grid', pointInPixel)) {
          return;
        }

        handleJumpPageData(siteHistoryData);
      });
    }
    if (siteHistoryData.length > 0) {
      chartObj.current.setOption(getOptions(sortHistoryListData(siteHistoryData)));
    }

    return () => {
      chartObj.current.dispose();
    };
  }, [siteHistoryData]);

  useEffect(() => {
    // 默认展示前三个
    let showData = getDefaultShowIndicator(indicatorData);
    setIndicatorShowValue(showData);
    setSelectValue(getDefaultShowIndicatorIndex(showData));
  }, [indicatorData]);

  useEffect(() => {
    let historyDParam = {
      businessId: currentBusinessData.businessId,
    };
    if (!currentBusinessData.indicatorList) {
      return;
    }

    if (currentIndicator) {
      historyDParam.indicatorId = currentIndicator.indicatorId;
    } else {
      if (currentBusinessData.indicatorList.length === 0) {
        return;
      }
      historyDParam.indicatorId = currentBusinessData.indicatorList[0].indicatorId;
    }

    if (currentSiteId !== -1) {
      historyDParam.siteId = currentSiteId;
    }

    // 折线图数据
    getHistoryData(
      historyDParam,
      ({data, resultCode}) => {
        if (chartObj.current) {
          const oldOption = chartObj.current.getOption();
          if (data.siteHistoryList.length > 0) {
            data.siteHistoryList = sortHistoryListData(data.siteHistoryList);
            let options = getOptions(data.siteHistoryList);
            oldOption.series = options.series;
            oldOption.xAxis = options.xAxis;
            oldOption.yAxis = options.yAxis;
            chartObj.current.clear();
            chartObj.current.setOption(oldOption);
            // 注册图点击事件
            chartObj.current.getZr().off('click');
            chartObj.current.getZr().on('click', param => {
              const pointInPixel = [param.offsetX, param.offsetY];
              if (!chartObj.current.containPixel('grid', pointInPixel)) {
                return;
              }
              handleJumpPageData(data.siteHistoryList);
            });
          }
        }
      },
    );
  }, [props.refreshFlag]);

  useEffect(() => {
    if (!props.showCharts) {
      return;
    }
    let chartDom = document.getElementById('lineChartDiv');
    let chartObj2 = echarts.getInstanceByDom(chartDom);
    if (chartObj2) {
      chartObj2.resize();
    }
  }, [props.showCharts]);

  const handleTabChange = (index, name) => {
    dispatch({
      currentIndicatorIndex: index,
    });
    let param = {
      businessId: currentBusinessData.businessId,
      indicatorId: indicatorData[index].indicatorId,

    };
    let {selectSiteId, selectedTime} = JSON.parse(sessionStorage.getItem('topoSession'));
    if (selectSiteId && selectSiteId !== -1) {
      param.siteId = selectSiteId;
    }
    let tmpId = ++incrementId;
    if (selectedTime !== 0) {
      param.currentTime = selectedTime;
    }
    getHistoryData(param, ({data, resultCode}) => {
      if (tmpId === incrementId) {
        dispatch({
          currentIndicator: indicatorData[index],
          siteHistoryData: data.siteHistoryList,
          currentIndicatorIndex: index,
        });
      }
    });
    sessionStorage.setItem(
      'topoSession',
      JSON.stringify({
        ...JSON.parse(sessionStorage.getItem('topoSession') || '{}'),
        indicatorId: indicatorData[index].indicatorId,
      }),
    );
  };

  const getDefaultShowIndicator = list => list.slice(0, 3);
  const getDefaultShowIndicatorIndex = list =>
    list.map(item => item.indicatorId);

  const getSelectData = indicatorData =>
    indicatorData.map(item => ({
      value: item.indicatorId,
      text: item.indexName,
      tipText: item.indexName,
    }));
  const handleIndicatorChange = (newValue, oldValue, event) => {
    // 修改选择值
    setSelectValue(newValue);

    // 修改showtab集合
    setIndicatorShowValue(
      indicatorData.filter(item => newValue.indexOf(item.indicatorId) !== -1),
    );

    // 修改当前指标和index  currentIndicator, siteHistoryData, currentIndicatorIndex 当前指标是否需改变
    if (currentIndicator.indicatorId === newValue[0]) {
      dispatch({
        currentIndicatorIndex: 0,
        currentIndicator: indicatorData.find(
          item => item.indicatorId === newValue[0],
        ),
      });
    } else {
      let params = {
        businessId: currentBusinessData.businessId,
        indicatorId: indicatorData.find(item => item.indicatorId === newValue[0]).indicatorId,
      };

      let {selectSiteId} = JSON.parse(sessionStorage.getItem('topoSession'));
      if (selectSiteId && selectSiteId !== -1) {
        params.siteId = selectSiteId;
      }

      getHistoryData(params, ({data, resultCode}) => {
        dispatch({
          currentIndicatorIndex: 0,
          currentIndicator: indicatorData.find(item => item.indicatorId === newValue[0]),
          siteHistoryData: data.siteHistoryList,
        });
      });
    }
  };
  const handleIndicatorSelect = event => {
    setIndicatorSelectShow(!indicatorSelectShow);

    let selectBtnTarget = document.getElementById('indicator_select_btn');
    let selectPanelTarget = document.getElementById('indicator_select_panel');
    document.addEventListener('click', current => {
      if (
        current.target.id.indexOf('indicator_select_checkbox') !== -1 ||
        current.target === selectBtnTarget ||
        current.target === selectPanelTarget ||
        selectBtnTarget.contains(current.target) ||
        selectPanelTarget.contains(current.target)
      ) {
        return;
      }
      setIndicatorSelectShow(false);
    });
  };

  let validtorRule = {minSelect: 1, maxSelect: 3};
  return (
    <div className="line_chart_tab">
      {indicatorData.length > 0 && (
        <div style={{width: '100%'}}>
          <Tab
            style={{display: 'inline-block', width: '80%'}}
            draggable={false}
            onClick={handleTabChange}
            selectedIndex={currentIndicatorIndex}
            titleLength={2}
          >
            {indicatorShowValue.map((item, index) => (
              <TabItem
                lazyLoad={true}
                tabItemStyle={{outline: 'none'}}
                titleLength={currentIndicatorIndex === index ? 8 : 2}
                title={item.indexName}
              >
                {' '}
              </TabItem>
            ))}
          </Tab>
          <Button
            className="indicator_select_btn"
            id="indicator_select_btn"
            onClick={handleIndicatorSelect}
            leftIconProps={{leftHoverIcon: edit}}
            leftIcon={edit}
            style={{
              display: 'inline-block',
              color: '#BBBBBB',
              background: '#272727',
              border: 'none',
              float: 'right',
              outline: 'none',
              opacity: '0.6',

              marginTop: '4px',

              marginRight: '-15px',
            }}
          />
          <p style={{
            fontSize: '12px',
            color: '#BBBBBB',
            lineHeight: '12px',
            fontFamily: 'HarmonyHeiTi',
            marginTop: '20px',
          }}
          >
            {siteHistoryData.length === 0 ? '' : siteHistoryData[0].indexUnit}
          </p>

          <div
            id="indicator_select_panel"
            style={{visibility: indicatorSelectShow ? 'visible' : 'hidden'}}
          >
            <p className="indicator_select_tip"> {$t('indicator.select.tip')}</p>
            <CheckboxGroup
              id="indicator_select_checkbox"
              onChange={handleIndicatorChange}
              data={getSelectData(indicatorData)}
              value={selectValue}
              validtor={validtorRule}
              style={{marginLeft: '16px'}}
              tipData={{placement: 'topRight', overlayStyle: {color: '#f5f5f5'}, color: '#393939'}}
            />
          </div>
        </div>
      )}
      <div id="lineChartDiv" className="alarmCls" />
    </div>
  );
}

export default ServiceLineChart;
