import React from 'react';
import { TextField, Tooltip } from 'eview-ui';
import styles from './index.less';

/**
 * 带搜索按钮的输入框
 */
const SearchInput = props => {
  const { id, placeholder, value, onChange, validator, onSearch, onRef, maxLength, inputStyle, tipsContent,
    tipsPosition, tipsTrigger, format, style } = props;
  return (
    <Tooltip content={tipsContent} placement={tipsPosition} trigger={tipsTrigger}>
      <div className={styles.dfInputFrame} style={{ float: 'right', position: 'relative', ...style }}>
        <TextField placeholder={placeholder} id={id}
          inputStyle={{ ...inputStyle, paddingRight: '28px' }} hintType='tip' value={value}
          onChange={onChange} tipDuration={6000} autoComplete="off"
          validator={validator} maxLength={maxLength} format={format || 'any'}
          ref={onRef} onKeyDown={onSearch}
        />
        <div className={styles.dfIconSearch} onClick={onSearch} />
      </div>
    </Tooltip>
  );
};

export default SearchInput;
