import React, { useContext, useEffect, useRef } from 'react';
import axios from 'axios';
import { request } from '@util';
import '@pages/businesstopo/css/grayscale/progressDetail.less';
import startIcon from '../../../../apps/businesstopo/assets/grayscale/start0.png';
import initIcon from '../../../../apps/businesstopo/assets/grayscale/init0.png';
import testIcon from '../../../../apps/businesstopo/assets/grayscale/test0.png';
import progressIcon from '../../../../apps/businesstopo/assets/grayscale/progress0.png';
import allIcon from '../../../../apps/businesstopo/assets/grayscale/all0.png';
import endIcon from '../../../../apps/businesstopo/assets/grayscale/end0.png';
import startRIcon from '../../../../apps/businesstopo/assets/grayscale/startr0.png';
import initRIcon from '../../../../apps/businesstopo/assets/grayscale/initr0.png';
import testRIcon from '../../../../apps/businesstopo/assets/grayscale/testr0.png';
import progressRIcon from '../../../../apps/businesstopo/assets/grayscale/progressr0.png';
import allRIcon from '../../../../apps/businesstopo/assets/grayscale/allr0.png';
import { BUSINESS_TOPO_CONTEXT } from '../../const';

import { $t } from '../../../../commonUtil';
import { _isZh } from '../../../../commonUtil/intl';
import { updateGrayScaleData } from '@pages/businesstopo/api';
import PropTypes from 'prop-types';

function ProgressDetail(props) {
  return (
    <div id="progressDetail">
      <div className="title">
        {props.grayStatus === '1' ? $t('grayscale.upgrade.in.progress') : $t('grayscale.rollback')}
      </div>

      <div className="detailTitle">
        {props.grayStatus === '1' ? $t('grayscale.upgrade.progress') : $t('grayscale.upgrade.terminated')}
      </div>
      <div className="detailStep">
        <div className="start">
          <div className="stepImg " style={{
            backgroundImage: `url(${props.grayStatus === '1' ? startIcon : startRIcon})`,
          }}
          />
          <span>{props.grayStatus === '1' ? $t('grayscale.preparation') : $t('grayscale.rollback.preparation')}</span>
        </div>
        <div className="init waitLine">
          <div className="stepImg" style={{
            backgroundImage: `url(${props.grayStatus === '1' ? initIcon : initRIcon})`,
          }}
          />
          <span>{props.grayStatus === '1' ? $t('grayscale.init') : $t('grayscale.rollback.init')}</span>
        </div>
        <div className="test waitLine">
          <div className="stepImg" style={{
            backgroundImage: `url(${props.grayStatus === '1' ? testIcon : testRIcon})`,
          }}
          />
          <span>{props.grayStatus === '1' ? $t('grayscale.test') : $t('grayscale.rollback.test')}</span>
        </div>
        <div className="stepProgress waitLine">
          <div className="stepImg" style={{
            backgroundImage: `url(${props.grayStatus === '1' ? progressIcon : progressRIcon})`,
          }}
          />
          <span style={{
            width: props.grayStatus === '1' ? null : '100px',
            position: props.grayStatus === '1' ? null : 'absolute',
            left: props.grayStatus === '1' ? null : '-28px',
          }}
          >
            {props.grayStatus === '1' ? $t('grayscale.step') : $t('grayscale.rollback.step')}
          </span>
        </div>
        <div className="all waitLine">
          <div className="stepImg" style={{
            backgroundImage: `url(${props.grayStatus === '1' ? allIcon : allRIcon})`,
          }}
          />
          <span style={{
            width: props.grayStatus === '1' ? null : '67px',
            position: props.grayStatus === '1' ? null : 'absolute',
            left: props.grayStatus === '1' ? null : '-16px',
          }}
          >
            {props.grayStatus === '1' ? $t('grayscale.all') : $t('grayscale.rollback.all')}
          </span>
        </div>
        <div className="end waitLine">
          <div className="stepImg" style={{
            backgroundImage: `url(${endIcon})`,
          }}
          />
          <span>{props.grayStatus === '1' ? $t('grayscale.end') : $t('grayscale.rollback.end')}</span>
        </div>
      </div>

      <div className="user" style={{
        marginTop: _isZh ? null : '4px',
      }}
      >
        <div className="userDesc">{$t('grayscale.user.dist')}</div>
        <div className="userNum">
          <span id="progressDetailUserNum">0</span>
          <span id="progressDetailUserUnit">%</span>
        </div>
      </div>

      <div className="progress">
        <div className="progressFirst" />
        <div className="progressSecond" />
      </div>

      <div className="footer">
        <div className="area">
          <div className="produce">{$t('grayscale.prod')}</div>
          <div className="grayscale">{$t('grayscale.area')}</div>
        </div>
      </div>
    </div>
  );
}

ProgressDetail.propTypes = {
  grayRequestDataRef: PropTypes.shape({
    current: PropTypes.shape({
      stepId: PropTypes.number,
    }),
  }),
};
export default ProgressDetail;

