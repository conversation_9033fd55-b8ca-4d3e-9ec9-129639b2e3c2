import React, {useContext, useEffect, useState, useRef} from 'react';
import '@pages/businesstopo/css/grayscale/progress.less';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import ProgressDetail from './progressDetail';
import {getGrayScaleData, getLightNode, updateGrayScaleData} from '../../api';
import startIcon from '../../../../apps/businesstopo/assets/grayscale/start.png';
import initIcon from '../../../../apps/businesstopo/assets/grayscale/init.png';
import testIcon from '../../../../apps/businesstopo/assets/grayscale/test.png';
import progressIcon from '../../../../apps/businesstopo/assets/grayscale/progress.png';
import allIcon from '../../../../apps/businesstopo/assets/grayscale/all.png';
import endIcon from '../../../../apps/businesstopo/assets/grayscale/end.png';
import startIcon0 from '../../../../apps/businesstopo/assets/grayscale/start0.png';
import initIcon0 from '../../../../apps/businesstopo/assets/grayscale/init0.png';
import testIcon0 from '../../../../apps/businesstopo/assets/grayscale/test0.png';
import progressIcon0 from '../../../../apps/businesstopo/assets/grayscale/progress0.png';
import allIcon0 from '../../../../apps/businesstopo/assets/grayscale/all0.png';
import endIcon0 from '../../../../apps/businesstopo/assets/grayscale/end0.png';
import startRIcon0 from '../../../../apps/businesstopo/assets/grayscale/startr0.png';
import initRIcon0 from '../../../../apps/businesstopo/assets/grayscale/initr0.png';
import testRIcon0 from '../../../../apps/businesstopo/assets/grayscale/testr0.png';
import progressRIcon0 from '../../../../apps/businesstopo/assets/grayscale/progressr0.png';
import allRIcon0 from '../../../../apps/businesstopo/assets/grayscale/allr0.png';
import startRIcon from '../../../../apps/businesstopo/assets/grayscale/startr.png';
import initRIcon from '../../../../apps/businesstopo/assets/grayscale/initr.png';
import testRIcon from '../../../../apps/businesstopo/assets/grayscale/testr.png';
import progressRIcon from '../../../../apps/businesstopo/assets/grayscale/progressr.png';
import allRIcon from '../../../../apps/businesstopo/assets/grayscale/allr.png';
import downloadIcon from '../../../../apps/businesstopo/assets/grayscale/download.svg';
import downloadGaryIcon from '../../../../apps/businesstopo/assets/grayscale/downloadgray.svg';
import {$t, registerResource} from '../../../../commonUtil';
import {_isZh} from '../../../../commonUtil/intl';
import i18n from '../../locales/grayscale';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {getQuery} from '@pages/businesstopo/a3dPages/components/utils';
import PropTypes from 'prop-types';

const ICON_LIST = [startIcon, initIcon, testIcon, progressIcon, allIcon, endIcon];
const ICON0_LIST = [startIcon0, initIcon0, testIcon0, progressIcon0, allIcon0, endIcon0];

const ICON_R_LIST = [startRIcon, initRIcon, testRIcon, progressRIcon, allRIcon, endIcon];
const ICON_R0_LIST = [startRIcon0, initRIcon0, testRIcon0, progressRIcon0, allRIcon0, endIcon0];

function stepHandler(from, grayRequestDataRef) {
  const stepList = $('#progressDetail .detailStep').children();
  stepList.each((index, ele) => {
    if (index < parseInt(from / 360 * 6)) {
      if (index !== 0) {
        $(ele).addClass('successLine');
        $(ele).removeClass('waitLine');
      }
      if (grayRequestDataRef.current.status === '1') {
        $(ele).find('div').css('background-image', `url(${ICON_LIST[index]})`);
      } else if (grayRequestDataRef.current.status === '2') {
        $(ele).find('div').css('background-image', `url(${ICON_R_LIST[index]})`);
      }
    } else {
      if (index !== 0) {
        $(ele).addClass('waitLine');
        $(ele).removeClass('successLine');
      }
      if (grayRequestDataRef.current.status === '1') {
        $(ele).find('div').css('background-image', `url(${ICON0_LIST[index]})`);
      } else if (grayRequestDataRef.current.status === '2') {
        $(ele).find('div').css('background-image', `url(${ICON_R0_LIST[index]})`);
      }
    }
  });
}

function Progress(props) {
  registerResource(i18n, 'grayscale');
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {showKPIPanel, showSiteRightPanel, solutionId} = state;
  const stepMap = {
    1: '0%',
    2: '10%',
    3: '10%',
    4: '10%',
    5: '60%',
    6: '100%',
  };

  const endStepId = 6;
  const closeGray = () => {
    setShowGray(false);
    updateGrayScaleData({
      instanceId: 1,
    });
  };

  const [showGray, setShowGray] = useState(false);
  let rightDisFlag = showKPIPanel;
  if (props.isSite) {
    rightDisFlag = showSiteRightPanel;
  }
  if (props.isMotype) {
    rightDisFlag = props.showMotypePanel;
  }

  const totalNum = 6;
  const total_percent = 100;
  const circle = 360;
  const [grayDeg, setGrayDeg] = useState(-1);
  const [userDeg, setUserDeg] = useState(-1);

  const [grayStatus, setGrayStatus] = useState('0');
  const intervalTime = 60000;
  const intervalTime2 = 5000;
  let grayFrameRef = useRef(null);
  let grayDegRef = useRef(0);
  let userFrameRef = useRef(null);
  let userDegRef = useRef(0);
  let grayRequestDataRef = useRef({
    stepId: 0,
    grayPercentage: 0,
    isFirstFlag: false,
    status: '1',
  });

  function func2() {
    if (!solutionId && !props.isSite) {
      return;
    }
    if (!props.isMotype) {
      let param = {};
      param.instanceId = solutionId;
      if (props.isSite) {
        const {siteId} = getQuery();
        param.instanceId = siteId;
      }
      getLightNode(
        param, ({data, resultCode}) => {
          if (!data) {
            return;
          }
          if (props.isSite && data.grayscaleList.length > 0) {
            eventBus.emit('to3d_updateDrillDownGray', data.grayscaleList);
          } else {
            eventBus.emit('to3d_updateGrayUpgrade', data.grayscaleList);
          }
        },
      );
    }
  }

  useEffect(() => {
    const func = isFirstFlag => {
      getGrayScaleData({}, ({data, resultCode}) => {
        if (resultCode === -1) {
          return;
        }
        if (data.stepId > 0 && data.grayDisplayFlag !== 1) {
          setShowGray(true);
        } else {
          setShowGray(false);
          return;
        }
        // 暂时兼容数据为null的场景
        grayRequestDataRef.current = data;
        grayRequestDataRef.current.status = '1';
        grayRequestDataRef.current.isFirstFlag = isFirstFlag;
        grayRequestDataRef.current.stepId = data.stepId;
        grayRequestDataRef.current.grayPercentage = data.grayPercentage;
        setGrayDeg(parseInt(data.stepId / totalNum * circle));
        setUserDeg(parseInt((total_percent - data.grayPercentage) / total_percent * circle)); // userVolume是生产区的比例
        if (data.isUpgrade || data.isUpgrade === null) {
          setGrayStatus('1');
        } else {
          setGrayStatus('2');
        }
      });
    };

    const interval = setInterval(() => {
      func(false);
      func2();
    }, intervalTime);

    setTimeout(() => {
      func2();
    }, intervalTime2);

    func(true); // 先执行一次

    return () => {
      clearInterval(interval);
    };
  }, [solutionId]);

  useEffect(() => {
    if (document.querySelector('.titleRing')) {
      document.querySelector('.titleRing').style.setProperty('--ringdeg0', '0deg');
      document.querySelector('.titleRing').style.setProperty('--ringdeg', '0deg');
      document.querySelector('.titleRing').style.setProperty('--ringdeg4', '0deg');
    }

    if (document.querySelector('.userRing')) {
      document.querySelector('.userRing').style.setProperty('--ringdeg0', '0deg');
      document.querySelector('.userRing').style.setProperty('--ringdeg', '0deg');
      document.querySelector('.userRing').style.setProperty('--ringdeg4', '0deg');
    }
  }, []);

  // 根据角度进行动画设置
  useEffect(() => {
    // 如果有在执行动画， 取消动画，进行新的动画
    if (grayFrameRef.current) {
      cancelAnimationFrame(grayFrameRef.current);
      grayFrameRef.current = null;
    }

    const func = () => {
      // 获取当前动画进行到的值
      let from = grayDegRef.current;
      let to = grayDeg;

      if (from === to) {
        // 退出动画
        if (grayFrameRef.current) {
          cancelAnimationFrame(grayFrameRef.current);
          grayFrameRef.current = null;
          return;
        }
      }

      // 降低
      if (from > to) {
        from -= 1;
      } else {
        from += 1;
      }
      if (grayRequestDataRef.current.isFirstFlag) {
        from = to; // 第一次不需要动画
      }

      // 开始的缺口最大为4
      let firstColorDeg = (from === 360 || from === 0) ? 0 : 4;
      let colorDef = from;
      let endColorDeg = (from === 360 || from === 0) ? from : from + 4;

      if (document.querySelector('.titleRing')) {
        document.querySelector('.titleRing').style.setProperty('--ringdeg0', `${firstColorDeg}deg`);
        document.querySelector('.titleRing').style.setProperty('--ringdeg', `${colorDef}deg`);
        document.querySelector('.titleRing').style.setProperty('--ringdeg4', `${endColorDeg}deg`);
      }

      $('#progressGrayNum').text(parseInt(from / 360 * 6));
      $('#progressGrayNumPercentage').text(stepMap[grayRequestDataRef.current.stepId]);
      stepHandler(from, grayRequestDataRef);

      if (grayRequestDataRef.current.userVolume !== 100) {
        $('#downloadIcon').css('background-image', `url(${downloadIcon})`);
        $('#downloadDom').addClass('download');
        $('#downloadDom').removeClass('downloadGary');
      } else {
        $('#downloadIcon').css('background-image', `url(${downloadGaryIcon})`);
        $('#downloadDom').addClass('downloadGary');
        $('#downloadDom').removeClass('download');
      }

      grayDegRef.current = from;
      grayFrameRef.current = requestAnimationFrame(func);
    };

    grayFrameRef.current = requestAnimationFrame(func);
    return () => {
      if (grayFrameRef.current) {
        cancelAnimationFrame(grayFrameRef.current);
        grayFrameRef.current = null;
      }
    };
  }, [grayDeg]);

  // 根据角度进行动画设置
  useEffect(() => {
    // 如果有在执行动画， 取消动画，进行新的动画
    if (userFrameRef.current) {
      cancelAnimationFrame(userFrameRef.current);
      userFrameRef.current = null;
    }

    const func = () => {
      // 获取当前动画进行到的值
      let from = userDegRef.current;
      let to = userDeg;

      if (from === to) {
        // 退出动画
        if (userFrameRef.current) {
          cancelAnimationFrame(userFrameRef.current);
          userFrameRef.current = null;
          return;
        }
      }

      // 降低
      if (from > to) {
        from -= 1;
      } else {
        from += 1;
      }
      if (grayRequestDataRef.current.isFirstFlag) {
        from = to; // 第一次不需要动画
      }

      // 开始的缺口最大为4
      let firstColorDeg = (from === 360 || from === 0) ? 0 : 4;
      let colorDef = from;
      let endColorDeg = (from === 360 || from === 0) ? from : from + 4;

      if (document.querySelector('.userRing')) {
        document.querySelector('.userRing').style.setProperty('--ringdeg0', `${firstColorDeg}deg`);
        document.querySelector('.userRing').style.setProperty('--ringdeg', `${colorDef}deg`);
        document.querySelector('.userRing').style.setProperty('--ringdeg4', `${endColorDeg}deg`);
      }

      $('#progressUserNum').text(grayRequestDataRef.current.grayPercentage);
      $('#progressDetail .progressFirst').css('flex-grow', parseInt(from / 360 * 100));
      $('#progressDetail .progressSecond').css('flex-grow', 100 - parseInt(from / 360 * 100));
      $('#progressDetailUserNum').text(grayRequestDataRef.current.grayPercentage);

      userDegRef.current = from;
      userFrameRef.current = requestAnimationFrame(func);
    };

    userFrameRef.current = requestAnimationFrame(func);
  }, [userDeg]);

  useEffect(() => {
    if (!showGray) {
      setGrayDeg(-1);
      setUserDeg(-1);
      setGrayStatus('0');
      grayFrameRef.current = null;
      grayDegRef.current = 0;
      userFrameRef.current = null;
      userDegRef.current = 0;
      grayRequestDataRef.current = {
        stepId: 0,
        grayPercentage: 0,
        isFirstFlag: false,
        status: '1',
      };
    }
  }, [showGray]);

  if (grayStatus === '0') {
    return (<div />);
  }

  return showGray && (
    <>
      <div id="progress" style={(rightDisFlag) ? {right: '416px'} : {right: '16px'}}>
        <ProgressDetail grayStatus={grayStatus} grayDeg={grayDeg} userDeg={userDeg}
          grayRequestDataRef={grayRequestDataRef} setShowGray={setShowGray}
        />
        {
          grayStatus === '1' &&
          <div id="smallProgress">
            <div className="title">{$t('grayscale.upgrade')}</div>
            <div className="titleProgress">
              <div className="titleRing" />
              <div className="titleProgressNum">
                <span id="progressGrayNum">0</span>/{totalNum}
              </div>

              <div className="titleProgressDesc">{$t('grayscale.progress')}</div>

              <div className="titleProgressDescNum" style={{fontSize: _isZh ? null : 11}}>{$t('grayscale.area')}
                <span id="progressGrayNumPercentage">0</span>
              </div>
            </div>

            <div className="user">
              <div className="userRing" />
              <div className="userNum">
                <span id="progressUserNum">0</span>
                <span id="progressUserUnit">%</span>
              </div>

              <div className="userDesc">{$t('grayscale.user')}</div>
            </div>
          </div>
        }
        {
          grayStatus === '2' &&
          <div id="smallProgressRollBack">
            <div className="title">{$t('grayscale.rollback')}</div>

            <div className="titleProgress">
              <div className="titleRing" />
              <div className="titleProgressNum">
                <span id="progressGrayNum">0</span>/{totalNum}
              </div>
            </div>
          </div>
        }
      </div>
      {grayRequestDataRef.current.stepId === endStepId && (
        <div style={(rightDisFlag) ? {right: '418px'} : {right: '18px'}} className='close_icon' onClick={closeGray} />
      )}
    </>

  );
}

Progress.propTypes = {
  isMotype: PropTypes.bool,
  isSite: PropTypes.bool,
  showMotypePanel: PropTypes.bool,
};
export default Progress;
