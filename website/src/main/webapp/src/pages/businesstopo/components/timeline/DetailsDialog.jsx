/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useEffect, useState, useRef} from 'react';
import {Row, Col, Button, Tooltip} from 'eview-ui';
import {$t} from '@util';
import {dateTimeFormat} from '@util/tools';
import bluePng from '@images/timeLine/blue.png';
import orangePng from '@images/timeLine/orange3.png';
import ringPng from '@images/timeLine/ring.png';
import {ALARM_LEVEL_CLASS, ALARM_LEVEL_TEXT, ALARM_TAG_CLASS, CAROUSEL_MAX} from '../../const/timeLine';
import {SINGLE_MAX_WIDTH, CAROUSEL_WIDTH, DETAIL_TYPE, DETAIL_VIEW_TYPE} from '../../const/timeLine';
import {getEventList, getTimeLineData} from '../../api/timeLine';
import '../../css/animation.css';
import PropTypes from 'prop-types';

function DetailsDialog({closeDialog, timeTrack, offsetX, idList, paramRef}) {
  const SUCCESS_CODE = 200;
  const [detailsInfo, setDetailsInfo] = useState({
    viewType: '',
    eventInfoArr: [],
    alarmInfoArr: [],
    singleInfo: {},
  });

  useEffect(() => {
    getDetailsInfo();
  }, []);

  const getDetailsInfo = () => {
    let param = paramRef.current;
    if (idList) {
      getEventList(
        {...param, timestamp:idList},
        resp => {
          if (!resp || resp.resultCode !== SUCCESS_CODE) {
            return;
          }
          if (resp.data.length === 0) {
            return;
          }
          handleRespData(resp.data);
        },
      );
    }
  };

  const handleRespData = respData => {
    let allData = respData;

    let eventList = allData.filter(item => item.category === DETAIL_TYPE.event);
    let alarmList = allData.filter(item => item.category === DETAIL_TYPE.alarm || item.category === DETAIL_TYPE.clear);

    if (eventList.length === 0 && alarmList.length === 0) {
      return;
    }

    let viewType = DETAIL_VIEW_TYPE.single;
    let singleInfo;
    let singleInfoType;
    if (eventList.length > 0 && alarmList.length > 0) {
      viewType = DETAIL_VIEW_TYPE.multiple;
    }
    if (eventList.length > 1 || alarmList.length > 1) {
      viewType = DETAIL_VIEW_TYPE.multiple;
    }
    if (viewType === DETAIL_VIEW_TYPE.single) {
      singleInfo = eventList[0] || alarmList[0];
      singleInfoType = eventList.length > 0 ? DETAIL_TYPE.event : alarmList[0].category;
    }

    setDetailsInfo({
      viewType,
      eventInfoArr:
        viewType === DETAIL_VIEW_TYPE.multiple ?
          eventList.map(item => getDetailInfoObject(item, DETAIL_TYPE.event)) :
          [],
      alarmInfoArr:
        viewType === DETAIL_VIEW_TYPE.multiple ?
          alarmList.map(item => getDetailInfoObject(item, item.category)) :
          [],
      singleInfo: viewType === DETAIL_VIEW_TYPE.single ? getDetailInfoObject(singleInfo, singleInfoType) : null,
    });
  };

  // 计算单个弹窗的位置
  const calcSingleDialogStyle = () => {
    let $timeLine = document.getElementById('adminConsoleContainer');
    let offset = (offsetX - SINGLE_MAX_WIDTH / 2) / $timeLine.offsetWidth;
    if (offset < 0.01) {
      offset = 0.01;
    }
    let maxOffset = ($timeLine.offsetWidth - SINGLE_MAX_WIDTH) / $timeLine.offsetWidth;
    if (offset > maxOffset) {
      offset = maxOffset;
    }
    return {left: `${offset * 100}%`};
  };

  const getDetailInfoObject = (info, type) => ({
    title: info.alarmName,
    type,
    level: info.severity,
    time: info.occurUtc,
    locationInfo: info.additionalInformation,
    occurTime: dateTimeFormat(info.occurTime),
    alarmSource: info.meName,
    csn: info.csn,
    analysisStatus: info.analysisStatus,
    selectedAssociationTask: info.selectedAssociationTask,
    executionTime: info.executionTime,
    indicatorId: info.indicatorId,
    alarmDetailId: info.alarmDetailId,
    analysisCompleted: info.analysisStatus === 'success' || info.analysisStatus === 'fail',
    needJumpAnalysis: info.analysisStatus !== null && info.analysisStatus !== undefined,
  })
  ;

  return (
    <>
      {detailsInfo.viewType === DETAIL_VIEW_TYPE.multiple && (
        <Carousel
          eventInfoArr={detailsInfo.eventInfoArr}
          alarmInfoArr={detailsInfo.alarmInfoArr}
          closeDialog={closeDialog}
          timeTrack={timeTrack}
          offsetX={offsetX}
        />
      )}
      {detailsInfo.viewType === DETAIL_VIEW_TYPE.single && (
        <SingleDetail
          info={{...detailsInfo.singleInfo}}
          viewType={DETAIL_VIEW_TYPE.single}
          closeDialog={closeDialog}
          timeTrack={timeTrack}
          customStyle={calcSingleDialogStyle()}
        />
      )}
    </>
  );
}

function SingleDetail(props) {
  const {info, viewType, closeDialog, customStyle, timeTrack} = props;
  const {
    type,
    title,
    level,
    time,
    locationInfo,
    occurTime,
    alarmSource,
    csn,
    analysisCompleted,
    analysisParam,
    needJumpAnalysis,
    alarmDetailId,
    selectedAssociationTask,
    executionTime,
    indicatorId,
  } = info;
  const handlerTimeTaskResult = respData => {
    let allData = respData.alarmDataList;
    let alarmObj = allData.filter(item => item.category === DETAIL_TYPE.alarm);
    if (alarmObj.length === 0) {
      return true;
    }
    let alarmList = alarmObj[0].alarmRecordList;
    if (alarmList.length === 0) {
      return true;
    }
    return alarmList[0].analysisStatus === 'success' || alarmList[0].analysisStatus === 'fail';
  };

  const detailDomRef = useRef(null);
  const defaultBoardColor = 'linear-gradient(to right, #272727, #272727), linear-gradient(to right, #272727, #272727)';

  useEffect(() => {
    if (needJumpAnalysis && detailDomRef.current) {
      if (!analysisCompleted) {
        let id = [];
        id.push(csn);
        let intervalStatus = false;
        const intervalId = setInterval(async() => {
          getEventList(
            {alarmCsnList: id},
            resp => {
              if (!resp || resp.resultCode !== 0) {
                intervalStatus = true;
                return;
              }
              if (resp.data.alarmDataList.length === 0) {
                intervalStatus = true;
                return;
              }
              intervalStatus = handlerTimeTaskResult(resp.data);
              if (intervalStatus) {
                clearInterval(intervalId);
                $(detailDomRef.current).css('backgroundImage', defaultBoardColor);
                $(detailDomRef.current).find('.detailsAlarmLabel').css('backgroundImage', `url(${bluePng})`);
                $(detailDomRef.current).find('.detailsAlarmLabel span').text($t('timeline.details.intelligent.analysis.completed'));
              }
            },
          );
        }, 1000 * 30);
      }
    }
  }, [needJumpAnalysis]);

  const onClickClose = () => {
    closeDialog();
  };

  let cardStyle = {
    ...(customStyle || {}),
    paddingTop: '58px',
    backgroundClip:  (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) ? 'padding-box, border-box' : null,
    backgroundOrigin:  (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) ? 'padding-box, border-box' : null,
    border: (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) ? '2px solid transparent' : null,
    '--analysisShow': 'inline-block',
    backgroundImage:defaultBoardColor,
  };

  return (
    <div
      className={viewType === DETAIL_VIEW_TYPE.single ? 'single_detail_container' : 'multiple_detail_container'}
      style={cardStyle}
      ref={detailDomRef}
    >
      {viewType === DETAIL_VIEW_TYPE.single && <div className='single_detail_container_child' />}
      {viewType === DETAIL_VIEW_TYPE.single &&
        <div className='close_details_icon' onClick={onClickClose} />}
      {(type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) && needJumpAnalysis && (
        <div
          className="detailsAlarmLabel"
          style={{
            backgroundImage: `url(${analysisCompleted ? bluePng : orangePng})`,
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
          }}
        >
          <img src={ringPng} alt="" />
          <span>
            {$t(
              `${
                analysisCompleted ?
                  'timeline.details.intelligent.analysis.completed' :
                  'timeline.details.intelligent.analysis'
              }`,
            )}
          </span>
        </div>
      )}

      <div>
        { (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) && (
          <div className={ALARM_LEVEL_CLASS[level]} style={{width: '24px', height: '24px'}} />
        )}
        {type === DETAIL_TYPE.event && <div className='event_title_icon' />}
        <Tooltip content={title} placement="topLeft" color="#393939" overlayStyle={{color: '#FFF'}}>
          <div className="detailsTitle">{title}</div>
        </Tooltip>
        { (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.clear) && <div className={ALARM_TAG_CLASS[level]}>{ (type === DETAIL_TYPE.alarm || type === DETAIL_TYPE.analysis) ? ALARM_LEVEL_TEXT[level] : 'Cleared'}</div>}
      </div>
      <div style={{marginTop: '24px', height: '85px', paddingRight: '24px'}}>
        <Row>
          <Col cols={24}>
            <Tooltip content={locationInfo} placement="topLeft" color="#393939"
              overlayStyle={{color: '#FFF'}}
            >
              <div className="detailsInfo detailsValue">{locationInfo}</div>
            </Tooltip>
          </Col>
        </Row>
        <div className="detailBottomContainer">
          <div style={{height: '21px'}}>
            <div className='details_alarmSource_icon' />
            <div className="detailsInfo" style={{color: '#BBBBBB'}}>
              {$t('timeline.details.alarm.source')}
            </div>
          </div>
          <div style={{height: '21px', marginTop: '6px', marginLeft: '19px', width: '140px'}}>
            <Tooltip content={alarmSource} placement="topLeft" color="#393939"
              overlayStyle={{color: '#FFF'}}
            >
              <div className="detailsInfo detailsValue">{alarmSource}</div>
            </Tooltip>
          </div>
        </div>
        <div className="detailBottomContainer" style={type === DETAIL_TYPE.alarm ? {float: 'right'} : null}>
          <div style={{height: '21px'}}>
            <div className='details_occurTime_icon' />
            <div className="detailsInfo" style={{color: '#BBBBBB'}}>
              {$t('timeline.details.alarm.occurTime')}
            </div>
          </div>
          <div style={{height: '21px', marginTop: '6px', marginLeft: '19px'}}>
            <div className="detailsInfo detailsValue">{occurTime}</div>
          </div>
        </div>
      </div>
      <div style={{textAlign: 'center', paddingRight: '24px'}}>
        <Button
          text={
            type === DETAIL_TYPE.event ?
              $t('timeline.common.button.eventDetails') :
              $t('timeline.common.button.alarmDetails')
          }
          status="primary"
          className="btn_event_backtrack"
          onClick={() => timeTrack(type, time, csn, analysisParam, needJumpAnalysis, selectedAssociationTask, executionTime, indicatorId)}
        />
      </div>
    </div>
  );
}

function Carousel(props) {
  const {eventInfoArr, alarmInfoArr, closeDialog, timeTrack, offsetX} = props;

  const getSelectedType = () => {
    if ((eventInfoArr.length > 0 && alarmInfoArr.length > 0) || alarmInfoArr.length > 0) {
      return DETAIL_TYPE.alarm;
    }
    if (eventInfoArr.length > 0) {
      return DETAIL_TYPE.event;
    }
  };

  const [pageData, setPageData] = useState({
    selectedType: getSelectedType(),
    currentPage: 1,
    totalPage: 1,
  });
  const [initData, setInitData] = useState({
    isShow: false,
    detailsContent: <></>,
    customStyle: {},
  });

  useEffect(() => {
    getCarouselContent(pageData.currentPage);
  }, [pageData.selectedType, pageData.currentPage]);

  // 计算轮播弹窗的位置
  const calcCarouselStyle = arrLen => {
    let bodyObj = document.getElementById('adminConsoleContainer');
    let containerWidth = CAROUSEL_WIDTH[arrLen] + 40;
    let offset = (offsetX - containerWidth / 2) / bodyObj.offsetWidth;
    if (offset < 0.01) {
      offset = 0.01;
    }
    let maxOffset = getMaxOffset(bodyObj.offsetWidth, containerWidth);
    if (offset > maxOffset) {
      offset = maxOffset;
    }
    return {left: `${offset * 100}%`, width: CAROUSEL_WIDTH[arrLen]};
  };

  const getMaxOffset = (bodyWidth, containerWidth) => {
    let offsetWidth = bodyWidth - containerWidth;
    return offsetWidth / bodyWidth;
  };

  const getCarouselContent = page => {
    let dataArr;
    if (pageData.selectedType === DETAIL_TYPE.event) {
      dataArr = eventInfoArr;
    } else {
      dataArr = alarmInfoArr;
    }

    let totalPage = Math.trunc(dataArr.length / CAROUSEL_MAX);
    let remainder = dataArr.length % CAROUSEL_MAX;
    if (remainder > 0) {
      totalPage++;
    }
    setPageData({...pageData, totalPage});

    let {length} = dataArr;
    dataArr.sort((a, b) => Boolean(b.analysisStatus) - Boolean(a.analysisStatus));
    let sliceStart = (page - 1) * CAROUSEL_MAX;
    let sliceEnd = Math.min((page - 1) * CAROUSEL_MAX + 3, length);
    let pageDataArr = dataArr.slice(sliceStart, sliceEnd);
    let contentArr = pageDataArr.map((data, index) => (
      <SingleDetail
        info={data}
        viewType={DETAIL_VIEW_TYPE.multiple}
        customStyle={index % CAROUSEL_MAX !== 0 ? {marginLeft: '8px'} : null}
        timeTrack={timeTrack}
      />
    ));
    setInitData({
      isShow: true,
      detailsContent: contentArr,
      customStyle: {
        ...calcCarouselStyle(contentArr.length),
        height: '312px',
      },
    });
  };

  const onChangeType = key => {
    setPageData({
      selectedType: key,
      currentPage: 1,
      totalPage: 1,
    });
  };

  const onChangePage = (pageNumber, offSet) => {
    setPageData({...pageData, currentPage: pageNumber + offSet});
  };

  const getTabClass = type => {
    if (eventInfoArr.length === 0 || alarmInfoArr.length === 0) {
      return 'carousel_tab';
    }
    if (type === pageData.selectedType) {
      return 'carousel_tab_active';
    }
    return 'carousel_tab';
  };

  return (
    initData.isShow && (
      <div id="carousel_container_div" className="carousel_container" style={initData.customStyle}>
        <div id="carouselTab" style={{display: 'inline'}}>
          {alarmInfoArr.length > 0 && (
            <div className={getTabClass(DETAIL_TYPE.alarm)}
              onClick={() => onChangeType(DETAIL_TYPE.alarm)}
            >{`${$t('timeline.details.tab.alarm')}(${alarmInfoArr.length > 999 ? '999+' : alarmInfoArr.length})`}
            </div>
          )}
          {eventInfoArr.length > 0 && (
            <div
              className={getTabClass(DETAIL_TYPE.event)}
              onClick={() => onChangeType(DETAIL_TYPE.event)}
              style={eventInfoArr.length > 0 ? {marginLeft: '24px'} : null}
            >{`${$t('timeline.details.tab.event')}(${eventInfoArr.length > 999 ? '999+' : eventInfoArr.length})`}
            </div>
          )}
        </div>
        <div className='carousel_close_icon' onClick={closeDialog} />
        <div id="carouselContent" style={{marginTop: '12px', display: 'flex', alignItems: 'end'}}>
          {pageData.currentPage !== 1 && (
            <div className='page_left_icon' onClick={() => onChangePage(pageData.currentPage, -1)} />
          )}
          {initData.detailsContent}
          {pageData.currentPage !== pageData.totalPage && (
            <div className='page_right_icon' onClick={() => onChangePage(pageData.currentPage, 1)} />
          )}
        </div>
      </div>
    )
  );
}

DetailsDialog.propTypes = {
  idList: PropTypes.string,
  offsetX: PropTypes.number,
  timeTrack: PropTypes.func,
  closeDialog: PropTypes.func,
};

export default DetailsDialog;
