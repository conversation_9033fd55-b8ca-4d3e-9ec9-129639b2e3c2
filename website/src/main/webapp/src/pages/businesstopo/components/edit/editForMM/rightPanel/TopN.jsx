import React, {useState, useRef} from 'react';
import {LabelField, TextField} from 'eview-ui';
import {$t} from '@util';
import '@pages/businesstopo/css/edit/index.css';
import './css/index.less';
import {PanelType} from './const';

/**
 * show 是否展示
 *
 */
const TopN = (props) => {
  const { name, setViewShow, topNCount, setTopNCount, typeSelect} = props;
  const [topN, setTopN] = useState(topNCount);
  const topRef = useRef(null);
  const isInRange = (value) => {
    // 将字符串转换为数字
    const number = Number(value);
    // 检查是否是有效的数字并且在范围内
    let result = !isNaN(number) && typeof number === 'number' && number >= 1 && number <= 20;
    return {
      result,
      message: !result ? $t('sys_valid_number_info') : '',
    };
  };
  return (
    <div>
      <div style={{ padding: '24px' }}>
        <div className="edit_site_name_title">
          {name}
        </div>
        <div className="edit_site_name_split_line" />
        <div style={{ marginBottom: '12px', marginTop: '12px'}}>
          <LabelField text="TopN" required={true} style={{ width: '90px' }} />
        </div>
        <TextField
          required={true}
          hideRequiredMark={true}
          hintType='tip'
          autoComplete='off'
          inputStyle={{ width: '352px' }}
          validator={(val, id) => isInRange(val)}
          value={typeSelect === PanelType.THIRD_TOPN ? topN.thrid : topN.app}
          ref={ref => topRef.current = ref}
          onChange={(v) => {
            // 回调
            setTopN({
              ...topNCount,
              [typeSelect === PanelType.THIRD_TOPN ? 'thrid' : 'app']:v,
            });
          }}
        />
        <div className="action-buttons" style={{ marginTop: '24px', right: '24px' }}>
          <button
            className="cancel-button"
            onClick={() => {
              // 回调
              setViewShow(false);
            }}
          >
            {$t('mm.edit.button.cancel')}
          </button>
          <button
            className="confirm-button"
            onClick={() => {
              if (!topRef.current.validate()) {
                topRef.current.focus();
                return;
              }
              // 回调
              setViewShow(false);
              setTopNCount(topN);
            }}
          >
            {$t('mm.edit.button.sure')}
          </button>
        </div>
      </div>
    </div>
  );
};

export {TopN};
