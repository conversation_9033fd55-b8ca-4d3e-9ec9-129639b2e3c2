import {request} from '@util';
const queryiniteditpage = '/rest/dvtopowebsite/v1/business/topo/queryiniteditpage';
const getMeasureUnitTreeUrl = '/rest/dvtopowebsite/v1/business/topo/common/meastypetree';
const queryNetWorkTreeByTypeUrl = '/rest/dvtopowebsite/v1/business/topo/common/querymotypetree';
const queryObjectTreeByTypeUrl = '/rest/dvtopowebsite/v1/business/topo/common/measobjectinfobytype';
const editbusinessUrl = '/rest/dvtopowebsite/v1/business/topo/editbusiness';
const editMMbusinessUrl = '/rest/dvtopowebsite/v1/business/topo/editoverviewformm';
// 非容量评估：按网元类型查询网元树
export const queryNetWorkTreeByType = (name, succ, fail) => {
  return request.get(`${queryNetWorkTreeByTypeUrl}?name=${name}`, succ, fail, false);
};

// 按网元实例查询测量单元树
export const getMeasureUnitTree = ({type, dn, searchKey, searchScope}, succ, fail) => {
  let urlContent = `&searchKey=${encodeURIComponent(searchKey)}&searchScope=${searchScope}`;
  return request.get(
    `${getMeasureUnitTreeUrl}?moType=${type}&dn=${dn}&version=*${urlContent}`,
    succ,
    fail,
    false,
  );
};

// 按网元类型查询测量对象
export const queryObjectTreeByType = ({type, measUnitKey}, succ, fail) => {
  return request.get(
    `${queryObjectTreeByTypeUrl}?moType=${type}&measUnitKey=${measUnitKey}&displayValue=&originalValue=`,
    succ,
    fail, false,
  );
};

export const getIniteditpage = (params, success, error) => request.post(queryiniteditpage, params, success, error, false);

export const editbusiness = (params, success, error) => request.post(editbusinessUrl, params, success, error, false);

export const editMMbusiness = (params, success, error) => request.post(editMMbusinessUrl, params, success, error, false);