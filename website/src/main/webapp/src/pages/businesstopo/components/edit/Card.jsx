import React, {useContext} from 'react';
import Tooltip from 'eview-ui/Tooltip';
import '@pages/businesstopo/css/edit/Card.css';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const'; // 引入CSS文件
const Card = ({ index, keyName, titleName, content, deleteCardHandele}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  return (
    <div className="card">
      <div className="card-header">
        <div>
          <span className="title-name">{titleName}</span>
        </div>
        <div>
          <span className="edit" onClick={() => {
            dispatch({
              editAddPannelShow: true,
              editAddPannelStatus: 2,
              editAddPannelKey: keyName,
            });
          }}
          />
          <span className="delete" onClick={() => deleteCardHandele(index, keyName)} />
        </div>
      </div>
      <div className="card-content">
        {content.map((item, index2) => (
          <div className="content-row" key={index2}>
            <Tooltip placement="top" content={item.name} trigger={['hover', 'focus']}>
              <span className="row-name">{item.name}</span>
            </Tooltip>
            <Tooltip placement="top" content={item.value} trigger={['hover', 'focus']}>
              <span className="row-value">{item.value}</span>
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Card;
