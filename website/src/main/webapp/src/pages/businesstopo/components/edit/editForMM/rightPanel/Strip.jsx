import React, {useEffect, useState, useRef} from 'react';
import {LabelField, TextField} from 'eview-ui';
import {$t, validate} from '@util';
import '@pages/businesstopo/css/edit/index.css';
import './css/index.less';

/**
 * show 是否展示
 *
 */
const Strip = (props) => {
  const {setStripName, stripName, setViewShow, topNCount, setTopNCount} = props;
  const [name, setName] = useState(stripName);
  const [topN, setTopN] = useState(topNCount);
  const siteNameRef = useRef(null);
  const topRef = useRef(null);
  const isInRange = (value) => {
    // 将字符串转换为数字
    const number = Number(value);
    // 检查是否是有效的数字并且在范围内
    let result = !isNaN(number) && typeof number === 'number' && number >= 1 && number <= 20;
    return {
      result,
      message: !result ? $t('sys_valid_number_info') : '',
    };
  };
  useEffect(() => {
    setName(stripName); // 当 stripName 改变时更新 name
  }, [stripName]); // 依赖于 stripName，当 stripName 发生变化时会执行
  return (
    <div>
      <div style={{ padding: '24px' }}>
        <div className="edit_site_name_title">
          {$t('mm.edit.stripName')}
        </div>
        <div className="edit_site_name_split_line" />
        <div style={{ marginBottom: '12px' }}>
          <LabelField text={$t('mm.edit.stripName')} required={true} style={{ width: '90px' }} />
        </div>
        <TextField
          required={true}
          hideRequiredMark={true}
          hintType='tip'
          autoComplete='off'
          inputStyle={{ width: '352px' }}
          validator={(val, id) => validate(['siteNameValidChar', 'checkLength'], val, id, null, 16)}
          value={name}
          ref={ref => siteNameRef.current = ref}
          onChange={(v) => {
            // 回调
            setName(v);
          }}
        />
        <div style={{ marginBottom: '12px', marginTop: '12px'}}>
          <LabelField text="TopN" required={true} style={{ width: '90px' }} />
        </div>
        <TextField
          required={true}
          hideRequiredMark={true}
          hintType='tip'
          autoComplete='off'
          inputStyle={{ width: '352px' }}
          validator={(val, id) => isInRange(val)}
          value={topN.strip}
          ref={ref => topRef.current = ref}
          onChange={(v) => {
            // 回调
            setTopN({
              ...topNCount,
              strip:v,
            });
          }}
        />
        <div className="action-buttons" style={{ marginTop: '24px', right: '24px' }}>
          <button
            className="cancel-button"
            onClick={() => {
              // 回调
              setViewShow(false);
            }}
          >
            {$t('mm.edit.button.cancel')}
          </button>
          <button
            className="confirm-button"
            onClick={() => {
              if (!siteNameRef.current.validate()) {
                siteNameRef.current.focus();
                return;
              }
              if (!topRef.current.validate()) {
                topRef.current.focus();
                return;
              }
              // 回调
              setStripName(name);
              setViewShow(false);
              setTopNCount(topN);
            }}
          >
            {$t('mm.edit.button.sure')}
          </button>
        </div>
      </div>
    </div>
  );
};

export {Strip};
