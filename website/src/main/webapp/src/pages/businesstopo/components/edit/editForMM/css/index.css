.topocontainer {
  display: flex;
  width: 100%;
  height: calc(100vh);
  overflow: auto;
}
.topocontainer::-webkit-scrollbar {
  width: 0;
  background-color: #272727;
}
#dvtopommedit{
  height: calc(100vh - 4rem);
  overflow: auto;
}

#dvtopommedit::-webkit-scrollbar {
  width: 0;
  background-color: #272727;
}

.leftPanelCls {
  flex: 0 0 20%;
  padding: 16px;
  background-color: #272727;
}

.bodyCls {
  flex: 1;
  background-color: #1f1f1f;
  padding: 64px 32px 32px 32px;
  overflow: auto;
}

.left_panel_separator {
  height: 2px;
  margin-top: 16px;
  background-color: #353333;
}

.left_panel_title {
  margin-top: 16px;
  font-size: 20px;
  color: rgb(245, 245, 245);
}

.left_panel_icon_area {
  margin-top: 16px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 8px;
}

.left_panel_icon {
  background-color: #313131;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  flex-direction: column;
  padding: 0 16px 16px 16px;
}

.left_panel_icon_name {
  font-size: 18px;
  color: #918f8f;
}

.body_head_container {
  display: grid;
  grid-template-columns: 200px auto;
  padding-bottom: 10px;
}

.body_head_title {
  border-radius: 4px;
  background: #393939;
  font-size: 16px;
  color: #e0e0e0;
  font-weight: bold;
  display: inline-block;
  padding: 8px 12px;
}

.edit_btn_container {
  display: flex;
  flex-direction: row;
  column-gap: 12px;
  justify-content: flex-end;
  cursor: pointer;
}

.edit_btn_confirm {
  background-color: #0067d1;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  padding: 0 12px;
  height: 32px;
  width: 88px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}

.edit_btn_cancel {
  background-color: #6c757d;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  padding: 0 12px;
  height: 32px;
  width: 88px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}

.body_content_container {
  margin-top: -17px;
  padding: 16px 16px 0 16px;
  transition: transform 1s ease-in-out;
}

.body_content_drop_area_container {
  display: flex;
  justify-content: center;
  position: relative;
}


.body_content_drop_area_text {
  font-size: 16px;
  color: #e0e0e0;
  font-weight: bold;
  margin-right: 12px;
  width: 164px;
  align-items: center;
  display: flex;
}

.body_content_drop_area {
  width: 55%;
  height: 120px;
  border-radius: 32px;
  border: 2px solid #4b4b4b;
  display: flex;
  justify-content: center;
  transition: background-color 0.3s ease;
  flex-wrap: wrap;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
}

.highlight {
  background-color: rgba(46, 148, 255, 0.1);
  border-color: #2e94ff;
}

.selected {
  border: 1px solid #2e94ff;
  position: relative;
  margin-right: 3px;
}
.editerror{
border: 1px solid #ff2e3f;
}

.delete-icon {
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin-top: 6px;
  background: url('../../../../../../apps/businesstopo/assets/pannel_close.svg');
  position: absolute;
  top: -4px;
  right: 0;
}

.body_content_drop_area_icon {
  height: 70px;
  width: 70px;
}

.body_content_drop_area_icon_text {
  color: #918f8f;
  display: flex;
  justify-content: center;
  margin-top: 5px;
  width: 75px;
  overflow-x: hidden;
  font-size: 12px;
}

.strip_container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 4px;
  margin-top: 50px;
}

.strip_title {
  font-weight: bold;
  margin-right: 10px;
  color: #e0e0e0;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.strip_area_wrapper {
  position: relative;
  width: 30%; /* 设置宽度为70% */
  margin: 10px 10% 0 10%; /* 设置上下10px，左右20% */
  display: flex;
  flex-direction: column; /* 纵向排列 */
  align-items: center; /* 居中对齐 */
}

.strip_area_div{
  padding: 20px;
  border: 2px solid #4B4B4B;
  border-radius: 36px;
  margin-right: 38px;
}
.delete-icon-mm{
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin-top: 6px;
  background: url('../../../../../../apps/businesstopo/assets/pannel_close.svg');
  position: absolute;
  top:-4px;
  right:0px;
}

.error-item-label2{
  margin-right: 10px; /* Space between label and value */
  font-weight: bold; /* Optional: make label bold */
  max-width: 170px;
}