/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext} from 'react';
import {Tooltip} from 'eview-ui';
import {BUSINESS_TOPO_CONTEXT} from '../const';
import {dateTimeFormat, jumpViewMoreHistoryPage, jumpViewMorePage} from '../../../commonUtil/tools';
import {$t} from '../../../commonUtil';
import PropTypes from 'prop-types';
import {getMoreAlarmData} from '@pages/businesstopo/api';

function AlarmDetail(props) {
  const {record} = props;
  const ALARM_MAP = {
    4: $t('alarm.text.info'), 3: $t('alarm.text.minor'), 2: $t('alarm.text.major'), 1: $t('alarm.text.critical'),
  };

  const alarmJump = () => {
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    getMoreAlarmData({
      csnList: record.csn,
      timestamp: topoSession.selectedTime,
    }, ({data, resultCode}) => {
      if (topoSession.selectedTime > 0) {
        jumpViewMoreHistoryPage(data);
      } else {
        jumpViewMorePage(data);
      }
    });
  };
  return (
    <div className='detail_panel' onClick={alarmJump}>
      <div className="detail_content">
        <div className={`alarm_level_${record.severity}_icon`} />
        <Tooltip content={record.alarmName} placement='topLeft' color='#393939' overlayStyle={{color: '#FFF'}}>
          <div className="detail_title">{record.alarmName}</div>
        </Tooltip>
        <span
          style={{width: '56px', height: '26px', zIndex: 1}}
          className={`detail_level_${record.severity}_btn`}
        >
          {ALARM_MAP[record.severity]}
        </span>
        <Tooltip content={record.additionalInformation} placement='topLeft' color='#393939'
          overlayStyle={{color: '#FFF'}}
        >
          <p className="detail_sub_title">
            {record.additionalInformation}
          </p>
        </Tooltip>
        <div style={{marginTop: '16px', marginBottom: '24px'}}>
          <div className='time_icon' />
          <p className="detail_alarm_value">
            {dateTimeFormat(record.occurUtc)}
          </p>
        </div>

      </div>
    </div>
  );
}

AlarmDetail.propTypes = {
  record: PropTypes.shape({
    alarmName: PropTypes.string,
    csn: PropTypes.string,
    occurUtc: PropTypes.string,
    severity: PropTypes.number,
    additionalInformation: PropTypes.string,
    service: PropTypes.string,
  }).isRequired,
};

export default AlarmDetail;
