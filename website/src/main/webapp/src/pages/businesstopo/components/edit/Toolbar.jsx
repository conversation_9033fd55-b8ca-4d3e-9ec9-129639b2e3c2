import React, {useEffect, useState, useContext} from 'react';
import {$t} from '@util';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
const Toolbar = ({handleConfirm, handleCancel}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  return (
    <div className="toolbar">
      <div className="button">{$t('overview.view.name')}</div>
      <div className="tools-container" style={{transform: state.editRightPannelOpen && state.editRightPannelShow ? 'translateX(-200px)' : 'translateX(150px)'}}>
        <div className="action-buttons">
          <button className="cancel-button" onClick={handleCancel}>{$t('button.cancel')}</button>
          <button className="confirm-button" onClick={handleConfirm} id="confirmToolbar">{$t('button.confirm')}</button>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;