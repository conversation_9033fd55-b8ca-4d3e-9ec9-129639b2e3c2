import React from 'react';
import {registerResource, $t} from '@util';
import i18n from '../local';
import KnowledgeBaseDetail from '../taskManagement/taskDetail/KnowledgeBaseDetail';
import AlgorithmDetail from '../taskManagement/taskDetail/AlgorithmDetail';
import BasicInfoDetail from '../taskManagement/taskDetail/BasicInfoDetail';

registerResource(i18n, 'alarmConfig');

export const TaskManageContext = React.createContext();
export const GlobalContext = React.createContext();
export const OPERATE_SUCCESS_CODE = 0;
export const incidentTaskType = 10;

export const MAX_ALARM_ID_WHITE_LIST = 100;

// 左侧菜单选项
export const MENUS_CONFIG_RULER = {
  header: {
    title: $t('alarm.edit.config.rule'),
  },
  menus: [
    {
      title: $t('alarm.edit.alarm.config.rule'),
      value: 'config',
    },
    {
      title: $t('intelligent.incident.task.manage.title'),
      value: 'taskManagement',
    },
  ],
};

// 智能Incident任务管理表格的列
export const taskListColumns = [
  {
    key: 'taskName',
    title: $t('intelligent.incident.task.manage.table.task.name'),
    dataIndex: 'taskName',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'algorithm',
    title: $t('intelligent.incident.task.manage.table.algorithm'),
    dataIndex: 'algorithm',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'solutionType',
    title: $t('intelligent.incident.task.manage.table.solution.type'),
    dataIndex: 'solutionType',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'knowledgeBase',
    title: $t('intelligent.incident.task.manage.table.knowledge.base'),
    dataIndex: 'knowledgeBase',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'trainStatus',
    title: $t('intelligent.incident.task.manage.table.train.status'),
    dataIndex: 'trainStatus',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'lastModificationTime',
    title: $t('intelligent.incident.task.manage.table.last.modify.time'),
    dataIndex: 'lastModificationTime',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
  {
    key: 'operation',
    title: $t('intelligent.incident.task.manage.table.operation'),
    dataIndex: 'operation',
    ellipsis: {
      hintType: 'tip',
      tipOnOverflow: true,
    },
  },
];

// 训练状态Map
export const TRAIN_STATUS_MAP = {
  SUCCESS: 1,
  FAILED: 2,
  TRAINING: 3,
  PARTIALSUCCESS: 4,
};

// 任务管理页面类型
export const TASK_PAGE_TYPE = {
  TASK_LIST: 'taskList',
  SELECT_KNOWLEDGE_BASE: 'selectKnowledgeBase',
  CONFIGURE_ALGORITHM: 'configureAlgorithm',
  BASIC_INFO: 'basicInfo',
  VIEW_INFO: 'viewTask',
};

// 创建任务步骤
export const CREATE_TASK_WIZARDS_PARAMS = [
  {
    text: $t('intelligent.incident.task.manage.select.knowledge.base'),
    value: TASK_PAGE_TYPE.SELECT_KNOWLEDGE_BASE,
  }, {
    text: $t('intelligent.incident.task.manage.config.algorithm'),
    value: TASK_PAGE_TYPE.CONFIGURE_ALGORITHM,
  }, {
    text: $t('intelligent.incident.task.manage.basic.information'),
    value: TASK_PAGE_TYPE.BASIC_INFO,
  },
];

// 算法参数表格的列
export const ALGORITHM_TABLE_COLUMNS = [
  {
    title: $t('intelligent.incident.task.manage.parameter.name'),
    key: 'name',
    width: '50%',
    allowSort: false,
  },
  {
    title: $t('intelligent.incident.task.manage.default.value'),
    key: 'defaultValue',
    width: '50%',
    allowSort: false,
  },
];

// 训练周期
export const TRAIN_EXECUTE_CYCLE = [
  {
    text: $t('intelligent.incident.task.manage.day.one'),
    value: '1|DAY',
  }, {
    text: $t('intelligent.incident.task.manage.day.three'),
    value: '3|DAY',
  }, {
    text: $t('intelligent.incident.task.manage.day.seven'),
    value: '7|DAY',
  },
];

// 任务详情页签
export const TASK_DETAIL_TAB = [
  {
    title: $t('intelligent.incident.task.manage.table.knowledge.base'),
    component: KnowledgeBaseDetail,
  },
  {
    title: $t('intelligent.incident.task.manage.table.algorithm'),
    component: AlgorithmDetail,
  },
  {
    title: $t('intelligent.incident.task.manage.basic.information'),
    component: BasicInfoDetail,
  },
];

// 是否可以修改任务
export const IS_CAN_MODIFY_TASK = {
  YES: 1,
  NO: 0,
};

// 分析结果是否可见值和显示的映射
export const DISPLAY_RESULT_MAP = {
  true: $t('intelligent.incident.task.manage.task.analysis.result.visible.yes'),
  false: $t('intelligent.incident.task.manage.task.analysis.result.visible.no'),  
};

// 训练周期值和显示的映射
export const TRAIN_CRON_MAP = {
  '1|DAY': $t('intelligent.incident.task.manage.day.one'),
  '3|DAY': $t('intelligent.incident.task.manage.day.three'),
  '7|DAY': $t('intelligent.incident.task.manage.day.seven'),  
};