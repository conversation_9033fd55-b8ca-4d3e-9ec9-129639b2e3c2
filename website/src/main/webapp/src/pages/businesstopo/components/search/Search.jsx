import React, {useContext, useEffect, useState, useRef} from 'react';
import {TablePro, Select, SearchInput} from 'eview-ui';
import {sorter} from '@digitalview/fe-utils';
import {$t, registerResource} from '@util';
import closeIcon from '../../../../apps/businesstopo/assets/edit/edit_close.svg';
import i18n from './local';
import {queryjumpingpath, searchmopath} from '@pages/businesstopo/components/search/api';
import './css/index.less';

const Search = ({solutionData}) => {
  registerResource(i18n, 'search');
  let labelStyle = {display: 'block', paddingLeft: '0px'};
  let compStyle = {marginRight: '5px', marginLeft: '20px'};
  const [tableShow, setTableShow] = useState(false);
  const [dataset, setDataset] = useState([]);
  const [select, setSelect] = useState(0);
  let options = [
    {text: $t('search.ne.name'), value: 0},
    {text: $t('search.app.name'), value: 6},
    {text: $t('search.pod.name'), value: 7},
    {text: $t('search.vm.name'), value: 8},
  ];

  useEffect(() => {
    setTableShow(false);
    setDataset([]);
  }, [solutionData]);

  let typeConvert = {
    6: 'APP',
    7: 'POD',
    8: 'HOST',
  };
  const columns = [
    {
      title: $t('search.ne.name'),
      width: 250,
      key: 'neName',
      dataIndex: 'neName',
      ellipsis: true,
      sorter: (a, b) => sorter(a.neName, b.neName),
      render: (text, record) => (
        <div style={{padding: '5px', cursor:'pointer'}}>
          <a
            onClick={() => {
              let param = {
                instanceId: record.instanceId,
                clusterId: record.belongApplicationId,
              };
              // HOST场景直接跳转
              if (select === 8) {
                let hostJumpUrl = `/eviewwebsite/index.html#path=/businesstopo/hosthome&vmId=${record.instanceId}&hostName=${record.neName}&isSearch=true&jumpId=${record.instanceId}`;
                const windowJump = window.open(encodeURI(hostJumpUrl));
                windowJump.opener = null;
                windowJump.location = hostJumpUrl;
              }

              queryjumpingpath(param, (data) => {
                if (data && data.resultCode === 0) {
                  let jumpUrl = `/eviewwebsite/index.html#path=/businesstopo/mohome&siteId=${data.data.siteId}&siteName=${data.data.siteName || data.data.hwsStripe}&moTypeId=${data.data.moTypeId}&moTypeName=${data.data.moTypeName}&solutionId=${solutionData[0].solutionId}&applicationType=${data.data.applicationType}&isSearch=true&jumpId=${record.instanceId}`;
                  if (data.data.applicationType === '4') {
                    jumpUrl = `/eviewwebsite/index.html#path=/businesstopo/hsmhome&siteId=${data.data.siteId}&siteName=${data.data.siteName || data.data.hwsStripe}&moTypeId=${data.data.moTypeId}&moTypeName=${data.data.moTypeName}&solutionId=${solutionData[0].solutionId}&applicationType=${data.data.applicationType}&isSearch=true&jumpId=${record.instanceId}`;
                  }
                  if (data.data.hwsAppSite) {
                    jumpUrl += ` &stripUnit=${data.data.hwsAppSite}`;
                  }
                  if (solutionData[0].solutionType === 3) {
                    jumpUrl += '&isMM=true';
                  }
                  const windowJump = window.open(encodeURI(jumpUrl));
                  windowJump.opener = null;
                  windowJump.location = jumpUrl;
                }
              });
            }}
          >
            {text}
          </a>
        </div>
      ),
    },
    {
      title: $t('search.ne.type'),
      width: 250,
      key: 'neType',
      ellipsis: true,
      dataIndex: 'neType',
      render: (text, record) => (
        <div style={{padding: '5px'}}>
          {typeConvert[text]}
        </div>
      ),
    },
    {
      title: $t('search.ne.app'),
      width: 250,
      key: 'neApp',
      ellipsis: true,
      dataIndex: 'neApp',
      render: (text, record) => (
        <div style={{padding: '5px'}}>
          {text}
        </div>
      ),
    },
    {
      title: $t('search.ne.site'),
      width: 250,
      key: 'neSite',
      ellipsis: true,
      dataIndex: 'neSite',
      render: (text, record) => (
        <div style={{padding: '5px'}}>
          {text}
        </div>
      ),
    },
  ];
  const searchResult = (key) => {
    let param = {
      solutionId: solutionData[0].solutionId,
      searchKey: key,
      searchType: select,
    };
    searchmopath(param, (data) => {
      if (data.resultCode === 0 && data.data !== null) {
        let newData = [];
        data.data.forEach((item, index) => {
          newData.push({
            key: index,
            neName: item.dnName,
            neType: item.dnType,
            neApp: item.belongApplication,
            neSite: item.belongSite,
            belongSiteId: item.belongSiteId,
            belongApplicationId: item.belongApplicationId,
            instanceId: item.instanceId,
            dnType: item.dnType,
          });
        });
        setDataset(newData);
      }
      setTableShow(true);
    });
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        position: 'absolute',
        left: '3rem',
        top: '70px',
        zIndex: 9998,
      }}
      >
        <div>
          <Select options={options}
            labelStyle={labelStyle}
            style={compStyle}
            popupDirection='bottom'
            value={select}
            onChange={(value) => {
              setDataset([]);
              setTableShow(false);
              setSelect(value);
            }}
          />
        </div>
        <div>
          <SearchInput
            labelStyle={labelStyle}
            placeholder={$t('search.name')}
            onSearch={searchResult}
          />
        </div>
      </div>
      {tableShow &&
        <div className='topoSearchNotPagingSelect' style={{
          position: 'absolute',
          left: '14rem',
          top: '140px',
          zIndex: 9998,
          width: '60%',
          height: '50%',
        }}
        >
          <TablePro
            columns={columns}
            dataset={dataset}
            pagination
            style={{
              'background-color': '#191919',
            }}
          />
          <div
            style={{
              backgroundImage: `url(${closeIcon})`,
              position: 'absolute',
              right: '0px',
              top: '1px',
              display: 'inline-block',
              'background-repeat': 'no-repeat',
              cursor: 'pointer',
              'background-color': '#3f3f3f',
              height: '24px',
              width: '24px',
            }}
            onClick={() => {
              setDataset([]);
              setTableShow(false);
            }}
          />
        </div>}

    </div>
  );
};


export default Search;