/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, {useContext, useState, useEffect} from 'react';
import {Tooltip} from 'eview-ui';
import {$t, registerResource} from '@util';
import i18n from '../../locales/index';
import '../../css/toolbar.less';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
import PropTypes from 'prop-types';
import {debounce} from '@digitalview/fe-utils';

function Toolbar({showRightPanel, showMotypePanel, isSite, isMotype, isDatabase, isMM, isMMSite}) {
  registerResource(i18n, 'timeline');
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const [showLegend, setShowLegend] = useState(false);
  const [showAerialView, setShowAerialView] = useState(false);
  const {showKPIPanel, showSiteRightPanel, showDataBaseRightPanel} = state;
  const onClickLegend = () => {
    setShowLegend(!showLegend);
  };

  const onClickEdit = () => {
    if (isMM) {
      location.replace('/eviewwebsite/index.html#path=/businesstopo/editmm');
    } else {
      location.replace('/eviewwebsite/index.html#path=/businesstopo/edit');
    }
  };

  const onClickConfig = () => {
    location.replace('/eviewwebsite/index.html#path=/businesstopo/config');
  };

  let isOverView = true;
  let rightDisFlag = showKPIPanel;
  if (isSite) {
    rightDisFlag = showSiteRightPanel;
    isOverView = false;
  }
  if (isMMSite) {
    isOverView = false;
  }
  if (isMotype) {
    rightDisFlag = showMotypePanel;
    isOverView = false;
  }
  if (isDatabase) {
    rightDisFlag = showDataBaseRightPanel;
    isOverView = false;
  }

  return (
    <>
      <div className="toolbar_container"
        style={rightDisFlag ? {right: '416px', bottom: isOverView ? '144px' : '60px'} : {
          right: '16px',
          bottom: isOverView ? '114px' : '60px',
        }}
      >
        <div className="toolbar_container_div" style={{height: '40px'}}>
          <Tooltip content={$t('defaultView')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
            <div className="icon_toolbar_reset" onClick={() => {
              eventBus.emit('to3d_resetCamera');
            }}
            />
          </Tooltip>
        </div>
        <div className="toolbar_container_div" style={{height: isOverView ? '190px' : '108px', marginTop: '8px'}}>
          <Tooltip content={$t('zoomIn')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
            <div className="icon_toolbar_zoomIn" onClick={() => {
              eventBus.emit('to3d_zoomIn');
            }}
            />
          </Tooltip>
          <Tooltip content={$t('zoomOut')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
            <div className="icon_toolbar_zoomOut" onClick={() => {
              eventBus.emit('to3d_zoomOut');
            }}
            />
          </Tooltip>
          {isOverView &&
            <Tooltip content={$t('edit')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
              <div className="icon_toolbar_edit" onClick={onClickEdit} />
            </Tooltip>}
          {isOverView &&
            <Tooltip content={$t('config')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
              <div className="icon_toolbar_config" onClick={onClickConfig} />
            </Tooltip>}
          <Tooltip content={$t('legend')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
            <div className="icon_toolbar_legend" onClick={onClickLegend} />
          </Tooltip>
          {isOverView &&
            <Tooltip content={$t('refresh')} placement="left" color="#393939" overlayStyle={{color: '#FFF'}}>
              <div className="icon_toolbar_refresh" onClick={debounce(() => {
                eventBus.emit('overview_manualRefresh');
              }, 1000)}
              />
            </Tooltip>}
        </div>
      </div>
      {showLegend && (
        <LegendShow showAerialView={showAerialView} showRightPanel={rightDisFlag} close={() => setShowLegend(false)}
          isMM={isMM}
        />
      )}
      {showAerialView && <AerialView showRightPanel={showRightPanel} close={() => setShowAerialView(false)} />}
    </>
  );
}

function LegendShow({showAerialView, showRightPanel, close, isMM}) {
  const mmData = [
    {className: 'icon_toolbar_legend_item_northbound', name: 'Third party'},
    {className: 'icon_toolbar_legend_item_group', name: $t('dRGroup')},
    {className: 'icon_toolbar_legend_item_app', name: 'Channel'},
    {className: 'icon_toolbar_legend_item_pod', name: 'POD/Instance'},
    {className: 'icon_toolbar_legend_item_vm', name: 'HOST'},
  ];
  const cbsData = [
    {className: 'icon_toolbar_legend_item_northbound', name: 'Common'},
    {className: 'icon_toolbar_legend_item_4g-service', name: $t('4gService')},
    {className: 'icon_toolbar_legend_item_5g-service', name: $t('5gService')},
    {className: 'icon_toolbar_legend_item_group', name: $t('dRGroup')},
    {className: 'icon_toolbar_legend_item_smpp', name: 'SMPP'},
    {className: 'icon_toolbar_legend_item_app', name: 'APP'},
    {className: 'icon_toolbar_legend_item_pod', name: 'POD/Instance'},
    {className: 'icon_toolbar_legend_item_vm', name: 'HOST'},
  ];
  const [legendData, setLegendData] = useState([]);

  useEffect(() => {
    if (isMM) {
      setLegendData(mmData);
    } else {
      setLegendData(cbsData);
    }
  }, []);

  const getStyle = () => {
    let right;
    if (showAerialView) {
      if (showRightPanel) {
        right = 806;
      } else {
        right = 406;
      }
    } else {
      if (showRightPanel) {
        right = 475;
      } else {
        right = 75;
      }
    }
    return {right: `${right}px`};
  };

  return (
    <div className="legendShowDiv" style={getStyle()}>
      <div>
        <div className="toolbar_show_title">{$t('legend')}</div>
        <div className="icon_toolbar_close" onClick={close} />
      </div>
      {legendData.map(item => (
        <div style={{width: '72px', display: 'inline-block', textAlign: 'center'}}>
          <div className={item.className} />
          <Tooltip content={item.name}
            placement="topLeft"
            color="#393939"
            overlayStyle={{color: '#FFF'}}
          >
            <div style={{
              fontSize: '12px',
              color: '#BBBBBB',
              overflow: 'hidden',
            }}
            >{item.name.length <= 10 ? item.name : `${item.name.slice(0, 7)}...`}
            </div>
          </Tooltip>
        </div>
      ))}
    </div>
  );
}

function AerialView({showRightPanel, close}) {
  return;
  <div className="legendShowDiv" style={showRightPanel ? {right: '475px'} : {right: '75px'}}>
    <div className="toolbar_show_title">鸟瞰图</div>
    <div className="icon_toolbar_close" onClick={close} />
  </div>;
}

Toolbar.propTypes = {
  showRightPanel: PropTypes.bool,
  showMotypePanel: PropTypes.bool,
  isSite: PropTypes.bool,
  isMotype: PropTypes.bool,
  isDatabase: PropTypes.bool,
};

export default Toolbar;
