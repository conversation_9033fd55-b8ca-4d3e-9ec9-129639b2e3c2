import React, { useContext, useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, TablePro, Loader } from 'eview-ui';
import styles from '../index.less';
import { $t, validate, modal } from '@util';
import {dateTimeFormat} from '@util/tools';
import SearchInput from '../../common/SearchInput';
import {
  taskListColumns,
  TaskManageContext,
  TASK_PAGE_TYPE,
  OPERATE_SUCCESS_CODE,
  TRAIN_STATUS_MAP,
  GlobalContext,
} from '../const';
import * as api from '../api';

let taskNameSearch;

const IncidentTaskList = (props) => {
  const { setShowAccordion } = props;
  const [state, dispatch] = useContext(TaskManageContext);
  const { theme } = useContext(GlobalContext);
  const { curTaskPage, taskListInfo } = state;
  const { emptyTableMsg, taskName, pageSize, 
    currentPage, autoRefreshTimeStamp, pageResults, recordCount} = taskListInfo;
  const taskListInfoRef = useRef({});
  const taskListTable = useRef('');

  // 存储更新后的任务列表相关信息
  useEffect(() => {
    taskListInfoRef.current = { ...taskListInfo };
  }, [taskListInfo]);

  // 表格相关信息改变时重新请求表格内容
  useEffect(() => {
    queryTaskList();
  }, [currentPage, pageSize, autoRefreshTimeStamp, theme]);

  useEffect(() => {
    let taskListTimer = 0;
    taskListTimer = setInterval(() => {
      setTaskListInfo({ autoRefreshTimeStamp: new Date().getTime() });
    }, 20000);
    return () => {
      // 组件销毁时，清除定时器
      clearInterval(taskListTimer);
    };
  }, []);

  // 更新任务列表相关信息公共方法
  const setTaskListInfo = (param) => {
    dispatch({
      type: 'setTaskListInfo',
      taskListInfo: { ...taskListInfoRef.current, ...param },
    });
  };

  // 查询任务列表
  const queryTaskList = () => {
    // 校验任务名称
    if (taskNameSearch && !taskNameSearch.validate()) {
      taskNameSearch.focus();
      return;
    }

    setTaskListInfo({ emptyTableMsg: $t('intelligent.incident.task.manage.table.loading') });
    api.queryTask({
      taskName,
      taskType: [10],
      paging: { pageSize, pageNumber: currentPage, sortField: '', sortType: '' },
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setTaskListInfo({
          emptyTableMsg: $t('intelligent.incident.task.manage.table.query.fail'), pageResults: [], 
        });
        return;
      }
      
      let rows = [];
      if (res.data && res.data.rows.length) {
        res.data.rows.forEach((item) => {
          rows.push({
            ...item,
            taskName: getCustomTaskName(item),
            algorithm: item.algorithmModelName,
            solutionType: item.solutionName,
            knowledgeBase: item.solutionTypeForRecommendation,
            trainStatus: getCustomTrainState(item),
            lastModificationTime: dateTimeFormat(item.updateTime),
            operation: getCustomOperation(item),
          });
        });
      }

      // 更新表格数据
      setTaskListInfo({
        recordCount: (res.data && res.data.total) ? res.data.total : 0,
        emptyTableMsg: $t('intelligent.incident.task.manage.table.no.data'),
        pageResults: rows,
      });
    }, err => {
      setTaskListInfo({
        emptyTableMsg: $t('intelligent.incident.task.manage.table.query.fail'), pageResults: [], 
      });
    });
  };

  // 根据任务名称过滤任务列表
  const searchTaskList = (event) => {
    if (taskNameSearch && !taskNameSearch?.validate()) {
      taskNameSearch.focus();
      return;
    }
    // 13: 键盘enter键
    if (event.keyCode === 13 || event.type === 'click') {
      // 改变时间去驱使加载当前页码的任务列表
      setTaskListInfo({
        currentPage: 1,
        autoRefreshTimeStamp: new Date().getTime(),
      });
    }
  };

  // 获取查看任务信息
  const getViewTaskInfo = (taskId) => {
    api.viewTask(
      {
        taskId,
      },
      (res) => {
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          modal.error(
            $t('intelligent.incident.task.manage.view.fail'),
            null,
            $t('intelligent.incident.task.manage.error.tip'),
            null,
            theme,
          );
          return;
        }
        setShowAccordion(false);
        dispatch({
          type: 'setViewDetailInfo',
          viewDetailInfo: res.data,
        });
        dispatch({
          type: 'setCurTaskPage',
          curTaskPage: TASK_PAGE_TYPE.VIEW_INFO,
        });
      },
      (err) => {
        modal.error(
          $t('intelligent.incident.task.manage.view.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
      }
    );
  };

  // 自定义任务名称表格列
  const getCustomTaskName = (item) => {
    return (
      <span
        style={{ color: 'rgb(24, 111, 194)', cursor: 'pointer' }}
        title={item.taskName}
        onClick={() => getViewTaskInfo(item.taskId)}
      >
        {item.taskName}
      </span>
    );
  };

  // 任务表格操作列公共入口：训练、删除
  const handleTask = (operateType, taskId) => {
    let failContent;
    let content;
    let operateFn;
    
    switch (operateType) {
      case 'train':
        failContent = $t(
          'intelligent.incident.task.manage.table.operation.train.failure'
        );
        content = $t(
          'intelligent.incident.task.manage.table.operation.train.tip'
        );
        operateFn = api.trainModel;
        break;
      case 'delete':
        failContent = $t(
          'intelligent.incident.task.manage.table.operation.delete.failure'
        );
        content = $t(
          'intelligent.incident.task.manage.table.operation.delete.tip'
        );
        operateFn = api.deleteTask;
        break;
      default:
        break;
    }

    const newTaskId = Number(taskId);
    modal.confirm(
      content,
      () => {
        operateFn(
          { taskId: newTaskId },
          (res) => {
            // 操作是否成功
            if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
              let queryFailContent = res.resultMessage || failContent;
              modal.error(
                queryFailContent,
                null,
                $t('intelligent.incident.task.manage.error.tip'),
                null,
                theme,
              );
              return;
            }

            // 改变时间去驱使加载当前页码的任务列表
            setTaskListInfo({
              autoRefreshTimeStamp: new Date().getTime(),
              currentPage: (operateType === 'delete' && pageResults.length === 1 && currentPage !== 1) ?
                (currentPage - 1) : currentPage,
            });
          },
          () => {
            modal.error(
              failContent,
              null,
              $t('intelligent.incident.task.manage.error.tip'),
              null,
              theme,
            );
          }
        );
      },
      $t('alarm.edit.confirm'),
      null,
      theme,
    );
  };

  // 自定义任务操作列
  const getCustomOperation = ({ taskId }) => {
    return (
      <div id='task_operate' className={styles.taskOperateCol}>
        <div
          title={$t('intelligent.incident.task.manage.table.operation.train')}
          className={styles.taskOperate}
          onClick={() => handleTask('train', taskId)}
        >
          {$t('intelligent.incident.task.manage.table.operation.train')}
        </div>
        <div
          title={$t('alarm.edit.delete')}
          className={styles.taskOperate}
          style={{ marginLeft: '1rem' }}
          onClick={() => handleTask('delete', taskId)}
        >
          {$t('alarm.edit.delete')}
        </div>
      </div>
    );
  };

  // 自定义训练状态表格列
  const getCustomTrainState = ({
    trainStatus,
  }) => {
    switch (trainStatus) {
      case TRAIN_STATUS_MAP.SUCCESS:
        return (
          <span
            className={`${styles.taskStatus} ${styles.success}`}
            title={$t(
              'intelligent.incident.task.manage.table.train.status.success'
            )}
          >
            {$t('intelligent.incident.task.manage.table.train.status.success')}
          </span>
        );
      case TRAIN_STATUS_MAP.PARTIALSUCCESS:
        return (
          <span
            className={`${styles.taskStatus} ${styles.partial_success}`}
            title={$t('intelligent.incident.task.manage.table.train.status.partial.success')}
          >
            {$t('intelligent.incident.task.manage.table.train.status.partial.success')}
          </span>
        );
      case TRAIN_STATUS_MAP.FAILED:
        return (
          <span
            className={`${styles.taskStatus} ${styles.fail}`}
            title={$t(
              'intelligent.incident.task.manage.table.train.status.fail'
            )}
          >
            {$t('intelligent.incident.task.manage.table.train.status.fail')}
          </span>
        );
      case TRAIN_STATUS_MAP.TRAINING:
        return (
          <span 
            className={`${styles.taskStatus} ${styles.running}`} 
            title={$t('intelligent.incident.task.manage.table.train.status.running')}
          >
            {$t('intelligent.incident.task.manage.table.train.status.running')}
          </span>
        );
      default:
        return '';
    }
  };

  // 自定义表格空数据时提示
  const getCustomEmptyNode = () => {
    return (
      <div className={styles.tableEmptyNode}>
         {emptyTableMsg}
      </div>
    );
  };

  return (
    <>
      <div className={styles.taskTopTitleContainer}>
        {$t('intelligent.incident.task.manage.title')}
      </div>
      <div className={styles.bottomPanelContainer}>
        <div className={styles.panelFirstRow}>
          <SearchInput
            placeholder={$t('intelligent.incident.task.manage.input.name')}
            id='searchTaskName'
            inputStyle={{ width: '15rem' }}
            value={taskName}
            onChange={(val) => setTaskListInfo({ taskName: val })}
            validator={(val, id) =>
              validate(['cmpValidChar', 'checkLength'], val, id, null, 200)}
            onSearch={searchTaskList}
            onRef={(ele) => (taskNameSearch = ele)}
          />
          <Button
            text={$t('alarm.edit.add')}
            status='primary'
            onClick={() => {
              setShowAccordion(false);
              dispatch({
                type: 'setCurTaskPage',
                curTaskPage: TASK_PAGE_TYPE.SELECT_KNOWLEDGE_BASE,
              });
            }}
          />
        </div>
        <div>
          <TablePro
            id='taskList-table'
            scroll={recordCount <= 10 || pageSize === 10 ? { x: '100%' } : { x: '100%', y: '38rem' }}
            columns={taskListColumns}
            dataset={pageResults}
            emptyNode={getCustomEmptyNode()}
            pagination={{
              recordCount: recordCount,
              pageSize: pageSize,
              currentPage: currentPage,
              onPageChange: (curPage) => {
                setTaskListInfo({ currentPage: curPage });
              },
              onPageSizeChange: (size) => {
                setTaskListInfo({ pageSize: size, currentPage: 1 });
              },
            }}
            ref={ele => taskListTable.current = ele}
          />
        </div>
      </div>
    </>
  );
};

export default IncidentTaskList;
