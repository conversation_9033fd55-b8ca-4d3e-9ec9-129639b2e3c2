/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React, {useEffect, useRef} from 'react';
import '@pages/businesstopo/css/tooltip/pod.less';
import {$t, registerResource, isZh} from '@util';
import PropTypes from 'prop-types';

function SiteTips(props) {
  return (
    <div id="sitetips">
      <div>
        <div style={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center', height: '100%', padding:'10px'}}>
          <div>{props.data.siteName}</div>
        </div>
      </div>
    </div>
  );
}

SiteTips.propTypes = {
  data: PropTypes.shape({
    moTypeName: PropTypes.string,
  }).isRequired,
};
export default SiteTips;
