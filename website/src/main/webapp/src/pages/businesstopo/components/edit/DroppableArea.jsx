import React, {useState, useContext} from 'react';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
import Tooltip from 'eview-ui/Tooltip';

const DroppableArea = ({
  text,
  id,
  highlightedArea,
  areaIcons,
  handleDrop,
  handleDragOver,
  handleDragLeave,
  selectedIcon,
  setSelectedIcon,
  handleDelete,
}) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);

  const handleIconSelect = (id1, iconId) => {
    if (selectedIcon === iconId) {
      setSelectedIcon(null); // Deselect if already selected
    } else {
      setSelectedIcon({
        id: id1, iconId,
      });
      dispatch({
        editRightPannelOpen: true,
        editRightPannelShow: true,
        editRightPanelSiteName: false,
      });
    }
  };

  return (
    <div className="droppable-area-container">
      <div className="area-text">{text}</div>
      <div
        id={id}
        className={`droppable-area ${highlightedArea === id ? 'highlight' : ''}`}
        onDrop={(e) => handleDrop(e, id)}
        onDragOver={(e) => handleDragOver(e, id)}
        onDragLeave={handleDragLeave}
      >
        {areaIcons[id].map((iconData, index) => (
          (
            <div
              key={index}
              className={`business-icon ${selectedIcon && selectedIcon.iconId === index && id === selectedIcon.id ? 'cbsselected' : ''} ${iconData.checkError ? 'error' : ''}`}
              onClick={() => handleIconSelect(id, index)}
            >
              <div><img src={iconData.icon} className="business-select" draggable={false} /></div>
              <div
                className={`${iconData.canDelete ? 'delete-icon' : ''} ${selectedIcon && selectedIcon.iconId === index && id === selectedIcon.id ? 'cbsselected' : ''}`}
                onClick={() => handleDelete(id, index)}
              />
              <Tooltip placement="top" content={iconData.businessName} trigger={['hover', 'focus']}>
                <div
                  className="business-name"
                >{iconData.businessName.length > 8 ? `${iconData.businessName.substring(0, 5)}...` : iconData.businessName}
                </div>
              </Tooltip>
            </div>
          )
        ))}
        <div className="line1" />
      </div>
    </div>
  );
};

export default DroppableArea;
