import React, { useContext, useState, useEffect } from 'react';
import { $t } from '@util';
import Tooltip from 'eview-ui/Tooltip';
import { BUSINESS_TOPO_CONTEXT } from '@pages/businesstopo/const';
import '@pages/businesstopo/css/edit/index.css';
import pageCss from './css/index.css';
import group from '../../../../../apps/businesstopo/assets/edit/group.png';

const StripsArea = ({ data, setPageData }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentData, setCurrentData] = useState({});
  const [areaStyle, setAreaStyle] = useState({ columns: 0, width: '' });

  useEffect(() => {
    if (data.length > 0) {
      const tempData = {
        stripeTeamId: data[currentIndex].stripeTeamId,
        stripeTeamName: data[currentIndex].stripeTeamName,
        groupList: data[currentIndex].groupList.map(item => ({ name: item.groupName, icon: group })),
      };
      setCurrentData(tempData);
      let columns = data[currentIndex].groupList.length > 6 ? 3 : 2;
      let width = '280px';
      setAreaStyle({ columns, width });
    }
  }, [currentIndex, data]);

  const closeRightPanel = () => {
    // closeRightPanel
  };

  const onClickBeforePage = () => {
    if (currentIndex === 0) {
      return;
    }
    setCurrentIndex(currentIndex - 1);
    setPageData(currentIndex + 1, data[currentIndex - 1].stripeTeamName);
    closeRightPanel();
  };

  const onClickNextPage = () => {
    if (currentIndex === data.length - 1) {
      return;
    }
    setCurrentIndex(currentIndex + 1);
    setPageData(currentIndex + 1, data[currentIndex + 1].stripeTeamName);
    closeRightPanel();
  };

  // 点击site区域打开编辑条带Name面板
  const onClickMiddleArea = (e) => {
    setPageData(currentIndex, data[currentIndex].stripeTeamName);
    e.stopPropagation();
  };

  return (
    <div className={pageCss.strip_container}>
      <div className={pageCss.strip_title}>
        <Tooltip placement="top" content={$t('edit.body.strips')} trigger={['hover', 'focus']}>
          <div className={pageCss.strip_title}>{$t('edit.body.strips')}</div>
        </Tooltip>
      </div>
      <div className={pageCss.strip_area_wrapper}>
        {currentIndex !== 0 && <div id="left-arrow-div" className="left-arrow-div" style={{left:'-100px'}} onClick={onClickBeforePage} />}
        <div className={pageCss.strip_area_div} onClick={onClickMiddleArea}>
          <div className="middle-area-title">{currentData?.stripeTeamName}</div>
          <div
            className="middle-area"
            style={{ display: 'grid', gap: '10px', gridTemplateColumns: `repeat(${areaStyle.columns}, 1fr)`, height:'calc(34vh - 9px)' }}
          >
            {currentData.groupList?.map((iconObj, index) => (
              <div key={index} style={{ height: '80px' }}>
                <div className="middle-area-icon">
                  <img src={iconObj.icon} draggable="false" alt="" style={{width: '40px', height: '50px'}} />
                </div>
                <Tooltip placement="top" content={iconObj.name} trigger={['hover', 'focus']}>
                  <div className="icon-name" style={{ width: '130px' }}>
                    {iconObj.name}
                  </div>
                </Tooltip>
              </div>
            ))}
          </div>
        </div>
        {currentIndex < data.length - 1 && <div id="right-arrow-div" className="right-arrow-div" style={{right:'-100px'}} onClick={onClickNextPage} />}
      </div>
    </div>
  );
};

export default StripsArea;
