/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useEffect, useMemo, useRef, useState} from 'react';
import styles from './index.less';
import {Button, Checkbox, TablePro, Select, LabelField, TextField} from 'eview-ui';
import {debounce, deepClone, sorter} from '@digitalview/fe-utils';
import {$t} from '@util';
import AlarmSelect from './alarmselect/alarmSelect';
import {queryalarmruledetail, modifyalarmrule, addalarmrule} from './api';
import {isZh, notification, validate} from '@util/index';

const AlarmConfigDetail = ({close, clickData, solutionId}) => {
  let style = {margin: '8px'};
  const btnStyle = {
    marginTop: '2px',
    marginLeft: '5px',
  };
  const columns = [
    {
      title: $t('alarm.edit.alarm.name'),
      width: 350,
      key: 'alarmName',
      dataIndex: 'alarmName',
      filter: (value, record) => record.alarmName.includes(value),
      sorter: (a, b) => sorter(a.alarmName, b.alarmName),
    },
    {
      title: $t('alarm.edit.alarm.id'),
      width: 350,
      key: 'alarmId',
      dataIndex: 'alarmId',
      ellipsis: {
        hintType: 'tip',
      },
      filter: (value, record) => record.alarmId.includes(value),
      sorter: (a, b) => sorter(a.alarmId, b.alarmId),
    },
  ];
  const trustColumns = [
    ...columns,
    {
      title: $t('alarm.edit.operation'),
      width: '200px',
      key: 'operation',
      render: (text, record) => (
        <div>
          <a
            onClick={() => {
              removeOrignTableData([record]);
            }}
            style={{marginRight: '1.5rem'}}
          >
            {$t('alarm.edit.delete')}
          </a>
          <a
            onClick={() => {
              setDataset(trustDataset);
              clickModelRef.current = 'trust';
              editOrignTableData(record);
            }}
          >
            {$t('alarm.edit.edit')}
          </a>
        </div>
      ),
    },
  ];
  const balckColumns = [
    ...columns,
    {
      title: $t('alarm.edit.operation'),
      width: '200px',
      key: 'operation',
      render: (text, record) => (
        <div>
          <a
            onClick={() => {
              removeOrignTableData([record]);
            }}
            style={{marginRight: '1.5rem'}}
          >
            {$t('alarm.edit.delete')}
          </a>
          <a
            onClick={() => {
              setDataset(blackDataset);
              clickModelRef.current = 'black';
              editOrignTableData(record);
            }}
          >
            {$t('alarm.edit.edit')}
          </a>
        </div>
      ),
    },
  ];
  const [checkboxStatus, setCheckboxStatus] = useState({});
  const [showModel, setShowModel] = useState(false);
  const [dataset, setDataset] = useState([]);
  const [trustDataset, setTrustDataset] = useState([]);
  const [blackDataset, setBlackDataset] = useState([]);
  const [trustSelect, setTrustSelect] = useState([]);
  const [blackSelect, setBlackSelect] = useState([]);
  const [record, setRecord] = useState('');
  const alarmBlackIdsRef = useRef([]);
  const alarmWhiteIdsRef = useRef([]);
  const clickModelRef = useRef('');
  const alarmNameRef = useRef(null);

  useEffect(()=>{
    if (clickData.data.isPreset && clickData.operation === 'modify' ) {
      notification.info($t('alarm.preset.tips'));
    }
  }, []);
  useEffect(() => {
    if (!dataset) {
      return;
    }
    if (clickModelRef.current === 'trust') {
      setTrustDataset(dataset);
    } else {
      setBlackDataset(dataset);
    }
  }, [dataset]);
  const editOrignTableData = (editData) => {
    setShowModel(true);
    setRecord(editData.alarmId);
  };
  const removeOrignTableData = (removeData) => {
    let removeData2 = removeData.filter(item => item && item.key);
    let newData = trustDataset;
    removeData2.forEach(item => {
      newData = newData.filter(data => data.key !== item.key);
    });
    setTrustDataset(newData);
    let newData2 = blackDataset;
    removeData2.forEach(item => {
      newData2 = newData2.filter(data => data.key !== item.key);
    });
    setBlackDataset(newData2);
  };
  let initCheckboxStatus = useRef({});
  let textChangeInitFlag = useRef(false);
  let chekcedChanage = useMemo(() => {
    let selectCheckedFalg = true;
    if (!checkboxStatus.alarmRuleName) {
      return true;
    }
    if (clickData.data.isPreset && clickData.operation === 'modify') {
      return true;
    }
    if (checkboxStatus.selectChecked !== undefined) {
      selectCheckedFalg = false;
    }
    const isEqual = alarmWhiteIdsRef.current.length === trustDataset.length &&
      alarmWhiteIdsRef.current.every(trust => trustDataset.some(obj => obj.alarmId === trust.conditionValue));
    const isEqual2 = alarmBlackIdsRef.current.length === blackDataset.length &&
      alarmBlackIdsRef.current.every(black => blackDataset.some(obj => obj.alarmId === black.conditionValue));
    let selectCheckedFalg2 = (
      checkboxStatus.selectChecked === initCheckboxStatus.current.selectChecked &&
      checkboxStatus.fullAlarmsChecked === initCheckboxStatus.current.fullAlarmsChecked &&
      checkboxStatus.criticalChecked === initCheckboxStatus.current.criticalChecked &&
      checkboxStatus.majorChecked === initCheckboxStatus.current.majorChecked &&
      checkboxStatus.minorChecked === initCheckboxStatus.current.minorChecked &&
      checkboxStatus.infoChecked === initCheckboxStatus.current.infoChecked &&
      checkboxStatus.manualClear === initCheckboxStatus.current.manualClear &&
      checkboxStatus.autoClear === initCheckboxStatus.current.autoClear &&
      checkboxStatus.fmAlarmsChecked === initCheckboxStatus.current.fmAlarmsChecked &&
      checkboxStatus.alarmRuleName === initCheckboxStatus.current.alarmRuleName
    );
    if (textChangeInitFlag.current) {
      if (!alarmNameRef.current.validate()) {
        return true;
      }
    }
    return (selectCheckedFalg2 && isEqual && isEqual2) || selectCheckedFalg;
  }, [checkboxStatus, trustDataset, blackDataset]);
  useEffect(() => {
    if (clickData.operation === 'add') {
      setCheckboxStatus({
        fmAlarmsChecked: true,
        alarmTrustlistChecked: true,
      });
      return;
    }
    let param = {
      solutionId,
      modelId: clickData.data.modelId,
      listeningScope: clickData.data.listeningScope,
      alarmRuleId: clickData.data.alarmRuleId,
    };
    let initStatus = {};
    queryalarmruledetail(param, (data) => {
      if (data && data.resultCode === 0) {
        initStatus = {
          selectChecked: clickData.data.listeningScope,
          fullAlarmsChecked: data.data.alarmFilterType === 2,
          fmAlarmsChecked: data.data.thresholdAlarmOpen,
          alarmLevelChecked: data.data.alarmSeverities.length !== 0,
          criticalChecked: data.data.alarmSeverities.includes(1),
          majorChecked: data.data.alarmSeverities.includes(2),
          minorChecked: data.data.alarmSeverities.includes(3),
          infoChecked: data.data.alarmSeverities.includes(4),
          alarmClearChecked: data.data.alarmClearTypes.length !== 0,
          alarmInfoChecked: data.data.conditionWhiteItems.length !== 0,
          manualClear: data.data.alarmClearTypes.includes(2) || data.data.alarmClearTypes.includes(0),
          autoClear: data.data.alarmClearTypes.includes(1) || data.data.alarmClearTypes.includes(0),
          alarmTrustlistChecked: data.data.conditionWhiteItems.length !== 0 ||
            data.data.alarmClearTypes.length !== 0 ||
            data.data.alarmSeverities.length !== 0 ||
            data.data.thresholdAlarmOpen,
          alarmBlocklistChecked: data.data.conditionBlackItems.length !== 0,
          alarmRuleName: clickData.data.alarmRuleName,
        };
      }

      initCheckboxStatus.current = deepClone(initStatus);
      setCheckboxStatus({
        ...deepClone(initStatus),
      });
      alarmBlackIdsRef.current = data.data.conditionBlackItems;
      alarmWhiteIdsRef.current = data.data.conditionWhiteItems;
      if (data.data.conditionWhiteItems.length !== 0) {
        let trustData = data.data.conditionWhiteItems.map((item, index) => {
          return {
            key: `white${index}`,
            alarmName: $t('alarm.edit.alarm.policy.alarm.select.relation1'),
            alarmId: item.conditionValue,
          };
        });
        setTrustDataset(trustData);
      }
      if (data.data.conditionBlackItems.length !== 0) {
        let blackData = data.data.conditionBlackItems.map((item, index) => {
          return {
            key: `black${index}`,
            alarmName: $t('alarm.edit.alarm.policy.alarm.select.relation1'),
            alarmId: item.conditionValue,
          };
        });
        setBlackDataset(blackData);
      }
    });
  }, []);
  let options = [{text: 'Member', value: 1}, {text: 'Timeline', value: 0}];
  const addAlarmDetail = () => {
    addalarmrule(getParam(), (data) => {
      if (data && data.resultCode !== -1) {
        notification.info($t('alarm.edit.alarm.add.sucess'));
        close();
      } else {
        notification.info(data.resultMessage);
      }
    });
  };

  const getParam = () => {
    let alarmSeverities = [];
    let alarmClearTypes = -1;
    if (checkboxStatus.criticalChecked) {
      alarmSeverities.push(1);
    }
    if (checkboxStatus.majorChecked) {
      alarmSeverities.push(2);
    }
    if (checkboxStatus.minorChecked) {
      alarmSeverities.push(3);
    }
    if (checkboxStatus.infoChecked) {
      alarmSeverities.push(4);
    }

    if (checkboxStatus.manualClear) {
      alarmClearTypes = 2;
    }
    if (checkboxStatus.autoClear) {
      alarmClearTypes = 1;
    }
    if (checkboxStatus.manualClear && checkboxStatus.autoClear) {
      alarmClearTypes = 0;
    }

    let alarmConditionWhiteItems = [];
    if (trustDataset.length > 0) {
      alarmConditionWhiteItems = trustDataset.map((item) => {
        return {
          conditionRule: 'IS IN',
          conditionColumn: 'alarmId',
          conditionValue: item.alarmId,
        };
      });
    }
    let alarmConditionBlackItems = [];
    if (blackDataset.length > 0) {
      alarmConditionBlackItems = blackDataset.map((item) => {
        return {
          conditionRule: 'IS IN',
          conditionColumn: 'alarmId',
          conditionValue: item.alarmId,
        };
      });
    }
    let param = {
      alarmRuleName: checkboxStatus.alarmRuleName,
      solutionId,
      listeningScope: checkboxStatus.selectChecked,
      alarmSeverities,
      thresholdsAlarmOpen: checkboxStatus.fmAlarmsChecked,
      alarmConditionWhiteItems,
      alarmConditionBlackItems,
    };
    if (clickData.operation === 'modify') {
      param.alarmRuleId = clickData.data.alarmRuleId;
    }
    if (alarmClearTypes !== -1) {
      param.alarmClearTypes = alarmClearTypes;
    }
    if (checkboxStatus.fullAlarmsChecked) {
      param.alarmFilterType = 2;
    }
    return param;
  };

  const modifyAlarmDetail = () => {
    modifyalarmrule(getParam(), (data) => {
      // 修改
      if (data && data.resultCode !== -1) {
        notification.info($t('alarm.edit.alarm.modify.sucess'));
      } else {
        notification.info(data.resultMessage);
      }
    });
  };
  return (
    <>
      <div className={styles.topo_alarm_right} id="rightDiv">
        <div className={styles.rightTitle}>{$t('alarm.edit.alarm.config.rule')}</div>
        <div className={styles.rightContent}>
          <ul>
            <li>
              <div style={{paddingLeft: '18px', marginBottom: '1rem'}}>
                <TextField label={$t('alarm.name')}
                  required={true}
                  hintType="tip"
                  labelStyle={{display: 'inline-block', width: '50px', marginRight: isZh ? '24px' : '53px'}}
                  validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 20)}
                  inputStyle={{
                    width: '300px',
                  }}
                  value={checkboxStatus.alarmRuleName}
                  onChange={(value) => {
                    textChangeInitFlag.current = true;
                    setCheckboxStatus({
                      ...checkboxStatus,
                      alarmRuleName: value,
                    });
                  }}
                  ref={ref => alarmNameRef.current = ref}
                />
              </div>
            </li>
            <li>
              <div style={{marginLeft: '1rem', position: 'relative'}}>
                <LabelField text={$t('alarm.range')} required={true} />
                <Select value={checkboxStatus.selectChecked}
                  options={options} onChange={(value) => {
                    setCheckboxStatus({
                      ...checkboxStatus,
                      selectChecked: value,
                    });
                  }}
                />
              </div>
            </li>
            <li>
              <Checkbox value="1" label={$t('alarm.edit.full.alarm')} checked={checkboxStatus.fullAlarmsChecked}
                style={style}
                tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                onChange={() => {
                  setCheckboxStatus({
                    ...checkboxStatus,
                    fullAlarmsChecked: !checkboxStatus.fullAlarmsChecked,
                  });
                }}
              />
            </li>
            <li>
              <Checkbox value="5" label={$t('alarm.edit.alarm.trustlist')}
                checked={checkboxStatus.alarmTrustlistChecked} style={style}
                tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                onChange={() => {
                  if (checkboxStatus.alarmTrustlistChecked) {
                    setCheckboxStatus({
                      ...checkboxStatus,
                      alarmTrustlistChecked: false,
                      criticalChecked: false,
                      majorChecked: false,
                      minorChecked: false,
                      infoChecked: false,
                      alarmLevelChecked: false,
                      fmAlarmsChecked: false,
                      alarmInfoChecked: false,
                      alarmClearChecked: false,
                      manualClear: false,
                      autoClear: false,
                    });
                    setTrustDataset([]);
                  } else {
                    setCheckboxStatus({
                      ...checkboxStatus,
                      alarmTrustlistChecked: !checkboxStatus.alarmTrustlistChecked,
                    });
                  }
                }}
              />
              {checkboxStatus.alarmTrustlistChecked &&
                <div style={{marginLeft: '2rem'}}>
                  <div>
                    <Checkbox value="3" label={$t('alarm.edit.alarm.policy.alarm')}
                      checked={checkboxStatus.alarmLevelChecked}
                      style={style}
                      tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                      onChange={() => {
                        if (checkboxStatus.alarmLevelChecked) {
                          setCheckboxStatus({
                            ...checkboxStatus,
                            criticalChecked: false,
                            majorChecked: false,
                            minorChecked: false,
                            infoChecked: false,
                            alarmLevelChecked: false,
                          });
                        } else {
                          setCheckboxStatus({
                            ...checkboxStatus,
                            alarmLevelChecked: !checkboxStatus.alarmLevelChecked,
                          });
                        }
                      }}
                    />
                  </div>
                  <div>
                    {checkboxStatus.alarmLevelChecked &&
                      <div style={{marginLeft: '2rem'}}>
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.alarm.critical')}
                          checked={checkboxStatus.criticalChecked} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              criticalChecked: !checkboxStatus.criticalChecked,
                            });
                          }}
                        />
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.alarm.major')}
                          checked={checkboxStatus.majorChecked} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              majorChecked: !checkboxStatus.majorChecked,
                            });
                          }}
                        />
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.alarm.minor')}
                          checked={checkboxStatus.minorChecked} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              minorChecked: !checkboxStatus.minorChecked,
                            });
                          }}
                        />
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.alarm.info')}
                          checked={checkboxStatus.infoChecked} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              infoChecked: !checkboxStatus.infoChecked,
                            });
                          }}
                        />
                      </div>}
                    <div>
                      <Checkbox value="4" label={$t('alarm.edit.alarm.policy.clear.alarm')}
                        checked={checkboxStatus.alarmClearChecked} style={style}
                        tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                        onChange={() => {
                          if (checkboxStatus.alarmClearChecked) {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              alarmClearChecked: false,
                              manualClear: false,
                              autoClear: false,
                            });
                          } else {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              alarmClearChecked: !checkboxStatus.alarmClearChecked,
                            });
                          }
                        }}
                      />
                    </div>

                    {checkboxStatus.alarmClearChecked &&
                      <div style={{marginLeft: '2rem'}}>
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.manual.clear.alarm')}
                          checked={checkboxStatus.manualClear} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              manualClear: !checkboxStatus.manualClear,
                            });
                          }}
                        />
                        <Checkbox value="2" label={$t('alarm.edit.alarm.policy.auto.clear.alarm')}
                          checked={checkboxStatus.autoClear} style={style}
                          tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                          onChange={() => {
                            setCheckboxStatus({
                              ...checkboxStatus,
                              autoClear: !checkboxStatus.autoClear,
                            });
                          }}
                        />
                      </div>}
                  </div>
                  <div>
                    <Checkbox value="2" label={$t('alarm.edit.fmOrIntelligent.alarm')}
                      checked={checkboxStatus.fmAlarmsChecked} style={style}
                      tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                      onChange={() => {
                        setCheckboxStatus({
                          ...checkboxStatus,
                          fmAlarmsChecked: !checkboxStatus.fmAlarmsChecked,
                        });
                      }}
                    />
                  </div>
                  <div>
                    <Checkbox value="4" label={$t('alarm.edit.alarm.policy.alarm.id')}
                      checked={checkboxStatus.alarmInfoChecked} style={style}
                      tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                      onChange={() => {
                        if ('alarm.edit.alarm.policy.alarm.id') {
                          setTrustDataset([]);
                        }
                        setCheckboxStatus({
                          ...checkboxStatus,
                          alarmInfoChecked: !checkboxStatus.alarmInfoChecked,
                        });
                      }}
                    />
                  </div>
                  {checkboxStatus.alarmInfoChecked &&
                    <div>
                      <div style={{
                        display: 'flex',
                        'flex-direction': 'row-reverse',
                        paddingBottom: '1rem',
                      }}
                      >
                        <Button
                          text={$t('alarm.edit.add')}
                          disabled={trustDataset.length >= 1}
                          status="primary"
                          size="small"
                          style={btnStyle}
                          onClick={() => {
                            setRecord('');
                            setShowModel(true);
                            setDataset(trustDataset);
                            clickModelRef.current = 'trust';
                          }}
                        />
                        <Button
                          text={$t('alarm.edit.delete')}
                          tipShow="overflow"
                          size="small"
                          style={btnStyle}
                          disabled={trustSelect.length === 0}
                          onClick={() => {
                            removeOrignTableData(trustSelect);
                          }}
                        />
                      </div>
                      <div className='topoNotPagingSelect2'>
                        <TablePro
                          columnResizable={false}
                          columns={trustColumns}
                          dataset={trustDataset}
                          pagination
                          selection={{
                            type: 'checkbox',
                            disabelCheckAssociated: true,
                            getCheckboxProps: (record2) => ({
                              name: record2.name,
                              disabled: record2.name === 'Disabled User',
                            }),
                            onChange: (selectedRowKeys, selectedRows) => {
                              setTrustSelect(selectedRows);
                            },
                          }}
                        />
                      </div>
                    </div>}
                </div>}
            </li>
            <li>
              <Checkbox value="5" label={$t('alarm.edit.alarm.blocklist')}
                checked={checkboxStatus.alarmBlocklistChecked} style={style}
                tipData={{disposeTimeOut: 3000}} labelClassName={styles.checkBoxLabel}
                onChange={() => {
                  if (checkboxStatus.alarmBlocklistChecked) {
                    setBlackDataset([]);
                  }
                  setCheckboxStatus({
                    ...checkboxStatus,
                    alarmBlocklistChecked: !checkboxStatus.alarmBlocklistChecked,
                  });
                }}
              />
              {checkboxStatus.alarmBlocklistChecked &&
                <div style={{marginLeft: '2rem'}}>
                  <div style={{
                    display: 'flex',
                    'flex-direction': 'row-reverse',
                    paddingBottom: '1rem',
                  }}
                  >
                    <Button
                      text={$t('alarm.edit.add')}
                      status="primary"
                      size="small"
                      style={btnStyle}
                      disabled={blackDataset.length >= 1}
                      onClick={() => {
                        setRecord('');
                        setShowModel(true);
                        setDataset(blackDataset);
                        clickModelRef.current = 'black';
                      }}
                    />
                    <Button
                      text={$t('alarm.edit.delete')}
                      tipShow="overflow"
                      size="small"
                      style={btnStyle}
                      disabled={blackSelect.length === 0}
                      onClick={() => {
                        removeOrignTableData(blackSelect);
                      }}
                    />
                  </div>
                  <div className='topoNotPagingSelect2'>
                    <TablePro
                      columnResizable={false}
                      columns={balckColumns}
                      dataset={blackDataset}
                      pagination
                      selection={{
                        type: 'checkbox',
                        disabelCheckAssociated: true,
                        getCheckboxProps: (record2) => ({
                          name: record2.name,
                          disabled: record2.name === 'Disabled User',
                        }),
                        onChange: (selectedRowKeys, selectedRows) => {
                          setBlackSelect(selectedRows);
                        },
                      }}
                    />
                  </div>
                </div>}
            </li>
            <li>
              <Button
                text={$t('alarm.edit.application')}
                status="primary"
                disabled={chekcedChanage}
                style={{marginTop: '20px'}}
                onClick={
                  debounce(() => {
                    if (clickData.operation === 'modify') {
                      modifyAlarmDetail();
                    } else {
                      addAlarmDetail();
                    }
                  }, 600)

                }
              />
              <Button
                text={$t('alarm.close')}
                style={{marginTop: '20px', marginLeft: '20px'}}
                onClick={close}
              />
            </li>
          </ul>
        </div>
      </div>
      {showModel &&
        <AlarmSelect
          showModel={showModel}
          setShowModel={setShowModel}
          setDataset={setDataset}
          modifyRecord={record}
        />}
    </>
  );
};

export default AlarmConfigDetail;