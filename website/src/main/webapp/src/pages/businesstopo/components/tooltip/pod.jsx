/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React, {useEffect, useRef} from 'react';
import '@pages/businesstopo/css/tooltip/pod.less';
import {$t, registerResource, isZh} from '@util';
import PropTypes from 'prop-types';

function Pod(props) {
  let {data} = props;
  let content = props.data.isSupportGray ? $t('suport.grayscale') : $t('notsuport.grayscale');
  function getTotalName() {
    // 当前是是数据库应用 或者是虚拟机
    if (props.data.applicationType === 3 || props.data.applicationType === 4 || props.data.environmentType === 1) {
      return $t('total.instances');
    } else {
      return $t('total.pod');
    }
  }

  function getErrorName() {
    // 当前是是数据库应用 或者是虚拟机
    if (props.data.applicationType === 3 || props.data.applicationType === 4 || props.data.environmentType === 1) {
      return $t('error.instances');
    } else {
      return $t('error.pod');
    }
  }

  function getRightEm() {
    // 当前是是数据库应用 或者是虚拟机
    if (props.data.applicationType === 3 || props.data.applicationType === 4 || props.data.environmentType === 1) {
      if (isZh) {
        return '4em';
      } else {
        return '6em';
      }
    } else {
      return '4.5em';
    }
  }

  return (
    <div id="pod">
      <div>
        <div style={{display: 'flex', 'justify-content':'space-between'}}>
          <div className="pod-left">{props.data.moTypeName}</div>
          <div>
            {props.suppotGray && <span className={props.data.isSupportGray ? 'supoort-gray' : 'supoort-not-gray'}>{content}</span>}
          </div>
        </div>
        <div style={{display: 'flex', 'margin-top': '1em'}}>
          <div className="pod-left-sec pod-min-font">{getTotalName()}</div>
          <div
            className="pod-min-font"
          >{getErrorName()}
          </div>
        </div>
        <div style={{display: 'flex', 'margin-top': '0.2em'}}>
          <div style={{display: 'flex', 'margin-right': getRightEm()}}>
            <div className="image-container" />
            <div className="text-container">
              {data.podCount}
            </div>
          </div>
          <div style={{display: 'flex'}}>
            <div className="image-container_error" />
            <div className={data.podErrorCount > 0 ? 'text-container-error' : 'text-container'}>
              {data.podErrorCount}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Pod.propTypes = {
  data: PropTypes.shape({
    isSupportGray: PropTypes.string,
    moTypeName: PropTypes.string,
    applicationType: PropTypes.number,
  }).isRequired,
};
export default Pod;
