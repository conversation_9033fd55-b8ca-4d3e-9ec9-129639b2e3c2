import React, {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {$t, registerResource} from '@util';
import ButtonGroup from '../../../common/ButtonGroup';
import i18n from './locale';
import Pie from '../../../common/Pie';
import Line from '../../../common/Line';
import SelectButtonGroup from '../../../common/SelectButtonGroup';
import AlarmEventPanel from '../../../AlarmEventPanel';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {handleJumpPageData} from '../../../../util';
import {BUSINESS_TOPO_CONTEXT, getHistoryIndexValueList, getName} from '../../../../const';
import {getAlarmData, getAlarmLevelData} from '../../../../api';
import {
  queryMMHistoryLineUrl as queryMMOverViewLineData,
  queryMMTopPie as queryMMOverViewTopData,
} from '../../../../a3dPages/components/MMOverviewPage/api';
import {deepClone, sorter} from '@digitalview/fe-utils';
import {sortHistoryListData} from '../../../../../../commonUtil/tools';

const MENU = {
  THIRD: 0,
  CHANNEL: 1,
  STRIP: 2,
};

const MODEL_TYPE = {
  [MENU.THIRD]: 1,
  [MENU.CHANNEL]: 101,
  [MENU.STRIP]: 2,
};

let _queryTopPieRequestId = 0;
let _queryLineRequestId = 0;
let _queryAlarmRequestId = 0;


const getMostIndicatorList = (dataList) => {
  const counterMap = {};
  const indexMap = {};
  const tempDataList = deepClone(dataList);

  tempDataList.forEach((data, index) => {
    let key = '';
    data.indicatorList.sort((a, b) => sorter(a.indexName, b.indexName));
    data.indicatorList.forEach(v => {
      key = `${key}__${v.moType}__${v.measTypeKey}__${v.measUnitKey}`;
    });

    counterMap[key] = (counterMap[key] || 0) + 1;
    if (!indexMap[key]) {
      indexMap[key] = [];
    }
    indexMap[key].push(index);
  });

  let counerEntries = [...Object.entries(counterMap)];
  counerEntries.sort((a, b) => sorter(a[1], b[1], 'descend'));

  if (counerEntries.length === 0) {
    return [];
  }

  if (counerEntries.length === 1) {
    return dataList[0]?.indicatorList || [];
  }

  if (counerEntries[0][1] > counerEntries[1][1]) {
    return dataList[indexMap[counerEntries[0][0]][0]]?.indicatorList || [];
  }

  return dataList[
    Math.min(
      ...counerEntries
        .filter(v => v[1] === counerEntries[0][1])
        .map(v => v[0])
        .map(v => indexMap[v])
        .flatMap(v => v)
    )]?.indicatorList || [];
};

/**
 *
 * 查询历史数据
 * instaceId
 *  条带 从over里取
 *  渠道三方 从top里取
 * @return {JSX.Element}
 */
const Panel = (props) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    isTimeTrack,
    selectedTime,
    solutionId,
  } = state;

  registerResource(i18n, 'mmRightPanel');

  const {refreshFlag} = props;
  const manageIdListRef = useRef([]);
  const [businessData, setBusinessData] = useState([]);
  useEffect(() => {
    eventBus.addListener('from3d_setGridData', (setting) => {
      setBusinessData(setting);
      manageIdListRef.current = (setting?.strip?.strips?.filter(v => v.isManage) || []).map(v => v.id);
    });
  }, []);

  /**
   * 数据依赖流程
   * A 右侧顶部按钮组 固定写死(靠点击事件或者左侧3D的点击进行切换)
   * B 饼图 依赖 A
   *
   * C 指标展示 依赖 A + 左侧3D 三方的选择
   * D 折线图 依赖C
   *
   * E 告警 全局 依赖 左侧3D 条带点击
   */
  const [chooseMenuId, setChooseMenuId] = useState(MENU.THIRD);
  const topButtonGroupProps = useMemo(() => {
    return [
      {
        id: MENU.THIRD,
        name: $t('mm.thirdPartyServices'),
        onClick: () => {
          setChooseMenuId(MENU.THIRD);
          stripIndicatorRef.current = null;
          eventBus.emit('to3d_clickMenu');
        },
      },
      {
        id: MENU.CHANNEL,
        name: $t('mm.channelBasedBusiness'),
        onClick: () => {
          setChooseMenuId(MENU.CHANNEL);
          stripIndicatorRef.current = null;
          eventBus.emit('to3d_clickMenu');
        },
      },
      {
        id: MENU.STRIP,
        name: $t('mm.systemServices'),
        onClick: () => {
          setChooseMenuId(MENU.STRIP);
          thirdOrChannelIndicatorListRef.current = null;
          eventBus.emit('to3d_clickMenu');
        },
      },
    ];
  }, []);

  // 饼图数据
  const [topPieData, setTopPieData] = useState([]);

  // 当前左侧是否点击了某个节点
  const [selectNode, setSelectNode] = useState({});
  const [stripeId, setStripeId] = useState('');
  useEffect(() => {
    eventBus.addListener('from3d_showPanel', (setting) => {
      switch (setting.type) {
        case 'app':
          setStripeId('');
          setChooseMenuId(MENU.THIRD);
          stripIndicatorRef.current = null;
          dispatch({showKPIPanel: true});
          break;
        case 'channel':
          setStripeId('');
          setChooseMenuId(MENU.CHANNEL);
          stripIndicatorRef.current = null;
          dispatch({showKPIPanel: true});
          break;
        case 'strip':
          setStripeId(setting.id);
          setChooseMenuId(MENU.STRIP);
          thirdOrChannelIndicatorListRef.current = null;
          // 管理类
          if (manageIdListRef.current.includes(setting.id)) {
            stripIndicatorRef.current = null;
          }
          dispatch({showKPIPanel: true});
          break;
        default:
          setStripeId('');
          break;
      }

      setSelectNode({
        ...setting,
      });
    });
  }, []);

  useEffect(() => {
    setSelectNode({}); // 时间回溯 重置选择的节点
  }, [selectedTime]);

  // 展示指标
  const topIndicatorList = useMemo(() => {
    let key = 'thirdParty';
    let tempData = businessData;
    let data = null;
    switch (chooseMenuId) {
      case MENU.THIRD:
        key = 'thirdParty';
        data = getMostIndicatorList(tempData[key] || []);
        break;
      case MENU.CHANNEL:
        key = 'channel';
        data = getMostIndicatorList(tempData[key] || []);
        break;
      case MENU.STRIP:
        data = (businessData?.strip?.strips?.filter(v => !v.isManage) || [])?.[0]?.indicatorList || [];
        break;
      default:
        break;
    }
    if (!data) {
      return [];
    }
    const goldList = data.filter(v => v.indicatorDisplayType === 1);
    const otherList = data.filter(v => v.indicatorDisplayType === 0);
    goldList.sort((a, b) => sorter(a.indexName, b.indexName));
    goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
    otherList.sort((a, b) => sorter(a.indexName, b.indexName));
    otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
    return [...goldList, ...otherList].map((v) => {
      return {
        id: v.indicatorId,
        indexName: v.indexName,
        measUnitKey: v.measUnitKey,
        measTypeKey: v.measTypeKey,
        originalValue: v.originalValue,
        moType: v.moType,
        indicatorDisplayType: v.indicatorDisplayType,
      };
    });
  }, [JSON.stringify(businessData), chooseMenuId]);

  const thirdOrChannelIndicatorListRef = useRef(null);
  const stripIndicatorRef = useRef(null);
  const indicatorList = useMemo(() => {
    if (!(manageIdListRef.current || []).includes(selectNode.id) && stripIndicatorRef.current) {
      return stripIndicatorRef.current.map(v => {
        return {
          id: v.id,
          indexName: v.indexName,
          measUnitKey: v.measUnitKey,
          measTypeKey: v.measTypeKey,
          originalValue: v.originalValue,
          moType: v.moType,
          indicatorDisplayType: v.indicatorDisplayType,
        };
      });
    }

    // 判断当前点击节点的指标，如果没有，则展示TOP的
    if (Boolean(selectNode.type) && selectNode.indicatorList) {
      const tempIndicatorList = selectNode.indicatorList;
      const goldList = tempIndicatorList.filter(v => v.indicatorDisplayType === 1);
      const otherList = tempIndicatorList.filter(v => v.indicatorDisplayType === 0);
      goldList.sort((a, b) => sorter(a.indexName, b.indexName));
      goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
      otherList.sort((a, b) => sorter(a.indexName, b.indexName));
      otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
      return [...goldList, ...otherList].map(v => {
        return {
          instanceId: v.instanceId,
          id: v.indicatorId,
          indexName: v.indexName,
        };
      });
    }

    return topIndicatorList;
  }, [JSON.stringify(topIndicatorList), JSON.stringify(selectNode), JSON.stringify(topPieData)]);

  // 指标数据格式
  const [chooseIndicatorId, setChooseIndicatorId] = useState({});
  const indicatorData = useMemo(() => {
    // 默认设置第0个id
    if (indicatorList.length > 0) {
      setChooseIndicatorId({id: indicatorList[0].id || '', instanceId: indicatorList[0].instanceId, index: 0});
    } else {
      setChooseIndicatorId({});
    }

    return indicatorList.map((v, index) => {
      return {
        id: `${v.id}~~${v.instanceId}`,
        name: v.indexName,
        onClick: () => {
          setChooseIndicatorId({id: v.id, instanceId: v.instanceId, index});
        },
      };
    });
  }, [JSON.stringify(indicatorList)]);

  // 请求TOP数据方法
  const reqPiePending = useRef(false); // 正在请求

  const getTopPieData = useCallback((param) => {
    const requestId = ++_queryTopPieRequestId;
    reqPiePending.current = true;
    queryMMOverViewTopData(param)
      .then((data) => {
        // 多次请求 只处理最后一次的
        if (requestId !== _queryTopPieRequestId) {
          return;
        }
        // 处理一下data
        // 设置TOP饼图
        const result = Object.entries(data?.data?.topIndicator || {}).reduce((curr, [key, value]) => {
          const obj = JSON.parse(key) || {};
          if (Object.keys(obj).length === 0 || value === null) {
            return curr;
          }

          let instanceId = obj.instanceId;
          if (param.modelType === MODEL_TYPE[MENU.STRIP]) {
            // 从overgrid里取
            instanceId = [...new Set((businessData?.strip?.strips?.filter(v => !v.isManage) || []).map(v => v.id))];
          }

          curr.push({
            id: `${obj.instanceId || ''}~~${obj.modelId || ''}~~${obj.modelName || ''}` || '',
            name: obj.modelName || '',
            value,
            instanceId,
            originalValue: obj.originalValue,
          });
          return curr;
        }, []);
        result.sort((a, b) => sorter(b.value, a.value));

        // 渠道和三方需要保留indicatorList
        if (param.modelType === MODEL_TYPE[MENU.THIRD] || param.modelType === MODEL_TYPE[MENU.CHANNEL]) {
          thirdOrChannelIndicatorListRef.current = data?.data?.indicatorList || [];
        } else {
          thirdOrChannelIndicatorListRef.current = null;
        }

        // 按indicatorId进行分组
        if (param.modelType === MODEL_TYPE[MENU.STRIP]) {
          let tempIndicatorList = (data?.data?.indicatorList || []).filter(v => [0, 1].includes(v.indicatorDisplayType));
          const tempIndicatorObj = tempIndicatorList.reduce((curr, next) => {
            if (curr[next.indexName]) {
              curr[next.indexName].instanceId.push(next.instanceId);
            } else {
              curr[next.indexName] = {
                ...next,
                instanceId: [next.instanceId],
              };
            }
            return curr;
          }, {});
          tempIndicatorList = Array.from(Object.values(tempIndicatorObj));
          const goldList = tempIndicatorList.filter(v => v.indicatorDisplayType === 1);
          const otherList = tempIndicatorList.filter(v => v.indicatorDisplayType === 0);
          goldList.sort((a, b) => sorter(a.indexName, b.indexName));
          goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
          otherList.sort((a, b) => sorter(a.indexName, b.indexName));
          otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
          stripIndicatorRef.current = [...goldList, ...otherList].map((v) => {
            return {
              id: v.indicatorId,
              indexName: v.indexName,
              measUnitKey: v.measUnitKey,
              measTypeKey: v.measTypeKey,
              originalValue: result.map(r => r.originalValue).filter(r => r),
              moType: v.moType,
              indicatorDisplayType: v.indicatorDisplayType,
              instanceId: [...new Set(v.instanceId)],
            };
          });
        } else {
          stripIndicatorRef.current = null;
        }

        reqPiePending.current = false;
        setTopPieData(result);
      })
      .catch((e) => {
        // 错误处理
        reqPiePending.current = false;
      });
  }, [JSON.stringify(businessData)]);

  const [pieParam, setPieParam] = useState({});
  useEffect(() => {
    if (!solutionId) {
      return;
    }
    let param = {
      solutionId,

      currentTime: selectedTime || 0,
    };
    param.modelType = MODEL_TYPE[chooseMenuId] || 1;

    if (stripeId) {
      param.modelType = 2; // 有条带id的时候强制置成2因为同时依赖了chooseMenuId，stripeId，但是 stripeId先更新
      if ([...new Set((businessData?.strip?.strips?.filter(v => !v.isManage) || []).map(v => v.id))].includes(stripeId)) {
        param.stripeId = stripeId;
      }
    }

    setPieParam(param);
  }, [chooseMenuId, solutionId, selectedTime, stripeId, JSON.stringify(businessData)]);

  useEffect(() => {
    if (Object.keys(pieParam).length > 0) {
      getTopPieData(pieParam);
    }
  }, [JSON.stringify(pieParam), refreshFlag]);

  // 请求Line数据方法
  const [lineData, setLineData] = useState({});
  const [jumpData, setJumpData] = useState([]);
  const getLineData = useCallback((param) => {
    const requestId = ++_queryLineRequestId;
    queryMMOverViewLineData(param)
      .then((data) => {
        // 多次请求 只处理最后一次的
        if (requestId !== _queryLineRequestId) {
          return;
        }
        // 要处理一下data
        const timeObj = {
          Today: 0,
          Yesterday: 86400000,
          'Last week': 604800000,
        };
        const todayObj = {
          Today: 0,
        };
        let obj;

        const dataList = sortHistoryListData(data?.data?.siteHistoryList || []);
        if (dataList.length > 1) {
          obj = todayObj;
        } else {
          obj = timeObj;
        }

        const seriesData = [];
        dataList.map(item => {
          for (let propName in obj) {
            if (item.comparativeValueMap[propName]) {
              let name = getName(propName);
              if (dataList.length > 1) {
                name = `${item.siteName || ''}`;
              }

              if (item.moName) {
                name = `${item.moName}_${name}`;
              }

              seriesData.push({
                name,
                id: `${name}_${seriesData.length}_${JSON.stringify(param)}`,
                data: getHistoryIndexValueList(item.comparativeValueMap[propName], timeObj[propName]),
              });
            }
          }
        });

        // 设置折线图
        setJumpData(dataList);

        setLineData({
          unit: dataList?.[0]?.indexUnit || '',
          dataList: seriesData,
          time: {
            startTime: dataList?.[0]?.startTime,
            endTime: dataList?.[0]?.endTime,
          },
        } || {});
      })
      .catch((e) => {
        // 错误处理
      });
  }, []);

  const pieMarginBottom = useMemo(() => {
    return '0px';
  }, [topPieData?.length]);

  const alarmMarginTop = useMemo(() => {
    return '-16px';
  }, [lineData?.dataList?.length, indicatorData.length, pieMarginBottom]);

  const [reqLineParam, setReqLineParam] = useState({});
  useEffect(() => {
    if (reqPiePending.current) { // 请求中，等待请求刷新
      return;
    }

    switch (selectNode.type) {
      case 'app':
        if (chooseIndicatorId.id) {
          setReqLineParam({
            businessId: selectNode.id,
            indicatorId: chooseIndicatorId.id,
            currentTime: selectedTime || 0,
            modelType: MODEL_TYPE[chooseMenuId],
          });
        } else {
          setReqLineParam({});
        }
        break;
      case 'channel':
        if (chooseIndicatorId.id) {
          setReqLineParam({
            channelId: selectNode.id,
            indicatorId: chooseIndicatorId.id,
            currentTime: selectedTime || 0,
            modelType: MODEL_TYPE[chooseMenuId],
          });
        } else {
          setReqLineParam({});
        }
        break;
      case 'strip':
        // 条带切换的时候，id是可能匹配不上的，只能用索引去匹配
        if (chooseIndicatorId.index !== undefined && stripIndicatorRef.current) {
          // 条带查折线图数据 instanceIds 使用overviewgrid里的数据, 其他数据使用top返回的数据
          setReqLineParam({
            moType: stripIndicatorRef.current[chooseIndicatorId.index]?.moType || '',
            measUnitKey: stripIndicatorRef.current[chooseIndicatorId.index]?.measUnitKey || '',
            measTypeKey: stripIndicatorRef.current[chooseIndicatorId.index]?.measTypeKey || '',
            indicatorDisplayType: stripIndicatorRef.current[chooseIndicatorId.index]?.indicatorDisplayType || 0,
            currentTime: selectedTime || 0,
            modelType: MODEL_TYPE[chooseMenuId],
            instanceIds: [selectNode.id],
            originalValues: topPieData.map(v => v.originalValue).flatMap(v => v).filter(v => v),
          });
        } else {
          setReqLineParam({});
        }
        break;
      default:
        if (stripIndicatorRef.current) {
          setReqLineParam({
            moType: stripIndicatorRef.current[chooseIndicatorId.index]?.moType || '',
            measUnitKey: stripIndicatorRef.current[chooseIndicatorId.index]?.measUnitKey || '',
            measTypeKey: stripIndicatorRef.current[chooseIndicatorId.index]?.measTypeKey || '',
            indicatorDisplayType: stripIndicatorRef.current[chooseIndicatorId.index]?.indicatorDisplayType,
            currentTime: selectedTime || 0,
            modelType: MODEL_TYPE[chooseMenuId],
            instanceIds: [...new Set(topPieData.map(v => v.instanceId).flatMap(v => v))],
            originalValues: topPieData.map(v => v.originalValue).flatMap(v => v).filter(v => v),
          });
        } else {
          if (chooseIndicatorId.id) {
            const indicator = topIndicatorList.find(v => v.id === chooseIndicatorId.id);
            setReqLineParam({
              moType: indicator?.moType || '',
              measUnitKey: indicator?.measUnitKey || '',
              measTypeKey: indicator?.measTypeKey || '',
              indicatorDisplayType: indicator?.indicatorDisplayType,
              currentTime: selectedTime || 0,
              modelType: MODEL_TYPE[chooseMenuId],
              instanceIds: (chooseIndicatorId.index === 0 && [...new Set(topPieData.map(v => v.instanceId).flatMap(v => v))]) || [solutionId],
              originalValues: topPieData.map(v => v.originalValue).flatMap(v => v).filter(v => v),
              indicatorList: (
                chooseIndicatorId.index === 0 &&
                thirdOrChannelIndicatorListRef.current &&
                JSON.stringify(thirdOrChannelIndicatorListRef.current)
              ) || undefined,
            });
          } else {
            setReqLineParam({});
          }
        }
        break;
    }
  }, [JSON.stringify(chooseIndicatorId), topPieData, selectedTime, JSON.stringify(topIndicatorList), solutionId]);

  useEffect(() => {
    if (Object.keys(reqLineParam).length > 0) {
      if (reqLineParam.moType === '') {
        return;
      }
      getLineData(reqLineParam);
    }
  }, [JSON.stringify(reqLineParam), refreshFlag]);

  useEffect(() => {
    // 告警数据
    if (!solutionId) {
      return;
    }
    let alarmParam = {
      instanceId: (selectNode.id) || solutionId,
    };

    if (selectedTime) {
      alarmParam.timestamp = selectedTime;
    }

    const requestId = ++_queryAlarmRequestId;
    if (!selectNode.id && chooseMenuId === MENU.THIRD) { // 只点了三方的菜单
      alarmParam.insLevel = MODEL_TYPE[MENU.THIRD];
      alarmParam.instanceId = solutionId;
      getAlarmLevelData(
        alarmParam,
        res => {
          // 多次请求 只处理最后一次的
          if (requestId !== _queryAlarmRequestId) {
            return;
          }

          let alarmDatas = res.data;
          dispatch({
            alarmEventData: alarmDatas.alarmDataList,
          });
        },
      );
    } else if (!selectNode.id && chooseMenuId === MENU.CHANNEL) { // 只点了渠道的菜单
      alarmParam.insLevel = MODEL_TYPE[MENU.CHANNEL];
      alarmParam.instanceId = solutionId;
      getAlarmLevelData(
        alarmParam,
        res => {
          // 多次请求 只处理最后一次的
          if (requestId !== _queryAlarmRequestId) {
            return;
          }

          let alarmDatas = res.data;
          dispatch({
            alarmEventData: alarmDatas.alarmDataList,
          });
        },
      );
    } else {
      getAlarmData(
        alarmParam,
        res => {
          // 多次请求 只处理最后一次的
          if (requestId !== _queryAlarmRequestId) {
            return;
          }

          let alarmDatas = res.data;
          dispatch({
            alarmEventData: alarmDatas.alarmDataList,
          });
        },
      );
    }
  }, [selectNode.id || chooseMenuId, selectedTime, isTimeTrack, solutionId, refreshFlag]);

  return (
    <div
      style={{
        marginTop: '-3.75rem',
        padding: '0 1rem',
      }}
    >
      <ButtonGroup
        {
        ...{
          editShowItem: false,
          style: {
            fontSize: '16px',
            lineHeight: '3rem',
            zIndex: '11',
            position: 'relative',
          },
          itemStyle: {
            flex: 1,
          },
          data: topButtonGroupProps,
          activateIndex: chooseMenuId,
        }
        }
      />

      <Pie
        {
        ...{
          title: '',
          data: topPieData,
          centerValue: String(topPieData.length || 'NA'),
          centerDesc: 'Top',
          style: {
            height: '290px',
            minHeight: '290px',
            marginTop: '-25px',
            marginBottom: pieMarginBottom,
            zIndex: '10',
            position: 'relative',
          },
        }
        }
      />
      {
        indicatorData.length > 0 && (
          <>
            <SelectButtonGroup
              {
              ...{
                data: indicatorData,
              }
              }
            />

            <Line
              data={lineData.dataList || []}
              unit={lineData.unit || ''}
              reqLineParam={reqLineParam}
              style={{
                height: '220px',
                minHeight: '220px',
                zIndex: '9',
                position: 'relative',
              }}
              onClick={(param, echartsIns) => {
                const pointInPixel = [param.offsetX, param.offsetY];
                if (!echartsIns.containPixel('grid', pointInPixel)) {
                  return;
                }
                handleJumpPageData(jumpData);
              }}
              time={lineData.time}
            />
          </>
        )
      }

      <div style={{
        marginTop: alarmMarginTop,
      }}
      >
        <AlarmEventPanel />
      </div>
    </div>
  );
};

export {
  Panel,
};
