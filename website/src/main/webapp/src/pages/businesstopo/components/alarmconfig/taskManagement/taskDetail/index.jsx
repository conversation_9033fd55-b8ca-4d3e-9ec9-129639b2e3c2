import React, { useContext, useState } from 'react';
import { Tab, TabItem } from 'eview-ui';
import styles from '../../index.less';
import { $t } from '@util';
import {
  TaskManageContext,
  TASK_PAGE_TYPE,
  TASK_DETAIL_TAB,
} from '../../const';
import { cancelCreateOrEditTask } from '../NewOrEditCommonUtils';

const TaskDetail = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const [selectedTab, setSelectedTab] = useState(0);

  const backTaskList = () => {
    cancelCreateOrEditTask(dispatch);
  };

  const switchTabItem = (index, title, event) => {
    if (index !== selectedTab) {
      cancelCreateOrEditTask(dispatch, false);
    }
    setSelectedTab(index);
  };

  return (
    <>
      <div className={styles.taskTopTitleContainer}>
        <div className={styles.backIcon} onClick={backTaskList} />
        <div>{$t('intelligent.incident.task.manage.task.detail')}</div>
      </div>
      <div className={styles.bottomPanelContainer} style={{paddingLeft: '6.25rem'}}>
        <Tab lazyLoad={true}
          draggable={false}
          selectedIndex={selectedTab}
          onClick={switchTabItem}
        >
          {
            TASK_DETAIL_TAB.map((item, keyIndex) => (
              <TabItem title={item.title} closable={false} key={keyIndex}>
                <item.component />
              </TabItem>
            ))
          }
        </Tab>        
      </div>
    </>
  );
};

export default TaskDetail;
