import {formatDate2SysSetting, sorter} from '@digitalview/fe-utils';
import React, {useEffect, useState, useRef, useContext} from 'react';
import {TablePro, Button, Toggle} from 'eview-ui';
import AlarmConfigDetail from './AlarmConfigDetail';
import {registerResource, $t} from '@util';
import i18n from './local';
import {deletealarmrule, queryalarmrules, refreshbusinesstopoalarm, startalarmrule} from './api';
import {getSolutionData} from '../../api';
import styles from './index.less';
import { modal, notification, setHelpId} from '@util/index';
import {GlobalContext} from './const';

const AlarmConfig = () => {
  const notAllowClcikStyle = {
    'pointer-events': 'none',
    color: 'gray',
    'text-decoration': 'none',
    cursor: 'not-allowed',
    marginRight: '1.5rem',
  };

  const allowClcikStyle = {
    marginRight: '1.5rem',
  };
  registerResource(i18n, 'alarmConfig');
  setHelpId('com.huawei.dvtopo.config');
  const { theme } = useContext(GlobalContext);

  // 告警展示配置表格的列
  const columns = [
    {
      title: $t('alarm.rule.name'),
      width: 220,
      key: 'alarmRuleName',
      dataIndex: 'alarmRuleName',
      sorter: (a, b) => sorter(a.alarmRuleName, b.alarmRuleName),
      render: (text, record) => (
        <a
          onClick={() => {
            setClickData({
              operation: 'modify',
              data: record,
            });
            setShowDefault(false);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: $t('alarm.range'),
      width: 220,
      key: 'alarmRange',
      dataIndex: 'alarmRange',
      sorter: (a, b) => sorter(a.alarmRange, b.alarmRange),
      render: (text, record) => (
        <div>
          {text}
        </div>
      ),
    },
    {
      title: $t('alarm.modify.person'),
      width: 220,
      key: 'modifyPerson',
      dataIndex: 'modifyPerson',
      sorter: (a, b) => sorter(a.modifyPerson, b.modifyPerson),
    },
    {
      title: $t('alarm.modify.time'),
      width: 220,
      key: 'modifyTime',
      dataIndex: 'modifyTime',
      sorter: (a, b) => sorter(a.modifyTime, b.modifyTime),
    },
    {
      title: $t('alarm.edit.operation'),
      width: 220,
      key: 'operation',
      render: (text, record) => (
        <div>
          <a
            onClick={() => {
              setClickData({
                operation: 'copy',
                data: record,
              });
              setShowDefault(false);
            }}
            style={allowClcikStyle}
          >
            {$t('alarm.edit.operation.copy')}
          </a>
          <a
            style={record.isPreset ? notAllowClcikStyle : allowClcikStyle}
            onClick={() => {
              let param = {
                solutionId: solutionRef.current,
                modelId: record.modelId,
                listeningScope: record.listeningScope,
                alarmRuleId: record.alarmRuleId,
              };
              deletealarmrule(param, (data) => {
                if (data && data.resultCode !== -1) {
                  notification.info($t('alarm.edit.alarm.delete.sucess'));
                  setDataset(prevData => prevData.filter(item => item.key !== record.key));
                } else {
                  notification.info(data.resultMessage);
                }
              });
            }}
          >
            {$t('alarm.edit.operation.delete')}
          </a>
        </div>
      ),
    },
    {
      title: $t('alarm.edit.rule.status'),
      width: 220,
      key: 'status',
      render: (text, record) => (
        <div>
          <Toggle toggled={record.isValidate} labelStyle={{marginLeft: '0.5rem'}}
            label={record.isValidate ? $t('alarm.edit.alarm.enable') : $t('alarm.edit.alarm.disabled')}
            labelPosition='after' onToggle={() => {
              let param = {
                solutionId: solutionRef.current,
                alarmRuleId: record.alarmRuleId,
                listeningScope: record.listeningScope,
                isValidate: !record.isValidate,
              };
              startalarmrule(param);
              setDataset(prevData =>
                prevData.map(item => {
                  if (item.key === record.key) {
                    return {...item, isValidate: !item.isValidate};
                  }
                  if (item.listeningScope === record.listeningScope) {
                    return {...item, isValidate: false};
                  }
                  return item;
                }
                )
              );
              notification.info($t('alarm.edit.alarm.enable.tips'));
            }}
          />
        </div>
      ),
    },
  ];
  const [dataset, setDataset] = useState([]);
  const [showDefault, setShowDefault] = useState(true);
  let [clickData, setClickData] = useState({});
  const solutionRef = useRef('');
  useEffect(() => {
    if (!showDefault) {
      return;
    }
    let param = {
      timestamp: 0,
    };
    getSolutionData(param, res => {
      if (res.data.length > 0) {
        solutionRef.current = res.data[0].solutionId;
        queryalarmrules({
          solutionId: res.data[0].solutionId,
        }, (data) => {
          if (data && data.resultCode === 0) {
            data.data.sort((a, b) => sorter(a.lastModifyTime, b.lastModifyTime, 'descend'));
            data.data.sort((a, b) => sorter(a.isValidate, b.isValidate, 'descend'));
            const convertedArray = data.data.map((item, index) => ({
              key: index,
              alarmRange: item.listeningScope === 0 ? 'Timeline' : 'Member',
              modelId: item.modelId,
              modifyPerson: item.modifyUser,
              isValidate: item.isValidate,
              modifyTime: item.lastModifyTime ? formatDate2SysSetting(item.lastModifyTime) : 'NA',
              listeningScope: item.listeningScope,
              alarmRuleId: item.alarmRuleId,
              alarmRuleName: item.alarmRuleName,
              isPreset:item.isPreset,
            }));
            setDataset(convertedArray);
          }
        });
      }
    });
  }, [showDefault]);

  return (
    <div className={styles.alarmEditContainer}>
      <div className={styles.topo_alarm_right} style={{ display: showDefault ? 'block' : 'none'}}>
        <div style={{
          display: 'flex',
          'flex-direction': 'row-reverse',
          padding: '1rem',
        }}
        >
          <Button
            text={$t('alarm.close')}
            tipShow='overflow'
            size='small'
            style={{
              marginRight: '1rem',
              marginTop: '0.5rem',
            }}
            onClick={() => {
              location.replace('/eviewwebsite/index.html#path=/businesstopo');
            }}
          />
          <Button
            text={$t('alarm.edit.refresh')}
            tipShow='overflow'
            size='small'
            style={{
              marginRight: '1rem',
              marginTop: '0.5rem',
            }}
            onClick={() => {
              modal.confirm($t('sys_confirm_content'), () => {
                refreshbusinesstopoalarm((data) => {
                  if (data && data.resultCode !== -1) {
                    notification.info($t('alarm.start.refresh'));
                  }
                });
              }, null, null, theme
              );
            }}
          />
          <Button
            text={$t('alarm.edit.add')}
            status='primary'
            size='small'
            style={{
              marginRight: '1rem',
              marginTop: '0.5rem',
            }}
            disabled={dataset.length >= 10}
            onClick={() => {
              setClickData({
                operation: 'add',
                data: {},
              });
              setShowDefault(false);
            }}
          />
        </div>
        <div className='bottomPanelContainer'>
          <div className='topoNotPagingSelect2'>
            <TablePro
              columnResizable={false}
              columns={columns}
              dataset={dataset}
              pagination
            />
          </div>
        </div>
      </div>
      {!showDefault &&
        <AlarmConfigDetail 
          close={() => {
            setShowDefault(true);
          }}
          clickData={clickData}
          solutionId={solutionRef.current}
        />}
    </div>
  );
};

export default AlarmConfig;