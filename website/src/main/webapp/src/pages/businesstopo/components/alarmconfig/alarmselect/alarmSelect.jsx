import React, {useRef, useState} from 'react';
import {Dialog, LabelField, Select, TextField} from 'eview-ui';
import {$t} from '@util';

const AlarmSelect = (props) => {
  const {showModel, setShowModel, setDataset, modifyRecord} = props;
  const [alarmId, setAlarmId] = useState(modifyRecord);
  const handleContentClose = () => {
    setShowModel(false);
  };

  const handleContentConfirm = () => {
    if (!selectRef.current.validate()) {
      selectRef.current.focus();
      return;
    }
    const newRecord = {alarmName: $t('alarm.edit.alarm.policy.alarm.select.relation1'), alarmId, key: Date.now()};

    setDataset([newRecord]);
    setShowModel(false);
  };
  let option1 = [{text: $t('alarm.edit.alarm.policy.alarm.select.name1'), value: 1}];
  let option2 = [{text: $t('alarm.edit.alarm.policy.alarm.select.relation1'), value: 1}];

  const selectRef = useRef(null);

  const splitString = (str) => {
    // 先按照逗号分割字符串
    const parts = str.split(',');
    // 遍历每个分割后的元素，检查它们的长度
    for (let part of parts) {
      if (part.length > 10) {
        return false; // 如果有任意一个元素长度超过10，返回false
      }
    }

    return true; // 如果所有元素的长度都 <= 10，返回 true
  };

  const validator = (param) => {
    let validationReturnMap = {};
    // 正则表达式：必须以数字开头，后续只允许数字或逗号
    const pattern = new RegExp('^[0-9][0-9,]*$');
    const arr = param.split(','); // 按逗号分隔

    // 校验是否符合要求，且确保没有连续逗号
    const isValid = pattern.test(param) && !param.includes(',,') && !arr.includes('0') && splitString(param);
    validationReturnMap.result = isValid;
    validationReturnMap.message = $t('alarm.edit.alarm.policy.alarm.select.tips');
    return validationReturnMap;
  };
  return (
    <Dialog
      titleStyle={{borderBottom: 'none', cursor: 'move'}}
      style={{height: '17rem', width: '50rem'}}
      isOpen={showModel}
      onClose={() => {
        setShowModel(false);
      }}
      buttons={[{text: $t('alarm.edit.cancel'), onClick: handleContentClose, status: 'primary'}, {
        text: $t('alarm.edit.confirm'),
        onClick: handleContentConfirm,
        status: 'primary',
      }]}
      buttonStyle={{textAlign: 'right'}}
      children={
        <div>
          <div>
            <LabelField text={$t('alarm.edit.alarm.policy.alarm.source')} required={true} />
          </div>
          <div style={{marginTop: '10px'}}>
            <Select options={option1} required={true} style={{paddingLeft: '0px'}}
              defaultLabel={$t('alarm.edit.alarm.policy.alarm.select')} value={1}
            />
            <Select options={option2} required={true} style={{paddingLeft: '5px'}}
              defaultLabel={$t('alarm.edit.alarm.policy.alarm.select')} value={1}
            />
            <TextField style={{paddingLeft: '5px', width: '20rem'}} onChange={(value) => {
              setAlarmId(value);
            }} validator={validator} ref={ref => selectRef.current = ref} value={modifyRecord}
            />
          </div>
        </div>
      }
    />
  );
};

export default AlarmSelect;