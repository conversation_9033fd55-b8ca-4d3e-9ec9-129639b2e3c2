/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useState} from 'react';
import * as echarts from 'echarts';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import {$t} from '@util';
import {getSiteDistritionData} from '@pages/businesstopo/api';

function SiteDistributionPieChart(props) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {siteDistributionData, indicatorData, currentBusinessData} = state;
  const siteNumber = siteDistributionData.length;
  const handleData = () => siteDistributionData.map(item => ({
    value: item.indicatorValue,
    name: `${item.siteName}`,
  }));

  useEffect(() => {
    let chartDom = document.getElementById('pieChartDiv');
    let chartObj = echarts.init(chartDom);
    chartObj.clear();

    chartObj.setOption(siteNumber < 8 ? getFewSiteChartOptions() : getMutiSiteChartOptions());

    // 注册图例点击事件
    chartObj.off('legendselectchanged');
    chartObj.on('legendselectchanged', legendData => {
      let allData = [...siteDistributionData];
      const filterData = allData.filter(item => legendData.selected[`${item.siteName}`]);
      const tps = getSumTPS(filterData);
      chartObj.setOption({
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: '40%',
              style: {
                text: tps,
                fontSize: 32,
                textAlign: 'center',
                lineHeight: 38,
                fill: '#F5F5F5 ',
              },
            },
          ],
        },
      });
    });

    window.addEventListener('resize', () => {
      chartObj.resize();
    });
  }, [siteDistributionData]);

  useEffect(() => {
    let chartDom = document.getElementById('pieChartDiv');
    let chartObj = echarts.getInstanceByDom(chartDom);
    if (chartObj === undefined) {
      return;
    }

    if (state.currentSiteName === '') {
      // 取消效果
      chartObj.dispatchAction({
        type: 'hideTip',
      });
      chartObj.dispatchAction({
        type: 'downplay',
      });
    } else {
      // 高亮和显示tips
      chartObj.dispatchAction({
        type: 'highlight', seriesIndex: 0, name: state.currentSiteName,
      });
      chartObj.dispatchAction({
        type: 'showTip', seriesIndex: 0, name: state.currentSiteName,
      });
    }
  }, [state.currentSiteName]);

  useEffect(() => {
    if (!props.showCharts) {
      return;
    }
    let chartDom = document.getElementById('pieChartDiv');
    let chartObj = echarts.getInstanceByDom(chartDom);
    if (chartObj) {
      chartObj.resize();
    }
  }, [props.showCharts]);

  useEffect(() => {
    if (!currentBusinessData.businessId) {
      return;
    }
    let distritionParam = {
      instanceId: currentBusinessData.businessId,
    };

    // 饼图数据
    getSiteDistritionData(
      distritionParam,
      ({data, resultCode}) => {
        if (resultCode === -1) {
          return;
        }
        dispatch({
          siteDistributionData: data.siteDataList,
        });
      },
    );
  }, [props.refreshFlag]);

  // 计算TPS总和
  const getSumTPS = data => {
    if (!data || data.length === 0) {return 'NA';}

    let sumTps = 0;
    let hasValidValue = false;

    for (const item of data) {
      if (item.indicatorValue !== null) {
        sumTps += item.indicatorValue;
        hasValidValue = true; // At least one valid value found
      }
    }

    if (!hasValidValue) {return 'NA';} // No valid values at all

    if (sumTps > 10000) {
      sumTps = Math.floor(sumTps);
    } else if (String(sumTps).includes('.')) {
      sumTps = sumTps.toFixed(2);
    }

    return sumTps;
  };

  let title = $t('pie.title.distributions');
  let innerTitle = $t('pie.title.tps.number');
  let titleRatio = $t('pie.title.ratio');

  let options = {
    color: ['#3481FF', '#19D895', '#36DDF2'], graphic: { // 添加原生图形元素组件
      elements: [{
        type: 'text',
        left: 'center',
        top: '40%',
        style: {
          text: getSumTPS(siteDistributionData),
          fontSize: 32,
          textAlign: 'center',
          lineHeight: 38, fill: '#F5F5F5 ',
        },
      }, {
        type: 'text', left: 'center', top: '60%', style: {
          text: innerTitle, fontSize: 12, textAlign: 'center', fill: '#BBBBBB',
        },
      }],
    }, title: {
      text: titleRatio,

      top: 'left', padding: 0, textStyle: {
        fontSize: 16, color: '#F5F5F5', lineHeight: 20, fontWeight: 500,
      },
    }, tooltip: {
      // position: 'right',
      trigger: 'item',
      backgroundColor: '#393939',
      borderColor: '#202020',
      borderWidth: 2,
      formatter(params) {
        let html = `
            <div style="display:inline-block;background-color:#393939;">
                <div style="color: #F2F2F2; font-weight: bold">${params.name}</div>
                <div style="color: #ECECEC">${params.percent}%&nbsp;&nbsp;&nbsp;&nbsp;TPS:${params.value}</div>
            </div>
            `;
        return html;
      },
    },

    legend: {
      orient: 'horizontal', bottom: '0%', left: 'center', right: 'center', type: 'scroll', icon: 'circle', itemGap: 70,

      textStyle: {
        color: '#BBBBBB',
      }, itemWidth: 15, itemHeight: 10, pageTextStyle: {
        color: '#BBBBBB',
      }, pageIconColor: '#aaa', pageIconInactiveColor: '#2f4554', formatter: param => param.trim() // 保证居中
      ,
    }, series: [{
      hoverAnimation: false, radius: ['100%', '95%'], center: ['50%', '50%'], name: title, type: 'pie',

      width: '60%', height: '60%', top: 'middle', bottom: 'middle', right: 'center', left: 'center', labelLine: {
        show: false,
      }, label: {
        show: false,

      }, emphasis: {
        focus: 'self',
      }, itemStyle: {
        borderRadius: 10, borderColor: '#272727', borderWidth: 0.4,
      }, data: handleData(),
    }],

  };
  const getFewSiteChartOptions = () => options;

  let optionParam = {
    color: ['#3481FF', '#19D895', '#36DDF2'], title: {
      text: title, top: 'left', textStyle: {
        fontSize: 16, color: '#F5F5F5', lineHeight: 20, fontWeight: 500,
      },
    }, tooltip: {
      trigger: 'item',
      confine: true,
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
  };
  const getMutiSiteChartOptions = () => {
    let options = {
      ...optionParam, series: [{
        avoidLabelOverlap: true,
        roseType: 'area',
        name: title,
        type: 'pie',
        width: 100,
        height: 100,
        top: 'middle',
        bottom: 'middle',
        right: 'center',
        left: 'center',
        radius: [20, 100],
        startAngle: 90,
        clockwise: false,
        emphasis: {
          label: {
            show: true, color: 'white',
          },
        },
        labelLine: {
          show: false, length: '1px',
        },
        labelLayout: {
          hideOverlap: true, // 隐藏重叠标签
          moveOverlap: 'shift', // 向上移动重叠的标签
        },
        label: {
          formatter: '{b}', overflow: 'break',

          // color:'#BBBBBB',
          color: ' #676767', rotate: 0.1, fontSize: 10, textAlign: 'center', position: 'outer',

        },
        data: handleData(),
      }],
    };
    return options;
  };

  return (

    <div id="pieChartDiv" className="alarmCls" />

  );
}

export default SiteDistributionPieChart;
