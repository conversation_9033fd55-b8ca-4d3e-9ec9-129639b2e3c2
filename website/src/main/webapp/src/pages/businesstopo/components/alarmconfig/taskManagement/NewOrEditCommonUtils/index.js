import {newOrEditTask, viewTask} from '../../api';
import { $t, modal } from '@util';
import {
  TASK_PAGE_TYPE,
  OPERATE_SUCCESS_CODE
} from '../../const';

export const cancelCreateOrEditTask = (dispatch, backList = true) => {
  if (backList) {
    dispatch({
      type: 'setCurTaskPage',
      curTaskPage: TASK_PAGE_TYPE.TASK_LIST,
    });
  }
  dispatch({
    type: 'setAlgorithmList',
    algorithmList: [],
  });
  dispatch({
    type: 'setKnowledgeBaseList',
    knowledgeBaseList: [],
  });
  dispatch({
    type: 'setSolutionList',
    solutionList: [],
  });
  dispatch({
    type: 'setSelectedKnowledgeBase',
    selectedKnowledgeBase: '',
  });
  dispatch({
    type: 'setSelectedSolution',
    selectedSolution: '',
  });
  dispatch({
    type: 'setAlgorithmParamsList',
    algorithmParamsList: {
      algorithmParam: [],
      algorithmModelId: '',
      algorithmModelName_Version: '',
      algorithmPageResults: [],
      algorithmParamRef: {},
    },
  });  
  dispatch({
    type: 'setBasicInfoList',
    basicInfoList: {
      taskName: '',
      displayResult: false,
      trainCycle: '',
      taskDescription: '',
    },
  });  
};

export const modifyTaskCommon = async (param, sucFn, taskId, dispatch, theme = 'evening') => {
  await newOrEditTask(param, res => {
    if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
      let msg = res.resultMessage ? res.resultMessage : $t('intelligent.incident.task.manage.save.fail.tip');
      modal.error(msg, null, $t('intelligent.incident.task.manage.error.tip'));
      return;
    }
    modal.success(
      $t('intelligent.incident.task.manage.create.success'), 
      null, 
      $t('intelligent.incident.task.manage.tip'),
    );
  }, err => {
    let msg = $t('intelligent.incident.task.manage.save.fail.tip');
    modal.error(msg, null, $t('intelligent.incident.task.manage.error.tip'));
  });
  viewTask(
    {
      taskId,
    },
    (res) => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        sucFn();
        modal.error(
          $t('intelligent.incident.task.manage.view.update.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
        return;
      }
      dispatch({
        type: 'setViewDetailInfo',
        viewDetailInfo: res.data,
      });
      sucFn();
    },
    (err) => {
      sucFn();
      modal.error(
        $t('intelligent.incident.task.manage.view.update.fail'),
        null,
        $t('intelligent.incident.task.manage.error.tip'),
        null,
        theme,
      );
    }
  );
};