.topoLegendDiv {
  display: flex;
  flex-wrap: wrap; /* 设置子元素在容器中流式布局 */
  text-align: left;
  column-gap: 20px;
  color: #BBB;
}

.topoLegendIcon {
  width: 8px;
  height: 8px;
  border-radius: 50%; /* 使元素成为圆形 */
  display: inline-block;
  margin-top: 4px;
}


.topoLegendItem {
  cursor: pointer;
  justify-content: center;
  display: flex;
  gap: 5px;
  position: relative;
}

.topoLegendItemText {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 在溢出的地方显示省略号 */
  font-size: 12px;
}

.topoMoreLegendDiv {
  display: flex;
  flex-wrap: nowrap; /* 设置子元素在容器中流式布局 */
  justify-content: center;
  text-align: left;
  gap: 20px;
  color: #BBB;
  position: relative;
}

.topoMoreLegendIcon {
  width: 8px;
  height: 8px;
  border-radius: 50%; /* 使元素成为圆形 */
  display: inline-block;
  margin-top: 4px;
}

.topoMoreLegendDivContainer {
  display: flex;
  flex-wrap: nowrap; /* 设置子元素在容器中流式布局 */
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
  cursor: pointer;
}

.topoMoreLegendItemText {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 在溢出的地方显示省略号 */
  font-size: 12px;
}

.topoMoreLegendItemMoreText {
  width: 40px;
  font-size: 12px;
  color: #56a3f3;
  cursor: pointer;
}


.topo-legend-dropdown-container {
  position: absolute;
  z-index: 9999999;
  box-shadow: -4px 0px 8px rgba(0,0,0,0.5);
  background-color: #30333B;
  border-radius: 4px;
  max-width: 300px;
  padding: 12px;
  top: 20px;
  visibility: hidden;
}

.topo-legend-dropdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 150px;
  overflow: auto;
  margin-top: 10px;
}

.topo-legend-item-dropdown {
  display: flex;
  height: 16px;
  align-items: center;
  font-size: 12px;
  border-radius: 2px;
  gap: 6px;
  color: #BBB;
  cursor: pointer;
  background-color: transparent;
  max-width: 100%;
}
