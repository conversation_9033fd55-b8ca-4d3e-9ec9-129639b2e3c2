/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React from 'react';
import ReactDOM from 'react-dom';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import Overview3D from '@pages/businesstopo/a3dPages/w3d/scenes/overview';
import {cancelPolling, createPolling} from '@pages/businesstopo/a3dPages/w3d/utils/polling';
import {
  getAlarmData,
  getHistoryData,
} from '@pages/businesstopo/api';
import {eventBusHandler} from './utils';
import * as api from '../../api';
import Business from '../tooltip/business';
import {setSessionData, updateStorage} from '../../util';
import Fault from '../tooltip/fault';
import {getMessage, registerResource} from '@pages/businesstopo/a3dPages/commonUtil/intl';
import i18n from '@pages/businesstopo/a3dPages/locales/w3d';
import {queryCurrentIds} from '../../api';
import SiteTips from '../tooltip/siteTips';
import {sorter} from '@digitalview/fe-utils';

registerResource(i18n, 'w3d');
let overview3D;
let overviewDispatch;
let overViewSolutionId;
let relation = [];

export const events = [
  {
    eventName: 'from3d_showPanel',

    // 2D-3D对接，点击事件，联动右侧面板
    fn: setting => {
      let {
        isTimeTrack,
        currentBusinessId,
        indicatorId,
        selectedTime,
        solutionId,
      } = JSON.parse(sessionStorage.getItem('topoSession'));
      let param = {
        businessId: currentBusinessId,
        indicatorId,
      };

      let alarmParam = {
        instanceId: solutionId,
      };

      // 点击站点
      if (setting.currentSiteId) {
        if (setting.currentSiteId !== -1) {
          setSessionData({
            selectSiteId: setting.currentSiteId,
          });
          param.siteId = setting.currentSiteId;
          overviewDispatch({
            currentSiteId: setting.currentSiteId,
          });
          alarmParam.instanceId = setting.currentSiteId;
        } else {
          setSessionData({
            selectSiteId: -1,
          });
          overviewDispatch({
            currentSiteId: -1,
          });
        }

        // 时间回溯模式
        if (isTimeTrack) {
          alarmParam.timestamp = selectedTime;
          param.currentTime = selectedTime;
        }

        // 更新告警数据
        getAlarmData(
          alarmParam,
          res => {
            overviewDispatch({
              alarmEventData: res.data.alarmDataList,
            });
          },
        );

        // 更新指标数据
        getHistoryData(
          param,
          ({data, resultCode}) => {
            overviewDispatch({
              siteHistoryData: data.siteHistoryList,
            });
          },
        );
      }
    },
  },
  {
    eventName: 'from3d_switchBusiness',

    // 2D-3D对接，点击切换当前business
    fn: async(setting, isPiu) => {
      if (isPiu) {
        updateStorage(setting.currentBusinessId);
        const lineData = await getLineData();
        lineData.switchBusiness = true;
        for (const lineItem of lineData) {
          lineItem.indicatorValue = null;
        }
        overview3D.update({line: lineData});
        return;
      }
      overviewDispatch({
        currentBusinessId: setting.currentBusinessId,
      });
      let lineData = await getLineData();
      lineData.switchBusiness = true;
      overview3D.update({line: lineData});
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_drillDownSite',
    fn(setting) {
      let {
        solutionId,
        selectedTime,
      } = JSON.parse(sessionStorage.getItem('topoSession'));
      if (selectedTime === 0) {
        destroyW3D();
        let hash = '';
        let siteId;
        if (setting.currentGroupId) {
          siteId = relation[setting.currentGroupId];
        } else {
          siteId = setting.currentSiteId;
        }
        hash = `path=/businesstopo/sitehome&siteId=${siteId}&solutionId=${solutionId}`;
        if (setting.currentGroupId) {
          hash += `&selectMold=${setting.currentGroupId}`;
        }
        hash += `&_t=${Math.round(Date.now() / 1000)}`;
        window.location.hash = hash;
        return;
      }
      let param = {
        instanceIdList: [],
        timestamp: selectedTime,
        targetTimestamp: 0,
      };
      param.instanceIdList.push(solutionId);
      if (setting.currentGroupId) {
        param.instanceIdList.push(setting.currentGroupId);
        param.instanceIdList.push(relation[setting.currentGroupId]);
      } else {
        param.instanceIdList.push(setting.currentSiteId);
      }
      queryCurrentIds(param, ({data}) => {
        if (!data || Object.entries(data).length === 0) {
          overviewDispatch({
            showDeleteInfo: true,
          });
          return;
        }
        destroyW3D();
        let hash = '';
        let siteId;
        if (setting.currentGroupId) {
          siteId = data[relation[setting.currentGroupId]];
        } else {
          siteId = data[setting.currentSiteId];
        }
        hash = `path=/businesstopo/sitehome&siteId=${siteId}&solutionId=${data[solutionId]}`;
        if (setting.currentGroupId) {
          hash += `&selectMold=${data[setting.currentGroupId]}`;
        }
        hash += `&_t=${Math.round(Date.now() / 1000)}`;
        window.location.hash = hash;
      });
    },
  },
  {
    // 2D-3D对接，hover业务分组，显示卡片
    eventName: 'from3d_showBusinessGroupTip',
    fn: (setting, piuData) => {
      if (piuData?.alarmSetting) {
        // 如果piu传入了告警数据，优先展示
        const dom = piuData.alarmSetting[setting?.data?.groupId]?.msgDiv;
        if (dom) {
          ReactDOM.render(
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '0.5rem',
              }}
            >
              {dom}
            </div>,
            setting.dom,
          );
        }
        return;
      }
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      if (setting.data.alarmCsn !== '') {
        ReactDOM.render(
          <Fault faultData={{
            params: {
              csnList: setting.data.alarmCsn,
              timestamp: topoSession.selectedTime,
            },
          }} isTimeTrack={topoSession.isTimeTrack}
          />,
          setting.dom,
        );
      }
    },
  },
  {
    // 2D-3D对接，hover tps连线，显示卡片
    eventName: 'from3d_showTPSCard',
    fn: setting => {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      ReactDOM.render(
        <Business linkData={{
          params: {
            indicatorId: topoSession.indicatorId,

            businessId: setting.data.businessId,
            siteId: setting.data.siteId,
          },
        }}
        isTimeTrack={topoSession.isTimeTrack}
        selectedTime={topoSession.selectedTime}
        goldIndicator={topoSession.businessData.find(v => v.businessId === setting.data.businessId).indicatorList.find(
          value => value.indicatorDisplayType === 1,
        ).indicatorId}
        />,
        setting.dom,
      );
    },
  }, {
    eventName: 'from3d_showBusinessTip',
    fn: setting => {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      if (setting.data.alarmCsn && setting.data.alarmCsn.split(',').length > 0) {
        let params = {
          params: {
            csnList: setting.data.alarmCsn,
            timestamp: topoSession.selectedTime,
          },
        };
        if (setting.data.businessType === 'south') {
          params.length = 1;
          params.top = -50;
        }
        ReactDOM.render(
          <Fault faultData={{
            ...params,
          }} isTimeTrack={topoSession.isTimeTrack}
          />,
          setting.dom,
        );
      }
    },
  }, {
    eventName: 'from3d_showSiteNameCard',
    fn(setting) {
      ReactDOM.render(
        <SiteTips data={setting.data} />,
        setting.dom,
      );
    },
  },
];

const getQuery = () => {
  const str = location.href.split(/[?&]/);
  const testReg = /^\w+=\d$/;
  const result = {};
  str.forEach(subStr => {
    if (testReg.test(subStr)) {
      const [key, value] = subStr.split('=');
      result[key] = value;
    }
  });
  return result;
};

const getRouteQuery = () => {
  const query = getQuery();
  const result = {
    showGui: query.debug === '1',
    msaa: query.msaa !== '0',
    hdp: 0,
  };
  return result;
};

const getLineData = async() => {
  let lineData = [];
  let {mainBussiness, selectedTime, isTimeTrack} = JSON.parse(sessionStorage.getItem('topoSession'));
  let lineNorthParam = {instanceId: mainBussiness.north.businessId};
  let lineSouthParam = {instanceId: mainBussiness.south.businessId};
  if (isTimeTrack && selectedTime > 0) {
    lineNorthParam.currentTime = selectedTime;
    lineSouthParam.currentTime = selectedTime;
  }

  let lineNorthData = await api.getSiteDistritionData(
    lineNorthParam,
    res => res,
  );
  dealLineData(lineNorthData.data.siteDataList, mainBussiness.north.businessId, 'north');

  let lineSouthData = await api.getSiteDistritionData(
    lineSouthParam,
    res => res,
  );
  dealLineData(lineSouthData.data.siteDataList, mainBussiness.south.businessId, 'south');
  lineData = [...lineNorthData.data.siteDataList, ...lineSouthData.data.siteDataList];
  return lineData;
};

const addSitePrefix = jsonData => {
  // 遍历siteList
  jsonData.siteTeamList.forEach(siteTeam => {
    siteTeam.siteList.forEach(site => {
      // 添加前缀"site"
      site.siteName = `${site.siteName}`;
    });
  });
  return jsonData;
};

const getOverViewGrid = async() => {
  let {
    isTimeTrack,
    selectedTime,
  } = JSON.parse(sessionStorage.getItem('topoSession'));
  let param = {
    solutionId: overViewSolutionId,
    timestamp: 0,
  };
  if (isTimeTrack) {
    param.timestamp = selectedTime;
  }
  let data = await api.getOverViewGrid(
    param,
    res => res,
  );
  // 处理数据
  data.data.siteTeamList.forEach(siteTeam => {
    siteTeam.siteList.forEach(site => {
      site.groupList = site.groupList.filter(group => {
        // 移除 hasInstance 为 0 的条目
        if (group.hasInstance === 0) {
          return false;
        }
        // 修改 isActiveDv 为 0 的条目
        if (group.isActiveDv === 0) {
          group.alarmCount = 0;
          group.alarmCsn = '';
        }
        return true;
      });
    });
  });
  return addSitePrefix(data.data);
};

const dealLineData = function(lineData, businessId, type) {
  lineData.forEach(item => {
    item.type = type;
    item.businessId = businessId;
  });
};

function getRelation(data) {
  data.siteTeamList.forEach(team => {
    team.siteList.forEach(site => {
      site.groupList.forEach(gorup => {
        relation[gorup.groupId] = site.siteId;
      });
    });
  });
  return undefined;
}

const sortByIsMain = (a, b) => {
  if (a.isMain && !b.isMain) {
    return -1;
  } else if (!a.isMain && b.isMain) {
    return 1;
  } else {
    return 0;
  }
};
export const getInitData = async(initFlagValue, solutionId) => {
  if (solutionId) {
    overViewSolutionId = solutionId;
  }
  if (!overViewSolutionId) {
    return {};
  }
  let data = await getOverViewGrid(initFlagValue);
  getRelation(data);
  let lineData = await getLineData();
  lineData.switchBusiness = false;
  let lineMap = {};
  lineData.forEach(item => {
    lineMap[item.siteId] = item.indicatorValue;
  });
  let business = data.businessList;
  let north = [];
  let south = [];
  business.forEach(item => {
    if (item.businessType === 'north') {
      north.push(item);
    } else {
      south.push(item);
    }
  });
  south.sort(sortByIsMain);
  north.sort(sortByIsMain);
  let site = data.siteTeamList.filter(item => item.siteList.length);
  site = site.sort((a, b) => sorter(a.siteTeamId, b.siteTeamId));
  site = site.map(item => {
    return {
      ...item,
      siteList: item.siteList.map(item2 => ({
        ...item2,
        groupList: item2.groupList.sort((a, b) => sorter(a.groupId, b.groupId)),
      })),
    };
  });
  const i18nMap = {
    northServiceSystem: getMessage('w3d.northServiceSystem'),
    southServiceSystem: getMessage('w3d.southServiceSystem'),
    CBS: getMessage('w3d.CBS'),
  };
  return {
    north, south, site, i18n: i18nMap, line: lineData, hasFiltered: data.hasFiltered,
  };
};

const modifyCssStyle = () => {
  document.querySelector('#topology3d_main_container').children[1].style.overflow = 'visible';
};

export const initW3D = async(container, dispatch, param, state) => {
  let selectSolutionIndex = 0;
  let solutionData = state.solutionData;
  let businessDataResult = state.businessDataResult;
  overviewDispatch = dispatch;
  events.forEach(item => {
    eventBus.addListener(item.eventName, item.fn);
  });

  overViewSolutionId = solutionData[selectSolutionIndex].solutionId;
  param.solutionId = solutionData[selectSolutionIndex].solutionId;
  let currentBussiness = businessDataResult.find(
    item => item.businessType === 'south' && item.isMain,
  );
  let currentNorthBussiness = businessDataResult.find(
    item => item.businessType === 'north' && item.isMain,
  );

  setSessionData({
    mainBussiness: {
      north: currentNorthBussiness,
      south: currentBussiness,
    },
  },
  );

  // 2D-3D对接，初始化更新数据
  const initData = await getInitData();
  if (initData.site.length === 0) {
    dispatch({
      showMessage: true,
    });
  } else {
    dispatch({
      showMessage: false,
    });
  }
  if (!overview3D) {
    overview3D = new Overview3D(eventBusHandler);
  }
  const routeQuery = getRouteQuery();
  initData.i18n.CBS = solutionData[selectSolutionIndex].solutionName;
  await overview3D.init(container.current, routeQuery, initData);
  modifyCssStyle();
  if (initData.hasFiltered && initData.hasFiltered === 1) {
    dispatch({
      hasFiltered: true,
    });
  }
  return initData;
};

/**
 * PIU场景所需的交互事件
 */
const events4Piu = events.filter(evt => !['from3d_showPanel', 'from3d_drillDownSite'].includes(evt.eventName));

/**
 * 给PIU场景使用的初始化方法，比业务拓扑原始场景少一些交互
 */
export const initW3D4Piu = async(container, config, topoData, piuData) => {
  events4Piu.forEach(item => {
    eventBus.addListener(item.eventName, item.fn);
  });

  if (!overview3D) {
    overview3D = new Overview3D(eventBusHandler);
  }
  await overview3D.init(container, config, topoData, piuData);
  modifyCssStyle();

  return overview3D;
};

export const update = updateData => {
  // 2D-3D对接，循环更新数据
  if (updateData) {
    overview3D.update(updateData);
  }
};

export const destroyW3D = () => {
  events.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  cancelPolling();
  overview3D?.destroy();
  overview3D = null;
};

/**
 * 给PIU场景使用的销毁方法
 */
export const destroyW3D4Piu = () => {
  events4Piu.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  cancelPolling();
  overview3D?.destroy();
  overview3D = null;
};

export const resizeW3D = () => {
  if (overview3D?.resize) {
    overview3D.resize();
  }
};
