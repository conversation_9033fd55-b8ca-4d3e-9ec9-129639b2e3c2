/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

/**
 * 网元类型下钻视图
 */
import React, {useEffect, useReducer, useRef, useState} from 'react';
import {ConfigProvider} from 'eview-ui';
import Crumbs from 'eview-ui/Crumbs';
import {dateTimeFormat} from '@util/tools';
import {$t, registerResource} from '@util';
import {STATUS} from '../../const/moTypeDrill';
import {
  getAppData,
  getMoTypeDrillData,
  getPodDeployData,
  getVMDeployData,
} from '../../api/moTypeDrill';
import {initState, reducer} from '../../reducer';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import i18n from '../../locales/moTypeDrill';
import '../../css/index.less';
import {getUrlParam, setSessionData} from '../../util';
import {MO_TYPE_HOME} from '../../const/timeLine';
import PodRightPanel from './PodRightPanel';
import VMRightPanel from './VMRightPanel';
import TimeLine from '../timeline/TimeLine';
import PodDrillDownW3D from '../../a3dPages/components/PodDrillDownPage';
import {eventBusHandler, getQuery} from '../../a3dPages/components/utils';
import Progress from '@pages/businesstopo/components/grayscale/progress';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import MessageDeatil from '@pages/businesstopo/components/tooltip/messageDeatil';
import AppRightPanel from '@pages/businesstopo/components/moTypeDrill/AppRightPanel';
import DivMessage from 'eview-ui/DivMessage';
import {getSolutionData, queryCurrentIds} from '../../api';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {isZh, setHelpId} from '../../../../commonUtil';
import {destroyW3D} from '@pages/businesstopo/components/sitedrilldown/SiteDrillDown3D';


function useStateAndRef(initialValue) {
  const [state, setState] = useState(initialValue);
  const ref = useRef(initialValue);

  const setStateAndRef = (updater) => {
    let newState;
    if (typeof updater === 'function') {
      newState = updater(state);
    } else {
      newState = updater;
    }
    setState(newState);
    ref.current = newState;
  };

  return [state, ref, setStateAndRef];
}


function MoTypeDrill() {
  registerResource(i18n, 'moTypeDrillI18n');
  setHelpId('com.huawei.dvtopo.business');
  const [state, dispatch] = useReducer(reducer, initState);
  const [showPodPanel, showPodPanelRef, setShowPodPanel] = useStateAndRef({
    isShow: false, isOpen: false, detailsInfoArr: [], podData: {}, isTimeTrack: false, trackAlarmData: {},
  });
    // 存储点击的pod的id, 确保消亡的时候能关闭
  const showPodIdRef = useRef(null);

  const [showVMPanel, setShowVMPanel] = useState({
    isShow: false, isOpen: false, vmData: {}, isTimeTrack: false, timeTrackVMData: {},
  });

  const [showAppPanel, setShowAppPanel] = useState({
    isShow: false, isOpen: false, appData: {}, isTimeTrack: false, timeTrackAppData: {},
  });

  const PERCENT = 100;
  const [pageParams, setPageParams] = useState({
    moTypeId: getUrlParam('moTypeId'),
    siteId: getUrlParam('siteId'),
    solutionId: getUrlParam('solutionId'),
    siteName: decodeURIComponent(getUrlParam('siteName')),
    moTypeName: getUrlParam('moTypeName'),
    isMM: Boolean(getUrlParam('isMM')),
    stripId: getUrlParam('stripId'),
    stripUnit: getUrlParam('stripUnit'),
    isSearch: getUrlParam('isSearch'),
  });
  const [drillData, setDrillData] = useState({isInit: true, data: null, events: [], isMM: false});
  const drillDataRef = useRef([]);
  const allAppData = useRef({});
  const allPodData = useRef({});
  const allVMData = useRef({});
  const isTimeTrack = useRef(false);
  const [data, setData] = useState([]);
  let moSelectedTime = useRef(0);
  let allPodNumber = useRef(0);
  let productRate = useRef(0);
  let isShowLeftText = useRef(true);
  let isVMScenario = useRef(false);
  const [refresh, setRefresh] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const initFlagRef = useRef(0);
  const [showGray, setShowGray] = useState(false);
  const notGray = 2;
  let style2 = {
    marginBottom: '20px',
    position: 'absolute',
    'z-index': '99999',
    right: '1%',
    top: '8%',
    width: '385px',
  };

  function showTips(setting) {
    let tipStr;
    setting.dom.innerHTML = '';
    if (setting.type === 'pod') {
      let podId = setting.data.dnId;
      let podData = allPodData.current[podId];
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (podData.availableStatus === STATUS.normal && podData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      let time = dateTimeFormat(podData.createTime);
      tipStr = `<div class="moType_tip_container">
                        <div class="moType_tipTitle">${setting.data.name}</div>
                        <div class=${className}>${status}</div>
                        <div class="moType_tipValue">${$t('moType.drill.rightPanel.time')} : ${formatDate2SysSetting(parseInt(podData.createTime))}</div>
                      </div>`;
    }

    if (setting.type === 'app') {
      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      let appData = allAppData.current[parseInt(setting.data.dnId)];
      if (appData.availableStatus === STATUS.normal && appData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }
      tipStr = `<div class="moType_tip_container">
                        <div class="moType_tipTitle">${setting.data.name}</div><div class="${className}">${status}</div>
                        <div class="moType_tipValue">${$t('w3d.version')}:${setting.data.version}</div>
                      </div>`;
    }
    if (setting.type === 'vm') {
      let vmId = setting.data.dnId;
      let vmData = allVMData.current[vmId];

      let status = $t('moType.drill.status.abnormal');
      let className = 'moType_abnormal_status';
      if (vmData.availableStatus === STATUS.normal && vmData.csnState === 0) {
        status = $t('moType.drill.status.normal');
        className = 'moType_status';
      }

      tipStr = `<div class="moType_tip_container">
                        <div class="moType_tipTitle">${setting.data.name}</div><div class="${className}">${status}</div>
                        <div class="moType_tipValue">${$t('moType.drill.vm.tooltip.vmIp')} : ${setting.data.ip || 'NA'}</div>
                      </div>`;
    }
    if (tipStr) {
      let tipParser = new DOMParser();
      let tipElement = tipParser.parseFromString(tipStr, 'text/html');
      setting.dom.append(tipElement.body.firstChild);
    }
  }

  function drillDownHostHandler(setting) {
    let hostUrl = location.href.replace('/eviewwebsite/index.html#path=/businesstopo/mohome', '/eviewwebsite/index.html#path=/businesstopo/hosthome');
    let {
      selectedTime,
    } = JSON.parse(sessionStorage.getItem('topoSession'));
    if (selectedTime === 0) {
      destroyW3D();
      location.replace(`${hostUrl}&vmId=${setting.dnId}&hostName=${allVMData.current[setting.dnId].vmName}`);
    } else {
      let param = {
        instanceIdList: [setting.dnId],
        timestamp: selectedTime,
        targetTimestamp: 0,
      };
      queryCurrentIds(param, ({data: idMap}) => {
        if (!idMap || Object.entries(idMap).length === 0) {
          return;
        }
        location.replace(`${hostUrl}&vmId=${idMap[setting.dnId]}&hostName=${allVMData.current[setting.dnId].vmName}`);
        destroyW3D();
      });
    }
  }

  const getA3dEvents = appDataList => {
    let productNumber = 0;
    allPodNumber.current = 0;
    appDataList.map(app => {
      allPodNumber.current += app.podDataList.length;
      if (app.isGray === 0) {
        productNumber += app.podDataList.length;
      }
      if (app.isGray === notGray) {
        isShowLeftText.current = false;
      }
    });
    productRate.current = Math.trunc((productNumber / allPodNumber.current) * PERCENT);
    return [{
      // 点击事件，联动右侧面板
      eventName: 'from3d_showPanel', async fn(setting) {
        if (setting.type === '') {
          closePodPanel();
          closeVMPanel();
          closeAppPanel();
        }
        if (setting.type === 'app') {
          closePodPanel();
          closeVMPanel();
          let appData = await queryAppData(setting.id);
          handleAppDetailsInfo(appData.respData, setting.id);
        }
        if (setting.type === 'pod') {
          closeAppPanel(setting);
          closeVMPanel(setting);
          let podDeployData = await queryPodDeployData(setting.id);
          podDeployData.dnId = setting.id;
          handlePodDetailsInfo(podDeployData.respData, setting.id, setting.name);
          eventBusHandler.emit('to3d_updateSelectedRelation', podDeployData);
          return;
        }
        if (setting.type === 'vm') {
          closeAppPanel(setting);
          closePodPanel(setting);
          let vmDeployData = await queryVmDeployData(setting.id);
          handleVMDetailsInfo(vmDeployData.respData, setting.id, setting.name);
          eventBusHandler.emit('to3d_updateSelectedRelation', vmDeployData);
        }
      },
    }, {
      // 悬浮事件，显示tooltip
      eventName: 'from3d_showCommonTip', fn(setting) {
        showTips(setting);
      },
    }, {
      // 下钻事件
      eventName: 'from3d_drillDownHostType', fn(setting) {
        drillDownHostHandler(setting);
      },
    }, {
      // 显示左侧详情
      eventName: 'from3d_showPodDrillDownDetail', fn(setting) {
        if (setting.type === 'app') {
          const dom = document.createElement('div');
          dom.classList.add('w3d-podDetail-block');
          const query = getQuery();
          if (parseInt(query.applicationType) === 3) {
            dom.innerText = 'DB';
          } else {
            dom.innerText = pageParams.isMM ? $t('moType.drill.leftText.mm.app') : $t('moType.drill.leftText.app');
            if (pageParams.isMM) { // MM 标签左对齐。在2k差不多，分辨率不同可能有差异，只能尽量
              dom.style = `transform: translate(${isZh ? -20 : 64}px, 0);`;
            }
          }
          setting.dom.append(dom);
        } else if (setting.type === 'vm') {
          const dom = document.createElement('div');
          dom.classList.add('w3d-podDetail-block');
          dom.innerText = 'HOST';
          setting.dom.append(dom);
        } else if (setting.type === 'pod') {
          const dom = document.createElement('div');
          dom.innerHTML = `
                 <div class='w3d-podDetail-block'>${isVMScenario.current ? $t('moType.drill.leftText.pod.instance') : $t('moType.drill.leftText.pod')}</div>
                 <div class='w3d-podDetail-block'>
                   <span>${allPodNumber.current}</span><span class='w3d-podDetail-gray'>${$t('moType.drill.leftText.unit')}</span>
                 </div> `;

          if (isShowLeftText.current) {
            dom.innerHTML += `<div class='w3d-podDetail-block'>
                   <div>${productRate.current}%</div>
                   <div class='w3d-podDetail-gray'>${$t('moType.drill.leftText.product')}</div>
                 </div>
                 <div class='w3d-podDetail-block'>
                   <div>${PERCENT - productRate.current}%</div>
                   <div class='w3d-podDetail-gray'>${$t('moType.drill.leftText.gray')}</div>
                 </div>`;
          }
          if (pageParams.isMM) { // MM 标签左对齐。在2k差不多，分辨率不同可能有差异，只能尽量
            dom.style = `transform: translate(${isZh ? -40 : -10}px, 0);`;
          }
          setting.dom.append(dom);
        }
      },
    }];
  };

  useEffect(async() => {
    if (refresh !== 0) {
      if (showPodPanelRef.isOpen) {
        let podDeployData = await queryPodDeployData(showPodPanel.podData.id);
        handlePodDetailsInfo(podDeployData.respData, showPodPanel.podData.id, showPodPanel.podData.name);
      }

      if (showPodPanelRef.isOpen) {
        let vmDeployData = await queryVmDeployData(showVMPanel.vmData.id);
        handleVMDetailsInfo(vmDeployData.respData, showVMPanel.vmData.id, showVMPanel.vmData.name);
      }

      if (showPodPanelRef.isOpen) {
        let appData = await queryAppData(showAppPanel.appData.id);
        handleAppDetailsInfo(appData.respData, showAppPanel.appData.id);
      }
    }
  }, [refresh]);

  useEffect(() => {
    let param = {timestamp: state.selectedTime || 0};
    getSolutionData(param, res => {
      // CBS才展示
      const solutionId = getQuery().solutionId;
      if (solutionId) {
        const needShow = (res.data || [])
          .find(v => String(v.solutionId) === getQuery().solutionId)?.solutionType === 1;
        setShowGray(needShow);
      }
    });
  }, [state.selectedTime]);

  useEffect(() => {
    setData([{
      title: $t('overViewName'), url: '/eviewwebsite/index.html#path=/businesstopo',
    }, {
      title: !pageParams.isMM ? `${decodeURIComponent(pageParams.siteName)}` : `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})`,
      url: pageParams.isMM ? `/eviewwebsite/index.html#path=/businesstopo/mmStripDrillDown&stripId=${pageParams.siteId}&stripName=${pageParams.siteName}&solutionId=${pageParams.solutionId}${pageParams.stripUnit ? `&stripUnit=${pageParams.stripUnit}` : ''}` : `/eviewwebsite/index.html#path=/businesstopo/sitehome&siteId=${pageParams.siteId}&solutionId=${pageParams.solutionId}`,
    }, {
      title: decodeURIComponent(pageParams.moTypeName),
    }]);
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    let currentSelectSolutionIndex = 0;
    dispatch({
      selectSolutionIndex: currentSelectSolutionIndex,
    });
    if (topoSession.isTimeTrack) {
      return;
    }

    queryDrillData(initFlagRef.current === 0);
    initFlagRef.current = 1;

    const intervalId = setInterval(() => {
      if (!isTimeTrack.current) {
        setRefresh(new Date().getTime());
        queryDrillData(false);
      }
    }, 60 * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  useEffect(() => {
    handleQueryPageData(drillData.data.businessClusterList);
  }, [drillData]);

  function isPodDestroyed(podData) {
    const dnIdSet = new Set();
    (podData.businessClusterList || []).forEach(cluster => {
      (cluster.podDataList || []).forEach(pod => {
        if (pod.dnId) {
          dnIdSet.add(pod.dnId);
        }
      });
    });
    if (!dnIdSet.has(showPodIdRef.current)) {
      closePodPanel();
    }
  }

  // 查询钻取数据
  const queryDrillData = initFlag => {
    let params = {
      instanceId: pageParams.moTypeId,
    };
    const query = getQuery();
    if (query.stripUnit) {
      params.stripeUnit = query.stripUnit;
    }
    getMoTypeDrillData(params, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      if (initFlag && resp.data.hasFiltered && resp.data.hasFiltered === 1) {
        dispatch({
          hasFiltered: true,
        });
      }
      if (resp.data && resp.data.businessClusterList && resp.data.businessClusterList.length > 0) {
        // 判断是否是pod消亡场景
        isPodDestroyed(resp.data);

        isVMScenario.current = resp.data.environmentType === 1;
        drillDataRef.current = resp.data.businessClusterList;
        setDrillData({
          isInit: initFlag,
          data: resp.data,
          events: getA3dEvents(resp.data.businessClusterList),
          isMM: Boolean(getUrlParam('isMM')),
        });
      } else {
        closePodPanel();
        closeAppPanel();
        closeVMPanel();
      }
    });
  };

  // 查询pod部署关系
  const queryAppData = appId => new Promise((resolve, reject) => {
    let param = {instanceId: appId};
    if (moSelectedTime.current !== 0) {
      param.timestamp = moSelectedTime.current;
    }
    getAppData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        reject(null);
        return;
      }
      resolve({
        respData: resp.data,
      });
    }, () => {
      reject(null);
    });
  });

  // 查询pod部署关系
  const queryPodDeployData = podId => new Promise((resolve, reject) => {
    let param = {instanceId: podId};
    if (moSelectedTime.current !== 0) {
      param.endTime = moSelectedTime.current;
    }
    getPodDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        reject(null);
        return;
      }
      resolve({
        respData: resp.data, dnId: resp.data.dnId, relation: resp.data.vmDataList.map(vm => vm.dnId),
      });
    }, () => {
      reject(null);
    });
  });

  // 查询VM部署关系
  const queryVmDeployData = vmId => new Promise((resolve, reject) => {
    let param = {instanceId: vmId};
    if (moSelectedTime.current !== 0) {
      param.endTime = moSelectedTime.current;
    }
    getVMDeployData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        reject(null);
        return;
      }
      resolve({
        respData: resp.data,
        dnId: resp.data.dnId,
        relation: resp.data.podInstanceIdList.map(num => num.toString()),
      });
    }, () => {
      reject(null);
    });
  });

  // 关闭pod详情面板
  const closePodPanel = setting => {
    showPodIdRef.current = null;
    setShowPodPanel({
      isShow: false, isOpen: false, detailsInfoArr: [], podData: {}, isTimeTrack: false, trackAlarmData: {},
    });
  };

  // 关闭pod详情面板
  const closeAppPanel = setting => {
    setShowAppPanel({
      isShow: false, isOpen: false, detailsInfoArr: [], appData: {}, isTimeTrack: false, trackAlarmData: {},
    });
  };

  // 关闭VM详情面板
  const closeVMPanel = () => {
    setShowVMPanel({isShow: false, isOpen: false, vmData: {}, isTimeTrack: false, timeTrackVMData: {}});
  };

  // 查询钻取数据后处理
  const handleQueryPageData = appDataList => {
    for (let app of appDataList) {
      allAppData.current[app.dnId] = app;
      app.podDataList.map(pod => {
        allPodData.current[pod.dnId] = pod;
      });
      if (app.vmDataList && app.vmDataList.length > 0) {
        app.vmDataList.map(vm => {
          allVMData.current[vm.dnId] = vm;
        });
      }
    }
  };

  // 处理pod右侧面板初始化数据
  const handlePodDetailsInfo = (respData, podId, podName, timeTrackPod) => {
    let vmData = {};
    if (respData.vmDataList && respData.vmDataList.length > 0) {
      vmData = respData.vmDataList[0];
    }
    let content = (
      <div style={{padding: '5px'}}>
        <p>
          {$t('moType.drill.vm.healthstatus')} :{' '}
          {$t(respData.csnState === 0 ? 'moType.drill.vm.normal' : 'moType.drill.vm.abnormal')}
        </p>
        <p>
          {$t('moType.drill.vm.status')} :{' '}
          {$t(
            respData.availableStatus === STATUS.normal ?
              'moType.drill.pod.status.normal' :
              'moType.drill.pod.status.abnormal',
          )}
        </p>
      </div>
    );

    let detailsInfoArr = [
      {
        value: respData.csnState === 0 && respData.availableStatus === STATUS.normal ? STATUS.normal : STATUS.failed,
        title: $t('moType.drill.status'),
        content,
      },
      {value: vmData.vmName, title: $t('moType.drill.rightPanel.host')},
      {
        value: pageParams.isMM ? `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})` : `${decodeURIComponent(pageParams.siteName)}`,
        title: pageParams.isMM ? $t('moType.drill.rightPanel.stripName') : $t('moType.drill.rightPanel.site'),
      },
      {value: respData.podIp ? respData.podIp : 'NA', title: $t('moType.drill.rightPanel.ip')},
      {
        value: vmData.vmIp ? vmData.vmIp : 'NA',
        title: $t('moType.drill.rightPanel.hostIP'),
      },
      {value: formatDate2SysSetting(parseInt(respData.createTime)), title: $t('moType.drill.rightPanel.time')},
      {
        value: respData.dockerName,
        title: isVMScenario.current ? $t('moType.drill.rightPanel.appName') : $t('moType.drill.rightPanel.dockerName'),
      },
    ];

    if (isVMScenario.current) {
      detailsInfoArr = detailsInfoArr.filter(item => item.title !== $t('moType.drill.rightPanel.ip'));
    }

    showPodIdRef.current = podId;
    setShowPodPanel({
      isShow: true,
      isOpen: true,
      detailsInfoArr,
      podData: {
        name: podName,
        id: podId,
        indicatorList: allPodData.current[podId].podIndicatorList ? allPodData.current[podId].podIndicatorList : [],
      },
      isTimeTrack: Boolean(timeTrackPod),
      trackAlarmData: timeTrackPod ? timeTrackPod.alarmInfo.alarmDataList : {},
    });
  };

  // 处理App右侧面板初始化数据
  const handleAppDetailsInfo = (respData, appId) => {
    let content = (
      <div style={{padding: '5px'}}>
        <p>
          {$t('moType.drill.vm.healthstatus')} :{' '}
          {$t(respData.csnState === 0 ? 'moType.drill.vm.normal' : 'moType.drill.vm.abnormal')}
        </p>
        <p>
          {$t('moType.drill.vm.status')} :{' '}
          {$t(respData.availableStatus === STATUS.normal ? 'moType.drill.pod.status.normal' : 'moType.drill.pod.status.abnormal')}
        </p>
      </div>
    );

    let detailsInfoArr = [{
      value: respData.csnState === 0 && respData.availableStatus === STATUS.normal ? STATUS.normal : STATUS.failed,
      title: $t('moType.drill.status'),
      content,
    }, {value: respData.insVersion, title: $t('w3d.version')}, {
      value: pageParams.isMM ? `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})` : `${decodeURIComponent(pageParams.siteName)}`,
      title: pageParams.isMM ? $t('moType.drill.rightPanel.stripName') : $t('moType.drill.rightPanel.site'),
    }, {
      value: respData.insType, title: $t('moType.drill.rightPanel.appType'),
    }, {
      value: respData.insIP ? respData.insIP : 'NA', title: $t('moType.drill.rightPanel.ipAddress'),
    },
    ];

    setShowAppPanel({
      isShow: true, isOpen: true, detailsInfoArr, appData: {
        name: respData.appName,
        id: appId,
      },
    });
  };

  // 处理VM右侧面板初始化数据
  const handleVMDetailsInfo = (respData, vmId, vmName, timeTrackVM) => {
    let normal = 0;
    let abnormal = 0;

    for (let pod of respData.podInstanceIdList) {
      let podData = allPodData.current[pod];
      if (!podData) {
        continue;
      }
      if (podData.csnState === 1) {
        abnormal++;
      } else {
        normal++;
      }
    }

    setShowVMPanel({
      isShow: true, isOpen: true, vmData: {
        id: vmId, name: vmName, normalPods: normal, abnormalPods: abnormal,
      }, isTimeTrack: Boolean(timeTrackVM), timeTrackVMData: timeTrackVM,
    });
  };

  // 时间回溯回调事件
  const timeTrack = timeStamp => {
    closeVMPanel();
    closeAppPanel();
    closePodPanel();
    dispatch({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    moSelectedTime.current = timeStamp;
    setSessionData({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    isTimeTrack.current = true;

    setRefresh(timeStamp);
    let param = {
      instanceIdList: [],
      timestamp: 0,
      targetTimestamp: timeStamp,
    };
    param.instanceIdList.push(pageParams.moTypeId);
    queryCurrentIds(param, async({data: idMap}) => {
      if (!idMap || Object.entries(idMap).length === 0) {
        setShowMessage(true);
        let nullData = {
          businessClusterList: [],
          podLevel: null,
          producePodColSize: null,
          grayPodColSize: null,
        };
        drillDataRef.current = [];
        setDrillData({
          isInit: false, data: nullData, events: getA3dEvents([]),
        });
        return;
      }

      let params = {
        instanceId: idMap[pageParams.moTypeId],
        timestamp: timeStamp,
      };
      const query = getQuery();
      if (query.stripUnit) {
        params.stripeUnit = query.stripUnit;
      }

      getMoTypeDrillData(params, resp => {
        if (!resp || resp.resultCode !== 0) {
          return;
        }
        if (resp.data) {
          if (resp.data.businessClusterList.length === 0) {
            setShowMessage(true);
          } else {
            setShowMessage(false);
          }
          drillDataRef.current = resp.data.businessClusterList;
          isVMScenario.current = resp.data.environmentType === 1;
          setDrillData({
            isInit: initFlagRef.current === 0,
            data: resp.data,
            events: getA3dEvents(resp.data.businessClusterList),
          });
          initFlagRef.current = 1;
        }
      });
    });
  };

  // 退出时间回溯回调事件
  const exitTimeTrack = () => {
    closeVMPanel();
    closeAppPanel();
    closePodPanel();
    dispatch({
      selectedTime: 0, isTimeTrack: false,
    });
    setShowMessage(false);
    isTimeTrack.current = false;
    moSelectedTime.current = 0;
    setSessionData({
      selectedTime: 0, isTimeTrack: false,
    });

    setRefresh(new Date().getTime());
    queryDrillData(false);
  };

  return (
    <BUSINESS_TOPO_CONTEXT.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div style={{background: '#191919'}} id="motypeDrillTopo">
          <PodDrillDownW3D pageParams={drillData} />
          <MessageDeatil display={showMessage} main={false} />
          {
            !pageParams.isSearch &&
                        <div className="moType_back_btn">
                          <Crumbs data={data} seprator="/" />
                        </div>
          }
          {showPodPanel.isShow && (
            <PodRightPanel
              closePanel={closePodPanel}
              detailsInfoArr={showPodPanel.detailsInfoArr}
              podData={showPodPanel.podData}
              moTypeId={pageParams.moTypeId}
              siteId={pageParams.siteId}
              isTimeTrack={showPodPanel.isTimeTrack}
              trackAlarmData={showPodPanel.trackAlarmData}
              isOpenPanel={showPodPanel.isOpen}
              setIsOpenPanel={flag => setShowPodPanel({...showPodPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          {showAppPanel.isShow && (
            <AppRightPanel
              closePanel={closeAppPanel}
              detailsInfoArr={showAppPanel.detailsInfoArr}
              appData={showAppPanel.appData}
              moTypeId={pageParams.moTypeId}
              siteId={pageParams.siteId}
              isTimeTrack={showAppPanel.isTimeTrack}
              trackAlarmData={showAppPanel.trackAlarmData}
              isOpenPanel={showAppPanel.isOpen}
              setIsOpenPanel={flag => setShowAppPanel({...showAppPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          {showVMPanel.isShow && (
            <VMRightPanel
              vmData={showVMPanel.vmData}
              closePanel={closeVMPanel}
              moTypeId={pageParams.moTypeId}
              siteId={pageParams.siteId}
              siteName={pageParams.siteName}
              isTimeTrack={showVMPanel.isTimeTrack}
              timeTrackVMData={showVMPanel.timeTrackVMData}
              isOpenPanel={showVMPanel.isOpen}
              setIsOpenPanel={flag => setShowVMPanel({...showVMPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          <TimeLine
            renderTopology={timeTrack}
            refreshFlag={refresh}
            backTimeTrack={exitTimeTrack}
            pageType={MO_TYPE_HOME}
          />
          {
            showGray && <Progress isMotype={true}
              showMotypePanel={showPodPanel.isOpen || showVMPanel.isOpen || showAppPanel.isOpen}
                        />
          }
          <Toolbar isMotype={true}
            showMotypePanel={showPodPanel.isOpen || showVMPanel.isOpen || showAppPanel.isOpen}
            isMM={pageParams.isMM}
          />
          <DivMessage text={$t('filtered_info')} type="warn" style={style2} display={state.hasFiltered}
            disposeTimeOut={3000} onClose={() => {
              dispatch({
                hasFiltered: false,
              });
            }}
          />
        </div>
      </ConfigProvider>
    </BUSINESS_TOPO_CONTEXT.Provider>
  );
}

export default MoTypeDrill;
