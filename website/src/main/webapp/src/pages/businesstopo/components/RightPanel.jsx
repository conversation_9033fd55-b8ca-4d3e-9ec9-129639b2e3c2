/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect} from 'react';

import {BUSINESS_TOPO_CONTEXT} from '../const';
import KPIOverviewPanel from './KPIOverviewPanel';
import {OVER_VIEW} from '@pages/businesstopo/const/timeLine';
import TopNList from '@src/apps/smartIncident/components/TopNList';

function RightPanel(props) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {showKPIPanel, solutionName} = state;

  const {refreshFlag} = props;

  return (
    <div
      className="business-right-panel" style={{width: showKPIPanel ? '400px' : '0px', overflowX: 'hidden'}}
    >
      <div style={{width: '400px'}}>
        <div
          className="right-panel-container"
          style={{visibility: showKPIPanel ? 'visible' : 'hidden'}}
        >
          <TopNList solutionName={solutionName} />
          <KPIOverviewPanel refreshFlag={refreshFlag} />

        </div>
      </div>
    </div>
  );
}

export default RightPanel;
