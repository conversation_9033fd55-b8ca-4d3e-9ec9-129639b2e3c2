/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useReducer, useState, useRef, useEffect} from 'react';
import { ConfigProvider } from 'eview-ui';
import '@pages/businesstopo/a3dPages/styles/split.less';
import '@pages/businesstopo/css/sitedrilldown/index.css';
import SiteRightPannel from './SiteRightPannel';
import {BUSINESS_TOPO_CONTEXT, refeshTime} from '../../const';
import {initState, reducer} from '../../reducer';
import '../../css/index.less';
import TimeLinePanel from '../timeline/TimeLinePanel';
import {SITE_HOME} from '../../const/timeLine';
import Progress from '../grayscale/progress';
import Crumbs from 'eview-ui/Crumbs';
import i18n from '../../locales/sitedrilldown';
import {
  destroyW3D, findItemById,
  initW3D,
  updateSiteData,
} from './SiteDrillDown3D';
import {getQuery} from '@pages/businesstopo/a3dPages/components/utils';
import * as api from '@pages/businesstopo/api';
import {setSessionData} from '@pages/businesstopo/util';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import {$t, registerResource} from '@util';
import MessageDeatil from '@pages/businesstopo/components/tooltip/messageDeatil';
import DivMessage from 'eview-ui/DivMessage';
import {getSolutionData, queryCurrentIds} from '../../api';

registerResource(i18n, 'tooltip');
let style2 = {
  marginBottom: '20px',
  position: 'absolute',
  'z-index': '99999',
  right: '1%',
  top: '8%',
  width: '385px',
};
const getSiteOverViewGrid = async params => {
  let data = await api.querySiteGridData(
    params,
    res => res,
  );

  const filteredDrGroupList = data.data.drGroupList.map(drGroup => {
    const filteredMoTypeInfoList = drGroup.moTypeInfoList.filter(moType => {
      // 过滤掉 hasInstance 等于 0 的项，其余不过滤
      return moType.hasInstance !== 0;
    });

    // 返回过滤后的drGroup对象，带上已经过滤后的moTypeInfoList
    return {
      ...drGroup,
      moTypeInfoList: filteredMoTypeInfoList,
    };
  });
  // 更新原数据对象
  data.data.drGroupList = filteredDrGroupList;
  let newEles = data.data.siteInfoList.sort((a, b) => a.siteName.localeCompare(b.siteName),
  );
  data.data.siteInfoList = newEles;
  return data;
};

// 过滤函数


function SiteHome() {
  const [state, dispatch] = useReducer(reducer, initState);
  const {selectedTime} = state;
  const [siteHistoryData, setSiteHistoryData] = useState({});
  const intervalId = useRef(0);
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [data, setData] = useState([]);
  const [siteDataVal, setSiteDataVal] = useState({});
  const [isVm, setIsVM] = useState(false);
  const w3dContainerRef = useRef();
  const {siteId, selectMold, solutionId} = getQuery();
  const groupId = selectMold;
  const [showMessage, setShowMessage] = useState(false);
  const [showGray, setShowGray] = useState(false);
  const changeUrl = (id) => {
    const map = getQuery();
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    let hash;
    if (topoSession.selectedTime === 0) {
      hash = `path=/businesstopo/sitehome&siteId=${id}&solutionId=${map.solutionId}`;
      window.location.hash = hash;
    } else {
      let param2 = {
        instanceIdList: [],
        timestamp: topoSession.selectedTime,
        targetTimestamp: 0,
      };
      param2.instanceIdList.push(id);
      queryCurrentIds(param2, async({data: idMap}) => {
        hash = `path=/businesstopo/sitehome&siteId=${idMap[id]}&solutionId=${map.solutionId}`;
        window.location.hash = hash;
      });
    }
  };
  const startRefresh = () => {
    setRefreshFlag(new Date().getTime());
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, refeshTime);
    intervalId.current = id;
  };
  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };
  const init = async() => {
    const siteData = await getSiteOverViewGrid({
      solutionId,
      siteId,
      timestamp: 0,
    },
    );
    if (siteData.data.environmentType === 1) {
      setIsVM(true);
    } else {
      setIsVM(false);
    }
    if (siteData.data.hasFiltered && siteData.data.hasFiltered === 1) {
      dispatch({
        hasFiltered: true,
      });
    }
    siteData.data.siteName = `${siteData.data.siteName}`;
    siteData.data.siteInfoList.forEach(siteInfo => {
      siteInfo.siteName = `${siteInfo.siteName}`;
    });
    setSiteDataVal(siteData);
    setSessionData({
      siteData,
    });
    setData([
      {
        title: $t('overViewName'),
        url: '/eviewwebsite/index.html#path=/businesstopo',
      }, {
        title: siteData.data.siteName,
      },
    ]);
    await initW3D(w3dContainerRef, dispatch, siteId, solutionId, {
      siteData: siteData.data,
      groupId,
      environmentType: siteData.data.environmentType,
      changeSite: async changeData => {
        changeUrl(changeData.siteId);
      },
    });
  };
  useEffect(() => {
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    destroyW3D();
    let currentSelectSolutionIndex = 0;
    dispatch({
      selectSolutionIndex: currentSelectSolutionIndex,
    });
    if (topoSession.isTimeTrack) {
      return;
    }
    init();
    setTimeout(() => {
      startRefresh();
    }, refeshTime);
    return () => destroyW3D();
  }, []);

  useEffect(() => {
    let param = {timestamp: state.selectedTime || 0};
    getSolutionData(param, res => {
      // CBS才展示
      if (solutionId) {
        const needShow = (res.data || [])
          .find(v => String(v.solutionId) === String(solutionId))?.solutionType === 1;
        setShowGray(needShow);
      }
    });
  }, [state.selectedTime]);

  useEffect(async() => {
    if (refreshFlag === 0 || siteDataVal === {}) {
      return;
    }
    updateSiteData(siteDataVal.data);
    setSessionData({
      siteData: siteDataVal,
    });
  }, [siteDataVal]);
  useEffect(async() => {
    if (refreshFlag === 0) {
      return;
    }
    const siteData = await getSiteOverViewGrid({
      solutionId,
      siteId,
      timestamp: selectedTime,
    },
    );
    if (siteData.data.environmentType === 1) {
      setIsVM(true);
    } else {
      setIsVM(false);
    }
    let currentMoData = state.currentMoData;
    let moTypeData = findItemById(siteData.data.drGroupList, currentMoData.id);
    if (moTypeData) {
      dispatch({
        currentMoData: {
          ...state.currentMoData,
          name: moTypeData.moTypeName,
          podCount: moTypeData.podCount,
          podErrorCount: moTypeData.podErrorCount,
          applicationType: moTypeData.applicationType,
          environmentType: moTypeData.environmentType,
        },
      });
    }
    siteData.data.siteName = `${siteData.data.siteName}`;
    siteData.data.siteInfoList.forEach(siteInfo => {
      siteInfo.siteName = `${siteInfo.siteName}`;
    });
    setSiteDataVal(siteData);
    siteData.isRefresh = true;
  }, [refreshFlag]);

  // 进入回溯状态
  const timeTrack = async time => {
    stopRefresh();
    destroyW3D();
    dispatch({selectedTime: time, sitePannelIsOpen: false, showSiteRightPanel: false, isTimeTrack: true});
    setSessionData({
      selectedTime: time,
      isTimeTrack: true,
    });
    let param = {
      instanceIdList: [],
      timestamp: 0,
      targetTimestamp: time,
    };
    param.instanceIdList.push(solutionId);
    param.instanceIdList.push(siteId);
    queryCurrentIds(param, async({data: idMap}) => {
      if (!idMap || Object.entries(idMap).length === 0) {
        setShowMessage(true);
        destroyW3D();
        return;
      }
      const siteData = await getSiteOverViewGrid({
        solutionId: idMap[solutionId],
        siteId: idMap[siteId],
        timestamp: time,
      },
      );
      if (siteData.data.environmentType === 1) {
        setIsVM(true);
      } else {
        setIsVM(false);
      }
      siteData.data.siteName = `${siteData.data.siteName}`;
      siteData.data.siteInfoList.forEach(siteInfo => {
        siteInfo.siteName = `${siteInfo.siteName}`;
      });
      if (siteData.data.drGroupList.length === 0) {
        setShowMessage(true);
      } else {
        setShowMessage(false);
      }
      setData([
        {
          title: $t('overViewName'),
          url: '/eviewwebsite/index.html#path=/businesstopo',
        }, {
          title: siteData.data.siteName,
        },
      ]);
      await initW3D(w3dContainerRef, dispatch, siteId, solutionId, {
        siteData: siteData.data,
        groupId: '',
        environmentType: siteData.data.environmentType,
        changeSite: async changeData => {
          changeUrl(changeData.siteId);
        },
      });
      setSessionData({
        selectedTime: time,
        isTimeTrack: true,
        siteData,
      });
    });
  };

  // 退出回溯状态
  const backTimeTrack = async() => {
    setShowMessage(false);
    startRefresh();
    dispatch({selectedTime: 0, isTimeTrack: false});
    setSessionData(
      {
        isTimeTrack: false,
        selectedTime: 0,
      });
    dispatch({sitePannelIsOpen: false, showSiteRightPanel: false});
    setSiteHistoryData({});
    destroyW3D();
    changeUrl(siteId);
  };

  return (
    <BUSINESS_TOPO_CONTEXT.Provider value={{state, dispatch}} >
      <ConfigProvider version="aui3-1" theme="evening">
        <div style={{background: '#191919'}} id="siteDrillTopo">
          {state.sitePannelIsOpen && <SiteRightPannel refresh={refreshFlag} isVm={isVm} />}
          <Toolbar isSite={true} />
          <div className="moType_back_btn">
            <Crumbs data={data} seprator="/" />
          </div>
          <div id="site_main_container" style={{height: '100%', width: '100%', 'backgroud-color': 'black'}}>
            <div
              className="topology3d_main_container_full w3d_container"
              ref={w3dContainerRef}
            />
            <MessageDeatil display={showMessage} main={false} />
          </div>

          <TimeLinePanel
            pageType={SITE_HOME}
            renderTopology={timeTrack}
            backTimeTrack={backTimeTrack}
          />
          {
            showGray && <Progress isSite={true} />
          }
        </div>
        <DivMessage text={$t('filtered_info')} type="warn" style={style2} display={state.hasFiltered}
          disposeTimeOut={3000} onClose={() => {
            dispatch({
              hasFiltered: false,
            });
          }}
        />
        <DivMessage text={$t('delete_info')} type="warn" style={style2}
          display={state.showDeleteInfo}
          disposeTimeOut={3000}
          onClose={() => {
            dispatch({
              showDeleteInfo: false,
            });
          }}
        />
      </ConfigProvider>
    </BUSINESS_TOPO_CONTEXT.Provider>
  );
}

export default SiteHome;
