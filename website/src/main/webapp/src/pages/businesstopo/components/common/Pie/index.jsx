import React, {useMemo, useEffect, useRef, useState} from 'react';
import * as echarts from 'echarts';
import Legend, {MoreLegend} from '../Legend';

const color = [
  'rgba(241,110,51)',
  'rgba(106,64,246)',
  'rgba(58,132,255)',
  'rgba(54,221,242)',
  'rgba(25,216,149)',
  'rgba(255,194,153)',
  'rgba(153,75,44)',
  'rgba(246,202,104)',
  'rgba(255,187,187)',
  'rgba(255, 0, 0)',
  'rgba(0, 255, 0)',
  'rgba(255, 255, 0)',
  'rgba(255, 0, 255)',
  'rgba(0, 128, 0)',
  'rgba(0, 0, 128)',
  'rgba(128, 128, 0)',
  'rgba(128, 0, 128)',
  'rgba(0, 128, 128)',
  'rgba(192, 192, 192)',
  'rgba(255, 20, 147)',
  'rgba(75, 0, 130)',
  'rgba(0, 0, 0)',
];

const _getPieOption = ({title, number, desc, data}) => {
  return {
    color,
    graphic: { // 添加原生图形元素组件
      elements: [{
        type: 'text',
        left: 'center',
        top: '40%',
        style: {
          text: number,
          fontSize: 32,
          textAlign: 'center',
          lineHeight: 38, fill: '#F5F5F5 ',
        },
      }, {
        type: 'text', left: 'center', top: '60%', style: {
          text: desc, fontSize: 12, textAlign: 'center', fill: '#BBBBBB',
        },
      }],
    },
    title: {
      show: false,
      text: title,
      top: 'left', padding: 0, textStyle: {
        fontSize: 16, color: '#F5F5F5', lineHeight: 20, fontWeight: 500,
      },
    },
    tooltip: {
      // position: 'right',
      trigger: 'item',
      backgroundColor: '#393939',
      borderColor: '#202020',
      borderWidth: 2,
      appendToBody: true,
      formatter(params) {
        return `
          <div style="display:inline-block;background-color:#393939;">
              <div style="color: #F2F2F2; font-weight: bold">${params.name}</div>
              <div style="color: #ECECEC">${params.percent}%&nbsp;&nbsp;&nbsp;&nbsp;TPS:${params.value}</div>
          </div>
        `;
      },
    },
    legend: {
      show: false,
      orient: 'horizontal', bottom: '0%', left: 'center', right: 'center', type: 'scroll', icon: 'circle', itemGap: 70,
      textStyle: {
        color: '#BBBBBB',
      }, itemWidth: 15, itemHeight: 10, pageTextStyle: {
        color: '#BBBBBB',
      }, pageIconColor: '#aaa', pageIconInactiveColor: '#2f4554', formatter: param => param.trim() // 保证居中
      ,
    },
    series: [{
      hoverAnimation: false,
      radius: ['100%', '95%'],
      center: ['50%', '50%'],
      name: title,
      type: 'pie',
      width: '60%',
      height: '60%',
      top: 'middle',
      bottom: 'middle',
      right: 'center',
      left: 'center',
      labelLine: {
        show: false,
      },
      label: {
        show: false,
      },
      emphasis: {
        focus: 'self',
      },
      itemStyle: {
        borderRadius: 10, borderColor: '#272727', borderWidth: 0.4,
      },
      data,
    }],
  };
};


/**
 *
 *
 * @param props
 * props.title: string
 * props.data
 * [{
 *   id: string;
 *   name: string;
 *   value: string | number;
 * }],
 * centerValue: string,
 * centerDesc: string,
 */
const Pie = (props) => {
  const {
    title = '',
    data = [],
    centerValue = '',
    centerDesc = '',
    style = {},
  } = props;

  const [selected, setSelected] = useState({});
  useEffect(() => {
    setSelected(data.reduce((curr, next) => {
      curr[next.id] = true;
      return curr;
    }, {}));
  }, [data]);

  const legendData = useMemo(() => {
    return data.map(v => {
      return {
        id: v.id,
        name: v.name,
        selected: selected[v.id],
      };
    });
  }, [selected]);

  const containerRef = useRef(null);
  const echartInstanceRef = useRef(null);

  const option = useMemo(() => {
    return _getPieOption({
      title,
      number: centerValue,
      desc: centerDesc,
      data,
    });
  }, [data, title, centerValue, centerDesc]);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    let instance = echarts.getInstanceByDom(containerRef.current);
    try {
      instance?.off('click'); // 解绑事件
      if (!instance) {
        instance = echarts.init(containerRef.current);
        instance.setOption(option);
      } else {
        instance.setOption({
          ...option,
          // @ts-expect-error 这里不进行检查 保留原先的图例勾选状态
          legend: { ...option.legend, selected: instance.getOption().legend[0].selected },
        }, {
          notMerge: true, // 配置由chart维度，不需要echarts自己维护
          lazyUpdate : true, // 异步画图
        });
      }
      // 重新监听事件， 因为chart实例更新了
      instance.on('click', (params) => {
        // 点击
      });
      echartInstanceRef.current = instance;
      window.a = instance;
    } catch (e) {
      // horizon effect对于错误无法抛出，这里主动抛出
      const _log = console;
      _log.error(e);
    }
  }, [option]);

  useEffect(() => {
    const container = containerRef.current;
    return () => {
      if (!container) {
        return;
      }
      const instance = echarts.getInstanceByDom(container);
      if (instance) {
        instance.off('click');
        echarts.dispose(instance);
        echartInstanceRef.current = undefined;
      }
    };
  }, []);

  return (
    <div style={style}>
      <div ref={containerRef} style={{width: '100%', height: '100%'}} />

      <div style={{padding: '0 30px'}}>
        <MoreLegend
          data={legendData}
          style={{marginTop: '-30px'}}
          color={color}
          onMouseEnter={(id) => {
            if (!echartInstanceRef.current) {
              return;
            }

            echartInstanceRef.current.dispatchAction({
              type: 'highlight',
              name: legendData.filter(v => v.id === id).map(v => v.name),
            });
          }}
          onMouseLeave={(id) => {
            if (!echartInstanceRef.current) {
              return;
            }

            echartInstanceRef.current.dispatchAction({
              type: 'downplay',
              name: legendData.filter(v => v.id === id).map(v => v.name),
            });
          }}
          onClick={(id) => {
            if (!echartInstanceRef.current) {
              return;
            }

            setSelected(prevSelected => {
              const oldSelected = { ...prevSelected };
              if (event.ctrlKey) {
                // 点击的时候按住ctl键，只改变当前图例的勾选状态
                oldSelected[id] = !oldSelected[id];
              } else {
                if (oldSelected[id]) {
                  // 点击的图例已勾选
                  if (Object.values(oldSelected).every(isSelect => isSelect)) {
                    // 当前为全选，单选该图例
                    for (const legendKey of Object.keys(oldSelected)) {
                      oldSelected[legendKey] = false;
                    }
                    oldSelected[id] = true;
                  } else {
                    // 当前为非全选，勾选全部图例
                    for (const legendKey of Object.keys(oldSelected)) {
                      oldSelected[legendKey] = true;
                    }
                  }
                } else {
                  // 点击的图例未勾选，单选该图例
                  for (const legendKey of Object.keys(oldSelected)) {
                    oldSelected[legendKey] = false;
                  }
                  oldSelected[id] = true;
                }
              }

              const selectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                return value;
              }).map(([key, value]) => {
                return {
                  name: legendData.find(v => v.id === key)?.name,
                };
              });
              const unSelectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                return !value;
              }).map(([key, value]) => {
                return {
                  name: legendData.find(v => v.id === key)?.name,
                };
              });
              echartInstanceRef.current.dispatchAction({
                type: 'legendSelect',
                batch: selectedNameList,
              });

              echartInstanceRef.current.dispatchAction({
                type: 'legendUnSelect',
                batch: unSelectedNameList,
              });
              return oldSelected;
            });
          }}
        />
      </div>
    </div>
  );
};

export default Pie;
