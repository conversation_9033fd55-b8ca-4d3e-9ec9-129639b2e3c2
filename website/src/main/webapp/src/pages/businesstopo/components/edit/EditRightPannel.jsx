import React, { useContext, useState, useEffect, useRef } from 'react';
import { LabelField, Toggle, Tooltip, TextField } from 'eview-ui';
import Card from '@pages/businesstopo/components/edit/Card';
import CustomSelect from '@pages/businesstopo/components/edit/CustomSelect';
import { BUSINESS_TOPO_CONTEXT } from '@pages/businesstopo/const';
import { $t, validate } from '@util';

const EditRightPannel = ({ editPannel, pannelContent, deleteCardHandele, allGroupData, setAllGroupData, areaIcons }) => {
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const { editRightPannelOpen, editRightPannelShow, editRightPanelSiteName } = state;

  const [siteName, setSiteName] = useState('');
  const siteNameRef = useRef(null);
  const nameInputRef = useRef(null);
  useEffect(() => {
    if (editRightPanelSiteName) {
      setSiteName(pannelContent?.siteData?.siteName);
    }
    if (pannelContent.businessName) {
      nameInputRef.current?.validate();
    }
  }, [pannelContent]);

  if (!pannelContent && !editRightPanelSiteName) {
    return <div />;
  }

  let style = {};
  let primaryIndex = -1;
  if (!editRightPanelSiteName) {
    let outline = 'none';
    if (!pannelContent.businessName || pannelContent.businessName.length === 0) {
      outline = '1px solid #e41f2b';
    }

    if (pannelContent.rightPannelData) {
      primaryIndex = pannelContent.rightPannelData.findIndex(item => item.indicatorDisplayType === 1);
    }
    primaryIndex = primaryIndex === -1 ? '' : primaryIndex;

    style = {
      width: '368px',
      backgroundColor: '#313131',
      color: '#f5f5f5',
      outline,
    };
  }

  let labelStyle = { display: 'block', paddingLeft: '0px' };
  const options = [
    { value: 'north', label: 'Northbound Service' },
    { value: 'south', label: 'Southbound Service' },
  ];
  const data = [0, 1];
  const toggleStyle = { verticalAlign: 'text-top' };
  let options1 = [];
  const options2 = [
    { value: '0', label: 'smpp' },
    { value: '1', label: 'common' },
    { value: '2', label: '4G' },
    { value: '3', label: '5G' },
  ];

  let cadList = [];
  if (pannelContent && pannelContent?.rightPannelData) {
    pannelContent.rightPannelData.forEach((pannelData, index) => {
      options1.push({
        value: index,
        label: $t('cbs.indicators') + (index + 1),
      });
      cadList.push({
        title: $t('cbs.indicators') + (index + 1),
        key: pannelData.key,
        content: [
          { name: $t('cbs.motype'), value: pannelData.moName },
          { name: $t('cbs.measUnitKey'), value: pannelData.measUnitName },
          { name: $t('cbs.measUnit.indicator'), value: pannelData.indexName },
          { name: $t('cbs.measUnit.originalValue'), value: pannelData.displayValue },
        ],
      });
    });
  }

  const onChangeSiteName = val => {
    setSiteName(val);
  };

  const customValidation = (val, id) => {
    let validationReturnMap = validate(['resourceValidChar', 'checkLength'], val, id, null, 128);
    if (!validationReturnMap.result) {
      return validationReturnMap;
    }
    //  重复名称校验
    let tmpData = [...areaIcons.north.map(item => item.businessName), ...areaIcons.south.map(item => item.businessName)];
    const count = tmpData.filter(item => item === val).length;
    if (count > 1) {
      validationReturnMap.result = false;
      validationReturnMap.message = $t('sys_valid_name_duplicate');
    }
    return validationReturnMap;
  };

  const onConfirmSiteName = () => {
    if (!siteNameRef.current.validate()) {
      siteNameRef.current.focus();
      return;
    }

    const currentId = pannelContent?.siteData?.siteId;
    const indexId = allGroupData.findIndex(item => item.siteId === currentId);
    if (indexId !== -1) {
      let tempAllData = [...allGroupData];
      tempAllData[indexId].siteName = siteName;
      setAllGroupData(tempAllData);
    }
    dispatch({
      editRightPanelSiteName: false,
      editRightPannelOpen: false,
      editRightPannelShow: false,
    });
  };

  const onCancelSiteName = () => {
    dispatch({
      editRightPanelSiteName: false,
      editRightPannelOpen: false,
      editRightPannelShow: false,
    });
  };

  return (
    state.editRightPannelOpen && (
      <div
        id="editRightPannel"
        style={{
          display: 'inline-block',
          right: 0,
          position: 'absolute',
          zIndex: 999,
          width: editRightPannelShow ? 400 : 0,
          height: 'calc(100vh - 2rem)',
          backgroundColor: '#272727',
        }}
      >
        <div
          className="dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-bottomPanel"
          style={{
            display: editRightPannelOpen ? '' : 'none',
            position: 'fixed',
            zIndex: 1000,
            opacity: 1,
          }}
          onClick={() => {
            dispatch({ editRightPannelOpen: true, editRightPannelShow: !editRightPannelShow });
          }}
        >
          <div className={`topo-common-panel-${editRightPannelShow ? 'close' : 'expend'}-vertical-right`} />
        </div>

        <div className="edit-right-panel" style={{ display: editRightPannelShow ? 'block' : 'none' }}>
          {editRightPanelSiteName ? (
            <div style={{ padding: '24px' }}>
              <div className="edit_site_name_title">
                {$t('site.title')}
              </div>
              <div className="edit_site_name_split_line" />
              <div style={{ marginBottom: '12px' }}>
                <LabelField text={$t('site.name')} required={true} style={{ width: '90px' }} />
              </div>
              <TextField
                required={true}
                hideRequiredMark={true}
                hintType="tip"
                autoComplete="off"
                inputStyle={{ width: '352px' }}
                validator={(val, id) => validate(['siteNameValidChar', 'checkLength'], val, id, null, 128)}
                value={siteName}
                onChange={onChangeSiteName}
                ref={ref => siteNameRef.current = ref}
              />
              <div className="action-buttons" style={{ marginTop: '24px', right: '24px' }}>
                <button className="cancel-button" onClick={onCancelSiteName}>{$t('button.cancel')}</button>
                <button className="confirm-button" onClick={onConfirmSiteName}>{$t('button.sure')}</button>
              </div>
            </div>
          ) : (
            <>
              <div className="right-pannel-text-area" style={{ marginTop: '24px', minHeight: '24px' }}>

                <Tooltip placement="topLeft" content={pannelContent.businessName} trigger={['hover']}>
                  <div style={{
                    maxWidth: '350px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                  >
                    { pannelContent.businessName}
                  </div>
                </Tooltip>
              </div>
              <div className="separator" />
              <div className="right-pannel-text-area">
                <LabelField text={$t('business.type.name')} required={true} />
              </div>
              <div style={{ margin: '16px 0px 0px 16px', width: '368px' }}>
                <TextField
                  labelStyle={labelStyle}
                  required={true}
                  hideRequiredMark={true}
                  hintType="tip"
                  validator={(val, id) => customValidation(val, id)}
                  autoComplete="off"
                  inputStyle={style}
                  value={pannelContent.businessName}
                  onChange={value => {
                    editPannel(pannelContent.business, 'businessName', value);
                  }}
                  ref={nameInputRef}
                  onBlur={() => {
                    return nameInputRef.current?.validate();
                  }}
                />
              </div>

              <div className="separator" style={{ width: '368px' }} />
              <div className="right-pannel-text-area">
                <LabelField text={$t('business.type')} required={true} />
              </div>
              <div style={{ margin: '16px 0px 0px 16px', width: '368px' }}>
                <CustomSelect
                  options={options}
                  value={pannelContent.business}
                  onChange={value => editPannel(pannelContent.business, 'businessType', value)}
                />
              </div>
              <div className="separator" />

              <div className="right-pannel-text-area">
                <LabelField text={$t('business.icon')} required={true} />
              </div>
              <div style={{ margin: '16px 0px 0px 16px', width: '368px' }}>
                <CustomSelect
                  options={options2}
                  value={pannelContent.iconType}
                  onChange={value => editPannel(pannelContent.business, 'businessIcon', value)}
                />
              </div>
              <div className="separator" style={{ width: '368px' }} />
              <div
                style={{
                  margin: '16px 18px 0px 30px',
                  display: 'flex',
                  'justify-content': 'space-between',
                  position: 'relative',
                }}
              >
                <LabelField
                  text={$t('business.main')}
                  required={true}
                  style={{ minWidth: '100px', display: 'inline-block' }}
                />
                <Tooltip placement="top" content={$t('business.main.tips')} trigger={['hover', 'focus']}>
                  <div className="info2" />
                </Tooltip>
                <Toggle
                  data={data}
                  toggled={pannelContent.isMain}
                  onToggle={value => editPannel(pannelContent.business, 'isMain', value)}
                  style={toggleStyle}
                />

                <LabelField text={$t('business.toggled')} required={true} />
                <Toggle
                  data={data}
                  toggled={pannelContent.toggled}
                  onToggle={value => editPannel(pannelContent.business, 'toggled', value)}
                  style={toggleStyle}
                />
              </div>

              <div className="separator" style={{ width: '368px' }} />
              <div
                style={{
                  'margin-left': '30px',
                  'margin-top': '20px',
                  display: 'flex',
                  'justify-content': 'space-between',
                  position: 'relative',
                }}
              >
                <LabelField text={$t('key.indicators')} required={true} style={{ color: '#e0e0e0' }} />
                <span
                  className="add_icon"
                  onClick={() => {
                    dispatch({
                      editAddPannelShow: true,
                      editAddPannelStatus: 1,
                      netWorkTreeParam: {},
                      unitTree: {
                        measureType: 'all',
                        measureUnit: '',
                        IndicatorId: '',
                      },
                      objectTable: {
                        isObjectNoData: true,
                      },
                    });
                  }}
                />
              </div>
              <div
                style={{
                  'margin-left': '30px',
                  display: 'flex',
                  'justify-content': 'space-between',
                  position: 'relative',
                }}
              >
                <div style={{ 'margin-top': '13px' }}>
                  <LabelField text={$t('primary.indicators')} required={true} style={{ color: '#e0e0e0' }} />
                </div>
                <Tooltip placement="top" content={$t('tps.indicators')} trigger={['hover', 'focus']}>
                  <div className="info" />
                </Tooltip>

                <div style={{ marginTop: '16px' }}>
                  <CustomSelect
                    options={options1}
                    value={primaryIndex}
                    onChange={value => editPannel(pannelContent.business, 'primaryIndicator', value)}
                    style={{ width: '180px', marginRight: '16px' }}
                  />
                </div>
              </div>
              <div className="cardList">
                {pannelContent.rightPannelData &&
                  cadList.map((card, index) => (
                    <Card
                      key={card.key}
                      index={index}
                      keyName={card.key}
                      titleName={card.title}
                      content={card.content}
                      deleteCardHandele={deleteCardHandele}
                    />
                  ))}
              </div>
              <div />
            </>
          )}
        </div>
      </div>
    )
  );
};

export default EditRightPannel;
