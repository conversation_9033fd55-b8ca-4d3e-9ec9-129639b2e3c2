import React from 'react';
import {$t} from '@util';
import { TASK_PAGE_TYPE } from '../const';

export const initialRuleConfigState = {
  curTaskPage: TASK_PAGE_TYPE.TASK_LIST,
  taskListInfo: {
    taskName: '',
    pageSize: 10,
    currentPage: 1,
    recordCount: 0,
    pageResults: [],
    emptyTableMsg: $t('intelligent.incident.task.manage.table.loading'),
    autoRefreshTimeStamp: 0,
  },
  algorithmList: [],
  knowledgeBaseList: [],
  solutionList: [],
  selectedSolution: '',
  selectedKnowledgeBase: '',
  algorithmParamsList: {
    querySuccess: false,
    algorithmParam: [],
    algorithmModelId: '',
    algorithmModelName_Version: '',
    algorithmPageResults: [],
    algorithmParamRef: {},
  },
  basicInfoList: {
    taskName: '',
    displayResult: false,
    trainCycle: '',
    taskDescription: '',
  },
  viewDetailInfo: null,
};

const configurePageInfoMap = new Map()
  .set('setCurTaskPage', (action) => ({ curTaskPage: action.curTaskPage}))
  .set('setTaskListInfo', (action) => ({ taskListInfo: action.taskListInfo }))
  .set('setAlgorithmList', (action) => ({ algorithmList: action.algorithmList }))
  .set('setKnowledgeBaseList', (action) => ({ knowledgeBaseList: action.knowledgeBaseList }))
  .set('setSolutionList', (action) => ({ solutionList: action.solutionList }))
  .set('setSelectedKnowledgeBase', (action) => ({ selectedKnowledgeBase: action.selectedKnowledgeBase }))
  .set('setSelectedSolution', (action) => ({ selectedSolution: action.selectedSolution }))  
  .set('setAlgorithmParamsList', (action) => ({ algorithmParamsList: action.algorithmParamsList }))
  .set('setBasicInfoList', (action) => ({ basicInfoList: action.basicInfoList }))
  .set('setViewDetailInfo', (action) => ({ viewDetailInfo: action.viewDetailInfo }));

export const ruleConfigReducer = (state, action) => {
  if (configurePageInfoMap.get(action.type)) {
    return {
      ...state,
      ...configurePageInfoMap.get(action.type)(action),
    };
  }
  return state;
};
