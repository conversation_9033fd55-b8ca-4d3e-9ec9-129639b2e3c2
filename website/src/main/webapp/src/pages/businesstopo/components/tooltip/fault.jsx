import '@pages/businesstopo/css/tooltip/fault.less';
import React, {useEffect, useState} from 'react';
import {Tooltip} from 'eview-ui';
import closeIcon from '../../../../apps/businesstopo/assets/closeicon.svg';
import {getAlarmDeatilData, getMoreAlarmData} from '../../api';
import {$t, registerResource} from '@util';
import {
  dateTimeFormat,
  jumpAlarmLogPage, jumpAlarmPage,
  jumpViewMoreHistoryPage,
  jumpViewMorePage,
} from '../../../../commonUtil/tools';
import i18n from '../../locales/tooltip';
import PropTypes from 'prop-types';
import {deepClone} from '@digitalview/fe-utils';

function Fault(props) {
  const [leftTriangle, setLeftTriangle] = useState(true);
  let length = props.faultData.length ? props.faultData.length : 4;
  let triangleStyle;
  let faultStyle;
  let faultWidth = '420px';
  if (props.faultData.isMM && props.faultData.isManager) {
    let clientX = props.faultData.event.clientX;
    let width = document.getElementById('topology3d_main_container').getBoundingClientRect().width;
    if (clientX + 430 < width) {
      faultStyle = {
        left: -5,
        bottom: -50,
      };
      setLeftTriangle(true);
    } else {
      faultStyle = {
        left: -425,
        bottom: -50,
      };
      setLeftTriangle(false);
    }
    triangleStyle = {bottom: '30px'};
  } else {
    setLeftTriangle(true);
    faultStyle = {
      left: 0,
      top: props.faultData.top ? props.faultData.top : -30,
    };
    triangleStyle = {top: '22px'};
  }

  if (props.faultData.isMM) {
    faultWidth = '400px';
  }

  registerResource(i18n, 'tooltip');

  const [chooseAlarm, setChooseAlarm] = useState(null);
  const [display, setDisplay] = useState(true);

  const [alarmData, setAlarmData] = useState({
    total: 0,
    dataList: [],
  });

  useEffect(() => {
    // 请求数据
    if (display) {
      let faultDataParam = deepClone(props.faultData.params);
      if (faultDataParam.csnList.length > 0) {
        const arr = faultDataParam.csnList.split(',');
        const first100 = arr.slice(0, 100);
        faultDataParam.csnList = first100.join(',');
      }
      getAlarmDeatilData({
        ...faultDataParam,
      }, ({data, resultCode}) => {
        setAlarmData({
          total: data.length,
          dataList: data,
          jump: data.alarmJumping,
        });
      });
    } else {
      // 清空
      setAlarmData({
        total: 0,
        dataList: [],
      });
    }

    setChooseAlarm(null);
  }, [props.faultData.display, JSON.stringify(props.faultData.params)]);

  return (
    display &&
    <div id="fault" style={{
      ...faultStyle,
      width:faultWidth,
    }} onClick={e => {
      e.stopPropagation();
      e.nativeEvent.stopImmediatePropagation();
    }}
    >
      {leftTriangle && <div className='triangle' style={{...triangleStyle}} />}
      { !leftTriangle && <div className='triangle2' style={{...triangleStyle}} />}
      <div className="header">
        <div className="title">{$t('tooltip.mo.exception.statistics')}&nbsp;({alarmData.total > 99 ? '99+' : alarmData.total})</div>
        <div
          className="close" style={{
            backgroundImage: `url(${closeIcon})`,
          }}
          onClick={e => {
            setChooseAlarm(null);
            setDisplay(false);
          }}
        />
      </div>

      <div className="hiddenScroll">
        <div className="contentList">
          {
            alarmData.dataList.slice(0, length).map((value, index) => (
              <div className={`content ${chooseAlarm === index ? 'clickBackground' : ''}`}>
                <div className="contentLeft">
                  <Tooltip
                    placement="topLeft"
                    content={value.alarmName}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="contentTitle">
                      {value.alarmName}
                    </div>
                  </Tooltip>
                  <div className="contentTime">
                    <div>{$t('tooltip.fault.view.time')}:&nbsp;&nbsp;</div>
                    <div>{dateTimeFormat(value.latestOccurUtc)}</div>
                  </div>
                </div>
                <div className="contentRight" onClick={e => {
                  setChooseAlarm(index);
                  if (props.faultData.params.timestamp > 0) {
                    jumpAlarmLogPage(value.csn, value.occurUtc);
                  } else {
                    jumpAlarmPage(value.csn, value.occurUtc);
                  }
                }}
                >
                  {$t('tooltip.view')}
                </div>
              </div>
            ))
          }
        </div>
      </div>

      <div className="more" onClick={e => {
        getMoreAlarmData(props.faultData.params, ({data, resultCode}) => {
          if (props.faultData.params.timestamp > 0) {
            jumpViewMoreHistoryPage(data);
          } else {
            jumpViewMorePage(data);
          }
        });
      }}
      >
        {$t('tooltip.view.more')}
      </div>
    </div>
  );
}

Fault.prototype = {
  faultData: PropTypes.shape({
    params: PropTypes.shape({
      siteId: PropTypes.number,
      timestamp: PropTypes.number,
    }).isRequired,
    isMM:PropTypes.bool,
  }).isRequired,
};

export default Fault;
