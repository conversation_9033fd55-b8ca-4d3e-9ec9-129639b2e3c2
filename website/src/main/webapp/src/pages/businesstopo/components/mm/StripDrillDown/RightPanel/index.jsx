import React, {useContext, useEffect, useRef, useState, useMemo, useCallback} from 'react';
import {$t, registerResource} from '@util';
import i18n from './locale';
import Pie from '../../../common/Pie';
import Line from '../../../common/Line';
import SelectButtonGroup from '../../../common/SelectButtonGroup';
import AlarmEventPanel from '../../../AlarmEventPanel';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import {handleJumpPageData} from '../../../../util';
import {BUSINESS_TOPO_CONTEXT, getHistoryIndexValueList, getName} from '../../../../const';
import {getAlarmData} from '../../../../api';
import './index.less';
import {getQuery} from '../../../../a3dPages/components/utils';
import {queryMMTopPie as queryMMStripDownTopData, queryMMHistoryLineUrl as queryMMStripDownLineData} from '../../../../a3dPages/components/MMOverviewPage/api';
import {getPodIndicatorData} from '../../../../api/moTypeDrill';
import {sorter} from '@digitalview/fe-utils';
import {sortHistoryListData} from '../../../../../../commonUtil/tools';

const MENU = {
  THIRD: 0,
  CHANNEL: 1,
  STRIP: 2,
};

const MODEL_TYPE = {
  [MENU.THIRD]: 1,
  [MENU.CHANNEL]: 101,
  [MENU.STRIP]: 2,
};

let _queryTopPieRequestId = 0;
let _queryLineRequestId = 0;
let _queryAlarmRequestId = 0;

/**
 *
 * @return {JSX.Element}
 */
const Panel = (props) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    isTimeTrack,
    selectedTime,
  } = state;
  const query = getQuery();

  registerResource(i18n, 'mmStripRightPanel');
  const {refreshFlag} = props;

  /**
   * 数据依赖流程
   * B 饼图 | 状态 依赖是否点击了XX
   *
   * C 指标展示 依赖 A + 左侧3D 选择
   * D 折线图 依赖C
   *
   * E 告警 全局 依赖 左侧3D 条带点击
   */
  // 饼图数据
  const [topPieData, setTopPieData] = useState([]);

  // 当前左侧是否点击了某个节点
  const [selectNode, setSelectNode] = useState({
    stripId: '',
    stripUnitId: '',
    nodeId: '',
  });

  const stripIdRef = useRef(null); // 存储stripId，给事件监听使用
  const solutionId = useMemo(() => {
    if (isTimeTrack) {
      stripIdRef.current = state.stripId;
      return state.solutionId;
    } else {
      stripIdRef.current = getQuery().stripId || '';
      return getQuery().solutionId || '';
    }
  }, [state.solutionId, state.stripId, isTimeTrack, selectedTime]);

  const [nodeInfoList, setNodeInfoList] = useState([]);

  useEffect(() => {
    eventBus.addListener('from3d_gridData', (setting) => {
      setSelectNode((prevState) => {
        return {
          stripId: getQuery().stripId, // 该变量是指为了触发刷新页面，实际使用参数的时候使用ref里的值
          stripUnitId: setting.id,
          nodeId: '',
        };
      });
      setNodeInfoList(setting.drGroupList || []);
    });

    eventBus.addListener('to3d_updateDrillDownData', (setting) => {
      setSelectNode((prevState) => {
        return {
          stripId: getQuery().stripId, // 该变量是指为了触发刷新页面，实际使用参数的时候使用ref里的值
          stripUnitId: setting.id,
          nodeId: '',
        };
      });
      setNodeInfoList(setting.drGroupList || []);
      dispatch({showKPIPanel: true});
    });

    eventBus.addListener('from3d_showPanel', (setting) => {
      if (setting.id) {
        setIndicatorList([]);
        setLineData({});
      }

      setSelectNode((prevState) => {
        if (!prevState.stripUnitId) {
          return prevState;
        }

        return {
          stripId: getQuery().stripId, // 该变量是指为了触发刷新页面，实际使用参数的时候使用ref里的值
          stripUnitId: prevState.stripUnitId,
          nodeId: setting.id || '',
        };
      });
      if (setting.id) {
        dispatch({showKPIPanel: true});
      }
    });

    eventBus.addListener('resetSolutionIdAndStripId', (setting) => {
      dispatch({solutionId: setting.solutionId, stripId: setting.stripId});
    });
  }, []);

  useEffect(() => {
    setSelectNode((prevState) => {
      return {
        ...prevState,
        nodeId: '',
      };
    }); // 时间回溯 重置选择的节点
  }, [selectedTime]);

  const [indicatorList, setIndicatorList] = useState([]);

  // 指标数据格式
  const [chooseIndicatorId, setChooseIndicatorId] = useState({});
  const indicatorData = useMemo(() => {
    return indicatorList.map((v, index) => {
      return {
        id: `${v.id}~~${v.instanceId}`,
        name: v.indexName,
        onClick: () => {
          setChooseIndicatorId({id: v.id, instanceId: v.instanceId});
          // 说明是实例的指标点击
          if (selectNode.nodeId) {
            let param = {
              instanceId: selectNode.nodeId,
              stripeId: stripIdRef.current,
              stripeUnit: selectNode.stripUnitId,
              indicatorId: v.id,
            };
            if (selectedTime) {
              param.endTime = selectedTime;
            }
            const requestId = ++_queryLineRequestId;
            getPodIndicatorData(param, (data) => {
              // 多次请求 只处理最后一次的
              if (requestId !== _queryLineRequestId) {
                return;
              }

              paintLine((data?.data?.podIndicatorList || []).map(v2 => {
                v2.dnList = [v2.dn];
                return v2;
              }));
            });
          }
        },
      };
    });
  }, [JSON.stringify(indicatorList)]);

  // 请求TOP数据方法
  const reqPiePending = useRef(false); // 正在请求
  const getTopPieData = useCallback((param) => {
    const requestId = ++_queryTopPieRequestId;
    reqPiePending.current = true;
    queryMMStripDownTopData(param)
      .then((data) => {
        // 多次请求 只处理最后一次的
        if (requestId !== _queryTopPieRequestId) {
          return;
        }
        reqPiePending.current = false;
        // 设置TOP饼图
        const result = Object.entries(data?.data?.topIndicator || {}).reduce((curr, [key, value]) => {
          const obj = JSON.parse(key) || {};
          let instanceId = obj.instanceId;
          curr.push({
            id: `${obj.instanceId || ''}~~${obj.modelId || ''}~~${obj.modelName || ''}` || '',
            name: obj.modelName || '',
            value,
            instanceId,
            originalValue: obj.originalValue,
          });
          return curr;
        }, []);
        result.sort((a, b) => sorter(b.value, a.value));

        const originalValues = result.map(v => v.originalValue).filter(v => v);

        // 按indicatorId进行分组
        let tempIndicatorList = (data?.data?.indicatorList || []).filter(v => [0, 1].includes(v.indicatorDisplayType));
        const tempIndicatorObj = tempIndicatorList.reduce((curr, next) => {
          if (curr[next.indexName]) {
            curr[next.indexName].instanceId.push(next.instanceId);
          } else {
            curr[next.indexName] = {
              ...next,
              instanceId: [next.instanceId],
            };
          }
          return curr;
        }, {});
        tempIndicatorList = Array.from(Object.values(tempIndicatorObj));
        const goldList = tempIndicatorList.filter(v => v.indicatorDisplayType === 1);
        const otherList = tempIndicatorList.filter(v => v.indicatorDisplayType === 0);
        goldList.sort((a, b) => sorter(a.indexName, b.indexName));
        goldList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
        otherList.sort((a, b) => sorter(a.indexName, b.indexName));
        otherList.sort((a, b) => sorter(a.indicatorSortNumber, b.indicatorSortNumber));
        setTopPieData(result);
        setIndicatorList([...goldList, ...otherList].map((v, i) => {
          return {
            id: `${v.indexName}_${i}_${stripIdRef.current}_${getQuery().stripUnit}`,
            indexName: v.indexName,
            measUnitKey: v.measUnitKey,
            measTypeKey: v.measTypeKey,
            originalValue: originalValues,
            moType: v.moType,
            indicatorDisplayType: v.indicatorDisplayType,
            instanceId: [stripIdRef.current],
          };
        }));
      })
      .catch((e) => {
        // 错误处理
        reqPiePending.current = false;
      });
  }, []);

  useEffect(() => {
    if (!solutionId) {
      return;
    }

    if (selectNode.nodeId || !stripIdRef.current || !selectNode.stripUnitId) {
      return;
    }

    let param = {
      solutionId,
      currentTime: selectedTime || 0,
      stripeId: stripIdRef.current,
      stripeUnit: selectNode.stripUnitId,
      modelType: MODEL_TYPE[MENU.STRIP],
    };
    getTopPieData(param);
  }, [solutionId, refreshFlag, selectedTime, JSON.stringify(selectNode)]);

  // 请求Line数据方法
  const [lineData, setLineData] = useState({});
  const [jumpData, setJumpData] = useState([]);

  const paintLine = useCallback((tempDataList) => {
    const timeObj = {
      Today: 0,
      Yesterday: 86400000,
      'Last week': 604800000,
    };
    const todayObj = {
      Today: 0,
    };
    let obj;

    const dataList = sortHistoryListData(tempDataList);
    if (dataList.length > 1) {
      obj = todayObj;
    } else {
      obj = timeObj;
    }

    const seriesData = [];
    dataList.map(item => {
      for (let propName in obj) {
        if (item.comparativeValueMap[propName]) {
          let name = getName(propName);
          if (dataList.length > 1) {
            name = `${item.siteName || item.podName || ''}`;
          }

          if (item.moName) {
            name = `${item.moName}_${name}`;
          }

          seriesData.push({
            name,
            id: `${name}_${seriesData.length}`,
            data: getHistoryIndexValueList(item.comparativeValueMap[propName], timeObj[propName]),
          });
        }
      }
    });

    // 设置折线图
    setJumpData(dataList);

    setLineData({
      unit: dataList?.[0]?.indexUnit || '',
      dataList: seriesData,
      time: {
        startTime: dataList?.[0]?.startTime,
        endTime: dataList?.[0]?.endTime,
      },
    } || {});
  }, []);

  const getLineData = useCallback((param) => {
    const requestId = ++_queryLineRequestId;
    queryMMStripDownLineData(param)
      .then((data) => {
        // 多次请求 只处理最后一次的
        if (requestId !== _queryLineRequestId) {
          return;
        }
        paintLine(data?.data?.siteHistoryList || []);
      })
      .catch((e) => {
        // 错误处理
      });
  }, []);

  const [reqLineParam, setReqLineParam] = useState({});
  useEffect(() => {
    if (reqPiePending.current || !chooseIndicatorId.id) { // 请求中，等待请求刷新
      return;
    }
    if (selectNode.nodeId) {
      return;
    }

    const indicator = indicatorList.find(v => v.id === chooseIndicatorId.id);
    setReqLineParam({
      moType: indicator?.moType || '',
      measUnitKey: indicator?.measUnitKey || '',
      measTypeKey: indicator?.measTypeKey || '',
      originalValues: indicator?.originalValue || [],
      indicatorDisplayType: indicator?.indicatorDisplayType || 0,
      currentTime: selectedTime || 0,
      modelType: MODEL_TYPE[MENU.STRIP],
      instanceIds: indicator?.instanceId || [],
      appSite: getQuery().stripUnit || '',
    });
  }, [JSON.stringify(chooseIndicatorId), selectedTime, JSON.stringify(indicatorList)]);

  useEffect(() => {
    if (Object.keys(reqLineParam).length > 0) {
      getLineData(reqLineParam);
    }
  }, [JSON.stringify(reqLineParam), refreshFlag]);

  // 实例折线图
  useEffect(() => {
    if (!selectNode.nodeId) {
      return;
    }

    let param = {
      instanceId: selectNode.nodeId,
      stripeId: stripIdRef.current,
      stripeUnit: selectNode.stripUnitId,
    };

    if (selectedTime) {
      param.endTime = selectedTime;
    }
    if (chooseIndicatorId.id) {
      param.indicatorId = chooseIndicatorId.id;
    }

    const requestId = ++_queryLineRequestId;
    getPodIndicatorData(param, (data) => {
      // 多次请求 只处理最后一次的
      if (requestId !== _queryLineRequestId) {
        return;
      }

      const reqIndicatorList = data?.data?.indicatorIdList || [];
      // 设置指标
      if (!chooseIndicatorId.id) {
        setIndicatorList(reqIndicatorList.map((v) => {
          return {
            id: v.indicatorId,
            indexName: v.indexName,
            measUnitKey: v.measUnitKey,
            measTypeKey: v.measTypeKey,
            originalValue: [],
            moType: v.moType,
            indicatorDisplayType: v.indicatorDisplayType,
          };
        }));
      }

      paintLine((data?.data?.podIndicatorList || []).map(v => {
        v.dnList = [v.dn];
        return v;
      }));
    }, () => {
      // 失败处理
    });
  }, [selectNode.nodeId, selectedTime, refreshFlag]);

  useEffect(() => {
    if (!stripIdRef.current || !selectNode.stripUnitId) {
      return;
    }

    // 告警数据
    let alarmParam = {
      stripeId: stripIdRef.current,
      stripeUnit: selectNode.stripUnitId,
      instanceId: selectNode.nodeId,
    };

    if (selectNode.nodeId) {
      alarmParam.drGroupId = (nodeInfoList || [])
        ?.find(v => v.moTypeInfoList.find(mo => mo.moTypeId === selectNode.nodeId))
        ?.drGroupId;
    }

    if (selectedTime) {
      alarmParam.timestamp = selectedTime;
    }

    const requestId = ++_queryAlarmRequestId;
    getAlarmData(
      alarmParam,
      res => {
        // 多次请求 只处理最后一次的
        if (requestId !== _queryAlarmRequestId) {
          return;
        }

        let alarmDatas = res.data;
        dispatch({
          alarmEventData: alarmDatas.alarmDataList,
        });
      },
    );
  }, [JSON.stringify(selectNode), selectedTime, isTimeTrack, solutionId, refreshFlag]);

  const instanceInfo = useMemo(() => {
    if (selectNode.nodeId) {
      const node = nodeInfoList
        .map(v => v.moTypeInfoList)
        .flatMap(v => v)
        .find(v => v.moTypeId === selectNode.nodeId);
      return {
        name: node?.moTypeName || '',
        count: node?.podCount || 0,
        errorCount: node?.podErrorCount || 0,
        showInstance: node?.applicationType === 3 || node?.applicationType === 4 || node?.environmentType === 1,
      };
    }
    return {
      name: '',
      count: 0,
      errorCount: 0,
      showInstance: false,
    };
  }, [selectNode.nodeId, JSON.stringify(nodeInfoList)]);

  const pieMarginBottom = useMemo(() => {
    return '0px';
  }, [topPieData?.length]);

  const alarmMarginTop = useMemo(() => {
    return '-16px';
  }, [lineData?.dataList?.length, pieMarginBottom, JSON.stringify(selectNode)]);

  return (
    <div
      style={{
        marginTop: '-3.75rem',
        padding: '1rem 1rem 0 1rem',
      }}
    >
      <TopTitle name={!selectNode.nodeId ? '' : instanceInfo.name} />
      {
        selectNode.nodeId &&
          <Progress
            count={instanceInfo.count}
            errorCount={instanceInfo.errorCount}
            needShowInstances={instanceInfo.showInstance}
          />
      }
      {
        !selectNode.nodeId && (
          <Pie
            {
            ...{
              title: '',
              data: topPieData,
              centerValue: String(topPieData.length || 'NA'),
              centerDesc: 'Top',
              style: {
                height: '290px',
                minHeight: '290px',
                marginTop: '-25px',
                marginBottom: pieMarginBottom,
                zIndex: '10',
                position: 'relative',
              },
            }
            }
          />
        )
      }

      <CenterTitle isStrip={!selectNode.nodeId} />

      <SelectButtonGroup
        {
        ...{
          data: indicatorData,
          autoChangeChoose: (index) => {
            if (indicatorList[index]?.id) {
              setChooseIndicatorId({
                id: indicatorList[index]?.id || '',
                instanceId: indicatorList[index]?.instanceId || [],
              });
            } else {
              setChooseIndicatorId({});
            }
          },
        }
        }
      />

      <Line
        data={lineData.dataList || []}
        unit={lineData.unit || ''}
        style={{
          height: '220px',
          minHeight: '220px',
          zIndex: '9',
          position: 'relative',
        }}
        onClick={(param, echartsIns) => {
          const pointInPixel = [param.offsetX, param.offsetY];
          if (!echartsIns.containPixel('grid', pointInPixel)) {
            return;
          }
          handleJumpPageData(jumpData);
        }}
        time={lineData.time}
      />

      <div
        style={{
          marginTop: alarmMarginTop,
        }}
      >
        <AlarmEventPanel isOverview={true} />
      </div>
    </div>
  );
};

const TopTitle = (props) => {
  const {name} = props;
  return !name ?
    <div className='strip-site-right-panel-text'>
      <span>{$t('mm.businessProportion')}</span>
    </div> :
    <div className='strip-site-right-panel-text'>
      <span>{`${name + $t('mm.detail')}`}</span>
    </div>;
};

const CenterTitle = (props) => {
  const {isStrip} = props;
  return isStrip ?
    <div className='strip-site-right-panel-text' style={{marginBottom: '0.5rem'}}>
      <span>{$t('mm.systemBusinessDataDistribution')}</span>
    </div> :
    <div className='strip-site-right-panel-text' style={{marginBottom: '0.5rem'}}>
      <span>{$t('mm.businessDataDistribution')}</span>
    </div>;
};


const Progress = (props) => {
  const {count, errorCount, needShowInstances} = props;

  const podText = useMemo(() => {
    // 当前是是数据库应用 或者是虚拟机 currentMoData.applicationType === 3 || isVm
    if (needShowInstances) {
      return `${$t('mm.total.instances')}（${count}）`;
    } else {
      return `Pods ${$t('mm.total')}（${count}）`;
    }
  }, [needShowInstances, count]);

  return (
    <div style={{marginTop: '24px'}}>
      <span className="mo_pod_text_mm">{podText}</span>
      <div style={{float: 'right', display: 'inline-block'}}>
        <div className='right-panel-kpi-red' />
        <span
          className='mo_pod_text_mm'
          style={{marginLeft: '4px'}}
        >
          {`${$t('abnormal')}(${errorCount})`}
        </span>
      </div>
      <div className="stripProgress">
        <div style={{flexGrow: errorCount}} className='stripProgressFirst' />
        <div
          style={{flexGrow: count - errorCount}}
          className='stripProgressSecond'
        />
      </div>
    </div>
  );
};


export {
  Panel,
};
