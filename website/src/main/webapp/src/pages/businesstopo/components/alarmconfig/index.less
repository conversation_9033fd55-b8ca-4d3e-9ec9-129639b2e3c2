.basic_container() {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}


.vertical_stretch_element() { // 自动撑满宽度样式
  width: 100%;
  .stretch_element();
}

.stretch_element() {
  flex-grow: 100;
  flex-shrink: 100;
}

.panel_content_common() {
  //min-height: 25rem;
  margin-top: 1rem;
  box-sizing: border-box;
  position: relative;
  .vertical_stretch_element();
}


.alarmEditContainer {
  display: flex;
  width: 100%;
  padding-left: 2rem;
  padding-right: 2rem;
  height: 100%;
  overflow: auto;

  .bottomPanelContainer {
    height: calc(100% - 7rem);
    padding: 1.25rem 1.5rem 1.5rem 1.5rem;
  }
  .topo_alarm_right {
    width: 100%;
    transition: width 0.3s ease;
    color: #f5f5f5;

    .rightTitle {
      font-size: 1.6rem;
      margin-bottom: 1rem;
      margin-top: 1rem;
    }

    .rightContent {
      width: 95%;
      height: 85%;
      overflow: auto;
      background: rgb(25, 25, 25);
      padding: 2rem;
      position: relative;

      ul {
        list-style-type: none; /* 去掉默认的列表样式 */
        padding: 0; /* 去掉默认的内边距 */
        margin: 0; /* 去掉默认的外边距 */
      }

      li {
        display: block; /* 确保 li 元素是块级元素，垂直排列 */
      }

      .close {
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
        width: 24px;
        height: 24px;
      }
    }

    .rightContent::-webkit-scrollbar {
      width: 0;
      background-color: #272727;
    }
  }

  .info{
    display: inline-block; /* 使得span元素可以设置宽高 */
    width: 20px; /* 设置宽度 */
    height: 20px; /* 设置高度 */
    background-image: url("../../../../apps/businesstopo/assets/edit/info.png"); /* 设置背景图像的URL */
    background-size: contain; /* 控制背景图像大小 */
    margin-left: 1rem;
    position: absolute;
    bottom: 8px;
  }

}

a:hover {
  cursor: pointer;
}

#alarmSelect {
  width: 80%;
  height: 80%;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-top: 0.5rem;

  .eui-table {
    .fmDefinition_table_height_reset1, .fmDefinition_table_height_reset2 {
      height: 2rem;
    }
  }

  .title {
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
    font-size: 14px;
  }

  .select_panel_content {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%;
    overflow: hidden;

    .panel_content2{
      .panel_content_common();
      //max-height: calc(100% - 2rem);

      .group_tree {
        .basic_container();
        max-width: 100%;
        max-height: 100%;
        overflow: auto;

        .eui_tree.eui_tree_first {
          padding-top: 0;
        }
      }
    }

    .alarm-pannel {
      border: 1px solid #484747;
      height: 100%;
      padding-left: 0.5rem;
      display: flex;
      .alarm-left {
        height: 100%;
        margin-right: 1rem;
        border-right: 1px solid #484747;
        padding-top: 0.5rem;
        .panel_header {
          .basic_container();
          height: 3rem;
          display: flex;
          padding: 1rem;
        }
        .panel_content {
          box-sizing: border-box;
          flex-grow: 100;
          flex-shrink: 100;
          margin-top: 1rem;
          max-height: calc(100% - 2rem);
          min-height: 25rem;
          position: relative;
          width: 100%;
          overflow-y: scroll;
        }
        .panel_content::-webkit-scrollbar {
          width: 0px; /* 滚动条宽度 */
          background-color: #272727; /* 滚动条背景颜色 */
        }

      }

      .alarm-right {
        height: 100%;
        margin-right: 1rem;
        padding-top: 0.5rem;
        .panel_header {
          .basic_container();
          height: 3rem;
          display: flex;
          padding-top: 1rem;
        }

        .panel_content {
          .panel_content_common();
          //max-height: calc(100% - 2rem);

          .group_tree {
            .basic_container();
            max-width: 100%;
            max-height: 100%;
            overflow: auto;

            .eui_tree.eui_tree_first {
              padding-top: 0;
            }
          }
        }
      }
    }

    .alarm-pannel2 {
      border: 1px solid #484747;
      height: 100%;
      padding-left: 1rem;
      padding-top: 0.5rem;
      .panel_header2 {
        .basic_container();
        height: 3rem;
        display: flex;
        padding-top: 1rem;
      }
      }
  }
}

.almCheckbox {
  margin-right: 0.5rem;

  &:last-child {
    margin-right: 0;
  }
}


.topoNotPagingSelect {
  position: absolute;
  bottom: -3rem;
  right: 0;

  .eui_paging_count, .select_in_paging {
    display: none;
  }
}
.topoNotPagingSelect2{
  .eui_paging_count, .select_in_paging {
    display: none;
  }
}
.alarmName_table_span{
  padding-left: 1.5rem;
  background-repeat: no-repeat;
  background-position-y: center;
  background-size: 1rem;
  display: inline-block;
}

.taskManagementContainer {
  flex-grow: 1;
  width: 100rem;
  padding: 1.25rem 2rem 0 2rem;
  .taskTopTitleContainer {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 500;
    .backIcon {
      width: 1rem;
      height: 1rem;
      margin-right: .625rem;
      cursor: pointer;
      background-size: contain;
    }
  }
  .bottomPanelContainer {
    margin-top: 1rem;
    height: calc(100% - 6rem);
    padding: 1.25rem 1.5rem 1.5rem 1.5rem;
    .panelFirstRow {
      display: flex;
      justify-content: space-between;
      margin-bottom: .75rem;
    }
    .inputContentContainer {
      margin-top: 4rem;
      width: 100%;
    }
    .inputContentContainer>tbody>tr>td {
      padding-bottom: 1.5rem;
    }
    .inputContentContainer>tbody>tr>td:first-child {
      width: 11.875rem;
      padding-left: 4rem;  
    }
    .bottomButtonRow {
      display: flex;
      margin-top: 4rem;
      margin-left: 4.5rem;
    }
    .tableEmptyNode {
      height: 3rem;
      width: 100%;
      line-height: 3rem;
      text-align: center;
    }
    .knowledgeRow {
      display: flex;
      .importKnowledge {
        line-height: 2rem;
        margin-left: 1rem;
        cursor: pointer;
        color: rgba(92, 162, 233, 1);
      }
    }
    .taskOperateCol {
      display: flex;
      color: rgb(24, 111, 194);
      .taskOperate {
        cursor: pointer;
      }
    }
    .taskStatus {
      width: 96%;
      overflow: hidden;
      text-align: center;
      display: inline-block;
      padding: 4px 2px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .success {
      background: #5ecc49;
    }
    .partial_success {
      background: #FFBB33;
    }
    .fail {
      background: #fc5043;
    }
    .running {
      background: #4EAFF5;
    }

    .detailContentContainer {
      margin-top: 1.875rem;
      tbody>tr>td {
        padding-bottom: 1.5rem;
      }
      tbody>tr>td:first-child {
        width: 11.875rem;
        padding-left: 1.125rem;
      }
      .overflowWrap {
        overflow-wrap: break-word;
        display: block;
        width: 58.75rem;
      }
    }
    .detailBottomButtonRow {
      display: flex;
      margin-left: 1.125rem;
      margin-top: 4rem;
    }
  }
}

.rule_config_evening {
  background: rgba(0, 0, 0, 1);

  .alarmEditContainer {
    .bottomPanelContainer {
      background-color: rgba(25, 25, 25, 1);
    }
    .topo_alarm_right {
      color: #f5f5f5;
      .rightContent {
        background: rgb(25, 25, 25);
      }
      .rightContent::-webkit-scrollbar {
        background: rgba(0, 0, 0, 1);
      }
      .checkBoxLabel {
        color: #f5f5f5;
      }
    }
  }

  .taskManagementContainer {
    .taskTopTitleContainer {
      color: rgba(255, 255, 255, 1);
      .backIcon {
        background-image: url(~@src/apps/businesstopo/assets/ic_public_recall_border_dark.svg);
      }
    }
    .bottomPanelContainer {
      background-color: rgba(25, 25, 25, 1);
    }
  }
}

.rule_config_lightday {
  background-color: rgba(243, 243, 243, 1);

  .alarmEditContainer {
    .bottomPanelContainer {
      background-color: rgba(255, 255, 255, 1);
    }
    .topo_alarm_right {
      .rightContent {
        background: rgba(255, 255, 255, 1);
      }
      .rightContent::-webkit-scrollbar {
        background-color: rgba(243, 243, 243, 1);
      }
      .checkBoxLabel {
      }
    }
  }

  .taskManagementContainer {
    .taskTopTitleContainer {
      .backIcon {
        background-image: url(~@src/apps/businesstopo/assets/ic_public_recall_border.svg);
      }
    }
    .bottomPanelContainer {
      background-color: rgba(255, 255, 255, 1);
    }
  }
}