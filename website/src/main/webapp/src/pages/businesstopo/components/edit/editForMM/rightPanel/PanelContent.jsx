import React, {use<PERSON>ontext, useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {<PERSON><PERSON><PERSON>, Toggle, SelectPro, TextField, Select} from 'eview-ui';
import {$t, validate} from '@util';
import {PanelType} from './const';
import closeIcon from '../../../../../../apps/businesstopo/assets/edit/edit_close.svg';
import addIcon from '../../../../../../apps/businesstopo/assets/edit/add.png';
import CardItem from './CardItem';
import {BUSINESS_TOPO_CONTEXT} from '@pages/businesstopo/const';
import {Strip} from './Strip';
import {BUSINESS_ICON_LIST} from '../const';
import {TopN} from './TopN';


const transformThird2Channel = (data) => {
  return {
    // 不需要 channelId: undefined,
    // 不需要 modelId: ,
    channelName: data.businessName,
    iconType: data.iconType,
    editType: data.editType,
    isDisplay: data.isDisplay,
    canDelete: data.canDelete,
    sortOrder: data.sortOrder,
    indicatorList: data.indicatorList,
    flag: data.flag,
    rightPannelData: data.rightPannelData,
    icon: data.icon,
    toggled: data.toggled,
    checkError: data.checkError,
    businessName: data.businessName,
  };
};

const transformChannel2Third = (data) => {
  return {
    businessName: data.businessName,
    businessType: null,
    isMain: false,
    solutionId: null,
    iconType: data.iconType,
    editType: data.editType,
    sortOrder: data.sortOrder,
    isDisplay: data.isDisplay,
    canDelete: data.canDelete,
    alarmCsn: null,
    indicatorList: data.indicatorList,
    associatedChannelList: [],
    flag: data.flag,
    rightPannelData: data.rightPannelData,
    icon: data.icon,
    toggled: data.toggled,
    checkError: data.checkError,
  };
};


const PanelContent = (props) => {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    close,
    data = {},
    nameOnChange,
    setStripName,
    areaIcons,
    setAreaIcons,
    setSelectedIcon,
    setViewShow,
    topNCount,
    setTopNCount,
  } = props;

  const {
    name = '',
    type = -1,
    index = 0,
    hidden = false,
    cardList = [],
    mainSelect = '',
    channelList = '',
    allChannelList = [],
  } = data;

  const updateData = useCallback((key, value) => {
    let obj;
    if (type === PanelType.THIRD) {
      obj = areaIcons.thirdPart?.[index];
    } else if (type === PanelType.CHANNEL) {
      obj = areaIcons.app?.[index];
    }

    if (!obj) {
      return;
    }
    obj.editType = 1;
    if (key === 'name') {
      obj.businessName = value;
      if (type === PanelType.CHANNEL) {
        obj.channelName = value;
      }
    } else if (key === 'toggled') {
      obj[key] = value;
      obj.isDisplay = !value;
    } else {
      obj[key] = value;
    }
    setAreaIcons({...areaIcons});
  }, [JSON.stringify(areaIcons), setAreaIcons, index, type]);

  const customValidation = (val, id) => {
    let validationReturnMap = validate(['resourceValidChar', 'checkLength'], val, id, null, 128);
    if (!validationReturnMap.result) {
      return validationReturnMap;
    }
    //  重复名称校验
    if (typeSelect === PanelType.THIRD) {
      let tmpData = areaIcons.thirdPart.map(item => item.businessName);
      const count = tmpData.filter(item => item === val).length;
      if (count > 1) {
        validationReturnMap.result = false;
        validationReturnMap.message = $t('sys_valid_name_duplicate');
      }
    } else if (typeSelect === PanelType.CHANNEL) {
      let tmpData = areaIcons.app.map(item => item.businessName);
      const count = tmpData.filter(item => item === val).length;
      if (count > 1) {
        validationReturnMap.result = false;
        validationReturnMap.message = $t('sys_valid_name_duplicate');
      }
    }
    return validationReturnMap;
  };

  /**
   * 表单
   */
  const nameInputRef = useRef(null);

  const typeSelectRef = useRef(null);
  const [typeSelect, setTypeSelect] = useState(type);

  useEffect(() => {
    setTypeSelect(type);
  }, [type]);

  useEffect(()=>{
    if (name) {
      setTimeout(()=>{nameInputRef.current?.validate();}, 10);
    }
  }, [name, type]);

  const [hiddenToggle, setHiddenToggle] = useState(hidden);
  useEffect(() => {
    setHiddenToggle(hidden);
  }, [hidden]);

  const [title, nameLabel] = useMemo(() => {
    let nowType = typeSelect;
    if (nowType === PanelType.THIRD) {
      return [$t('mm.edit.thirdParty'), $t('mm.edit.businessTypeName')];
    } else if (nowType === PanelType.CHANNEL) {
      return [$t('mm.edit.channel'), $t('mm.edit.channelName')];
    } else if (nowType === PanelType.STRIP) {
      return [$t('mm.edit.strip'), $t('mm.edit.stripName')];
    }
    return ['', ''];
  }, [typeSelect]);

  const [cardItems, setCardItems] = useState(cardList);
  useEffect(() => {
    setCardItems(cardList);
  }, [JSON.stringify(cardList)]);

  const mainSelectRef = useRef(null);
  const [mainIndicatorSelect, setMainIndicatorSelect] = useState(mainSelect);
  useEffect(() => {
    setMainIndicatorSelect(mainSelect);
  }, [mainSelect]);

  const channelSelectRef = useRef(null);
  const [channelSelect, setChannelSelect] = useState(channelList);
  useEffect(() => {
    setTimeout(() => {
      setChannelSelect(channelList);
    }, 10);
  }, [JSON.stringify(channelList)]);

  const getStripParams = () => {
    return {
      setStripName: (value) => setStripName(index, value),
      stripName: name,
      setViewShow,
      topNCount,
      setTopNCount,
    };
  };
  if (PanelType.STRIP === typeSelect) {
    return <Strip {...getStripParams()} />;
  }

  if (PanelType.THIRD_TOPN === typeSelect || PanelType.CHANNEL_TOPN === typeSelect) {
    return <TopN name={name} setViewShow={setViewShow} typeSelect={typeSelect} topNCount={topNCount} setTopNCount={setTopNCount} />;
  }

  return (
    <div className='content'>
      <div className='header'>
        <div className='title'>
          {title}
        </div>
        <div
          className='close'
          style={{backgroundImage: `url(${closeIcon})`}}
          onClick={close}
        />
      </div>
      <div className="splitLine" />
      <div style={{marginBottom: '12px'}}>
        <LabelField text={nameLabel} required={true} style={{width: '90px'}} />
      </div>
      <TextField
        required={true}
        hideRequiredMark={true}
        hintType="tip"
        autoComplete="off"
        inputStyle={{width: 368}}
        validator={(val, id) => customValidation(val, id)}
        value={name}
        onChange={(value1, value2, event) => {
          nameOnChange?.(value1, value2, event);
          updateData('name', value1);
        }}
        ref={nameInputRef}
        onBlur={() => {
          return nameInputRef.current?.validate();
        }}
      />

      <div style={{margin: '12px 0'}}>
        <LabelField text={$t('mm.edit.businessType')} required={true} />
      </div>
      <div>
        <Select
          selectClassName='selectCls'
          options={[
            {text: $t('mm.edit.thirdParty'), value: PanelType.THIRD},
            {text: $t('mm.edit.channel'), value: PanelType.CHANNEL},
          ]}
          required={true}
          value={typeSelect}
          ref={typeSelectRef}
          onChange={(value, oldValue, text, oldText) => {
            setTypeSelect(value);
            if (value === oldValue) {
              return;
            }

            let obj;
            if (oldValue === PanelType.THIRD) {
              obj = areaIcons.thirdPart?.[index];
              let length = areaIcons.app.length;
              if (obj) {
                obj.editType = 1;
                obj.icon = BUSINESS_ICON_LIST[1].icon,
                setAreaIcons(prevState => {
                  const updatedAreaIcons = {...prevState}; // 复制当前状态
                  updatedAreaIcons.app.push(transformThird2Channel(obj)); // 添加到 app 数组
                  updatedAreaIcons.thirdPart = updatedAreaIcons.thirdPart.filter((v, i) => i !== index); // 过滤 thirdPart
                  return updatedAreaIcons; // 返回更新后的状态
                });
                setSelectedIcon({
                  areaId: 'app',
                  iconId: length,
                });
              }
            } else if (oldValue === PanelType.CHANNEL) {
              obj = areaIcons.app?.[index];
              let length = areaIcons.thirdPart.length;
              if (obj) {
                obj.editType = 1;
                obj.icon = BUSINESS_ICON_LIST[0].icon,
                setAreaIcons(prevState => {
                  const updatedAreaIcons = {...prevState}; // 复制当前状态
                  updatedAreaIcons.thirdPart.push(transformChannel2Third(obj));
                  updatedAreaIcons.app = updatedAreaIcons.app.filter((v, i) => i !== index);
                  return updatedAreaIcons; // 返回更新后的状态
                });
                setSelectedIcon({
                  areaId: 'thirdPart',
                  iconId: length,
                });
              }
            }
          }}
        />
      </div>
      <div className="splitLine" />

      {
        [PanelType.THIRD, PanelType.CHANNEL].includes(typeSelect) &&
        <>
          <div style={{margin: '12px 0', position: 'relative'}}>
            <LabelField text={$t('mm.edit.hide')} required={true} />
            <Toggle
              className='rightToggle'
              data={[false, true]}
              toggled={hiddenToggle}
              onToggle={(value) => {
                setHiddenToggle(value);
                updateData('toggled', value);
              }}
            />
          </div>
          <div className="splitLine" />

          {/* 添加指标 */}
          <div style={{margin: '12px 0', position: 'relative'}}>
            <LabelField text={$t('mm.edit.keyIndicators')} required={true} />
            <span
              className="indicatorsAddIcon"
              style={{
                backgroundImage: `url(${addIcon})`,
              }}
              onClick={() => {
                dispatch({
                  editAddPannelShow: true,
                  editAddPannelStatus: 1,
                  netWorkTreeParam: {},
                  unitTree: {
                    measureType: 'all',
                    measureUnit: '',
                    IndicatorId: '',
                  },
                  objectTable: {
                    isObjectNoData: true,
                  },
                });
              }}
            />
          </div>

          {/* 选择指标为主指标 */}
          <div style={{margin: '12px 0', position: 'relative'}}>
            <LabelField text={$t('mm.edit.primaryIndicators')} required={true} />
            <Select
              className='associatedIndicatorsSelectCls'
              options={
                cardItems.map((v, i) => {
                  return {
                    text: `${$t('mm.edit.indicators')}${i + 1}`,
                    value: v.key,
                  };
                })
              }
              required={true}
              value={mainIndicatorSelect}
              ref={mainSelectRef}
              onChange={(value, oldValue, text, oldText) => {
                setMainIndicatorSelect(value);

                const index2 = cardItems.findIndex(v => v.key === value);

                let obj;
                if (type === PanelType.THIRD) {
                  obj = areaIcons.thirdPart?.[index];
                } else if (type === PanelType.CHANNEL) {
                  obj = areaIcons.app?.[index];
                }

                if (!obj) {
                  return;
                }
                obj.editType = 1;
                if (obj?.rightPannelData?.[index2]) {
                  obj.rightPannelData[index2].indicatorDisplayType = 1;
                  obj.rightPannelData.filter((v, i) => i !== index2).map(v => v.indicatorDisplayType = 0);
                }
                if (obj?.indicatorList?.[index2]) {
                  obj.indicatorList[index2].indicatorDisplayType = 1;
                  obj.indicatorList.filter((v, i) => i !== index2).map(v => v.indicatorDisplayType = 0);
                }
                setAreaIcons({...areaIcons});
              }}
            />
          </div>

          <div className='cardListContainer'>
            {
              cardItems.map((item, index2) => {
                return (
                  <CardItem
                    key={item.key}
                    titleName={`${$t('mm.edit.indicators')}${index2 + 1}`}
                    moName={item.moName || ''}
                    indexName={item.indexName || ''}
                    measUnitName={item.measUnitName || ''}
                    displayValue={item.displayValue || ''}
                    editClick={() => {
                      dispatch({
                        editAddPannelShow: true,
                        editAddPannelStatus: 2,
                        editAddPannelKey: item.key,
                      });
                    }}
                    deleteClick={() => {
                      setCardItems(cardItems.filter(v => {
                        return item.key !== v.key;
                      }));

                      let obj;
                      if (type === PanelType.THIRD) {
                        obj = areaIcons.thirdPart?.[index];
                      } else if (type === PanelType.CHANNEL) {
                        obj = areaIcons.app?.[index];
                      }

                      if (!obj) {
                        return;
                      }
                      obj.rightPannelData = obj.rightPannelData.filter((v, i) => i !== index2);
                      obj.indicatorList = obj.indicatorList.filter((v, i) => i !== index2);
                      obj.editType = 1;
                      setAreaIcons({...areaIcons});
                    }}
                  />
                );
              })
            }
          </div>

          {
            typeSelect === PanelType.THIRD &&
            <>
              <div style={{margin: '12px 0'}}>
                <LabelField text={$t('mm.edit.associatedAccessChannel')} required={true} />
              </div>
              <div>
                <SelectPro
                  ref={channelSelectRef}
                  className='selectCls'
                  selectClassName='selectCls'
                  mode="single"
                  options={allChannelList}
                  value={channelSelect}
                  enableCloseIcon
                  onChange={(value, options) => {
                    setChannelSelect(value);
                    updateData('associatedChannelList', [{channelName: options.text, channelId: options.value}]);
                  }}
                />
              </div>
            </>
          }
        </>
      }
    </div>
  );
};

export {PanelContent};
