/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect} from 'react';
import * as api from '../../api';
import {BUSINESS_TOPO_CONTEXT} from '../../const';
import AlarmEventPanel from '../AlarmEventPanel';
import {$t} from '@util';
import PodPanelChart from '@pages/businesstopo/components/moTypeDrill/PodPanelChart';
import {HEALTH_STATUS, STATUS, STATUS_NEW_TEXT} from '@pages/businesstopo/const/moTypeDrill';
import errorIcon from '@images/error.png';
import Tooltip from 'eview-ui/Tooltip';

function SiteRightPannel({refresh, isVm}) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {dvDetailsInfoArr, sitePannelIsOpen, currentMoData, showSiteRightPanel} = state;

  useEffect(() => {
    if (currentMoData.id === -1 || !sitePannelIsOpen) {
      return;
    }

    let param = {
      instanceId: currentMoData.id,
    };
    if (state.isTimeTrack) {
      param.timestamp = state.selectedTime;
    }

    api.getAlarmData(param, res => {
      dispatch({
        alarmEventData: res.data.alarmDataList,
      });
    });
  }, [currentMoData, state.isTimeTrack, state.selectedTime, refresh]);
  const getColor = (index, value) => {
    if (index === 0) {
      if (value === 'healthy') {
        return '#00a874'; // red color
      } else {
        return '#F43146';
      }
    } else {
      return '#f5f5f5';
    }
  };

  const getItemValue = (index, item) => {
    if (index === 0) {
      return item.value === 'healthy' ? $t('site.drill.healthy') : $t('site.drill.unhealthy');
    }
    return item.value;
  };

  const handleClick = (index) => {
    if (index === 0) {
      window.open('/eviewwebsite/index.html#path=/dvpmApp/pmSelfMonitor');
    }
  };

  function getName() {
    // 当前是是数据库应用 或者是虚拟机
    if (currentMoData.applicationType === 3 || isVm) {
      return `${$t('total.instances')}（${currentMoData.podCount}）`;
    } else {
      return `Pods${$t('total')}（${currentMoData.podCount}）`;
    }
  }

  return (
    <div style={{
      display: 'inline-block',
      right: 0,
      position: 'absolute',
      zIndex: 999,
      width: showSiteRightPanel ? 400 : 0,
      height: 'calc(100vh - 4rem)',
    }}
    >
      <div
        className="dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-bottomPanel"
        style={{
          display: sitePannelIsOpen ? '' : 'none', position: 'fixed', zIndex: 1000, opacity: 1,
        }}
        onClick={() => {
          dispatch({sitePannelIsOpen: true, showSiteRightPanel: !showSiteRightPanel});
        }}
      >
        <div className={`topo-common-panel-${showSiteRightPanel ? 'close' : 'expend'}-vertical-right`} />
      </div>

      <div className="site-right-panel" style={{display: showSiteRightPanel ? 'block' : 'none'}}>
        <div className="site-right-panel-text">
          <span>{`${currentMoData.name + $t('detail')}`}</span>
        </div>

        {dvDetailsInfoArr.length > 0 ? (
          <div className="pod-detail-right-panel-container">
            {dvDetailsInfoArr.map((item, index) => (
              <div key={index} style={{paddingBottom: '16px', width: '100px'}}>
                <Tooltip content={getItemValue(index, item)} placement='right' color='#393939'
                  overlayStyle={{color: '#FFF'}}
                >
                  <div
                    className="pod-detail-title"
                    style={{color: getColor(index, item.value)}}
                    onClick={() => {
                      handleClick(index);
                    }}
                  >
                    <span style={{marginRight: '5px'}}>
                      {getItemValue(index, item)}
                    </span>
                    {(index === 0 && item.value === STATUS.failed) && (
                      <img
                        style={{width: '15px', height: '15px'}}
                        src={errorIcon}
                      />
                    )}
                  </div>
                </Tooltip>
                <div className="pod-detail-value">{item.title}</div>
              </div>
            ))}
          </div>
        ) :
          (
            <div style={{marginTop: '24px'}}>
              <span className="mo_pod_text">{getName()}</span>
              <div style={{float: 'right', display: 'inline-block'}}>
                <div className='right-panel-kpi-red' />
                <span
                  className='mo_pod_text'
                  style={{marginLeft: '4px'}}
                >{`${$t('abnormal')}(${currentMoData.podErrorCount})`}
                </span>
              </div>
              <div className="progress">
                <div style={{flexGrow: currentMoData.podErrorCount}} className='progressFirst' />
                <div style={{flexGrow: currentMoData.podCount - currentMoData.podErrorCount}}
                  className='progressSecond'
                />
              </div>
            </div>
          )}
        <div style={{marginTop: '32px', height: '344px'}}>
          <PodPanelChart podData={currentMoData} refresh={refresh} />
        </div>
        <div>
          <AlarmEventPanel />
        </div>

      </div>
    </div>
  );
}

export default SiteRightPannel;
