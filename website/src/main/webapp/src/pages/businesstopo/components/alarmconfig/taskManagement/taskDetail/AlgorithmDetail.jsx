import React, { useContext, useState, useEffect, useRef } from 'react';
import { Row, Col, Tooltip, Button, Select, Table, TextField, TextArea } from 'eview-ui';
import { $t, modal, validate } from '@util';
import styles from '../../index.less';
import LabelHint from '../../../common/LabelHint';
import {
  TaskManageContext,
  ALGORITHM_TABLE_COLUMNS,
  incidentTaskType,
  OPERATE_SUCCESS_CODE,
  MAX_ALARM_ID_WHITE_LIST,
  IS_CAN_MODIFY_TASK,
  GlobalContext,
} from '../../const';
import { queryAlgorithmList, getAlgorithmParams } from '../../api';
import { validateByInt, validFloat } from '../NewOrEditCommonUtils/AlgorithmPageUtil';
import { cancelCreateOrEditTask, modifyTaskCommon } from '../NewOrEditCommonUtils';

let algorithmTable;
let algorithmSelect;
let algorithmInfo = {};
// 用于在algorithmParamInput中找到相应元素
let declineTimeInput = '';
let maxIncidentEndTimeInput = '';
let alarmIntervalInput = '';
let alarmWhitelistInput = '';

const AlgorithmDetail = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const { theme } = useContext(GlobalContext);
  const { viewDetailInfo, algorithmList, algorithmParamsList } = state;
  const [modifyTask, setModifyTask] = useState(false);
  const paramTableData = useRef([]); // 存储算法参数表格数据
  const algorithmParamInput = useRef(algorithmParamsList.algorithmParamRef); // 存储每个算法参数输入框的ref

  // 进入页面以及切换修改状态时将任务详情信息转换为表格参数
  useEffect(() => {
    let algorithmParams = JSON.parse(viewDetailInfo.algorithmParam) || [];
    let rows = (algorithmParams && algorithmParams.length) ? getAlgorithmRows(algorithmParams) : [];

    setAlgorithmParamsList({
      algorithmParam: algorithmParams,
      algorithmModelId: viewDetailInfo.algorithmModelId,
      algorithmModelName_Version: viewDetailInfo.algorithmModelName,
      algorithmPageResults: rows,
    });
  }, [modifyTask]);

  // 进入修改状态时查询算法列表
  useEffect(() => {
    if (!modifyTask) {
      return;
    }
    queryAlgorithmList(
      { taskType: incidentTaskType },
      (res) => {
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          modal.error(
            $t('intelligent.incident.task.manage.algorithm.query.fail'),
            null,
            $t('intelligent.incident.task.manage.error.tip'),
            null,
            theme,
          );
          return null;
        }
        const algorithmListOptions = res.data.map(ele => {
          return {
            text: `${ele.algorithmName}_${ele.version}`,
            value: ele.id,
          };
        });
        dispatch({
          type: 'setAlgorithmList',
          algorithmList: algorithmListOptions,
        });
        return res.data;
      },
      () => {
        modal.error(
          $t('intelligent.incident.task.manage.algorithm.query.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
      }
    );
  }, [modifyTask]);

  useEffect(() => {
    algorithmInfo = { ...algorithmParamsList };
  }, [algorithmParamsList]);

  const setAlgorithmParamsList = (param) => {
    dispatch({
      type: 'setAlgorithmParamsList',
      algorithmParamsList: { ...algorithmInfo, ...param },
    });
  };
  
  // 选择算法下拉框回调
  const onSelectAlgorithm = (value, oldValue, text) => {
    let param = {
      modelName: text.split('_')[0],
      interfaceVersion: text.split('_')[1],
      feature: '',
    };
    getAlgorithmParams(
      param,
      (res) => {
        // 查询算法参数失败
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          setAlgorithmParamsList({
            querySuccess: false,
            algorithmModelId: value,
            algorithmPageResults: [],
          });
          modal.error(
            $t('intelligent.incident.task.manage.algorithm.param.query.fail'),
            null,
            $t('intelligent.incident.task.manage.error.tip'),
            null,
            theme,
          );
          return;
        }
        updateAlgorithm(
          res,
          value,
          text,
        );
      },
      (err) => {
        setAlgorithmParamsList({
          querySuccess: false,
          algorithmModelId: value,
          algorithmPageResults: [],
        });
      }
    );
  };

  // 根据所选算法设置初始算法参数列表数据
  const updateAlgorithm = (res, algorithmModelId, algorithmModelName_Version) => {
    let algorithmParams = JSON.parse(res.data) || [];
    let rows = (algorithmParams && algorithmParams.length) ? getAlgorithmRows(algorithmParams) : [];

    setAlgorithmParamsList({
      querySuccess: true,
      algorithmParam: algorithmParams,
      algorithmModelId,
      algorithmModelName_Version,
      algorithmPageResults: rows,
    });
  };

  // 获取算法参数表格行
  const getAlgorithmRows = (algorithmParam) => {
    paramTableData.current = algorithmParam;
    let rows = [];
    algorithmParam.forEach((item, index) => {
      rows.push({
        name: tableRowTip(item), 
        defaultValue: getCustomDefaultValue(index, item)});
    });
    return rows;
  };

  // 算法参数提示
  const tableRowTip = item => {
    return item.description ?
      <LabelHint label={item.displayName || item.parameterName} hint={item.description} 
        isHelp={true} require={item.required} noColon={!item.required} theme={theme}
      /> :
      (item.displayName || item.parameterName);
  };

  // 自定义算法参数值表格列
  const getCustomDefaultValue = (index, paramItem) => {
    const { parameterDefaultValue, type, valueRange, parameterName, required } = paramItem;
    getCustomizedInput(parameterName, index);

    let parameterVal = parameterDefaultValue;
    let validParam = [];
    switch (type) {
      case 'int':
        validParam = validateByInt(valueRange);
        break;
      case 'float':
        validParam = validFloat(valueRange, required);
        break;
      default:
        break;
    }

    const commonProps = {
      inputStyle: { width: '30rem' },
      value: parameterVal,
      id: `defaultParam_${index}`,
      autoComplete: 'off',
      ref: ele => algorithmParamInput.current[`defaultParamInput_${index}`] = ele,
      hintType: 'tip',
      validator: (val, id) => validate(validParam.rules, val, id, validParam.min, validParam.max),
      onChange: (val) => changeParamVal(index, val),
      disabled: !modifyTask,
    };
    if (parameterName === 'toggling_alarm_trust_list') {
      return (
        <TextArea
          {...commonProps}
          rows={3}
          maxLength={1024}
          style={{ marginTop: '5px' }}
          validator={(val, id) => validate(['numberAndCommaValidChar', 'togglingAlarmMaxNumberValidate'], val, id, MAX_ALARM_ID_WHITE_LIST, 1024)}
        />     
      );
    } else {
      return <TextField {...commonProps} />;
    }
  };

  const getCustomizedInput = (parameterName, index) => {
    if (parameterName === 'decay_time_compress') {
      declineTimeInput = `defaultParamInput_${index}`;
    }
    if (parameterName === 'maximum_incident_end_time') {
      maxIncidentEndTimeInput = `defaultParamInput_${index}`;
    }
    if (parameterName === 'toggling_alarm_interval_compress') {
      alarmIntervalInput = `defaultParamInput_${index}`;
    }
    if (parameterName === 'toggling_alarm_trust_list') {
      alarmWhitelistInput = `defaultParamInput_${index}`;
    }
  };

  // 改变某一算法参数的回调方法
  const changeParamVal = (index, val) => {
    if (paramTableData.current.length) {
      paramTableData.current[index].parameterDefaultValue = val;
      setAlgorithmParamsList({
        algorithmPageResults: getAlgorithmRows(paramTableData.current),
        algorithmParam: paramTableData.current,
      });
    }
  };

  // 是否有修改任务权限
  const isCanModifyPermission = () => {
    return viewDetailInfo.canModify === IS_CAN_MODIFY_TASK.YES;
  };

  const switchModifyTask = () => {
    setModifyTask(!modifyTask);
  };

  // 校验页面输入项
  const verifyData = () => {
    // 校验算法列表是否正常选择
    if (!algorithmSelect.validate()) {
      modal.error(
        $t('intelligent.incident.task.manage.algorithm.please.select'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    // 校验是否有算法参数列表
    if (!(algorithmParamsList.algorithmParam && algorithmParamsList.algorithmParam.length)) {
      modal.error(
        $t('intelligent.incident.task.manage.algorithm.no.param.list'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }    
    // 校验算法表格中的默认参数
    for (let i = 0; i < algorithmParamsList.algorithmParam.length; i++) {
      let validIndex = algorithmParamInput.current[`defaultParamInput_${i}`];
      if (validIndex && !validIndex.validate()) {
        validIndex.focus();
        return false;
      }
    }    

    if (declineTimeInput && maxIncidentEndTimeInput &&
      parseInt(algorithmParamInput.current[declineTimeInput].getValue()) >= parseInt(algorithmParamInput.current[maxIncidentEndTimeInput].getValue())) {
      modal.error(
        $t('intelligent.incident.task.manage.algorithm.param.decline.end.time.tip'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    return true;
  };
  
  const confirmModifyTask = () => {
    if (!verifyData()) {
      return;
    }
    const param = {
      ...viewDetailInfo,
      algorithmModelId: algorithmParamsList.algorithmModelId,
      algorithmModelName: algorithmParamsList.algorithmModelName_Version,
      algorithmParam: JSON.stringify(handleAlgorithmParam(algorithmParamsList.algorithmParam)),
    };
    modifyTaskCommon(param, switchModifyTask, viewDetailInfo.taskId, dispatch, theme);
  };

  const handleAlgorithmParam = (param) => {
    let temp = JSON.parse(JSON.stringify(param));
    temp.map(item => {
      if (item.type === 'int' || item.type === 'float') {
        item.parameterDefaultValue = `${item.parameterDefaultValue}`;
      }
    });
    return temp;
  };

  return (
    modifyTask ? (
      <>
        <Row className={styles.detailContentContainer}>
          <Col cols={19}>
            <table>
              <tbody>
                <tr>
                  <td>
                    <LabelHint
                      label={$t('intelligent.incident.task.manage.table.algorithm')}
                      require={true}
                      theme={theme}
                    />
                  </td>
                  <td>
                    <Select
                      selectStyle={{ width: '33.75rem' }}
                      id='algorithmInput'
                      options={algorithmList}
                      value={algorithmParamsList.algorithmModelId}
                      onChange={onSelectAlgorithm}
                      required
                      validator={(val, id) => validate(['required'], val, id)}
                      hintType='tip'
                      ref={(field) => (algorithmSelect = field)}
                    />
                  </td>
                </tr>
                <tr>
                  <td />
                  <td>
                    <Table
                      columns={ALGORITHM_TABLE_COLUMNS}
                      showEmptyImage={false}
                      maxHeight={480}
                      width='100%'
                      splitPagination={false}
                      dataset={algorithmParamsList.algorithmPageResults}
                      ref={(table) => {
                        algorithmTable = table;
                      }}
                      id='taskAlgorithmParam'
                    />
                  </td>
                </tr>
              </tbody>
            </table>          
          </Col>
        </Row>
        <div className={styles.detailBottomButtonRow}>
          <Button
            text={$t('alarm.edit.cancel')}
            style={{ float: 'left', marginRight: '1.25rem' }}
            onClick={switchModifyTask}
          />
          <Button
            text={$t('intelligent.incident.task.manage.task.detail.confirm')}
            status='primary'
            onClick={confirmModifyTask}
          />
        </div>      
      </>
      ) : (
      <Row className={styles.detailContentContainer}>
        <Col cols={19}>
          <table>
            <tbody>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.table.algorithm')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.algorithmModelName}
                </td>
              </tr>
              <tr>
                <td />
                <td>
                  <Table
                    columns={ALGORITHM_TABLE_COLUMNS}
                    showEmptyImage={false}
                    maxHeight={480}
                    splitPagination={false}
                    dataset={algorithmParamsList.algorithmPageResults}
                    ref={(table) => {
                      algorithmTable = table;
                    }}
                    id='taskAlgorithmParam'
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </Col>
        <Col cols={1} />
        <Col cols={2}>
          {isCanModifyPermission() ?
            <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' 
              onClick={switchModifyTask}
            /> :    
            <Tooltip content={$t('intelligent.incident.task.manage.task.detail.no.support.modify')}>
                <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' disabled />
            </Tooltip>}
        </Col>
      </Row> )
  );
};

export default AlgorithmDetail;
