import '@pages/businesstopo/css/tooltip/business.less';
import React, {useEffect, useRef, useState} from 'react';
import * as echarts from 'echarts';
import closeIcon from '../../../../apps/businesstopo/assets/closeicon.svg';
import {queryOverviewSiteLinkData} from '../../api';
import {getHistoryIndexValueList, getHistorySeries, LINE_CHART_COLOR} from '../../const';
import {handleJumpPageData} from '../../util';
import {dateFormatToHour} from '../../../../commonUtil/tools';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {queryMMHistoryLineUrl} from '../../a3dPages/components/MMOverviewPage/api';

function setTipsPosition(chartDomRef, size, point) {
  const TOOLTIP_OFFSET = 4;
  // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
  const canvasRect = chartDomRef.current?.getBoundingClientRect();
  const {contentSize: tooltipSize, viewSize: canvasSize} = size;
  let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
  let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
  if (canvasRect) {
    // 校正tooltip的 top 定位，防止超出可视窗口
    const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
    const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
    if (toolTipExceedViewport || toolTipExceedCanvas) {
      top = -canvasRect.top;
    }
  }
  return [left, top];
}

function Business(props) {
  const {isTimeTrack, selectedTime, goldIndicator} = props;
  const [display, setDisplay] = useState(true);
  const [linkData, setLinkData] = useState({
    title: '',
    dataList: [],
  });

  const [chartData, setChartData] = useState([]);

  let chartDomRef = useRef();
  const intervalTime = 50;

  let grid = {
    left: '2%',
    right: '3%',
    bottom: '15%',
    top: '10%',
    containLabel: true,
  };
  let legendOption = {
    orient: 'horizontal',
    bottom: '0%',
    type: 'scroll',
    icon: 'circle',
    itemWidth: 15,
    itemHeight: 10,
    textStyle: {
      color: '#BBBBBB',
    },
    show: true,
  };

  function getFormatter() {
    return params => {
      // 获取时间（假设 x 轴为时间）
      const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
      // 遍历每条线的数据，保留默认的值展示
      const values = params
        .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
        .join('<br>');
      // 返回自定义的提示框内容
      return `<strong>${time}</strong><br>${values}`;
    };
  }

  const getOption = () => {
    let options = {
      tooltip: {
        trigger: 'axis',
        borderColor: '#393939',
        backgroundColor: '#393939',
        appendToBody: true,
        axisPointer: {
          lineStyle: {
            color: '#0d2d80',
          },
        },
        textStyle: {
          color: '#f5f5f5',
        },
        enterable: true,
        position: (point, params, dom, rect, size) => {
          return setTipsPosition(chartDomRef, size, point);
        },
        formatter: getFormatter(),
      },
      grid,
      textStyle: {
        color: '#BBBBBB',
      },
      legend: legendOption,
      xAxis: {
        type: 'time',
        boundaryGap: true,
        axisLine: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 3,
          },
        },
        axisTick: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 2,
          },
        },

        axisLabel: {
          color: '#BBBBBB',
          interval: 0,
          borderColor: 'transparent',
          borderWidth: 10,
          fontSize: 10,
          margin: 10,
          hideOverlap: true,
          showMinLabel: false,
          showMaxLabel: false,
          formatter: value => {
            // 将日期格式化为字符串
            const unit = (chartData[0].endTime - chartData[0].startTime) / 12;
            if (value > parseInt(chartData[0].startTime) && value < parseInt(chartData[0].endTime)) {
              if (Math.abs(value - chartData[0].startTime) < unit || Math.abs(value - chartData[0].endTime) < unit) {
                return '';
              }
              return dateFormatToHour(value);
            }
            return '';
          },
        },
        splitLine: {
          show: false,
        },
        min: parseInt(chartData[0].startTime),
        max: parseInt(chartData[0].endTime),
      },
      yAxis: {
        type: 'value',

        // name:unit,
        nameTextStyle: {
          color: '#BBBBBB',
        },
        splitLine: {
          lineStyle: {
            color: '#4E4E4E',
          },
        },
        axisLabel: {
          color: '#BBBBBB',
          fontSize: 10,
          margin: 5,
          hideOverlap: true,
          align: 'right',
          overflow: 'break',
          width: 200,
        },
      },
      series: getHistorySeries(chartData, true, false),
    };
    return options;
  };

  useEffect(() => {
    if (!chartDomRef.current) {
      return;
    }
    if (chartData.length === 0) {
      return;
    }
    let chartObj = echarts.init(chartDomRef.current);
    let option = getOption();
    chartObj.setOption(option);

    // 注册图点击事件
    chartObj.getZr().off('click');
    chartObj.getZr().on('click', param => {
      const pointInPixel = [param.offsetX, param.offsetY];
      if (!chartObj.containPixel('grid', pointInPixel)) {
        return;
      }
      handleJumpPageData(chartData);
    });

    return () => {
      chartObj.dispose();
    };
  }, [chartData]);

  useEffect(() => {
    // 请求数据
    if (display) {
      let param = {
        ...props.linkData.params,
        indicatorId: goldIndicator,
      };
      if (isTimeTrack) {
        param.currentTime = selectedTime;
      }
      if (props.isMM) {
        param = {...param, ...props.linkData.params};
        queryMMHistoryLineUrl(
          param, ({data, resultCode}) => {
            if (data.siteHistoryList.length > 0) {
              setLinkData({
                title: data.siteHistoryList[0].indexName,
                dataList: data.siteHistoryList[0],
              });
              setTimeout(() => {
                // 返回太快dom未生成，图不展示
                setChartData(data.siteHistoryList);
              }, intervalTime);
            }
          }
        );
      } else {
        queryOverviewSiteLinkData(param, ({data, resultCode}) => {
          if (data.siteHistoryList.length > 0) {
            setLinkData({
              title: data.siteHistoryList[0].indexName,
              dataList: data.siteHistoryList[0],
            });

            setTimeout(() => {
              // 返回太快dom未生成，图不展示
              setChartData(data.siteHistoryList);
            }, intervalTime);
          }
        }
        );
      }
    } else {
      // 清空
      setLinkData({
        title: '',
        dataList: [],
      });
    }
  }, [JSON.stringify(props.linkData.params)]);

  return (
    display &&
    <div id="business" style={{
      left: 0,
      top: -42,
    }} onClick={e => {
      e.stopPropagation();
      e.nativeEvent.stopImmediatePropagation();
    }}
    >
      <div className="triangle2" />
      {/** 需要边框 */}
      <div className="triangle" />
      <div className="header">
        <div className="title">{linkData.title}</div>
        <div
          className="close"
          style={{
            backgroundImage: `url(${closeIcon})`,
          }}
          onClick={() => {
            setDisplay(false);
          }}
        />
      </div>
      <p style={{
        fontSize: '12px',
        color: '#BBBBBB',
        lineHeight: '12px',
        fontFamily: 'HarmonyHeiTi',
        marginLeft: '5px',
      }}
      >
        {chartData.length === 0 ? '' : chartData[0].indexUnit}
      </p>
      <div ref={chartDomRef} className="chart" />
    </div>
  );
}

export default Business;
