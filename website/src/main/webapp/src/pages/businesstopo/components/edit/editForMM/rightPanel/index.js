import React, { useContext, useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { ConfigProvider } from 'eview-ui';
import { $t, validate, registerResource, isZh } from '@util';
import i18n from './locale';
import {PanelContent} from './PanelContent';
import '@pages/businesstopo/css/edit/index.css';
import './css/index.less';

/**
 * 暴露setViewShow函数，控制组件是否展示
 *
 *
 */
const MMRightPanel = forwardRef((props, ref) => {
  registerResource(i18n, 'mmEditRightPanel');

  const {viewShow, setViewShow} = props;

  useImperativeHandle(ref, () => {
    return {};
  });

  useEffect(() => {
    if (viewShow) {
      props.setEditRightPanelShow(true);
    }
  }, [viewShow]);

  return (
    viewShow &&
    <ConfigProvider version="aui3-1" locale={isZh ? 'zh' : 'en'} theme="evening">
      <div
        id='editRightPanel'
        style={{
          width: props.editRightPanelShow ? 400 : 0,
          display: 'inline-block',
          top:0,
        }}
      >
        <div
          className='dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-bottomPanel'
          style={{position: 'fixed', zIndex: 1000, opacity: 1}}
          onClick={() => {
            props.setEditRightPanelShow(!props.editRightPanelShow);
          }}
        >
          <div className={`topo-common-panel-${props.editRightPanelShow ? 'close' : 'expend'}-vertical-right`} />
        </div>

        <div style={{display: props.editRightPanelShow ? 'inline-block' : 'none'}}>
          <PanelContent
            close={() => setViewShow(false)}
            setViewShow={setViewShow}
            {...props}
          />
        </div>

      </div>
    </ConfigProvider>
  );
});

export {MMRightPanel};
