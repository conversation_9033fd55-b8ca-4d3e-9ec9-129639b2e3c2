/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import React, {useContext, useEffect, useRef, useState, useMemo, CheckboxGroup} from 'react';
import {$t} from '@util';
import '@pages/businesstopo/css/chooseVM/rightPanel.less';
import {Toolt<PERSON>, Tab, TabItem, Button} from 'eview-ui';
import * as echarts from 'echarts';
import {dateFormatToHour, jumpToPMHistoryDataPage, sortHistoryListData} from '../../../../commonUtil/tools';
import AlarmEventPanel from '../AlarmEventPanel';
import {BUSINESS_TOPO_CONTEXT, getHistorySeries, LINE_CHART_COLOR} from '../../const';
import {getAlarmEventData, getVMIndicatorData} from '../../api/moTypeDrill';
import {STATUS, STATUS_NEW_TEXT, STATUS_TEXT} from '../../const/moTypeDrill';
import PropTypes from 'prop-types';
import errorIcon from '@images/error.png';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {getQuery} from '../../a3dPages/components/utils';
import {getUrlParam} from '../../util';
import {MoreLegend} from '../common/Legend';
import SelectButtonGroup from '../common/SelectButtonGroup';
import edit from '../../../../apps/businesstopo/assets/line_chart_edit.svg';

function VMRightPanel(props) {
  const {isTimeTrack, timeTrackVMData, isOpenPanel, setIsOpenPanel} = props;
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);

  let chartDomRef = useRef(null);

  let chartObjRef = useRef(null);
  // 全量得指标头信息，定时更新
  const [rawIndicatorList, setRawIndicatorList] = useState([]);

  const [currentIndex, setCurrenIndex] = useState(0);

  const [indicatorData, setIndicatorData] = useState({});
  const gridOption = {
    left: '0px',
    right: '5%',
    bottom: '10%',
    top: '14%',
    containLabel: true,
  };
  const legendOption = {
    show: getUrlParam('isMM') ? false : true,
    orient: 'horizontal',
    bottom: '0%',
    type: 'scroll',
    icon: 'circle',
    itemWidth: 15,
    itemHeight: 10,
    textStyle: {
      color: '#BBBBBB',
    },
  };

  const [selectValue, setSelectValue] = useState([]);

  // 控制多指标下拉框是否展示
  const [indicatorSelectShow, setIndicatorSelectShow] = useState(false);

  const handleIndicatorChange = (newValue, oldValue, event) => {
    // 修改选择值
    setSelectValue(newValue);
    if (newValue.indexOf(currentIndex) === -1) {
      setCurrenIndex(newValue[0]);
    }
  };

  const getSelectData = () => rawIndicatorList.map((item, index) => ({
    value: index,
    text: item.indexName,
    tipText: item.indexName,
  }));

  // 切换指标显示
  const onIndicatorChange = index => {
    setCurrenIndex(selectValue[index]);
  };

  useEffect(() => {
    if (selectValue.length === 0) {
      return;
    }
    let params = {
      instanceId: props.vmData.id,
      indicatorId: rawIndicatorList[currentIndex].indicatorId,
    };

    getVMIndicatorData(
      params,
      res => {
        if (!res || res.resultCode !== 0) {
          return;
        }
        res.data.vmIndicatorList = sortHistoryListData(res.data.vmIndicatorList || []);
        setIndicatorData(res.data);
      },
    );
  }, [currentIndex]);

  // 右边下拉的切换指标
  const handleIndicatorSelect = event => {
    setIndicatorSelectShow(!indicatorSelectShow);

    let selectBtnTarget = document.getElementById('indicator_select_btn');
    let selectPanelTarget = document.getElementById('indicator_select_panel');
    document.addEventListener('click', current => {
      const condition = current.target.id.indexOf('indicator_select_checkbox') !== -1 ||
        current.target === selectBtnTarget ||
        current.target === selectPanelTarget ||
        selectBtnTarget.contains(current.target) ||
        selectPanelTarget.contains(current.target);
      if (condition) {
        return;
      }
      setIndicatorSelectShow(false);
    });
  };

  const [seriesLegendData, setSeriesLegendData] = useState([]);
  const [selected, setSelected] = useState({});
  useEffect(() => {
    setSelected(seriesLegendData.reduce((curr, next) => {
      curr[next.id] = selected[next.id] === false ? false : true;
      return curr;
    }, {}));
  }, [JSON.stringify(seriesLegendData)]);

  const legendData = useMemo(() => {
    return seriesLegendData.map(v => {
      return {
        id: v.id,
        name: v.name,
        selected: selected[v.id],
      };
    });
  }, [selected]);

  function getPosition() {
    return (point, params, dom, rect, size) => {
      let chartDom = document.getElementById('vmChart');
      const TOOLTIP_OFFSET = 4;
      // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
      const canvasRect = chartDom.getBoundingClientRect();
      const {contentSize: tooltipSize, viewSize: canvasSize} = size;
      let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
      let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
      if (canvasRect) {
        // 校正tooltip的 top 定位，防止超出可视窗口
        const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
        const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
        if (toolTipExceedViewport || toolTipExceedCanvas) {
          top = -canvasRect.top;
        }
      }
      return [left, top];
    };
  }

  function getFormatter() {
    return (params) => {
      // 获取时间（假设 x 轴为时间）
      const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
      // 遍历每条线的数据，保留默认的值展示
      const values = params
        .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
        .join('<br>');
      // 返回自定义的提示框内容
      return `<strong>${time}</strong><br>${values}`;
    };
  }

  const getOption = () => {
    return {
      tooltip: {
        show: true,
        trigger: 'axis',
        borderColor: '#393939',
        backgroundColor: '#393939',
        axisPointer: {
          lineStyle: {
            color: '#57617B',
          },
        },
        textStyle: {
          color: '#FFFFFF',
        },
        appendToBody: true,
        enterable: true,
        position: getPosition(),
        formatter: getFormatter(),
      },
      color: [LINE_CHART_COLOR[0][0], LINE_CHART_COLOR[1][0], LINE_CHART_COLOR[2][0]],
      grid: gridOption,
      textStyle: {
        color: '#BBBBBB',
      },
      legend: legendOption,
      xAxis: [
        {
          type: 'time',
          boundaryGap: true,
          axisLine: {
            lineStyle: {
              color: '#BBBBBB',
              opacity: 0.1,
              type: 'solid',
              width: 3,
            },
          },

          axisTick: {
            lineStyle: {
              color: '#BBBBBB',
              opacity: 0.1,
              type: 'solid',
              width: 2,
            },
          },

          axisLabel: {
            color: '#BBBBBB',
            interval: 0,
            borderColor: 'transparent',
            borderWidth: 10,
            fontSize: 10,
            margin: 10,
            hideOverlap: true,
            showMinLabel: false,
            showMaxLabel: false,
            formatter: value => {
              let startTime = parseInt(indicatorData.vmIndicatorList[0].startTime);
              let endTime = parseInt(indicatorData.vmIndicatorList[0].endTime);
              const unit = (endTime - startTime) / 12;
              if (value > startTime && value < endTime) {
                if (Math.abs(value - startTime) < unit || Math.abs(value - endTime) < unit) {
                  return '';
                }
                return dateFormatToHour(value);
              }
              return '';
            },
          },

          splitLine: {
            show: false,
          },
          min: parseInt(indicatorData.vmIndicatorList[0].startTime),
          max: parseInt(indicatorData.vmIndicatorList[0].endTime),
        },
      ],
      yAxis: {
        type: 'value',
        boundaryGap: true,
        nameTextStyle: {
          color: '#BBBBBB',
        },
        splitLine: {
          lineStyle: {
            color: '#4E4E4E',
          },
        },
        axisLabel: {
          color: '#BBBBBB',
          fontSize: 10,
          margin: 5,
          hideOverlap: true,
          align: 'right',
          overflow: 'break',
          width: 200,
        },
      },
      series: getHistorySeries(indicatorData.vmIndicatorList, true, rawIndicatorList.length === 0),
    };
  };

  useEffect(() => {
    let params = {
      instanceId: props.vmData.id,
    };
    if (rawIndicatorList.length > 0 && currentIndex > 0) {
      params.indicatorId = rawIndicatorList[currentIndex].indicatorId;
    }
    if (state.isTimeTrack) {
      params.endTime = state.selectedTime;
    }
    if (isTimeTrack) {
      setIndicatorData(timeTrackVMData.vmIndicator);
      return;
    }
    getVMIndicatorData(
      params,
      res => {
        if (!res || res.resultCode !== 0) {
          return;
        }
        if (selectValue.length === 0) {
          setRawIndicatorList(res.data.indicatorIdList);
          if (res.data.indicatorIdList.length >= 3) {
            setSelectValue([0, 1, 2]);
          } else {
            const array = [];
            for (let i = 0; i < res.data.indicatorIdList.length; i++) {
              array.push(i);
            }
            setSelectValue(array);
          }
        }
        res.data.vmIndicatorList = sortHistoryListData(res.data.vmIndicatorList || []);
        setIndicatorData(res.data);
      },
    );
  }, [props.vmData.id, props.moTypeId, props.siteId, props.refresh]);

  useEffect(() => {
    if (isTimeTrack) {
      dispatch({alarmEventData: timeTrackVMData.vmAlarmInfo.alarmDataList});
      return;
    }
    let params = {
      instanceId: props.vmData.id,
    };
    if (state.isTimeTrack) {
      params.timestamp = state.selectedTime;
    }
    getAlarmEventData(
      params,
      res => {
        if (!res || res.resultCode !== 0) {
          return;
        }
        dispatch({alarmEventData: res.data.alarmDataList});
      },
    );
  }, [props.vmData.id, props.moTypeId, props.refresh]);

  useEffect(() => {
    if (!chartDomRef.current || !indicatorData.vmIndicatorList || indicatorData.vmIndicatorList.length === 0) {
      return;
    }
    let chartObj = chartObjRef.current;
    const option = getOption();
    if (!chartObjRef.current) {
      chartObjRef.current = echarts.init(chartDomRef.current);
      chartObj = chartObjRef.current;
      chartObj.setOption(option);
    } else {
      const oldOption = chartObj.getOption();
      if (oldOption.series.length !== 0) {
        oldOption.xAxis = option.xAxis;
        oldOption.yAxis = option.yAxis;
        oldOption.series = option.series;
        chartObj.clear();
        chartObj.setOption(oldOption);
      }
    }
    chartObj.getZr().off('click');
    chartObj.getZr().on('click', param => {
      const pointInPixel = [param.offsetX, param.offsetY];
      if (!chartObj.containPixel('grid', pointInPixel)) {
        return;
      }
      let dataArr = indicatorData.vmIndicatorList;
      jumpToPMHistoryDataPage(dataArr, dataArr[0].startTime, dataArr[0].endTime);
    });

    setSeriesLegendData((option.series || []).map(v => {
      return {
        name: v.name,
        id: v.name,
      };
    }));
  }, [indicatorData, isOpenPanel, props.refresh]);

  let content = (
    <div style={{padding: '5px'}}>
      <p>
        {$t('moType.drill.vm.healthstatus')} :{' '}
        {$t(indicatorData.csnState === 0 ? 'moType.drill.vm.normal' : 'moType.drill.vm.abnormal')}
      </p>
      <p>
        {$t('moType.drill.vm.status')} :{' '}
        {$t(indicatorData.availableStatus === STATUS.normal ? 'moType.drill.pod.status.normal' : 'moType.drill.pod.status.abnormal')}
      </p>
    </div>
  );
  return (
    <>
      {isOpenPanel && (
        <div id="chooseVMRightPanel">
          <div>
            <div className={`header ${getUrlParam('isMM') ? 'moType_right_panel_title_mm' : ''}`}>
              <Tooltip
                placement="right"
                content={props.vmData.name}
                color="#393939"
                overlayStyle={{color: '#FFF'}}
              >
                <div className="title">{props.vmData.name}</div>
              </Tooltip>
            </div>

            <div className="baseInfo">
              <div className="baseInfoTable">
                <div className="baseInfoTableSpec">
                  <Tooltip
                    placement="right"
                    content={
                      content
                    }
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div
                      className={`${indicatorData.csnState === 0 && indicatorData.availableStatus === STATUS.normal ? 'status success' : 'status error'}`}
                    >
                      <span style={{marginRight: '5px'}}>
                        {indicatorData.csnState === 0 && indicatorData.availableStatus === STATUS.normal ?
                          $t('moType.drill.status.normal') :
                          $t('moType.drill.status.abnormal')}
                      </span>
                      {(indicatorData.csnState !== 0 || indicatorData.availableStatus !== STATUS.normal) && (
                        <img
                          style={{width: '15px', height: '15px'}}
                          src={errorIcon}
                        />
                      )}
                    </div>

                  </Tooltip>
                  <div className="desc">{$t('moType.drill.status')}</div>
                </div>
                <div className="baseInfoTableSpec">
                  <Tooltip
                    placement="right"
                    content={indicatorData.memoryTotal}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="status">{indicatorData.memoryTotal}</div>
                  </Tooltip>
                  <div className="desc">{$t('moType.drill.vm.memoryTotal')}(GB)</div>
                </div>
                <div className="baseInfoTableSpec">
                  <Tooltip
                    placement="right"
                    content={indicatorData.cpuNum}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="status">{indicatorData.cpuNum}</div>
                  </Tooltip>
                  <div className="desc">vCPU({$t('moType.drill.vm.num')})</div>
                </div>
                <div className="baseInfoTableSpec">
                  <Tooltip
                    placement="right"
                    content={indicatorData.vmName}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="status">{indicatorData.vmName}</div>
                  </Tooltip>
                  <div className="desc">{$t('moType.drill.vm.hostname')}</div>
                </div>
                <div className="baseInfoTableSpec">
                  <Tooltip
                    placement="right"
                    content={indicatorData.vmIp}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="status">{indicatorData.vmIp ? indicatorData.vmIp : 'NA'}</div>
                  </Tooltip>
                  <div className="desc">{$t('moType.drill.vm.host')}IP</div>
                </div>
              </div>

              <div className="baseInfoLine">
                <div className="memory">
                  <div className="baseInfoLineText">
                    <div
                      className='baseInfoLineDesc'
                    >{$t('moType.drill.vm.memoryUsage')}&nbsp;&nbsp;(%)
                    </div>
                    <div className="baseInfoLineStat">
                      <div className="baseInfoLineNum">{indicatorData.memoryUsageRate}%</div>
                    </div>
                  </div>
                  <div className="baseInfoLineChart">
                    <div
                      className="baseInfoLineChartNum"
                      style={{width: `${indicatorData.memoryUsageRate || 0}%`}}
                    />
                  </div>
                </div>
                {
                  parseInt(getQuery().applicationType) !== 3 &&
                  <div className="pod">
                    <div className="baseInfoLineText">
                      <div className="baseInfoLineDesc">{$t('moType.drill.vm.podNum')}</div>
                      <div className="area">
                        <div className="error">
                          {$t('moType.drill.vm.abnormal')}&nbsp;({props.vmData.abnormalPods})
                        </div>
                        <div className="normal">
                          {$t('moType.drill.vm.normal')}&nbsp;({props.vmData.normalPods})
                        </div>
                      </div>
                    </div>
                    <div className="progress">
                      <div
                        className="progressFirst"
                        style={{
                          flexGrow: props.vmData.abnormalPods,
                        }}
                      />
                      <div
                        className="progressSecond"
                        style={{
                          flexGrow: props.vmData.normalPods,
                        }}
                      />
                    </div>
                  </div>
                }
              </div>
            </div>

            <div className="resourceUse">
              <div
                className={`${getUrlParam('isMM') ? 'resourceUseTitleMM' : 'resourceUseTitle'}`}
              >{$t('moType.drill.vm.resourceUsage')}
              </div>

              {rawIndicatorList.length > 1 && (
                <div style={{width: '100%', height: '12%', marginTop: '1rem'}}>
                  {
                    getUrlParam('isMM') ?
                      <SelectButtonGroup
                        {
                        ...{
                          data: rawIndicatorList.map((item, index) => {
                            return {
                              id: `${index}___${item.indexName}`,
                              name: String(item.indexName),
                              onClick: () => {
                                setCurrenIndex(index);
                              },
                            };
                          }),
                          editButtonGroupClick: (newValue) => {
                            handleIndicatorChange((newValue || []).map(v => {
                              return Number(String(v).split('___')[0]);
                            }));
                          },
                        }
                        }
                      /> :
                      <>
                        <Tab
                          style={{display: 'inline-block', width: '85%'}}
                          draggable={false}
                          onClick={onIndicatorChange}
                          selectedIndex={selectValue.indexOf(currentIndex)}
                          titleLength={2}
                        >
                          {selectValue.map((item) => (
                            <TabItem key={item} lazyLoad={true}
                              tabItemStyle={{outline: 'none'}}
                              title={rawIndicatorList[item].indexName}
                              titleLength={item === currentIndex ? 8 : 2}
                            />
                          ))}
                        </Tab>

                        <Button
                          className="indicator_select_btn"
                          id="indicator_select_btn"
                          onClick={handleIndicatorSelect}
                          leftIconProps={{leftHoverIcon: edit}}
                          leftIcon={edit}
                          style={{
                            display: 'inline-block',
                            color: '#BBBBBB',
                            background: '#272727',
                            border: 'none',
                            float: 'right',
                            outline: 'none',
                            opacity: '0.6',

                            marginTop: '4px',

                            marginRight: '-15px',
                          }}
                        />
                        <div id="indicator_select_panel"
                          style={{visibility: indicatorSelectShow ? 'visible' : 'hidden'}}
                        >
                          <p className="indicator_select_tip"> {$t('indicator.select.tip')}</p>
                          <CheckboxGroup
                            id="indicator_select_checkbox"
                            onChange={handleIndicatorChange}
                            data={getSelectData()}
                            value={selectValue}
                            validtor={{minSelect: 1, maxSelect: 3}}
                            style={{marginLeft: '16px'}}
                            tipData={{placement: 'topRight', overlayStyle: {color: '#f5f5f5'}, color: '#393939'}}
                          />
                        </div>
                      </>
                  }
                </div>
              )}
              <p
                style={{
                  fontSize: '12px',
                  color: '#BBBBBB',
                  lineHeight: '12px',
                  fontFamily: 'HarmonyHeiTi',
                  marginTop: '20px',
                }}
              >
                {indicatorData?.vmIndicatorList?.[0]?.indexUnit ?? ''}
              </p>
              <div className='chart' ref={chartDomRef} id="vmChart" />
              {
                getUrlParam('isMM') &&
                <div style={{padding: '0 30px'}}>
                  <MoreLegend
                    data={legendData}
                    style={{marginTop: '-20px'}}
                    iconStyle={{
                      width: '10px',
                      height: '10px',
                      top: '5px',
                    }}
                    color={LINE_CHART_COLOR.map(v => v[0])}
                    onClick={(clickId) => {
                      const chartObj = echarts.getInstanceByDom(chartDomRef.current);
                      if (!chartObj) {
                        return;
                      }

                      setSelected(prevSelected => {
                        const oldSelected = {...prevSelected};
                        if (event.ctrlKey) {
                          // 点击的时候按住ctl键，只改变当前图例的勾选状态
                          oldSelected[clickId] = !oldSelected[clickId];
                        } else {
                          if (oldSelected[clickId]) {
                            // 点击的图例已勾选
                            if (Object.values(oldSelected).every(isSelect => isSelect)) {
                              // 当前为全选，单选该图例
                              for (const legendKey of Object.keys(oldSelected)) {
                                oldSelected[legendKey] = false;
                              }
                              oldSelected[clickId] = true;
                            } else {
                              // 当前为非全选，勾选全部图例
                              for (const legendKey of Object.keys(oldSelected)) {
                                oldSelected[legendKey] = true;
                              }
                            }
                          } else {
                            // 点击的图例未勾选，单选该图例
                            for (const legendKey of Object.keys(oldSelected)) {
                              oldSelected[legendKey] = false;
                            }
                            oldSelected[clickId] = true;
                          }
                        }

                        const selectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                          return value;
                        }).map(([key, value]) => {
                          return {
                            name: legendData.find(v => v.id === key)?.name,
                          };
                        });
                        const unSelectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                          return !value;
                        }).map(([key, value]) => {
                          return {
                            name: legendData.find(v => v.id === key)?.name,
                          };
                        });
                        chartObj.dispatchAction({
                          type: 'legendSelect',
                          batch: selectedNameList,
                        });

                        chartObj.dispatchAction({
                          type: 'legendUnSelect',
                          batch: unSelectedNameList,
                        });
                        return oldSelected;
                      });
                    }}
                  />
                </div>
              }
            </div>

            <div className="alarmComponent">
              <AlarmEventPanel />
            </div>
          </div>
        </div>
      )}
      <div
        className={isOpenPanel ? 'moType_drill_right_close_div' : 'moType_drill_right_open_div'}
        onClick={() => setIsOpenPanel(!isOpenPanel)}
      >
        <div className={isOpenPanel ? 'moType_drill_right_close' : 'moType_drill_right_open'} />
      </div>
    </>
  );
}

VMRightPanel.propTypes = {
  vmData: PropTypes.shape({
    id: PropTypes.number,
  }).isRequired,
  moTypeId: PropTypes.number,
};

export default VMRightPanel;
