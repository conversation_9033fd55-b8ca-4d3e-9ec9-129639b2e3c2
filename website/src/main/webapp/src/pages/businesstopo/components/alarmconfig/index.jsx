import React, { useState, useReducer, useEffect } from 'react';
import { ConfigProvider, Accordion } from 'eview-ui';
import { isZh } from '@util/index';
import { MENUS_CONFIG_RULER, TaskManageContext, GlobalContext } from './const';
import AlarmConfig from './AlarmConfig';
import TaskManagement from './taskManagement';
import { ruleConfigReducer, initialRuleConfigState } from './reducer';
import styles from './index.less';

// 从url中获取subMenu的值
const getDefaultCompKey = () => {
  const result = /subMenu=([^&]+)/g.exec(window.location.href);
  return result ? result[1].split('?')[0] : 'config';
};

const RuleConfig = () => {
  const [curComponent, setCurComponent] = useState(encodeURIComponent(getDefaultCompKey()));
  const contextValue = useReducer(ruleConfigReducer, initialRuleConfigState);
  const [curTheme, setCurTheme] = useState(piu.get('theme'));
  const [showAccordion, setShowAccordion] = useState(true);
  
  const menuItemClick = (node) => {
    setCurComponent(node.value);
    window.location.hash = `#path=/businesstopo/config&subMenu=${encodeURIComponent(node.value)}`;
  };

  piu.attach(piu, {
    $stateChange: {
      theme: theme => {
        setCurTheme(theme);
      },
    },
  });

  const accordionWidth = isZh ? '16rem' : '19rem';
  return (
    <ConfigProvider
      listenTheme={true}
      version='aui3-1'
      theme={curTheme}
      locale={isZh ? 'zh' : 'en'}
    >
      <GlobalContext.Provider value={{theme: curTheme}}>
        <div
          style={{
            height: 'calc(100vh - 4rem)',
            display: 'flex',
            width: '100%',
          }}
          className={curTheme === 'evening' ? styles.rule_config_evening : styles.rule_config_lightday}
        >
          {showAccordion && (
            <Accordion
              data={MENUS_CONFIG_RULER.menus}
              headerText={MENUS_CONFIG_RULER.header.title}
              headerIconPosition='left'
              selectedValue={curComponent}
              showArrow={true}
              onClick={menuItemClick}
              style={{
                width: accordionWidth,
              }}
            />
          )}
          {curComponent === 'config' && <AlarmConfig />}
          {curComponent === 'taskManagement' && (
            <TaskManageContext.Provider value={contextValue}>
              <TaskManagement setShowAccordion={setShowAccordion} />
            </TaskManageContext.Provider>
          )}
        </div>
      </GlobalContext.Provider>
    </ConfigProvider>
  );
};

export default RuleConfig;
