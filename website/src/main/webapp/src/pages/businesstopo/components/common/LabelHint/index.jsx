import React, { useState } from 'react';
import { Icon, Tooltip } from 'eview-ui';
import { isZh } from '@util/index';
import tipBrightIcon from '../../../../../apps/businesstopo/assets/tipHighLight.png';
import tipGrayIcon from '../../../../../apps/businesstopo/assets/tip.png';
import styles from './index.less';

/**
 * 表单中 有？的 label
 */
const LabelHint = ({ require, label, noColon, isHelp, hint, theme = 'evening' }) => {
  const [isHighLight, setIsHighLight] = useState(false);
  const [colon] = useState(isZh ? '：' : ':');

  return (
    <div style={{ position: 'relative', height: '2rem', lineHeight: '2rem' }}>
      {require && <span className={styles.labelSpan}>*</span>}
      <span style={{ float: 'left', paddingLeft: !noColon ? '8px' : '', color: theme === 'evening' ? 'rgba(255, 255, 255, 1)' : null}}>
        {label}{!noColon && colon}
      </span>
      <Tooltip content={hint} placement='topLeft'>
        {isHelp && (
          <div onMouseEnter={() => setIsHighLight(true)}
            onMouseLeave={() => setIsHighLight(false)} style={{ display: 'inline-block' }}
          >
            <Icon style={{ marginLeft: '2px' }} iconUrl={!isHighLight ? tipGrayIcon : tipBrightIcon} />
          </div>
        )}
      </Tooltip>
    </div>
  );
};

export default LabelHint;
