export default {
  'zh-cn': {
    'mm.edit.businessTypeName': '第三方名称',
    'mm.edit.channelName': '渠道名称',
    'mm.edit.stripName': '条带配置',
    'mm.edit.businessType': '业务类型',
    'mm.edit.associatedIndicators': '关联指标',
    'mm.edit.keyIndicators': '关键指标',
    'mm.edit.primaryIndicators': '主要指标',
    'mm.edit.thirdParty': '第三方',
    'mm.edit.channel': '渠道',
    'mm.edit.indicators': '指标',
    'mm.edit.associatedAccessChannel': '关联接入渠道',
    'mm.edit.strip': '条带',
    'mm.edit.motype': '网元类型',
    'mm.edit.measUnitKey': '测量单元',
    'mm.edit.measUnit.indicator': '测量指标',
    'mm.edit.measUnit.originalValue': '测量对象',
    'mm.edit.hide': '隐藏',
    'mm.edit.name.repeats': '名称重复',
    'mm.edit.no.primary': '没有主指标',
    'mm.edit.no.indicator': '没有指标',
    'mm.edit.hidden.all': '不能全部隐藏',
    'mm.edit.channel.length':'不能没有渠道',
    'business.check.failed': '校验失败,请重新检查',
    'error.msg':'错误信息',
    'mm.edit.button.cancel': '取消',
    'mm.edit.button.sure': '确定',
    'kpi.new.task.resource.loading': '正在加载，请稍后...',
    'kpi.new.task.resource.network': '网元类型',
    'kpi.new.task.resource.all': '全部',
    'kpi.new.task.resource.measure.unit': '测量单元',
    'kpi.new.task.resource.measure.index': '测量指标',
    'task.filter.measure.keyword': '测量单元或测量指标',
    'kpi.new.task.resource.no.data': '暂无数据',
    'kpi.new.task.resource.measure.object': '测量对象',
    'button.cancel': '取消',
    'button.confirm': '保存',
    sys_resource_valid_audit_char:
      '输入不能包含特殊字符！其中，特殊字符为#+&|<>\'";\\',
    site_name_valid_audit_char: '输入不能包含特殊字符！其中，特殊字符为#%()/+&|<>\'";\\',
    sys_valid_name_length_info: '字符长度不能超过{0}',
    sys_valid_number_info: 'TopN必须是数字且大于等于1，小于等于20',
    sys_valid_name_duplicate: '名称不能重复',
  },
  'en-us': {
    'kpi.new.task.resource.loading': 'Loading, Please wait...',
    'kpi.new.task.resource.network': 'NE Type',
    'kpi.new.task.resource.all': 'All',
    'kpi.new.task.resource.measure.unit': 'Measurement Unit',
    'kpi.new.task.resource.measure.index': 'Measurement Indicator',
    'task.filter.measure.keyword': 'Measurement Unit or Measurement Indicator',
    'kpi.new.task.resource.no.data': 'No records found.',
    'kpi.new.task.resource.measure.object': 'Measurement Object',
    'mm.edit.businessTypeName': 'Third party Name',
    'mm.edit.channelName': 'Channel Name',
    'mm.edit.stripName': 'Strip Configuration',
    'mm.edit.businessType': 'Business Type',
    'mm.edit.associatedIndicators': 'Associated Indicators',
    'mm.edit.keyIndicators': 'Key Indicators',
    'mm.edit.primaryIndicators': 'Primary Indicators',
    'mm.edit.thirdParty': 'Third party',
    'mm.edit.channel': 'Channel',
    'mm.edit.strip': 'Strip',
    'mm.edit.indicators': 'Indicator',
    'mm.edit.associatedAccessChannel': 'Associated Access Channel',
    'mm.edit.motype': 'MoType',
    'mm.edit.measUnitKey': 'MeasUnit',
    'mm.edit.measUnit.indicator': 'Indicators',
    'mm.edit.measUnit.originalValue': 'OriginalValue',
    'mm.edit.hide': 'Hide',
    'mm.edit.name.repeats': 'Duplicate name.',
    'mm.edit.no.primary':'There is no primary indicator.',
    'mm.edit.no.indicator': 'There is no indicator.',
    'mm.edit.hidden.all': 'You can\'t hide all of them.',
    'business.check.failed': 'Verification failed. Please check again.',
    'error.msg':'Error msg',
    'mm.edit.button.cancel': 'Cancel',
    'mm.edit.button.sure': 'Confirm',
    'button.cancel': 'Cancel',
    'button.confirm': 'Save',
    sys_resource_valid_audit_char: 'The value cannot contain the following special characters: #+&|<>\'";\\',
    site_name_valid_audit_char: 'The value cannot contain the following special characters: #%()/+&|<>\'";\\',
    sys_valid_name_length_info: 'The length cannot exceed {0} characters.',
    'mm.edit.channel.length':'Can\'t be without channels',
    sys_valid_number_info: 'The value of TopN must be a number ranging from 1 to 20.',
    sys_valid_name_duplicate: 'The name cannot be duplicated',
  },
};
