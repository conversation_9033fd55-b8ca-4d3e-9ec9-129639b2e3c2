#editRightPanel {
  display: inline-block;
  right: 0;
  position: absolute;
  z-index: 999;
  height: calc(100vh - 4rem);
  background-color: #272727;
  overflow-x: hidden;


  .content {
    padding: 24px 16px;

    .header {
      display: inline-block;
      position: relative;
      width: 100%;

      .title {
        color: rgb(245, 245, 245);
        font-size: 20px;
        font-weight: 500;
        line-height: 27px;
        letter-spacing: 0px;
        text-align: left;
      }
      .close {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
      }
    }

    .splitLine {
      height: 1px;
      background-color: #353333;
      margin: 16px 0;
    }

    .selectCls {
      width: 368px!important;
    }

    .rightToggle {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }

    .indicatorsAddIcon{
      display: inline-block; /* 使得span元素可以设置宽高 */
      width: 20px; /* 设置宽度 */
      height: 20px; /* 设置高度 */
      background-size: contain; /* 控制背景图像大小 */
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }

    .cardListContainer {
      overflow: auto;
    }

    .cardItem {
      width: 348px;
      height: 154px;
      border-radius: 4px;
      background: #313131;
      padding: 0 10px;
      margin-top: 10px;
      color: rgb(187, 187, 187);
      font-size: 14px;
      font-weight: 400;

      .cardItemContent {
        display: flex;
        flex-direction: column;
      }

      .cardItemHeader {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
      }

      .cardItemContentRow {
        padding: 10px 0 0 0;
        display: flex;
      }
    }


    .associatedIndicatorsSelectCls {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
  }
}

#editRightPanel::-webkit-scrollbar {
  width: 0;
  background-color: #272727;
}

.cardListContainer::-webkit-scrollbar {
  width: 0;
  background-color: #272727;
}
