/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

/**
 * 网元类型下钻视图
 */
import React, {useEffect, useReducer, useRef, useState} from 'react';
import {ConfigProvider} from 'eview-ui';
import Crumbs from 'eview-ui/Crumbs';
import {$t, registerResource} from '@util';
import {getAppData, getMoTypeDrillData} from '../../api/moTypeDrill';
import {initState, reducer} from '../../reducer';
import {BUSINESS_TOPO_CONTEXT as TopoContext} from '../../const';
import i18n from '../../locales/moTypeDrill';
import '../../css/index.less';
import {getUrlParam, setSessionData} from '../../util';
import {HMS_TYPE_HOME, MO_TYPE_HOME} from '../../const/timeLine';
import TimeLine from '../timeline/TimeLine';
import PodDrillDownW3D from '../../a3dPages/components/PodDrillDownPage';
import {getQuery} from '../../a3dPages/components/utils';
import Toolbar from '@pages/businesstopo/components/toolbar/Toolbar';
import {queryCurrentIds} from '../../api';
import HSMRightPanel from '@pages/businesstopo/components/hsm/HSMRightPanel';
import HSMPanelChart from '@pages/businesstopo/components/hsm/HSMPanelChart';
import {resizeW3D} from '@pages/businesstopo/a3dPages/components/PodDrillDownPage/podDrillDown3D';
import {setHelpId} from '@util/index';


function HSMTypeDrill() {
  registerResource(i18n, 'moTypeDrillI18n');
  setHelpId('com.huawei.dvtopo.business');
  const [state, dispatch] = useReducer(reducer, initState);
  const [refresh, setRefresh] = useState(0);
  const [showAppPanel, setShowAppPanel] = useState({
    isShow: false, isOpen: false, appData: {}, isTimeTrack: false, timeTrackAppData: {},
  });
  const [data, setData] = useState([]);
  const [pageParams, setPageParams] = useState({
    moTypeId: getUrlParam('moTypeId'),
    siteId: getUrlParam('siteId'),
    solutionId: getUrlParam('solutionId'),
    siteName: decodeURIComponent(getUrlParam('siteName')),
    moTypeName: getUrlParam('moTypeName'),
    stripId: getUrlParam('stripId'),
    stripUnit: getUrlParam('stripUnit'),
    isSearch: getUrlParam('isSearch'),
  });
  const [selectId, setSelectId] = useState(getUrlParam('moTypeId'));
  const [drillData, setDrillData] = useState({isInit: true, data: null, events: [], isMM: false});
  const initFlagRef = useRef(0);
  let moSelectedTime = useRef(0);
  const isTimeTrack = useRef(false);
  const getA3dEvents = () => {
    return [{
      // 点击事件，联动右侧面板
      eventName: 'from3d_showPanel', async fn(setting) {
        if (setting.type === 'app') {
          let appData = await queryAppData(setting.id);
          handleAppDetailsInfo(appData.respData, setting.id);
          setSelectId(setting.id);
        } else {
          setShowAppPanel({
            isShow: false, isOpen: false, appData: {},
          });
          setSelectId(getUrlParam('moTypeId'));
        }
      },
    }, {
      // 悬浮事件，显示tooltip
      eventName: 'from3d_showCommonTip', fn(setting) {
        let tipStr;
        setting.dom.innerHTML = '';
        if (setting.type === 'app') {
          tipStr = `<div class="moType_tip_hsm_container">
                        <div class="moType_tipTitle">${setting.data.name}</div>
                      </div>`;
        }
        if (tipStr) {
          let tipParser = new DOMParser();
          let tipElement = tipParser.parseFromString(tipStr, 'text/html');
          setting.dom.append(tipElement.body.firstChild);
        }
      },
    }];
  };

  useEffect(() => {
    if (showAppPanel.isOpen) {
      dispatch({showKPIPanel: true});
    } else {
      dispatch({showKPIPanel: false});
    }
    resizeW3D();
  }, [showAppPanel.isOpen]);

  useEffect(() => {
    dispatch({showKPIPanel: false});
    setData([{
      title: $t('overViewName'), url: '/eviewwebsite/index.html#path=/businesstopo',
    }, {
      title: `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})`,
      url: `/eviewwebsite/index.html#path=/businesstopo/mmStripDrillDown&stripId=${pageParams.siteId}&stripName=${pageParams.siteName}&solutionId=${pageParams.solutionId}${pageParams.stripUnit ? `&stripUnit=${pageParams.stripUnit}` : ''}`,
    }, {
      title: decodeURIComponent(pageParams.moTypeName),
    }]);
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    let currentSelectSolutionIndex = 0;
    dispatch({
      selectSolutionIndex: currentSelectSolutionIndex,
    });
    if (topoSession.isTimeTrack) {
      return () => {
        // 空实现
      };
    }
    queryDrillData(true);
    initFlagRef.current = 1;
    const intervalId = setInterval(() => {
      if (!isTimeTrack.current) {
        setRefresh(new Date().getTime());
        queryDrillData(false);
      }
    }, 60 * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // 查询钻取数据
  const queryDrillData = initFlag => {
    let params = {
      instanceId: pageParams.moTypeId,
    };
    const query = getQuery();
    if (query.stripUnit) {
      params.stripeUnit = query.stripUnit;
    }
    getMoTypeDrillData(params, resp => {
      if (!resp || resp.resultCode !== 0) {
        return;
      }
      if (resp.data && resp.data.businessClusterList && resp.data.businessClusterList.length > 0) {
        resp.data.applicationType = 4;
        setDrillData({
          isInit: initFlag,
          data: resp.data,
          events: getA3dEvents(resp.data.businessClusterList),
          isMM: Boolean(getUrlParam('isMM')),
        });
      } else {
        closeAppPanel();
      }
    });
  };

  // 查询pod部署关系
  const queryAppData = appId => new Promise((resolve, reject) => {
    let param = {instanceId: appId};
    if (moSelectedTime.current !== 0) {
      param.timestamp = moSelectedTime.current;
    }
    getAppData(param, resp => {
      if (!resp || resp.resultCode !== 0) {
        reject(new Error('failed'));
        return;
      }
      resolve({
        respData: resp.data,
      });
    }, () => {
      reject(new Error('failed'));
    });
  });

  // 关闭pod详情面板
  const closeAppPanel = setting => {
    setShowAppPanel({
      isShow: false, isOpen: false, detailsInfoArr: [], appData: {}, isTimeTrack: false, trackAlarmData: {},
    });
  };

  // 处理App右侧面板初始化数据
  const handleAppDetailsInfo = (respData, appId) => {
    let detailsInfoArr = [
      {
        value: respData.appName, title: $t('moType.drill.rightPanel.appName'),
      },
      {
        value: `${decodeURIComponent(pageParams.siteName)}(${decodeURIComponent(pageParams.stripUnit)})`,
        title: $t('moType.drill.rightPanel.stripName'),
      }, {
        value: respData.insIP ? respData.insIP : 'NA', title: $t('moType.drill.rightPanel.ipAddress'),
      },
    ];

    setShowAppPanel({
      isShow: true, isOpen: true, detailsInfoArr, appData: {
        name: respData.appName,
        id: appId,
      },
    });
  };

  // 时间回溯回调事件
  const timeTrack = timeStamp => {
    closeAppPanel();
    dispatch({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    moSelectedTime.current = timeStamp;
    setSessionData({
      selectedTime: timeStamp, isTimeTrack: true,
    });
    isTimeTrack.current = true;
    setRefresh(timeStamp);
    let param = {
      instanceIdList: [],
      timestamp: 0,
      targetTimestamp: timeStamp,
    };
    param.instanceIdList.push(pageParams.moTypeId);
    queryCurrentIds(param, async({data: idMap}) => {
      if (!idMap || Object.entries(idMap).length === 0) {
        let nullData = {
          businessClusterList: [],
          podLevel: null,
          producePodColSize: null,
          grayPodColSize: null,
          applicationType: 4,
        };
        setDrillData({
          isInit: false, data: nullData, events: getA3dEvents(resp.data.businessClusterList),
        });
        return;
      }
      let params = {
        instanceId: idMap[pageParams.moTypeId],
        timestamp: timeStamp,
      };
      const query = getQuery();
      if (query.stripUnit) {
        params.stripeUnit = query.stripUnit;
      }

      getMoTypeDrillData(params, resp => {
        if (!resp || resp.resultCode !== 0) {
          return;
        }
        resp.data.applicationType = 4;
        if (resp.data) {
          setDrillData({
            isInit: initFlagRef.current === 0, data: resp.data, events: getA3dEvents(resp.data.businessClusterList),
          });
          initFlagRef.current = 1;
        }
      });
    });
  };

  // 退出时间回溯回调事件
  const exitTimeTrack = () => {
    moSelectedTime.current = 0;
    closeAppPanel();
    dispatch({
      selectedTime: 0, isTimeTrack: false,
    });
    isTimeTrack.current = false;
    setSessionData({
      selectedTime: 0, isTimeTrack: false,
    });
    setRefresh(new Date().getTime());
    queryDrillData(false);
  };

  return (
    <TopoContext.Provider value={{state, dispatch}}>
      <ConfigProvider version="aui3-1" theme="evening">
        <div style={{
          background: '#191919',
          width: showAppPanel.isOpen ? 'calc(100% - 400px)' : '100%',
        }} id="motypeDrillTopo"
        >
          <PodDrillDownW3D pageParams={drillData} />
          {
            !pageParams.isSearch &&
            <div className="moType_back_btn">
              <Crumbs data={data} seprator="/" />
            </div>
          }
          <HSMPanelChart id={selectId} showKPIPanel={showAppPanel.isOpen} refresh={refresh} />
          {showAppPanel.isShow && (
            <HSMRightPanel
              closePanel={closeAppPanel}
              detailsInfoArr={showAppPanel.detailsInfoArr}
              appData={showAppPanel.appData}
              moTypeId={pageParams.moTypeId}
              isTimeTrack={showAppPanel.isTimeTrack}
              trackAlarmData={showAppPanel.trackAlarmData}
              isOpenPanel={showAppPanel.isOpen}
              setIsOpenPanel={flag => setShowAppPanel({...showAppPanel, isOpen: flag})}
              refresh={refresh}
            />)}
          <TimeLine
            renderTopology={timeTrack}
            refreshFlag={refresh}
            backTimeTrack={exitTimeTrack}
            pageType={HMS_TYPE_HOME}
          />
          <Toolbar isMotype={true} showMotypePanel={showAppPanel.isOpen} isMM={pageParams.isMM} />
        </div>
      </ConfigProvider>
    </TopoContext.Provider>
  );
}

export default HSMTypeDrill;
