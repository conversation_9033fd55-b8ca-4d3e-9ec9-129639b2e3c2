.topoButtonGroup {
  display: flex;
  flex-wrap: nowrap; /* 不允许换行 */
  gap: 20px;
  justify-content: space-around;
  font-family: HarmonyOSHans;
}


.topoButtonGroupItem {
  cursor: pointer;

  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 在溢出的地方显示省略号 */
}

.topoSelectTab {
  display: flex;
  width: 100%;
  align-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 20px;
  color: rgb(201, 201, 201);
  font-size: 1rem;
  position: relative;
}

.topoSelectTabActive {
  border-bottom: 2px solid rgb(46, 148, 255);
  color: rgb(46, 148, 255);
}

.topoSelectTabItem {
  flex-grow: 0;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  z-index: 5;
}

.topoSelectTabItem:hover {
  color: #fff;
}

.topoSelectTabItemSpan {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

