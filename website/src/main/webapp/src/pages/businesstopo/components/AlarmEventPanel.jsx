/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useState} from 'react';
import {BUSINESS_TOPO_CONTEXT} from '../const';
import Tab, {TabItem} from 'eview-ui/Tab';
import {Button} from 'eview-ui';
import AlarmDetail from './AlarmDetail';
import {$t} from '../../../commonUtil';
import EventDetail from './EventDetail';
import {getAlarmData, getMoreAlarmData} from '../api';
import {jumpViewMorePage, jumpViewMoreEventPage, jumpViewMoreHistoryPage} from '../../../commonUtil/tools';
import AnalysisDetail from '@pages/businesstopo/components/AnalysisDetail';

function AlarmEventPanel(props) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const {currentEvent, alarmEventData, alarmShowIndex} = state;

  // 告警
  const [alarmData, setAlarmData] = useState({});

  // 当前展示的告警/事件集合
  const [currentInfo, setCurrentInfo] = useState([]);

  // 当前展示智能运维告警集合
  const [currentAnalysisInfo, setCurrentAnalysisInfo] = useState([]);
  // 当前展示智能运维告警信息
  const [analysisInfo, setAnalysisInfo] = useState({
    showMore: false,
    index: 0,
  });

  const PAGE_SIZE = 10;
  // 数量
  const [alarmCount, setAlarmCount] = useState([0, 0, 0, 0, 0]);

  useEffect(() => {
    if (!props.isOverview) {
      return;
    }
    if (!state.solutionId) {
      return;
    }
    const {selectSiteId, solutionId} = JSON.parse(sessionStorage.getItem('topoSession'));
    if (!solutionId) {
      return;
    }
    let alarmParam = {
      instanceId: state.solutionId,
    };
    if (selectSiteId && selectSiteId !== -1) {
      alarmParam.instanceId = selectSiteId;
    }

    // 告警数据
    getAlarmData(
      alarmParam,
      res => {
        let alarmDatas = res.data;
        dispatch({
          alarmEventData: alarmDatas.alarmDataList,
        });
      },
    );
  }, [
    props.refreshFlag,
  ]);

  useEffect(() => {
    if (alarmEventData.length === 0) {
      setAlarmData({});
      setCurrentInfo([]);
      setAlarmCount([0, 0, 0, 0, 0, 0]);
    }
    // 告警信息
    let alarmInfo = alarmEventData.find(item => item.category === 1);
    setAlarmData(alarmInfo);

    // 设置数量
    setAlarmCount([alarmInfo.alarmTotalCount, getAlarmCount(alarmInfo.alarmLevelList, 1), getAlarmCount(alarmInfo.alarmLevelList, 2), getAlarmCount(alarmInfo.alarmLevelList, 3), getAlarmCount(alarmInfo.alarmLevelList, 4)]);
    if (alarmShowIndex === 0) {
      let eventInfo = alarmEventData.find(item => item.category === 1);
      if (currentEvent === 'all') {
        setCurrentInfo(eventInfo === undefined ? [] : eventInfo.alarmRecordList);
      }
      if (currentEvent === 'urgent') {
        setCurrentInfo(eventInfo.alarmRecordList.filter(item => item.severity === '1'));
      }
      if (currentEvent === 'import') {
        setCurrentInfo(eventInfo.alarmRecordList.filter(item => item.severity === '2'));
      }
      if (currentEvent === 'minor') {
        setCurrentInfo(eventInfo.alarmRecordList.filter(item => item.severity === '3'));
      }
      if (currentEvent === 'info') {
        setCurrentInfo(eventInfo.alarmRecordList.filter(item => item.severity === '4'));
      }
    } else {
      handleTabChange(alarmShowIndex);
    }
  }, [alarmEventData]);

  const getAlarmCount = (list, levle) => {
    let data = list.find(item => item.level === levle);
    return data === undefined ? 0 : data.count;
  };

  // 根据级别和时间排序
  const sortAlarm = list => list;

  const clickAll = () => {
    dispatch({
      currentEvent: 'all',
    });
    setCurrentInfo(sortAlarm(alarmData.alarmRecordList));
  };

  const clickUrgent = () => {
    dispatch({
      currentEvent: 'urgent',
    });
    setCurrentInfo(alarmData.alarmRecordList.filter(item => item.severity === '1'));
  };

  const clickImport = () => {
    dispatch({
      currentEvent: 'import',
    });
    setCurrentInfo(alarmData.alarmRecordList.filter(item => item.severity === '2'));
  };

  const clickMinor = () => {
    dispatch({
      currentEvent: 'minor',
    });
    setCurrentInfo(alarmData.alarmRecordList.filter(item => item.severity === '3'));
  };

  const clickInfo = () => {
    dispatch({
      currentEvent: 'info',
    });
    setCurrentInfo(alarmData.alarmRecordList.filter(item => item.severity === '4'));
  };
  const handleTabChange = (index, title) => {
    dispatch({
      alarmShowIndex: index,
      currentEvent: 'all',
    });
    if (index === 0) {
      let eventInfo = alarmEventData.find(item => item.category === 1);
      setCurrentInfo(eventInfo === undefined ? [] : eventInfo.alarmRecordList);
      setCurrentAnalysisInfo([]);
      setAnalysisInfo({
        showMore: false,
        index: 0,
      });
    } else if (index === 2) {
      let eventInfo = alarmEventData.find(item => item.category === 3);
      setCurrentInfo(eventInfo === undefined ? [] : eventInfo.alarmRecordList);
      setCurrentAnalysisInfo([]);
      setAnalysisInfo({
        showMore: false,
        index: 0,
      });
    } else {
      let alarmInfoData = alarmEventData.find(item => item.category === 1);
      if (alarmInfoData === undefined) {
        alarmInfoData = [];
      } else {
        alarmInfoData = alarmInfoData.alarmRecordList.filter(record => record.analysisStatus !== null && record.analysisStatus !== undefined);
      }
      if (alarmInfoData.length > PAGE_SIZE && analysisInfo.index < alarmInfoData.length) {
        setAnalysisInfo({
          showMore: true,
          index: analysisInfo.index > PAGE_SIZE ? analysisInfo.index : PAGE_SIZE,
        });
      } else {
        setAnalysisInfo({
          showMore: false,
          index: alarmInfoData.length,
        });
      }
      setCurrentInfo([]);
      const endPoint = analysisInfo.index > PAGE_SIZE ? analysisInfo.index : PAGE_SIZE;
      setCurrentAnalysisInfo(alarmInfoData.splice(0, endPoint));
    }
  };

  const analysiscShowMore = (index) => {
    let alarmInfo = alarmEventData.find(item => item.category === 1);
    if (alarmInfo === undefined) {
      alarmInfo = [];
    } else {
      alarmInfo = alarmInfo.alarmRecordList.filter(record => record.analysisStatus !== null && record.analysisStatus !== undefined);
    }
    let newIndex = index + PAGE_SIZE < alarmInfo.length ? index + PAGE_SIZE : alarmInfo.length;
    if (index + PAGE_SIZE < alarmInfo.length) {
      setAnalysisInfo({
        showMore: true,
        index: newIndex,
      });
    } else {
      setAnalysisInfo({
        showMore: false,
        index: alarmInfo.length,
      });
    }
    setCurrentAnalysisInfo(alarmInfo.splice(0, newIndex));
  };

  let isAll = currentEvent === 'all';
  let isUrgent = currentEvent === 'urgent';
  let isImport = currentEvent === 'import';
  let isMinor = currentEvent === 'minor';
  let isInfo = currentEvent === 'info';
  return (
    <div className="alarm_event_panel">
      <Tab draggable={false} onClick={handleTabChange} selectedIndex={alarmShowIndex}>
        <TabItem title={$t('alarm')} tabItemStyle={{outline: 'none', margin: '0 10px 0 0'}} />
        <TabItem title={$t('analysis')} tabItemStyle={{outline: 'none', margin: '0 10px'}} />
        <TabItem title={$t('event')} tabItemStyle={{outline: 'none', margin: '0 10px'}} />
      </Tab>
      {alarmShowIndex === 0 &&
        <div style={{marginTop: '-10'}}>
          <div
            id="alarmEventDiv"
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              paddingRight: '24px',
              marginTop: '18px',
              marginBottom: '1rem',
              position: 'sticky',
              background: '#272727',
              zIndex: 2,
            }}
          >
            <Button onClick={clickAll} size="small" className={isAll ? 'alarm_level_btn_blue' : 'alarm_level_btn'}
              style={{
                background: isAll ? 'rgba(46,148,255,0.10 )' : '#272727',
                color: isAll ? '#2E94FF' : ' #BBBBBB',
                outline: 'none',
                padding: '0px',
              }} text={`${$t('alarm.text.all')}(${alarmCount[0] > 99 ? '99+' : alarmCount[0]})`}
            />
            <Button onClick={clickUrgent} size="small" className={isUrgent ? 'alarm_level_btn_blue' : 'alarm_level_btn'}
              style={{
                background: isUrgent ? 'rgba(46,148,255,0.10 )' : '#272727',
                color: isUrgent ? '#2E94FF' : ' #BBBBBB',
                outline: 'none',
                padding: '0px',
              }} text={`${$t('alarm.text.critical')}(${alarmCount[1] > 99 ? '99+' : alarmCount[1]})`}
            />
            <Button onClick={clickImport} size="small" className={isImport ? 'alarm_level_btn_blue' : 'alarm_level_btn'}
              style={{
                background: isImport ? 'rgba(46,148,255,0.10 )' : '#272727',
                color: isImport ? '#2E94FF' : ' #BBBBBB',
                outline: 'none',
                padding: '0px',
              }} text={`${$t('alarm.text.major')}(${alarmCount[2] > 99 ? '99+' : alarmCount[2]})`}
            />
            <Button onClick={clickMinor} size="small" className={isMinor ? 'alarm_level_btn_blue' : 'alarm_level_btn'}
              style={{
                background: isMinor ? 'rgba(46,148,255,0.10 )' : '#272727',
                color: isMinor ? '#2E94FF' : ' #BBBBBB',
                outline: 'none',
                padding: '0px',
              }} text={`${$t('alarm.text.minor')}(${alarmCount[3] > 99 ? '99+' : alarmCount[3]})`}
            />
            <Button onClick={clickInfo} size="small" className={isInfo ? 'alarm_level_btn_blue' : 'alarm_level_btn'}
              style={{
                background: isInfo ? 'rgba(46,148,255,0.10 )' : '#272727',
                color: isInfo ? '#2E94FF' : ' #BBBBBB',
                outline: 'none',
                padding: '0px',
              }} text={`${$t('alarm.text.info')}(${alarmCount[4] > 99 ? '99+' : alarmCount[4]})`}
            />
          </div>
          <div>
            {currentInfo.length > 0 && currentInfo.slice(0, 10).map(item => <AlarmDetail record={item} />)}
          </div>
        </div>}
      {alarmShowIndex === 2 &&
        <div>
          <div>
            {currentInfo.length > 0 && currentInfo.slice(0, 10).map(item => <EventDetail record={item} />)}
          </div>
        </div>}
      {
        alarmShowIndex === 1 &&
        <div>
          <div>
            {
              currentAnalysisInfo.length > 0 && currentAnalysisInfo.map((item, index) =>
                <AnalysisDetail key={index}
                  record={item}
                />,
              )
            }
          </div>
        </div>
      }

      {currentInfo.length > 0 &&
        <div style={{
          fontSize: '14px',
          color: '#2E94FF',
          textAlign: 'center',
          display: 'inline-block',
          paddingBottom: '18px',
          paddingTop: '16px',
          width: '100%',
        }} className="topo-click-scale" onClick={e => {
          if (alarmShowIndex === 0) {
            let topoSession = JSON.parse(
              sessionStorage.getItem('topoSession') || '{}',
            );
            getMoreAlarmData({
              csnList: currentInfo.map(v => v.csn).join(','),
              timestamp: topoSession.selectedTime,
            }, ({data, resultCode}) => {
              if (topoSession.selectedTime > 0) {
                jumpViewMoreHistoryPage(data);
              } else {
                jumpViewMorePage(data);
              }
            });
          } else if (alarmShowIndex === 2) {
            getMoreAlarmData({csnList: currentInfo.map(v => v.csn).join(','), dataType: 'event'}, ({
              data, resultCode,
            }) => {
              jumpViewMoreEventPage(data);
            });
          }
        }}
        >
          {
            $t('alarm.view.more')
          }
        </div>}

      {currentAnalysisInfo.length > 0 && analysisInfo.showMore &&
        <div style={{
          fontSize: '14px',
          color: '#2E94FF',
          textAlign: 'center',
          display: 'inline-block',
          paddingBottom: '18px',
          paddingTop: '16px',
          width: '100%',
        }} className="topo-click-scale" onClick={e => {
          if (alarmShowIndex === 0) {
            let topoSession = JSON.parse(
              sessionStorage.getItem('topoSession') || '{}',
            );
            getMoreAlarmData({
              csnList: currentInfo.map(v => v.csn).join(','),
              timestamp: topoSession.selectedTime,
            }, ({data, resultCode}) => {
              if (topoSession.selectedTime > 0) {
                jumpViewMoreHistoryPage(data);
              } else {
                jumpViewMorePage(data);
              }
            });
          } else if (alarmShowIndex === 1) {
            analysiscShowMore(analysisInfo.index);
          } else {
            getMoreAlarmData({csnList: currentInfo.map(v => v.csn).join(','), dataType: 'event'}, ({
              data, resultCode,
            }) => {
              jumpViewMoreEventPage(data);
            });
          }
        }}
        >
          {
            $t('alarm.load.more')
          }
        </div>}

    </div>
  );
}

export default AlarmEventPanel;
