import React, {useState, useEffect, useMemo, useRef} from 'react';
import * as echarts from 'echarts';
import styles from './index.less';
import {formatDate2SysSetting} from '@digitalview/fe-utils';
import {dateFormatToHour} from '../../../../../commonUtil/tools';
import Legend, {MoreLegend} from '../Legend';

const color = [
  ['rgba(241,110,51)', 'rgba(241,110,51, 0.5)', 'rgba(241,110,51, 0.1)'],
  ['rgba(106,64,246)', 'rgba(106,64,246, 0.5)', 'rgba(106,64,246, 0.1)'],
  ['rgba(58,132,255)', 'rgba(58,132,255, 0.5)', 'rgba(58,132,255, 0.1)'],
  ['rgba(54,221,242)', 'rgba(54,221,242, 0.5)', 'rgba(54,221,242, 0.1)'],
  ['rgba(25,216,149)', 'rgba(25,216,149, 0.5)', 'rgba(25,216,149, 0.1)'],
  ['rgba(255,194,153)', 'rgba(255,194,153, 0.5)', 'rgba(255,194,153, 0.1)'],
  ['rgba(153,75,44)', 'rgba(153,75,44, 0.5)', 'rgba(153,75,44, 0.1)'],
  ['rgba(246,202,104)', 'rgba(246,202,104, 0.5)', 'rgba(246,202,104, 0.1)'],
  ['rgba(255,187,187)', 'rgba(255,187,187, 0.5)', 'rgba(255,187,187, 0.1)'],
  ['rgba(255, 0, 0)', 'rgba(255, 0, 0, 0.5)', 'rgba(255, 0, 0, 0.1)'],
  ['rgba(0, 255, 0)', 'rgba(0, 255, 0, 0.5)', 'rgba(0, 255, 0, 0.1)'],
  ['rgba(255, 255, 0)', 'rgba(255, 255, 0, 0.5)', 'rgba(255, 255, 0, 0.1)'],
  ['rgba(255, 0, 255)', 'rgba(255, 0, 255, 0.5)', 'rgba(255, 0, 255, 0.1)'],
  ['rgba(0, 128, 0)', 'rgba(0, 128, 0, 0.5)', 'rgba(0, 128, 0, 0.1)'],
  ['rgba(0, 0, 128)', 'rgba(0, 0, 128, 0.5)', 'rgba(0, 0, 128, 0.1)'],
  ['rgba(128, 128, 0)', 'rgba(128, 128, 0, 0.5)', 'rgba(128, 128, 0, 0.1)'],
  ['rgba(128, 0, 128)', 'rgba(128, 0, 128, 0.5)', 'rgba(128, 0, 128, 0.1)'],
  ['rgba(0, 128, 128)', 'rgba(0, 128, 128, 0.5)', 'rgba(0, 128, 128, 0.1)'],
  ['rgba(192, 192, 192)', 'rgba(192, 192, 192, 0.5)', 'rgba(192, 192, 192, 0.1)'],
  ['rgba(255, 20, 147)', 'rgba(255, 20, 147, 0.5)', 'rgba(255, 20, 147, 0.1)'],
  ['rgba(75, 0, 130)', 'rgba(75, 0, 130, 0.5)', 'rgba(75, 0, 130, 0.1)'],
  ['rgba(0, 0, 0)', 'rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 0.1)'],
];

const _baseOption = () => {
  return {
    grid: {
      left: '0px',
      right: '3%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    textStyle: {
      color: '#BBBBBB',
    },
    legend: {
      show: false,
      orient: 'horizontal',
      bottom: '0%',
      type: 'scroll',
      icon: 'circle',
      itemWidth: 15,
      itemHeight: 10,
      textStyle: {
        color: '#BBBBBB',
      },
      pageIconColor: '#aaa',
      pageIconInactiveColor: '#2f4554',
      pageTextStyle: {
        color: '#FFFFFF',
      },
    },
    yAxis: {
      type: 'value',
      boundaryGap: true,
      nameTextStyle: {
        color: '#BBBBBB',
      },
      splitLine: {
        lineStyle: {
          color: '#BBBBBB',
          opacity: 0.1,
        },
      },
      axisLabel: {
        color: '#BBBBBB',
        fontSize: 10,
        margin: 5,
        hideOverlap: true,
        align: 'right',
        overflow: 'break',
        width: 200,
      },
    },
  };
};

const _getOption = ({startTime, endTime, data, chartDom}) => {
  return {
    tooltip: {
      trigger: 'axis',
      borderColor: '#393939',
      backgroundColor: '#393939',
      appendToBody: true,
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      textStyle: {
        color: '#f5f5f5',
      },
      enterable: true,
      position: (point, params, dom, rect, size) => {
        if (!chartDom) {
          return [0, 0];
        }
        const TOOLTIP_OFFSET = 4;
        // tip 优先不超过画布的右边界和下边界（point 是鼠标相对画布左、上边界的距离）
        const canvasRect = chartDom.getBoundingClientRect();
        const {contentSize: tooltipSize, viewSize: canvasSize} = size;
        let left = (point[0] + TOOLTIP_OFFSET + tooltipSize[0] > canvasSize[0]) ? point[0] - tooltipSize[0] - TOOLTIP_OFFSET : point[0] + TOOLTIP_OFFSET;
        let top = (point[1] + TOOLTIP_OFFSET + tooltipSize[1] > canvasSize[1]) ? point[1] - TOOLTIP_OFFSET - tooltipSize[1] : point[1] + TOOLTIP_OFFSET;
        if (canvasRect) {
          // 校正tooltip的 top 定位，防止超出可视窗口
          const toolTipExceedViewport = canvasRect.top > 0 && top < 0 && Math.abs(top) > canvasRect.top;
          const toolTipExceedCanvas = canvasRect.top < 0 && top < Math.abs(canvasRect.top);
          if (toolTipExceedViewport || toolTipExceedCanvas) {
            top = -canvasRect.top;
          }
        }
        return [left, top];
      },
      formatter: (params) => {
        // 获取时间（假设 x 轴为时间）
        const time = formatDate2SysSetting(params[0].axisValue); // X轴的值，即时间
        // 遍历每条线的数据，保留默认的值展示
        const values = params
          .map(item => `${item.marker} ${item.seriesName}: ${item.data.value[1]}`)
          .join('<br>');

        // 返回自定义的提示框内容
        return `<strong>${time}</strong><br>${values}`;
      },
    },
    ..._baseOption(),
    xAxis: [
      {
        type: 'time',
        boundaryGap: true,
        axisLine: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 3,
          },
        },

        axisTick: {
          lineStyle: {
            color: '#BBBBBB',
            opacity: 0.1,
            type: 'solid',
            width: 2,
          },
        },

        axisLabel: {
          color: '#BBBBBB',
          interval: 0,
          borderColor: 'transparent',
          borderWidth: 10,
          fontSize: 10,
          margin: 10,
          hideOverlap: true,
          showMinLabel: false,
          showMaxLabel: false,
          formatter: value => {
            const unit = (endTime - startTime) / 12;
            if (value > parseInt(startTime) && value < parseInt(endTime)) {
              if (Math.abs(value - startTime) < unit || Math.abs(value - endTime) < unit) {
                return '';
              }
              return dateFormatToHour(value);
            }
            return '';
          },
        },
        splitLine: {
          show: false,
        },
        min: parseInt(startTime),
        max: parseInt(endTime),
      },
    ],
    series: _getSeries(data),
  };
};

const _getSeries = (data) => {
  return data.map((v, i) => {
    return {
      lineStyle: {
        normal: {
          width: 1,
        },
      },
      symbol: 'none',
      legendHoverLink: true,
      name: v.name,
      id: v.id,
      data: v.data,
      type: 'line',
      smooth: true,
      itemStyle: {
        normal: {
          color: color[i % color.length][0],
        },
      },
      areaStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: color[i % color.length][1],
              },
              {
                offset: 1,
                color: color[i % color.length][2],
              },
            ],
            false,
          ),
        },
      },
    };
  });
};


/**
 *
 *
 * @param props
 * props.data
 * [{
 *   id: string;
 *   name: string;
 *   data: Array<[time, value]>;
 * }],
 */
const Line = (props) => {
  const {
    data = [],
    unit = '',
    style = {},
    onClick = (params, instance) => {
      // 默认空函数
    },
    time = null,
    reqLineParam = {},
  } = props;

  const timeRange = useMemo(() => {
    if (time) {
      return time;
    }
    const times = data.map(v => v.data).flatMap(v => v).map(v => parseInt(v?.value?.[0]));
    times.sort();
    if (times.length === 0) {
      return {
        startTime: 0,
        endTime: 0,
      };
    }

    return {
      startTime: times[0],
      endTime: times[times.length - 1],
    };
  }, [data, time]);

  const [selected, setSelected] = useState({});
  useEffect(() => {
    setSelected(data.reduce((curr, next) => {
      curr[next.id] = selected[next.id] === false ? false : true;
      return curr;
    }, {}));
  }, [data]);

  const legendData = useMemo(() => {
    return data.map(v => {
      return {
        id: v.id,
        name: v.name,
        selected: selected[v.id],
      };
    });
  }, [selected]);

  const containerRef = useRef(null);
  const echartInstanceRef = useRef(null);

  const option = useMemo(() => {
    return _getOption({
      ...timeRange,
      data,
      chartDom: containerRef.current,
    });
  }, [data, timeRange]);

  useEffect(()=>{
    const myChart = echarts.getInstanceByDom(containerRef.current);
    // 销毁图表实例
    if (myChart) {
      myChart.dispose();
    }
  }, [JSON.stringify(reqLineParam)]);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    let instance = echarts.getInstanceByDom(containerRef.current);
    try {
      instance?.getZr()?.off('click'); // 解绑事件
      if (!instance) {
        instance = echarts.init(containerRef.current);
        instance.setOption(option);
      } else {
        instance.setOption({
          ...option,
          // @ts-expect-error 这里不进行检查 保留原先的图例勾选状态
          legend: { ...option.legend, selected: instance.getOption().legend[0].selected },
        }, {
          notMerge: true, // 配置由chart维度，不需要echarts自己维护
          lazyUpdate : true, // 异步画图
        });
      }
      // 重新监听事件， 因为chart实例更新了
      instance.getZr().on('click', (params) => {
        onClick?.(params, instance);
      });
      echartInstanceRef.current = instance;
    } catch (e) {
      // horizon effect对于错误无法抛出，这里主动抛出
      const _log = console;
      _log.error(e);
    }
  }, [option]);

  useEffect(() => {
    const container = containerRef.current;
    return () => {
      if (!container) {
        return;
      }
      const instance = echarts.getInstanceByDom(container);
      if (instance) {
        instance.off('click');
        echarts.dispose(instance);
        echartInstanceRef.current = undefined;
      }
    };
  }, []);

  return (
    <div style={style}>
      <p
        className={styles.topoLineUnit}
      >
        {unit}
      </p>
      <div ref={containerRef} style={{width: '100%', height: '100%'}} />

      <div style={{padding: '0 30px'}}>
        <MoreLegend
          data={legendData}
          style={{marginTop: '-30px'}}
          iconStyle={{
            width: '10px',
            height: '10px',
            top: '5px',
          }}
          color={color.map(v => v[0])}
          onClick={(id) => {
            if (!echartInstanceRef.current) {
              return;
            }

            setSelected(prevSelected => {
              const oldSelected = { ...prevSelected };
              if (event.ctrlKey) {
                // 点击的时候按住ctl键，只改变当前图例的勾选状态
                oldSelected[id] = !oldSelected[id];
              } else {
                if (oldSelected[id]) {
                  // 点击的图例已勾选
                  if (Object.values(oldSelected).every(isSelect => isSelect)) {
                    // 当前为全选，单选该图例
                    for (const legendKey of Object.keys(oldSelected)) {
                      oldSelected[legendKey] = false;
                    }
                    oldSelected[id] = true;
                  } else {
                    // 当前为非全选，勾选全部图例
                    for (const legendKey of Object.keys(oldSelected)) {
                      oldSelected[legendKey] = true;
                    }
                  }
                } else {
                  // 点击的图例未勾选，单选该图例
                  for (const legendKey of Object.keys(oldSelected)) {
                    oldSelected[legendKey] = false;
                  }
                  oldSelected[id] = true;
                }
              }

              const selectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                return value;
              }).map(([key, value]) => {
                return {
                  name: legendData.find(v => v.id === key)?.name,
                };
              });
              const unSelectedNameList = Object.entries(oldSelected).filter(([key, value]) => {
                return !value;
              }).map(([key, value]) => {
                return {
                  name: legendData.find(v => v.id === key)?.name,
                };
              });
              echartInstanceRef.current.dispatchAction({
                type: 'legendSelect',
                batch: selectedNameList,
              });

              echartInstanceRef.current.dispatchAction({
                type: 'legendUnSelect',
                batch: unSelectedNameList,
              });
              return oldSelected;
            });
          }}
        />
      </div>
    </div>
  );
};

export default Line;
