/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import React from 'react';
import ReactDOM from 'react-dom';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import Home3D from '@pages/businesstopo/a3dPages/w3d/scenes/siteDrillDown';
import {eventBusHandler, getRouteQuery} from '@pages/businesstopo/a3dPages/components/utils';
import Pod from '@pages/businesstopo/components/tooltip/pod';
import ErrConnection from '@pages/businesstopo/components/tooltip/errConnection';
import {deepClone} from '@pages/businesstopo/util';
import DVTips from '@pages/businesstopo/components/tooltip/dvTips';
import SiteTips from '@pages/businesstopo/components/tooltip/siteTips';
import {$t} from '@util';
import {queryCurrentIds} from '../../api';

let home3D;
let siteId;
let siteInfoList;
let siteSolutionId;
let siteDispatch;
const moTypeMap = {};
const getDrGroupList = data => {
  const drGroupList = data.drGroupList.map(drGroup => {
    const newDrGroup = {...drGroup};
    newDrGroup.moTypeInfoList = drGroup.moTypeInfoList.filter(moType => moType.moTypeInstanceCount > 0);
    return newDrGroup;
  });
  return drGroupList;
};

const getRelationship = (data, drGroupList) => {
  const siteIds = data.siteInfoList.map(item => item.siteId);
  const relationship = {};
  drGroupList.forEach(drGroup => {
    drGroup.moTypeInfoList.forEach(moType => {
      relationship[moType.moTypeId] = {
        moTypeLinks: new Set(),
        siteLinks: [],
      };
    });
  });
  data.moTypeLinks.forEach(relation => {
    const id1 = relation.fromMoTypeId;
    const id2 = relation.toMoTypeId;
    if (relationship[id1] && relationship[id2]) {
      relationship[id1].moTypeLinks.add({id: id2, protocol: relation.protocol, realation: 'to'});
      relationship[id2].moTypeLinks.add({id: id1, protocol: relation.protocol, realation: 'from'});
    }
  });

  Object.keys(relationship).forEach(moTypeId => {
    relationship[moTypeId].moTypeLinks = [...relationship[moTypeId].moTypeLinks].map(obj => ({
      to: obj.id,
      protocol: obj.protocol,
      realation: obj.realation,
    }));
  });

  data.moSiteLinks.forEach(link => {
    const keys = [link.fromMoTypeId, link.toSiteId];
    for (const key of keys) {
      // key为非站点且未初始化
      if (!relationship[key] && !siteIds.includes(key)) {
        relationship[key] = {
          moTypeLinks: new Set(),
          siteLinks: [],
        };
      }
    }
    // 网元类型到站点
    if (!siteIds.includes(link.fromMoTypeId)) {
      relationship[link.fromMoTypeId].siteLinks.push({
        to: link.toSiteId,
        alarmCount: 0,
        protocol: link.protocol,
        realation: 'to',
      });
    } else {
      relationship[link.toSiteId].siteLinks.push({
        to: link.fromMoTypeId,
        alarmCount: 0,
        protocol: link.protocol,
        realation: 'from',
      });
    }
  });
  for (const key in relationship) {
    if (Object.prototype.hasOwnProperty.call(relationship, key)) {
      const {siteLinks} = relationship[key];
      if (siteLinks.length > 1) {
        const validLinks = siteLinks.filter(link => link.protocol);
        const protocols = [...new Set(validLinks.map(link => link.protocol))];
        siteLinks.forEach(link => {
          link.protocol = protocols.join('&');
        });
      }
    }
  }
  return relationship;
};

// 处理数据结构
let lastResult;
const getData = (value, environmentType) => {
  let data = deepClone(value);
  data.drGroupList.forEach(item => {
    item.moTypeInfoList.forEach(moType => {
      moTypeMap[moType.moTypeId] = moType.moTypeName;
    });
  });
  siteInfoList = data.siteInfoList;
  const siteInfo = data.siteInfoList.find(item => item.siteId === parseInt(siteId));
  if (siteInfo) {
    siteInfo.moTypeList = [];
  }

  const drGroupList = getDrGroupList(data);
  const relationship = getRelationship(data, drGroupList);
  let positionSetting = [];
  if (!data.autoLayoutsFlag) {
    positionSetting = transformedData(data.drGroupList);
  }
  const result = {
    id: data.siteId,
    name: data.siteName,
    autoPosition: data.autoLayoutsFlag,
    positionSetting,
    drGroupList,
    relationship,
    siteInfoList: data.siteInfoList.map(siteInfo => {
      const newSiteInfo = {...siteInfo};
      delete newSiteInfo.moTypeList;
      return newSiteInfo;
    }),
  };
  result.drGroupList.forEach(group => {
    group.moTypeInfoList.forEach(info => {
      info.environmentType = environmentType;
      if (Object.keys(info).includes('grayStatus')) {
        info.isSupportGray = info.grayStatus;
        delete info.grayStatus;
      }
    });
  });
  lastResult = result;
  return result;
};

export const findItemById = (arr, id) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = 0; j < arr[i].moTypeInfoList.length; j++) {
      if (arr[i].moTypeInfoList[j].moTypeId === id) {
        return arr[i].moTypeInfoList[j];
      }
    }
  }
  return null; // 如果没有找到匹配的子数组，则返回 null
};
const events = [
  {
    eventName: 'from3d_showPanel',
    // 2D-3D对接，点击事件，联动右侧面板
    fn(setting) {
      if (setting.id) {
        let {siteData} = JSON.parse(sessionStorage.getItem('topoSession'));
        let moTypeData = findItemById(siteData.data.drGroupList, setting.id);
        if (setting.isActiveDv === 0) {
          siteDispatch({
            sitePannelIsOpen: false,
            showSiteRightPanel: false,
          });
          return;
        }
        let dvDetailsInfoArr = setting.isActiveDv === 1 ? [
          {
            value: setting.isHealthy === 1 ? 'healthy' : 'unhealthy',
            title: $t('site.drill.status'),
          },
          {
            value: moTypeData.appName,
            title: $t('site.drill.rightPanel.name'),
          },
          {
            value: moTypeData.nodeIp ? moTypeData.nodeIp : 'NA',
            title: $t('site.drill.rightPanel.ipAddress'),
          },
          {
            value: moTypeData.nodeVersion,
            title: $t('site.drill.rightPanel.version'),
          },
        ] : [];

        siteDispatch({
          currentMoData: {
            siteId,
            id: setting.id,
            name: moTypeData.moTypeName,
            podCount: moTypeData.podCount,
            podErrorCount: moTypeData.podErrorCount,
            applicationType: moTypeData.applicationType,
          },
          sitePannelIsOpen: true,
          showSiteRightPanel: true,
          currentEvent: 'all',
          alarmShow: true,
          moIndicatorIndex: 0,
          dvDetailsInfoArr,
        });
      } else {
        siteDispatch({
          sitePannelIsOpen: false,
          showSiteRightPanel: false,
        });
      }
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showDbTip',
    fn(setting) {
      const isDvNode = setting.data.isActiveDv === 0 || setting.data.isActiveDv === 1;
      ReactDOM.render(
        isDvNode ? <DVTips data={setting.data} /> : <Pod data={setting.data} suppotGray={true} />,
        setting.dom,
      );
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showPiuDbTip',
    fn(setting, piuData) {
      const isDvNode = setting.data.isActiveDv === 0 || setting.data.isActiveDv === 1;
      const dom = piuData.alarmSetting[setting?.data?.moTypeId]?.msgDiv;
      if (dom) {
        ReactDOM.render(
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '0.5rem',
          }}
          >
            <div>
              {isDvNode ? <DVTips data={setting.data} /> : <Pod data={setting.data} suppotGray={true} />}
            </div>
            <div className='topoPiuErrorMotypeInfo'>
              {dom}
            </div>
          </div>,
          setting.dom,
        );
      }
    },
  },
  {
    // 2D-3D对接，hover业务 db/app ，显示卡片
    eventName: 'from3d_showSiteTip',
    fn(setting) {
      ReactDOM.render(
        <SiteTips data={setting.data} />,
        setting.dom,
      );
    },
  },
  {
    // 2D-3D对接，hover业务 dbLine ，显示卡片
    eventName: 'from3d_showDbLineTip',
    fn(setting) {
      let name = siteInfoList.find(item => item.siteId === setting.data.siteId).siteName;
      let data = {
        moName: moTypeMap[setting.data.moTypeId],
        display: true,
        params: {
          instanceId: setting.data.moTypeId,
          siteId: setting.data.siteId,
        },
        name,
      };
      ReactDOM.render(<ErrConnection errorData={data} />, setting.dom);
    },
  },
  {
    // 2D-3D对接，重置选中站点
    eventName: 'from3d_changeSite',
    async fn(setting) {
      this.changeSite(setting);
    },
  },
  {
    // 2D-3D对接，双击事件，下钻
    eventName: 'from3d_drillDownMoType',
    fn(setting) {
      if (setting.isActiveDv === 0 || setting.isActiveDv === 1) {
        return;
      }
      let {
        selectedTime,
      } = JSON.parse(sessionStorage.getItem('topoSession'));
      if (selectedTime === 0) {
        destroyW3D();
        location.replace(`/eviewwebsite/index.html#path=/businesstopo/mohome&siteId=${setting.siteId}&moTypeId=${setting.moTypeId}&siteName=${setting.siteName}&moTypeName=${setting.moTypeName}&solutionId=${siteSolutionId}&applicationType=${setting.applicationType}`);
        return;
      }
      let param = {
        instanceIdList: [],
        timestamp: selectedTime,
        targetTimestamp: 0,
      };
      param.instanceIdList.push(setting.siteId);
      param.instanceIdList.push(setting.moTypeId);
      queryCurrentIds(param, ({data}) => {
        if (!data || Object.entries(data).length === 0) {
          overviewDispatch({
            showDeleteInfo: true,
          });
          return;
        }
        destroyW3D();
        location.replace(`/eviewwebsite/index.html#path=/businesstopo/mohome&siteId=${data[setting.siteId]}&moTypeId=${data[setting.moTypeId]}&siteName=${setting.siteName}&moTypeName=${setting.moTypeName}&solutionId=${siteSolutionId}&applicationType=${setting.applicationType}`);
      });
    },
  },
];

export const initW3D = async(container, dispatch, id, solutionId, setting) => {
  siteId = id;
  siteSolutionId = solutionId;
  siteDispatch = dispatch;
  events.forEach(item => {
    item.fn = item.fn.bind(setting);
    eventBus.addListener(item.eventName, item.fn);
  });

  // 将数据转化成3D需要的数据
  const data = getData(setting.siteData, setting.environmentType);
  if (!home3D) {
    home3D = new Home3D(eventBusHandler);
    if (setting.isPiu) {
      home3D.setPiuEnv();
      home3D.setPiuData(setting.piuData);
      home3D.setPiuCameraSetting(setting.cameraSetting);
    }
  }
  const routeQuery = getRouteQuery();
  await home3D.init(container.current, routeQuery, {sitesDrillDown: data});
  if (setting.groupId) {
    eventBus.emit('to3d_focusGroup', setting.groupId);
  }
};

export const updateSiteData = data => {
  // 2D-3D对接，切换站点更新站点数据
  const siteData = getData(data);
  eventBus.emit('to3d_updateDrillDownStatus', siteData);
};

export const destroyW3D = () => {
  events.forEach(item => {
    eventBus.removeListener(item.eventName, item.fn);
  });

  home3D?.destroy();
  home3D = null;
};

export const resizeW3D = () => {
  if (home3D?.resize) {
    home3D.resize();
  }
};

const transformedData = data => data.map(item => ({
  groupKey: item.groupKey,
  drGroupName: item.drGroupName, // Replace with actual translation function
  xPos: item.position[0].xPos,
  zPos: item.position[0].zPos,
  xLen: item.position[0].xLen,
  zLen: item.position[0].zLen,
  row: item.position[0].row,
}));
