import React, { useContext, useEffect } from 'react';
import styles from '../index.less';
import { TaskManageContext, TASK_PAGE_TYPE } from '../const';
import IncidentTaskList from './IncidentTaskList';
import SelectKnowledgeBase from './SelectKnowledgeBase';
import ConfigureAlgorithm from './ConfigureAlgorithm';
import BasicInfo from './BasicInfo';
import TaskDetail from './taskDetail';
import { setHelpId } from '@util/index';

const TaskManagement = (props) => {
  const { setShowAccordion } = props;
  const [state, dispatch] = useContext(TaskManageContext);
  const { curTaskPage } = state;
  setHelpId('com.huawei.dvtopo.AIOpsIncident.task');

  useEffect(() => {
    if (curTaskPage === TASK_PAGE_TYPE.TASK_LIST) {
      setShowAccordion(true);
    }
  }, [curTaskPage]);

  return (
    <div className={styles.taskManagementContainer}>
      {curTaskPage === TASK_PAGE_TYPE.TASK_LIST && (
        <IncidentTaskList setShowAccordion={setShowAccordion} />
      )}
      {curTaskPage === TASK_PAGE_TYPE.SELECT_KNOWLEDGE_BASE && (
        <SelectKnowledgeBase />
      )}
      {curTaskPage === TASK_PAGE_TYPE.CONFIGURE_ALGORITHM && (
        <ConfigureAlgorithm />
      )}
      {curTaskPage === TASK_PAGE_TYPE.BASIC_INFO && (
        <BasicInfo />
      )}
      {curTaskPage === TASK_PAGE_TYPE.VIEW_INFO && (
        <TaskDetail />
      )}
    </div>
  );
};

export default TaskManagement;
