import React, { useContext, useState, useEffect } from 'react';
import { $t } from '@util';
import Tooltip from 'eview-ui/Tooltip';
import { BUSINESS_TOPO_CONTEXT } from '@pages/businesstopo/const';
import group from '../../../../apps/businesstopo/assets/edit/group.png';

const MiddleArea = ({ data, setPanelContent }) => {
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const { solutionName } = state;

  const [showName, setShowName] = useState(`${solutionName} ${$t('cbs.service')}`);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentData, setCurrentData] = useState({});
  const [areaStyle, setAreaStyle] = useState({ columns: 0, width: '' });

  useEffect(() => {
    if (data.length > 0) {
      const tempData = {
        siteId: data[currentIndex].siteId,
        siteName: data[currentIndex].siteName,
        groupList: data[currentIndex].groupList.map(item => ({ name: item.groupName, icon: group })),
      };
      setCurrentData(tempData);

      // 根据 items 的长度决定列数
      let columns = data[currentIndex].groupList.length > 6 ? 3 : 2;
      let width = data[currentIndex].groupList.length > 6 ? '170px' : 'auto';
      setAreaStyle({ columns, width });
    }
  }, [currentIndex, data]);

  const closeRightPanel = () => {
    dispatch({
      editRightPanelSiteName: false,
      editRightPannelOpen: false,
      editRightPannelShow: false,
    });
  };

  const onClickBeforePage = () => {
    if (currentIndex === 0) {
      return;
    }
    setCurrentIndex(currentIndex - 1);
    closeRightPanel();
  };

  const onClickNextPage = () => {
    if (currentIndex === data.length - 1) {
      return;
    }
    setCurrentIndex(currentIndex + 1);
    closeRightPanel();
  };

  // 点击site区域打开编辑siteName面板
  const onClickMiddleArea = () => {
    dispatch({
      editRightPanelSiteName: true,
      editRightPannelOpen: true,
      editRightPannelShow: true,
    });
    setPanelContent({ siteData: currentData });
  };

  return (
    <div className="middle-area-container">
      <div className="middle-area-text">
        <Tooltip placement="top" content={showName} trigger={['hover', 'focus']}>
          <div className="middle-area-text">{showName}</div>
        </Tooltip>
      </div>
      <div className="middle-area-wrapper">
        {currentIndex !== 0 && <div id="left-arrow-div" className="left-arrow-div" onClick={onClickBeforePage} />}
        <div id="middle-area-div" className="middle-area-div" onClick={onClickMiddleArea}>
          <div className="middle-area-title">{currentData?.siteName}</div>
          <div
            className="middle-area"
            style={{ display: 'grid', gap: '10px', gridTemplateColumns: `repeat(${areaStyle.columns}, 1fr)` }}
          >
            {currentData.groupList?.map((iconObj, index) => (
              <div key={index} style={{ height: '120px' }}>
                <div className="middle-area-icon">
                  <img src={iconObj.icon} draggable="false" alt="" />
                </div>
                <Tooltip placement="top" content={iconObj.name} trigger={['hover', 'focus']}>
                  <div className="icon-name" style={{ width: areaStyle.width }}>
                    {iconObj.name}
                  </div>
                </Tooltip>
              </div>
            ))}
          </div>
        </div>
        {currentIndex !== data.length - 1 && <div id="right-arrow-div" className="right-arrow-div" onClick={onClickNextPage} />}
      </div>
    </div>
  );
};

export default MiddleArea;
