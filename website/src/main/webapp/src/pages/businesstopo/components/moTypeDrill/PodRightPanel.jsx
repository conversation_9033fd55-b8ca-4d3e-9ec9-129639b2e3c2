/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext, useEffect } from 'react';
import { Tooltip } from 'eview-ui';
import '../../css/moTypeDrill.less';
import { BUSINESS_TOPO_CONTEXT } from '../../const';
import PodPanelChart from './PodPanelChart';
import AlarmEventPanel from '../AlarmEventPanel';
import { getAlarmEventData } from '../../api/moTypeDrill';
import errorIcon from '@images/error.png';
import PropTypes from 'prop-types';
import { HEALTH_STATUS, STATUS, STATUS_NEW_TEXT, STATUS_TEXT } from '@pages/businesstopo/const/moTypeDrill';
import {getUrlParam} from '../../util';

function PodRightPanel(props) {
  const { detailsInfoArr, podData, isOpenPanel, setIsOpenPanel } = props;
  const { state, dispatch } = useContext(BUSINESS_TOPO_CONTEXT);
  const {
    isTimeTrack,
    selectedTime,
  } = state;
  useEffect(() => {
    queryAlarmData();
  }, [podData.id, selectedTime, props.refresh]);

  // 查询告警/事件数据
  const queryAlarmData = () => {
    let params = {
      instanceId: podData.id,
    };
    if (isTimeTrack) {
      params.timestamp = selectedTime;
    }
    getAlarmEventData(
      params,
      res => {
        if (!res || res.resultCode !== 0) {
          return;
        }
        dispatch({ alarmEventData: res.data.alarmDataList });
      },
    );
  };

  const getColor = (index, value) => {
    if (value === STATUS.normal || value === HEALTH_STATUS.normal) {
      return '#00a874'; // red color
    } else if (value === STATUS.failed || value === HEALTH_STATUS.failed) {
      return '#F43146';
    } else {
      return '#f5f5f5';
    }
  };

  return (
    <>
      {isOpenPanel && (
        <div className="moType_right_panel">
          <div>
            <div className={getUrlParam('isMM') ? 'moType_right_panel_title_mm' : 'moType_right_panel_title'}>
              <Tooltip content={podData.name} id='title_tooltip' color='#393939'
                overlayStyle={{ color: '#FFF' }}
              >
                <div className="moType_right_panel_title_text">{`${podData.name}`}</div>
              </Tooltip>
            </div>
            <div style={{ marginTop: '32px' }}>
              <div className="pod-detail-right-panel-container">
                {detailsInfoArr.map((item, index) => (
                  <div style={{ paddingBottom: '16px', width: '100px' }}>
                    <Tooltip content={index === 0 ? item.content : item.value} placement='right' color='#393939'
                      overlayStyle={{ color: '#FFF', wordBreak: 'break-all' }}
                    >
                      <div
                        className="pod-detail-title"
                        style={{ color: getColor(index, item.value) }}
                      >
                        <span
                          style={{ marginRight: '5px' }}
                        > {(index === 0) ? STATUS_NEW_TEXT[item.value] : item.value}
                        </span>
                        {(index === 0 && item.value === STATUS.failed) && (
                          <img
                            style={{ width: '15px', height: '15px' }}
                            src={errorIcon}
                          />
                        )}
                      </div>
                    </Tooltip>
                    <div className="pod-detail-value">{item.title}</div>
                  </div>
                ))}
              </div>
            </div>
            <div style={{ marginTop: '16px', height: '344px' }}>
              <PodPanelChart podData={podData} refresh={props.refresh} />
            </div>
            <div id="moType_alarm_event" style={{ marginTop: '32px' }}>
              <AlarmEventPanel />
            </div>
          </div>
        </div>
      )}
      <div
        className={isOpenPanel ? 'moType_drill_right_close_div' : 'moType_drill_right_open_div'}
        onClick={() => setIsOpenPanel(!isOpenPanel)}
      >
        <div className={isOpenPanel ? 'moType_drill_right_close' : 'moType_drill_right_open'} />
      </div>
    </>
  );
}

PodRightPanel.propTypes = {
  detailsInfoArr: PropTypes.array,
  isOpenPanel: PropTypes.bool,
  setIsOpenPanel: PropTypes.func,
  podData: PropTypes.shape({
    name: PropTypes.string,
    id: PropTypes.number,
  }).isRequired,
};
export default PodRightPanel;
