/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, {useContext, useEffect, useState, useRef} from 'react';
import {BUSINESS_TOPO_CONTEXT} from '../const';
import {setSessionData, throttle} from '../util';
import Tooltip from 'eview-ui/Tooltip';
import {getSolutionData, saveConfiguration} from '../api';
import {OVER_VIEW} from '@pages/businesstopo/const/timeLine';


function Switch({nowSolutionData}) {
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const [show, setShow] = useState(false);
  const switchRef = useRef(null); // 用来引用模态框

  useEffect(() => {
    // 定义事件处理函数
    const handleClick = (event) => {
      if (switchRef.current && !switchRef.current.contains(event.target)) {
        // 如果点击不在模态框内，关闭模态框
        setShow(false);
      }
    };

    // 添加全局点击事件监听
    document.addEventListener('click', handleClick);

    // 清理函数：组件卸载时移除事件监听
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []); // 空依赖数组确保该效果只在组件挂载和卸载时执行
  const switchClick = () => {
    setShow(!show);
  };

  const handleClick = (index) => {
    saveConfiguration({
      solutionId: nowSolutionData[index].solutionId,
      homeType: OVER_VIEW,
    }, () => {
      let changeIndex = state.selectSolutionIndex + 1;
      setSessionData({
        selectedTime: 0,
        isTimeTrack: false,
      });
      dispatch({
        selectSolutionIndex: changeIndex,
        selectedTime: 0,
        isTimeTrack: false,
      });
    });
    setShow(false);
  };
  const throttleClick = throttle((index) => handleClick(index), 2000);

  return (
    nowSolutionData.length > 1 && (
      <div className="switch" ref={switchRef}>
        <div className="switch_icon" onClick={switchClick} />
        {show && (
          <div className="switch_content" style={{height: `${40 * nowSolutionData.length}px`}}>
            {nowSolutionData.map((child, index) => (
              <div
                key={index}
                className={index === 0 ? 'switch_content_item_selected' : 'switch_content_item'}
                onClick={() => throttleClick(index)}
              >
                <Tooltip content={child.solutionName} trigger={['hover', 'focus']}>
                  <span
                    style={{
                      display: 'inline-block',
                      maxWidth: '100px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {child.solutionName}
                  </span>
                </Tooltip>
              </div>
            ))}
          </div>
        )}
      </div>
    )
  );
}

export default Switch;
