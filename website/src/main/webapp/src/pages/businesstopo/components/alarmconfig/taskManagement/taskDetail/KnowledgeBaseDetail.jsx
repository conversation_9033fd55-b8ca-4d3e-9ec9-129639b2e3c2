import React, { useContext, useState, useEffect, useRef } from 'react';
import { Row, Col, Tooltip, Button, Select } from 'eview-ui';
import { $t, modal, validate } from '@util';
import {goToNewPage} from '@digitalview/fe-utils';
import { querySolution, queryKnowledgeList } from '../../api';
import styles from '../../index.less';
import LabelHint from '../../../common/LabelHint';
import { modifyTaskCommon } from '../NewOrEditCommonUtils/index';
import {
  TaskManageContext,
  OPERATE_SUCCESS_CODE,
  IS_CAN_MODIFY_TASK,
  GlobalContext,
} from '../../const';
import { cancelCreateOrEditTask } from '../NewOrEditCommonUtils';

const KnowledgeBaseDetail = (props) => {
  const [state, dispatch] = useContext(TaskManageContext);
  const { theme } = useContext(GlobalContext);
  const { solutionList, knowledgeBaseList, viewDetailInfo, selectedSolution, selectedKnowledgeBase } = state;
  const [modifyTask, setModifyTask] = useState(false);
  const solutionInputRef = useRef(null);
  const knowledgeBaseInputRef = useRef(null);  

  // 进去页面和切换状态时还原选中值，进入修改状态时查询解决方案列表和知识库列表
  useEffect(() => {
    dispatch({
      type: 'setSelectedSolution',
      selectedSolution: viewDetailInfo.solutionName,
    });
    dispatch({
      type: 'setSelectedKnowledgeBase',
      selectedKnowledgeBase: viewDetailInfo.solutionTypeForRecommendation,
    });
    if (!modifyTask) {
      return;
    }
    querySolution(res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error(
          $t('intelligent.incident.task.manage.solution.query.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
        return;
      }
      const solutionListOptions = res.data.map(ele => {
        return {
          text: ele.solutionName,
          value: ele.solutionName,
        };
      });
      dispatch({
        type: 'setSolutionList',
        solutionList: solutionListOptions,
      });
    }, () => {
      modal.error(
        $t('intelligent.incident.task.manage.solution.query.fail'),
        null,
        $t('intelligent.incident.task.manage.error.tip'),
        null,
        theme,
      );
    });
    queryKnowledgeList(2, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error(
          $t('intelligent.incident.task.manage.knowledge.query.fail'),
          null,
          $t('intelligent.incident.task.manage.error.tip'),
          null,
          theme,
        );
        return;
      }
      const knowledgeBaseListOptions = res.data.map((ele, index) => {
        return {
          text: ele,
          value: ele,
        };
      });
      dispatch({
        type: 'setKnowledgeBaseList',
        knowledgeBaseList: knowledgeBaseListOptions,
      });
    }, () => {
      modal.error(
        $t('intelligent.incident.task.manage.knowledge.query.fail'),
        null,
        $t('intelligent.incident.task.manage.error.tip'),
        null,
        theme,
      );
    });
  }, [modifyTask]);

  // 选择解决方案下拉框回调
  const onSelectSolution = (value) => {
    dispatch({
      type: 'setSelectedSolution',
      selectedSolution: value,
    });
  };

  // 选择知识库下拉框回调
  const onSelectKnowledgeBase = (value) => {
    dispatch({
      type: 'setSelectedKnowledgeBase',
      selectedKnowledgeBase: value,
    });
  };

  // 是否有修改任务权限
  const isCanModifyPermission = () => {
    return viewDetailInfo.canModify === IS_CAN_MODIFY_TASK.YES;
  };

  const importKnowledgeClick = () => {
    const pageUrl = `${location.origin}/eviewwebsite/index.html#path=/aiOpsService&subMenu=neKnowledge`;
    goToNewPage(pageUrl, true);  
  };

  const switchModifyTask = () => {
    setModifyTask(!modifyTask);
  };

  // 校验页面输入项
  const verifyData = () => {
    if (!solutionInputRef.current.validate()) {
      modal.error(
        $t('intelligent.incident.task.manage.solution.please.select'),
        null,
        $t('intelligent.incident.task.manage.tip'),
        null,
        theme,
      );
      return false;
    }
    return true;
  };

  const confirmModifyTask = () => {
    if (!verifyData()) {
      return;
    }
    const param = {
      ...viewDetailInfo,
      solutionName: selectedSolution,
      solutionTypeForRecommendation: selectedKnowledgeBase,
    };
    modifyTaskCommon(param, switchModifyTask, viewDetailInfo.taskId, dispatch, theme);
  };

  return (
    modifyTask ? (
      <>
        <div className={styles.detailContentContainer}>
          <table>
            <tbody>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.solution')}
                    require={true}
                    theme={theme}
                  />
                </td>
                <td>
                  <Select
                    selectStyle={{ width: '33.75rem' }}
                    options={solutionList}
                    value={selectedSolution}
                    onChange={onSelectSolution}
                    required
                    validator={(val, id) => validate(['required'], val, id)}   
                    ref={ele => solutionInputRef.current = ele}   
                  />
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t(
                      'intelligent.incident.task.manage.table.knowledge.base'
                    )}
                    theme={theme}
                    isHelp={true}
                    hint={$t('intelligent.incident.task.manage.knowledge.tip.select.appropriate')}
                  />
                </td>
                <td>
                  <div className={styles.knowledgeRow}>
                    <Select
                      selectStyle={{ width: '33.75rem' }} 
                      options={knowledgeBaseList}
                      value={selectedKnowledgeBase}
                      onChange={onSelectKnowledgeBase}
                      ref={ele => knowledgeBaseInputRef.current = ele}  
                    />
                    <div className={styles.importKnowledge} onClick={importKnowledgeClick}>
                      {$t(
                        'intelligent.incident.task.manage.import.knowledge.base'
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className={styles.detailBottomButtonRow}>
          <Button
            text={$t('alarm.edit.cancel')}
            style={{ float: 'left', marginRight: '1.25rem' }}
            onClick={switchModifyTask}
          />
          <Button
            text={$t('intelligent.incident.task.manage.task.detail.confirm')}
            status='primary'
            onClick={confirmModifyTask}
          />
        </div>      
      </>
      ) : (
      <Row className={styles.detailContentContainer}>
        <Col cols={20}>
          <table>
            <tbody>
              <tr>
                <td>
                  <LabelHint
                    label={$t('intelligent.incident.task.manage.solution')}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.solutionName}
                </td>
              </tr>
              <tr>
                <td>
                  <LabelHint
                    label={$t(
                      'intelligent.incident.task.manage.table.knowledge.base'
                    )}
                    theme={theme}
                  />
                </td>
                <td>
                  {viewDetailInfo.solutionTypeForRecommendation}
                </td>
              </tr>
            </tbody>
          </table>
        </Col>
        <Col cols={2}>
          {isCanModifyPermission() ?
            <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' 
              onClick={switchModifyTask}
            /> :    
            <Tooltip content={$t('intelligent.incident.task.manage.task.detail.no.support.modify')}>
                <Button text={$t('intelligent.incident.task.manage.table.operation.modify')} status='primary' disabled />
            </Tooltip>}
        </Col>
      </Row> )
  );
};

export default KnowledgeBaseDetail;
