@tooltipBgColor: #393939;
@tooltipFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@tooltipNormalFontColor:#2E94FF;
@tooltipGrayFontColor: #BBBBBB;

.connectionErrHeight{
  height: 315px;
}

#errConnection {
  display: inline-block;
  position: fixed;

  width: 425px;
  margin: 0 -40px;
  padding: 24px 24px 24px 24px;

  background: @tooltipBgColor;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.50);
  box-sizing: border-box;
  border-radius: 4px;

  backdrop-filter: blur(16px);

  font-family: HarmonyHeiTi;
  font-size: 16px;
  letter-spacing: 1px;
  color: @tooltipFontColor;
  line-height: 16px;
  cursor: pointer;

  .triangle {
    position: absolute;
    left: -10px;
    top: 22px;

    width: 0;
    height: 0;

    border-right: 10px solid @tooltipBgColor;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
  }

  .header {
    display: flex;
    align-items: center;
    position: relative;
    padding: 15px;

    .title {

    }

    .status {
      padding: 5px 8px;
      margin-left: 6px;

      border: 1px solid @tooltipErrFontColor;
      border-radius: 16px;

      font-size: 12px;
      color: @tooltipErrFontColor;
      text-align: center;
      line-height: 14px;
    }

    .errStatus {
      padding: 5px 8px;
      margin-left: 6px;

      border: 1px solid @tooltipErrFontColor;
      border-radius: 16px;

      font-size: 12px;
      color: @tooltipErrFontColor;
      text-align: center;
      line-height: 14px;
    }

    .normalStatus {
      padding: 5px 8px;
      margin-left: 6px;

      border: 1px solid @tooltipNormalFontColor;
      border-radius: 16px;

      font-size: 12px;
      color: @tooltipNormalFontColor;
      text-align: center;
      line-height: 14px;
    }

    .close {
      position: absolute;
      right: 0;
      display: inline-block;
      height: 16px;
      width: 16px;
      background-repeat: no-repeat;
    }
  }

  .chart {
    display: inline-block;
    margin: 12px 0 0 0;
    width: 100%;
    height: 202px;

    background: rgba(25,25,25,0.25);
    border-radius: 4px;
   /* 或者 overflow-x: scroll; */
  }
  .chartScroll{
    overflow-x: scroll;
  }

  .hiddenScroll {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .contentList {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 220px;
    overflow-y: scroll;
    padding-right: 50px; /**隐藏滚动条*/

    .content {
      margin-top: 12px;
      position: relative;
      border-bottom: 1px solid rgba(238,238,238,0.10);
      padding-bottom: 8px;
      width: 100%;

      .contentLeft {

        .contentTitle {
          font-size: 14px;
          color: @tooltipFontColor;
          margin-bottom: 8px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .contentText {
          display: flex;
          margin-bottom: 8px;

          font-size: 12px;
          color: @tooltipGrayFontColor;
        }

        .contentTime {
          display: flex;
          margin-bottom: 8px;

          font-size: 12px;
          color: @tooltipGrayFontColor;
        }
      }

      .contentRight {
        padding: 4px 15px;
        position: absolute;
        right: 0;
        top: 22px;

        border: 1px solid @tooltipErrFontColor;
        border-radius: 4px;
        box-sizing: border-box;

        font-size: 12px;
        color: @tooltipErrFontColor;
      }
    }
  }
}

#errConnection ::-webkit-scrollbar {
  width: 12px !important;
  height: 6px !important;
}
.chartScroll::-webkit-scrollbar {
  background: @tooltipBgColor;
}


.chartScroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 0.4rem;
  background-color: rgba(238, 238, 238, 0.1) !important;
}