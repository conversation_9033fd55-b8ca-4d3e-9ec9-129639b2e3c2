.moType_drill_container {
  width: 100%;
  height: calc(100vh - 9rem);
  position: relative;
}

#moType_drill_container>div>div>div:not(:last-child) {
  display: none;
}

.moType_back_btn {
  position: absolute;
  display: inline-block;
  top: 80px;
  left: 16px;
}

.moType_right_panel {
  top: 4rem;
  right: 0;
  position: absolute;
  z-index: 999;
  width: 400px;
  float: right;
  background: #272727;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(0px);
  box-sizing: border-box;
  height: calc(100vh - 4rem);
  overflow: hidden;
}
.moType_right_panel > div {
  width: 400px;
  padding: 24px 16px 0;
  box-sizing: border-box;
}
.moType_right_panel:hover {
  overflow-y: scroll;
}

.moType_right_panel_title {
  font-size: 20px;
  color: #f5f5f5;
}

.moType_right_panel_title_mm {
  font-size: 16px;
  color: rgb(201, 201, 201);
}

.moType_right_panel_title_text {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 340px;
}
#title_tooltip {
  .eui-tooltip-content-inner {
    border-color: #393939;
    background-color: #393939;
  }
}

.pod-detail-right-panel-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-row-gap: 9px;
  grid-column-gap: 32px;
  width: 368px;
}

.pod-detail-title {
  font-size: 14px;
  font-weight: 500;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pod-detail-value {
  font-size: 12px;
  color: #bbbbbb;
  margin-top: 2px;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.moType_tip_container {
  height: 84px;
  color: #f5f5f5;
  background-color: #393939;
  padding: 16px;
  box-sizing: border-box;
}

.moType_tip_hsm_container {
  height: 54px;
  color: #f5f5f5;
  background-color: #393939;
  padding: 16px;
  box-sizing: border-box;
}


.moType_tipTitle {
  font-size: 16px;
  line-height: 20px;
  display: inline-block;
}

.moType_status {
  height: 21px;
  font-size: 12px;
  color: #00a874;
  border: 1px solid #00a874;
  border-radius: 2px;
  display: inline-block;
  margin-left: 8px;
  text-align: center;
  line-height: 21px;
  padding: 0 2px;
}


.moType_abnormal_status {
  height: 21px;
  font-size: 12px;
  color: #e10f35;
  border: 1px solid #e10f35;
  border-radius: 2px;
  display: inline-block;
  margin-left: 8px;
  text-align: center;
  line-height: 21px;
  padding: 0 2px;
}

.moType_tipValue {
  font-size: 14px;
  line-height: 16px;
  margin-top: 16px;
}

.moType_topology_text_app {
  font-size: 14px;
  color: #f5f5f5;
  margin-bottom: 6rem;
}
.moType_topology_text_vm {
  font-size: 14px;
  color: #f5f5f5;
  margin-top: 6rem;
}
.moType_topology_text_title {
  font-size: 14px;
  color: #f5f5f5;
}
.moType_topology_text_podCount {
  font-size: 14px;
  color: #f5f5f5;
  display: inline-block;
  margin-top: 32px;
}
.moType_topology_text_podCountUnit {
  font-size: 14px;
  color: #676767;
  display: inline-block;
}
.moType_topology_text_value {
  font-size: 14px;
  color: #f5f5f5;
  margin-top: 24px;
}
.moType_topology_text_text {
  font-size: 12px;
  color: #676767;
  margin-top: 4px;
}

.moType_icon_div_close {
  display: inline-block;
  cursor: pointer;
  width: 16px;
  height: 16px;
  float: right;
  margin-top: 6px;
  background: url('../../../apps/businesstopo/assets/pannel_close.svg');
}

.podPanelChartContainer {
  position: relative;
  background: #272727;
  height: 100%;
  .eui_tab {
    background: #272727;
  }
  .eui_tab_content.active {
    height: 80%;
  }
  .eui_tab_bar {
    display: none;
  }
  .eui_tab_title {
    padding-left: 0;
    font-size: 16px;
    .active:focus {
      outline: none;
    }
  }
  .eui_tab_title.active {
    margin-bottom: -0.0625rem;
    height: 1.875rem;
    line-height: 1.875rem;
    color: #2e94ff;
    font-weight: 700;
    border-bottom: 0.125rem solid #2e94ff;
    flex-shrink: 0;
  }
  .eui_tab_title:hover {
    color: #2e94ff;
  }
  .eui_tab_title.active.lastActive {
    padding-right: 0;
  }

  .eui_tab_title.display {
    display: inline-block;
    max-width: 100px;
  }
}

.indicator_select_btn{
  .eui-btn-icon {
    height: 1.5rem;
    width: 1.2rem;
  }
}


.podPanelChartDiv {
  width: 100%;
  height: 88%;
  min-height: 156px;
  display: inline-block;
}

.icon_moType_back_btn {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/moTypeDrill/back.svg') no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: inline-block;
  top: 2px;
  cursor: pointer;
}

#moType_alarm_event {
  .alarm_event_panel {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }
  .eui_tab_title {
    margin-left: 0 !important;
    margin-right: 24px !important;
    line-height: 26px !important;
  }
  #alarmEventDiv {
    margin-top: 10px !important;
    padding-right: 0px !important;
    button {
      margin-left: 0 !important;
    }
  }
  .detail_panel {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

.moType_drill_right_close_div {
  position: fixed;
  width: 1rem;
  height: 5.5rem;
  top: calc(50% - 2.75rem);
  right: 398px;
  z-index: 999;
  opacity: 1;
}
.moType_drill_right_open_div {
  position: fixed;
  width: 1rem;
  height: 5.5rem;
  top: calc(50% - 2.75rem);
  right: 0px;
  z-index: 99999999;
  opacity: 1;
}
.moType_drill_right_close {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/close.svg")
  no-repeat;
  background-size: 100% 100%;
}

.moType_drill_right_open {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/open.svg")
  no-repeat;
  background-size: 100% 100%;
}


#hsmPanelChart{
  height: 300px;
}