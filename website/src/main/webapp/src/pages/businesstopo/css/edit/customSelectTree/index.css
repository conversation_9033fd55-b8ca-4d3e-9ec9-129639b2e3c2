/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */


#custom-select-tree {
  position: absolute;
  width: 80%;
  top:19%;
  left: 9%;
  background-color: #272727;
  padding: 20px;
  height: 460px;
  z-index: 9999;
  overflow: auto;
}
#custom-select-tree ::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: #393939;
}
.df-input-frame .df-icon-search{
  position: absolute;
  margin: 0px;
  top: 0.5rem;
  right: 0.44rem;
}
/*自带搜索按钮的输入框*/
.df-icon-search {
  cursor: pointer;
  background: url(../../../../../apps/businesstopo/assets/edit/search.png) no-repeat;
  background-size: cover;
  margin: 0.32rem 0.32rem 0 0;
  width: 1rem;
  height: 1rem;
  display: inline-block;
  vertical-align: middle;
  float: left;
}

/*任务管理*/
#filter_group li {
  float: left;
  margin-right: 10px;
}

.task-status {
  width: 100%;
  overflow: hidden;
  text-align: center;
  display: inline-block;
  padding: 4px 2px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-status.success {
  background: #5ecc49;
}

.task-status.wait {
  background: #bbb;
}

.task-status.failed {
  background: #fc5043;
}

.task-status.warn {
  background: #FFBB33;
}

.task-status.warnning {
  line-height: 20px;
}

.task-status.running {
  background: #4EAFF5;
}

#task_operate li {
  float: left;
  margin-right: 5px;
}

#task_operate li>div {
  cursor: pointer;
}

#viewTask .eui_label,
#newTask .eui_label {
  margin-right: 0px !important;
}

.icon-alarm {
  width: 1rem;
  height: 1rem;
  display: block;
  float: left;
  margin-top: 0.5rem;
  margin-right: 0.3125rem;
  background-size: cover;
}

/*改变开关颜色*/
#newTask .eui_toggle_container.toggled:hover .eui_toggle_track {
  background-color: rgb(24, 111, 194) !important;
}

#newTask .eui_toggle_container.toggled .eui_toggle_track {
  border-radius: 1.875rem;
  background-color: rgb(24, 111, 194) !important;
  margin-top: 0;
}

.task_zh .task-info>tbody>tr>td:first-child {
  width: 190px;
}

.task_en .task-info>tbody>tr>td:first-child,
.task_en .taskInfo>tbody>tr>td:first-child,
.task_en .task-view>tbody>tr>td:first-child {
  width: 260px;
}

.task_en .task-basicInfo>tbody>tr>td:first-child {
  width: 266px !important;
}

.task_en .algorithm-info>tbody>tr>td:first-child {
  width: 335px !important;
}

.kpi-dialog .task-info>tbody>tr>td:first-child {
  width: 175px;
}

.associated_en .kpi-dialog .task-info>tbody>tr>td:first-child,
.associated_en #kpiNodeInfo>tbody>tr>td:first-child {
  width: 260px;
}

.table_label {
  color: #999999;
}

.task-info>tbody>tr>td,
.taskInfo>tbody>tr>td {
  padding-bottom: 24px;
  vertical-align: top;
}

.task-view,
.task-info {
  table-layout: fixed;
  overflow: hidden;
}

.taskInfo>tbody>tr>td:first-child,
.task-view>tbody>tr>td:first-child {
  width: 190px;
  padding-left: 18px;
}

.task-view>tbody>tr>td {
  padding-bottom: 24px;
  vertical-align: top;
}

#source_measure {
  overflow: hidden;
}

#source_measure>li {
  float: left;
  width: 30%;
}

.measure_border {
  border: 1px solid #e6e6e6;
  height: auto;
  height: 21.875rem;
  overflow-y: auto;
  margin-top: 5px;
}

/*自定义table样式*/
.table_hed_separator {
  display: inline-block;
  height: 1rem;
  width: 1px;
  background: #e6e6e6;
  position: absolute;
  top: .5rem;
  left: -0.5rem;
}

.loading {
  margin-top: 10px;
  color: #F5F5F5;
}

.loading>span {
  width: 15px;
  height: 15px;
  background: url(../../../../../apps/businesstopo/assets/edit/in.gif) 0px 0px no-repeat;
  margin: 5px 5px 0px 5px;
  display: block;
  float: left;
}

.disableClick {
  pointer-events: none;
  color: #999 !important;
}

#taskType .eui_sltCard_button:nth-child(3) {
  pointer-events: none;
  color: #999 !important;
}

.task_en #taskType .eui_sltCard_button:first-child {
  width: 210px !important;
}

.task_en #taskType .eui_sltCard_button:nth-child(3),
.task_en #taskType .eui_sltCard_button:nth-child(4) {
  width: 180px !important;
}

.task_en #taskType .eui_sltCard_button:nth-child(5),
.task_en #taskType .eui_sltCard_button:nth-child(2) {
  width: 96px !important;
}

.task_en #taskType .eui_sltCard_button:last-child {
  width: 126px !important;
}

#customKpi .eui_table_paging>div,
#measureTypePageTable .eui_table_paging>div {
  display: none !important;
}

#alarmRule table.eui_table_tb>tbody>tr {
  height: 2rem !important;
}

#alarmPageTable .eui_table_paging>div,
#alarmPageTable .eui_table_paging>div {
  display: none !important;
}

#sourceType .eui_label {
  color: #000 !important;
}

/*映射关系表格*/
.mappingTable {
  width: 80%;
  float: left;
}

table.mappingTable>thead>tr {
  box-sizing: border-box;
  background-color: #fff;
  font-size: .875rem;
  color: #212121;
  height: 2rem;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
}

table.mappingTable>tbody>tr:nth-child(odd) {
  background-color: #fafafa;
}

table.mappingTable>tbody>tr:first-child {
  border-top: 1px solid #e6e6e6;
}

table.mappingTable>tbody>tr {
  border-bottom: 1px solid #e6e6e6;
  box-sizing: border-box;
  height: 3rem;
}

table.mappingTable>tbody>tr>td {
  vertical-align: middle;
  color: #212121;
  padding: 0 5px;
}

table.mappingTable>thead>tr>th {
  padding: 0 1.25rem 0 0.5rem;
  white-space: nowrap;
  font-weight: 400;
  box-sizing: border-box;
  position: relative;
  outline: none;
}

table.mappingTable .eui_table_hed_separator {
  display: inline-block;
  height: 1rem;
  width: 1px;
  background: #e6e6e6;
  position: absolute;
  top: .5rem;
  left: 0;
}

.requireSelect .eui-select-require-tip {
  z-index: 1
}

/*多选框去除搜索框*/
.multi-search-input-wrapper {
  display: none;
}

/*处理IE有滚动条问题*/
.eui_tab_body {
  overflow: hidden !important;
}

.eui_search_container {
  height: 2rem;
}

/*超出指定宽度换行：任务描述、定位信息*/
.overflow-wrap {
  overflow-wrap: break-word;
  display: block;
  width: 940px;
}

/*隐藏告警关联规则导入失败的刷新按钮*/
#correlationRuleUpload .euiWin-refreshsvg {
  display: none;
}

.eui_MessageDialog_Panel_Info {
  white-space: pre-line;
}

.input_error {
  outline: 1px solid #e41f2b;
  outline-offset: -1px;
}


.thumbnail {
  background-size: contain;
  height: 19.625rem;
}

.viewThumbnail {
  background-size: contain;
  height: 23.75rem;
  width: 42.5rem;
}

#kpiNodeInfo .table_label {
  color: #000;
  padding-left: 16px;
}

#kpiNodeInfo>tbody>tr>td {
  padding-bottom: 24px;
  vertical-align: top;
}

#kpiNodeInfo>tbody>tr>td:first-child {
  width: 175px;
}

.my-icon-class {
  width: 1.5rem;
  height: 1.5rem;
}

.taskType-widget {
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-width: 1px;
  border-color: #cccbcb;
  border-style: solid;
  width: 34rem;
  margin: 10px 2% 20px 0px;
  cursor: pointer;
}

.taskType-widget-selected {
  filter: progid:DXImageTransform.Microsoft.Shadow(color='rgba(30, 117, 199, 1)', Direction=135, Strength=5);
  /*for ie6,7,8*/
  -moz-box-shadow: 0px 0px 10px rgba(30, 117, 199, 1);
  /*firefox*/
  -webkit-box-shadow: 0px 0px 10px rgba(30, 117, 199, 1);
  /*webkit*/
  box-shadow: 0px 0px 10px rgba(30, 117, 199, 1);
  /*opera或ie9*/
  box-sizing: border-box;
  border-width: 1px;
  border-color: rgb(242, 242, 242);
  border-style: solid;
}

.taskType-widget-disabled {
  cursor: not-allowed;
}

.type-title {
  display: block;
  text-align: center;
  width: 100%;
  padding: 1rem 0 1.875rem 0;
  font-size: 1.125rem;
}

#risk-disabled .eui-btn-icon,
#custom-disabled .eui-btn-icon {
  width: 1.5rem;
  height: 1.5rem;
}

/*pql表达式表格内容超长换行*/
#pqlTable tbody>tr>td.eui_table_cell.eui_table_ellipsis {
  word-wrap: break-word;
  word-break: normal;
  white-space: unset;
  padding: 10px 5px;
}

/*导入状态*/
.task-status.import {
  max-width: 200px;
}

.import-status.wait>span {
  background: #dadada;
}

.import-status.success>span {
  background: #5ecc49;
}

.import-status.failed>span {
  background: #fc5043;
}

.import-status.running>span {
  width: 16px;
  height: 16px;
  background: url(../../../../../apps/businesstopo/assets/edit/in.gif) 0px 0px no-repeat;
}

.import-status>span {
  float: left;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 6px 8px 0px 0px;
}

#taskUpload .euiWin-refreshsvg {
  display: none;
}

.euiGlobal_loading_content_cls {
  top: 45%;
}

.pql-result {
  float: left;
  width: 230px;
  margin-left: 8px;
}

.eui_textArea_area {
  padding: 0.125rem 1rem !important;
}

#DoubleSelect .eui_searchInput {
  width: 100% !important;
}

#taskUpload .euiReact-scrollbar-default {
  min-height: 2rem !important;
  max-height: 4rem !important;
}

#taskUpload {
  max-height: unset !important;
}

#alarmIdList {
  border: 1px solid #e6e6e6;
  width: 650px;
  height: 480px;
  overflow: auto;
}

.alarm-id-list {
  letter-spacing: 8px;
}

#alarmIdList li {
  padding: .125rem 1rem;
  cursor: pointer;
}

#alarmIdList li:hover {
  background: #efefef;
}

#alarmIdPaging .eui_selectCom.select_in_paging,
#measureObjTable .eui_selectCom.select_in_paging {
  margin-right: 0 !important;
}

#alarmIdPaging .eui_selectCom.select_in_paging .eui_select_div .eui_select,
#measureObjTable .eui_selectCom.select_in_paging .eui_select_div .eui_select {
  width: 6rem !important;
  padding: 0 0.3rem !important;
}

#alarmIdPaging .eui_paging_input,
#measureObjTable .eui_paging_input {
  margin-left: 0 !important;
}

#alarmIdPaging .eui_paging_page,
#measureObjTable .eui_paging_page {
  margin: 0.125rem 0.1rem !important;
}

#alarmIdPaging .eui_paging_count,
#measureObjTable .eui_paging_count {
  padding-right: 0 !important;
}

#alarmIdPaging .eui_paging_arrow_boarder,
#measureObjTable .eui_paging_arrow_boarder {
  margin: 0 0.2rem !important;
}

#alarmIdPaging .eui_popup_option,
#measureObjTable .eui_popup_option {
  padding-left: 0.3rem !important;
  padding-right: 0.3rem !important;
}

#measureObjTable .eui_table_paging {
  margin-top: 0 !important;
  height: 4rem;
}

/*资源选择成员/辅助指标展示列/潮汐调度预测任务下拉框样式*/
#memberSelect .eui_select_div,
.predictTask .eui_select_div,
.showCol .eui_select_div {
  width: 100%;
}

.solutionInputActive,
.solutionInputActive:hover {
  outline: 1px solid #186fc2;
  outline-offset: -1px;
}

.capacityButtonGroupCls {
  text-align: center;
  position: absolute;
  bottom: 15px;
  width: 97%;
  background: #fff;
  padding: 10px 0;
}

.featureTypeNodeCls {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.featureTypeKpiNodeCls {
  max-width: 500px;
  margin-left: 10px;
  line-height: 1.7rem;
}

#measurePageTableView {
  width: 99.5% !important;
}

/*双机分组列表表格行最后一个t样式*/
#clusterGroupTable tbody>tr>td:last-child {
  padding-right: 5px !important;
}

/*设置竖向滚动条*/
#clusterGroupTable .eui_table_content {
  max-height: 19.875rem;
  /*overflow-y: auto;*/
}

.hidePopUpTipCls {
  display: none !important;
}

.alarmNoDataHintCls{
  margin-top: 30%;
}

#taskList-table .eui_table_div table.eui_table_tb>tbody>tr>td.eui_table_cell:last-of-type{
    padding-right: 0 !important;
}
