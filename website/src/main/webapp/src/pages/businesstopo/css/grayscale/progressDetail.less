@tooltipBgColor: #393939;
@tooltipFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@tooltipGrayFontColor: #BBBBBB;
@tooltipSuccessFontColor: #09AA71;
@tooltipStatBorderColor: #676767;


#progressDetail {
  display: inline-block;
  width: 660px;
  height: 295px;
  padding: 20px 24px 24px 24px;

  position: absolute;
  top: 0;
  right: 0;
  opacity: 0;

  transform: translateX(calc(100% - 88px));
  transition: all 0.2s ease-in-out;

  letter-spacing: 1px;
  box-sizing: border-box;

  background: @tooltipBgColor;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  backdrop-filter: blur(4px);

  .close_icon {
    width: 22px;
    height: 22px;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+6Z2i5p2/5YWz6ZetaWNvbjwvdGl0bGU+CiAgICA8ZyBpZD0i6aG16Z2iLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSLliIflm74y77yI5oyB57ut5pu05paw77yJIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMTMuMDAwMDAwLCAtNDQ5LjAwMDAwMCkiIGZpbGw9IiNCQkJCQkIiPgogICAgICAgICAgICA8ZyBpZD0ieuWGhemDqC9pYy9jbG9zZS0yIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMy4wMDAwMDAsIDQ0OS4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zLjQ1NTU4MjYyLDIuNjk2Njk5MTQgTDcuODc0NjQwODgsNy4xMTU2NDA4OCBMMTIuMjk0NDE3NCwyLjY5NjY5OTE0IEMxMi41MTEzNzUzLDIuNDc5NzQxMiAxMi44NDgxNTU4LDIuNDU1NjM0NzYgMTMuMDkxNzQxLDIuNjI0Mzc5ODMgTDEzLjE3ODMwMDksMi42OTY2OTkxNCBDMTMuMzk1MjU4OCwyLjkxMzY1NzA4IDEzLjQxOTM2NTIsMy4yNTA0Mzc1OCAxMy4yNTA2MjAyLDMuNDk0MDIyNzYgTDEzLjE3ODMwMDksMy41ODA1ODI2MiBMOC43NTc2NDA4OCw3Ljk5OTY0MDg4IEwxMy4xNzgzMDA5LDEyLjQxOTQxNzQgQzEzLjQyMjM3ODUsMTIuNjYzNDk1MSAxMy40MjIzNzg1LDEzLjA1OTIyMzIgMTMuMTc4MzAwOSwxMy4zMDMzMDA5IEMxMi45MzQyMjMyLDEzLjU0NzM3ODUgMTIuNTM4NDk1MSwxMy41NDczNzg1IDEyLjI5NDQxNzQsMTMuMzAzMzAwOSBMNy44NzQ2NDA4OCw4Ljg4MjY0MDg4IEwzLjQ1NTU4MjYyLDEzLjMwMzMwMDkgQzMuMjM4NjI0NjgsMTMuNTIwMjU4OCAyLjkwMTg0NDE4LDEzLjU0NDM2NTIgMi42NTgyNTksMTMuMzc1NjIwMiBMMi41NzE2OTkxNCwxMy4zMDMzMDA5IEMyLjM1NDc0MTIsMTMuMDg2MzQyOSAyLjMzMDYzNDc2LDEyLjc0OTU2MjQgMi40OTkzNzk4MywxMi41MDU5NzcyIEwyLjU3MTY5OTE0LDEyLjQxOTQxNzQgTDYuOTkwNjQwODgsNy45OTk2NDA4OCBMMi41NzE2OTkxNCwzLjU4MDU4MjYyIEMyLjMyNzYyMTQ2LDMuMzM2NTA0OTQgMi4zMjc2MjE0NiwyLjk0MDc3NjgyIDIuNTcxNjk5MTQsMi42OTY2OTkxNCBDMi44MTU3NzY4MiwyLjQ1MjYyMTQ2IDMuMjExNTA0OTQsMi40NTI2MjE0NiAzLjQ1NTU4MjYyLDIuNjk2Njk5MTQgWiIgaWQ9Iui3r+W+hCI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) no-repeat;
    background-size: 100% 100%;
    vertical-align: bottom;
    display: inline-block;
    float: right;
    cursor: pointer;
    margin-right: 55px;
  }

  .title {
    font-size: 16px;
    color: @tooltipFontColor;
    text-align: center;
  }

  .detailTitle {
    margin-top: 12px;
    font-size: 14px;
    color: @tooltipFontColor;
  }

  .detailStep {
    padding-top: 16px;
    display: flex;
    justify-content: space-between;

    font-size: 12px;
    color: @tooltipGrayFontColor;
    text-align: center;

    .successLine {
      position: relative;

      &:before {
        display: inline-block;
        content: " ";
        width: 72px;
        height: 1px;
        position: absolute;
        top: 20px;
        margin-left: -72px;
        background-image: linear-gradient(270deg, rgba(92, 172, 255, 0.00) 1%, rgba(92, 172, 255, 0.60) 19%, rgba(92, 172, 255, 0.60) 83%, rgba(92, 172, 255, 0.00) 100%);
        border-radius: 2px;
      }

    }

    .waitLine {
      position: relative;
      font-size: 12px;
      color: rgba(238, 238, 238, 0.30);
      text-align: center;

      &:before {
        display: inline-block;
        content: " ";
        width: 72px;
        height: 1px;
        position: absolute;
        top: 20px;
        margin-left: -72px;
        background-image: linear-gradient(270deg, rgba(238, 238, 238, 0.00) 1%, rgba(238, 238, 238, 0.10) 19%, rgba(238, 238, 238, 0.10) 81%, rgba(238, 238, 238, 0.00) 99%);
        border-radius: 2px;
      }

    }

    .stepImg {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-repeat: no-repeat;
      background-size: cover;
    }

    span {
      width: 56px;
      display: block;
    }
  }

  .user {
    display: flex;
    position: relative;
    width: 100%;
    margin-top: 34px;

    .userDesc {
      font-size: 14px;
      color: @tooltipFontColor;
      padding-top: 2px;
    }

    .userNum {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 20px;
      color: @tooltipFontColor;

      #progressDetailUserUnit {
        font-size: 12px;
        color: @tooltipGrayFontColor;
        margin-left: 4px;

      }
    }
  }

  .progress {
    padding-top: 8px;
    display: flex;
    width: 100%;
    gap: 3px;

    .progressFirst {
      flex: 0;
      height: 6px;
      border-radius: 4px;
      background: #745EEF;

    }

    .progressSecond {
      flex: 100;
      height: 6px;
      background: rgba(238, 238, 238, 0.30);
      border-radius: 4px;
    }
  }

  .footer {
    margin-top: 12px;
    display: flex;
    position: relative;
    user-select: none;

    .download {
      &:active:hover {
        transform: scale(1.03, 0.97);
      }

      &:hover {
        cursor: pointer;
      }

      font-size: 12px;
      color: #5CACFF;
      display: flex;
      gap: 6px;

      span {
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }

    .downloadGary {
      &:hover {
        cursor: not-allowed;
      }

      font-size: 12px;
      color: @tooltipGrayFontColor;
      display: flex;
      gap: 6px;

      span {
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }

    .area {
      display: flex;
      position: absolute;
      top: 0;
      right: 0;
      gap: 24px;

      font-size: 12px;
      color: @tooltipGrayFontColor;

      .produce {
        &:before {
          content: " ";
          width: 10px;
          height: 10px;
          background: #745EEF;
          border-radius: 50%;
          display: inline-block;
          margin-right: 8px;
        }


      }

      .grayscale {
        &:before {
          content: " ";
          width: 10px;
          height: 10px;
          background: rgba(238, 238, 238, 0.30);
          border-radius: 50%;
          display: inline-block;
          margin-right: 8px;
        }
      }
    }
  }
}