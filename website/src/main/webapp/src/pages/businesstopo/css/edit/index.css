html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
}

.topocontainer {
    display: flex;
    overflow: auto;
    position: relative;
    height: calc(100vh);
}

.child1 {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    background-color: #272727;
}
.child1 .eui_textfield_input_wrapper{
    position: static !important;
}

.text-area {
    padding-left: 16px;
    text-align: start;
    flex: 0 0 2%;
    height: 18px;
    font-size:20px;
    color:#F5F5F5;
}
.custom-input {
    border-radius : 4px ;
    background : #EEEEEE0D ;
    border : 1px solid #676767 ;
    padding: 5px 10px;
    width: 368px;
    height: 30px;
    line-height: 20px;
    border-radius: 4px;
    box-sizing: border-box;
    outline: none;
    color: white;
    margin-left: 20px;
    margin-top: 10px;
}

.custom-input::placeholder {
    color: gray; /* 设置占位符文字颜色为灰色 */
}

.custom-input:focus {
    box-shadow: none; /* 移除焦点时的阴影效果 */
}
.separator {
    height: 1px;
    background-color: #353333;
    margin: 10px 16px;
    padding: 1px;
}
.left_separator{
    height: 1px;
    background-color: #353333;
    margin: 40px 16px 10px 16px;
    padding: 1px;
}
.cardList{
    overflow: auto;
    height: 38vh;
}
.right-pannel-text-area{
    font-size : 16px ;
    color : #F5F5F5 ;
    fontFamily : 鸿蒙黑体 ;
    margin-left: 30px;
}

.cardList::-webkit-scrollbar{
    width: 0px; /* 滚动条宽度 */
    background-color: #272727; /* 滚动条背景颜色 */
}
.add_icon{
    display: inline-block; /* 使得span元素可以设置宽高 */
    width: 20px; /* 设置宽度 */
    height: 20px; /* 设置高度 */
    background-image: url('../../../../apps/businesstopo/assets/edit/add.png') ; /* 设置背景图像的URL */
    background-size: contain; /* 控制背景图像大小 */
    position: absolute;
    right: 20px;
    /* 如果需要设置背景图像的位置，可以使用 background-position 属性 */
    /* 如果需要重复背景图像，可以使用 background-repeat 属性 */
}

.icon-area-container {
    flex: 0 0 98%;
    margin-top: 5px; /* 负的顶部外边距，减少第一行和父容器之间的间距 */
}
.icon-area{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap; /* 允许换行 */
    align-items: flex-start; /* 垂直方向上对齐到起始位置 */
    padding-left: 16px;
}
.icon-area .icon {
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    font-size: 2rem; /* 根据需要设置图标大小 */
    height: 92px; /* 调整高度以保持一致性 */
    width: 109px;
    margin-bottom: 16px; /* 图标之间的垂直间距 */
    background-color: #313131;
    border-radius: 2px;
    border: 1px dashed #3D3D3D;
    position: relative;
}
.icons {
    display: flex;
    flex-direction: column;
}

.child2 {
    flex: 0 0 80%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    overflow: hidden;
    background-color: #1F1F1F;
}
.child2-content{
    width: 80%;
    position: relative;
    transition: transform 1s ease;
}

.droppable-area-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-left: 55px; /* 确保与 middle-area-container 对齐 */
}

.droppable-area-container .area-text {
    font-size: 16px;
    color: #e0e0e0;
    font-family: 'HarmonyOS Sans', sans-serif;
    font-weight: bold;
    margin-right: 10px;
    width: 164px;
}

.droppable-area {
    height: 120px;
    width: 63%; /* 设置宽度为70% */
    margin: 30px 5%; /* 设置上下10px，左右20% */
    border-radius: 36px;
    border: 2px solid #4B4B4B;
    display: flex;
    align-items: center;
    justify-content: center; /* 居中对齐 */
    transition: background-color 0.3s ease;
    flex-wrap: wrap;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
}

.droppable-area .icon {
    margin: 5px;
}

.droppable-area.highlight {
    background-color: rgba(46, 148, 255, 0.1);
    border-color: #2E94FF;
}

.confirm-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border: 1px solid gray;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.confirm-dialog p {
    margin: 0 0 10px;
}

.confirm-dialog button {
    margin: 5px;
}

.middle-area-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    padding-left: 55px; /* 确保与 droppable-area-container 对齐 */
}

.middle-area-text {
    font-weight: bold;
    margin-right: 10px;
    color: #e0e0e0;
    width: 180px;
    white-space: nowrap;        /* 防止文本换行 */
    overflow: hidden;           /* 隐藏溢出的内容 */
    text-overflow: ellipsis;    /* 使用省略号表示溢出的文本 */
}

.middle-area-wrapper {
    position: relative;
    width: 50%; /* 设置宽度为70% */
    margin: 10px 10%; /* 设置上下10px，左右20% */
    display: flex;
    flex-direction: column; /* 纵向排列 */
    align-items: center; /* 居中对齐 */
}

.middle-area-div {
  padding: 10px 10px 20px;
  border: 2px solid #4B4B4B;
  border-radius: 36px;
}

.middle-area-title {
  height: 24px;
  margin-left: 16px;
  color:#f5f5f5;
}

.middle-area {
  height: 400px;
  justify-items: center;  /* 水平居中对齐网格项 */
  align-items: center;    /* 垂直居中对齐网格项 */
  /* 使网格容器本身居中（可选，取决于外部容器的设置） */
  justify-content: center; /* 水平居中对齐网格容器 */
  align-content: center;   /* 垂直居中对齐网格容器 */
}

.middle-area-icon{
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    font-size: 2rem; /* 根据需要设置图标大小 */
    margin-bottom: 5px; /* 图标之间的垂直间距，减小这个值 */
    border-radius: 2px;

}

.left-arrow-div {
  position: absolute;
  width: 32px;
  height: 32px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  background: url('../../../../apps/businesstopo/assets/timeLine/page_left.svg') no-repeat;
  left: 10px;  /* 左边箭头 */
}
.left-arrow-div:hover {
  background: url('../../../../apps/businesstopo/assets/timeLine/page_left_hover.svg') no-repeat;
}
.left-arrow-div:active {
  background: url('../../../../apps/businesstopo/assets/timeLine/page_left_click.svg') no-repeat;
}

.right-arrow-div {
  position: absolute;
  width: 32px;
  height: 32px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  background: url('../../../../apps/businesstopo/assets/timeLine/page_right.svg') no-repeat;
  right: 10px;  /* 右边箭头 */
}
.right-arrow-div:hover {
  background: url('../../../../apps/businesstopo/assets/timeLine/page_right_hover.svg') no-repeat;
}
.right-arrow-div:active {
  background: url('../../../../apps/businesstopo/assets/timeLine/page_right_click.svg') no-repeat;
}

.icon-name{
    text-align: center;
    font-size : 14px ;
    color : #FFFFFF ;
    white-space: nowrap;        /* 防止文本换行 */
    overflow: hidden;           /* 隐藏溢出的内容 */
    text-overflow: ellipsis;    /* 使用省略号表示溢出的文本 */
    height: 20px;
}
.business-icon{
    height: 70px; /* 调整高度以保持一致性 */
    width: 70px;
    margin-bottom: 40px;
}

.delete-icon{
    cursor: pointer;
    width: 16px;
    height: 16px;
    margin-top: 6px;
    background: url('../../../../apps/businesstopo/assets/pannel_close.svg');
    position: absolute;
    top:-4px;
    right:0px;
    display: none;
}

.delete-icon.cbsselected {
    display: block;
}


.business-name{
    color: #918f8f;
    display: flex;
    justify-content: center;
    margin-top: 5px;
    width: 75px;
    overflow-x: hidden;
    font-size: 12px;
}

.business-icon img{
    height: 70px; /* 调整高度以保持一致性 */
    width: 70px;
    position: relative;
}
.icon-area .icon {
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    font-size: 2rem; /* 根据需要设置图标大小 */
    height: 92px; /* 调整高度以保持一致性 */
    width: 109px;
    margin-right: 10px; /* 图标之间的水平间距 */
    margin-bottom: 5px; /* 图标之间的垂直间距，减小这个值 */
    background-color: #313131;
    border-radius: 2px;
    border: 1px dashed #3D3D3D;
}
.icon_name{
    position: absolute;
    margin-bottom: -45px;
    font-size: 20px;
    color: #918f8f;
}
.icon-area .icon img{
    height: 65px; /* 可调整高度以保持一致性 */
    width: 65px;
    margin: 0 10px; /* Adjust spacing between images as needed */
    position: absolute;
    top:0%
}

.toolbar {
    display: flex;
    align-items: center;
    margin-top: 15px;
}

.toolbar .button {
    border-radius:4px;
    background:#393939;
    height: 32px;
    width: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #e0e0e0;
    font-family: 'HarmonyOS Sans', sans-serif;
    font-weight: bold;
    margin-right: 10px;
    margin-left: 16px;
}

.tools-container{
    width: 70%;
    display:flex;
    justify-content: center;
    margin-left: 40px;
    position: relative;
    transition: transform 0.5s ease;
    z-index: 9999;
}

.tools-container .tools {
    display: flex;
    background-color: #393939;
    z-index: 99999;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 8px 8px 0 8px;
    box-sizing: border-box;
    height: 28px;
    margin-right: -140px;
}

.toolbar .edit {
    width: 13px;
    height: 11px;
    background: url('../../../../apps/businesstopo/assets/edit/edit.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: bottom;
    display: inline-block;
    cursor: pointer;

}
.toolbar .delete {
    width: 13px;
    height: 11px;
    background: url('../../../../apps/businesstopo/assets/edit/delete.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: bottom;
    display: inline-block;
    cursor: pointer;
}


.toolbar .tool:hover {
    background-color: #ccc;
}

.line1 {
    position: absolute;
    width: 2px;
    height: 57px;
    background-color: #c1c1c1;
    top: calc(103%);
    left: 49%;
}

#south .line1{
    position: absolute;
    width: 2px;
    height: 57px;
    background-color: #c1c1c1;
    top: calc(-52%);
    left: 49%;
}
.line1:before,
.line1:after {
    content: '';
    position: absolute;
    width: 6px; /* 圆点的直径 */
    height: 6px; /* 圆点的直径 */
    background:#FFFFFF;
    border-radius: 50%; /* 使其成为圆形 */
}
.line1:before {
    top: -2px; /* 调整以放置在开始节点 */
    left: -2px; /* 调整以放置在开始节点 */
}

.line1:after {
    bottom: -2px; /* 调整以放置在结束节点 */
    left: -2px; /* 调整以放置在结束节点 */
}

.line2 {
    position: absolute;
    width: 2px;
    height: 62px;
    background-color: #c1c1c1;
    top: calc(74% - 4px);
    left: 53%;
}

.line2:before,
.line2:after {
    content: '';
    position: absolute;
    width: 6px; /* 圆点的直径 */
    height: 6px; /* 圆点的直径 */
    background-color: white; /* 圆点颜色 */
    border-radius: 50%; /* 使其成为圆形 */
}
.line2:before {
    top: -2px; /* 调整以放置在开始节点 */
    left: -2px; /* 调整以放置在开始节点 */
}

.line2:after {
    bottom: -2px; /* 调整以放置在结束节点 */
    left: -2px; /* 调整以放置在结束节点 */
}
.business-icon {
    /* existing styles */
}

.business-icon.cbsselected {
    border: 1px solid #2E94FF;
    position: relative;
    margin-right: 3px;
}
.business-icon.error {
    border: 1px solid #ff2e3f;
    position: relative;
}


.info{
    display: inline-block; /* 使得span元素可以设置宽高 */
    width: 20px; /* 设置宽度 */
    height: 20px; /* 设置高度 */
    background-image: url("../../../../apps/businesstopo/assets/edit/info.png"); /* 设置背景图像的URL */
    background-size: contain; /* 控制背景图像大小 */
    position: absolute;
    left: 134px;
    top:18px
}

.info2{
    display: inline-block; /* 使得span元素可以设置宽高 */
    width: 20px; /* 设置宽度 */
    height: 20px; /* 设置高度 */
    background-image: url("../../../../apps/businesstopo/assets/edit/info.png"); /* 设置背景图像的URL */
    background-size: contain; /* 控制背景图像大小 */
    position: absolute;
    left: 108px;
    top:5px
}

.ellipsis{
    width: 200px; /* 设置固定宽度，或使用 max-width: 100%; */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-buttons {
    display: flex;
    position: absolute;
    right: -92px;
    transition: transform 0.5s ease;
}

.action-buttons .cancel-button,
.action-buttons .confirm-button {
    padding: 10px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 32px;
    width: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.action-buttons .cancel-button {
    background-color: #6c757d;
    color: white;
}
.action-buttons .cancel-button-disable {
    background-color: #6c757d;
    color: white;
    pointer-events: none;
    opacity: 0.5;
    padding: 10px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 32px;
    width: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.action-buttons .confirm-button {
    background-color: #0067D1;
    color: white;
}

.action-buttons .confirm-button-disable {
    background-color: #0067D1;
    color: white;
    pointer-events: none;
    opacity: 0.5;
    padding: 10px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 32px;
    width: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
}


.dv-topo-right-panel-div-btn {
    top: calc(50% - 2.75rem);
    width: 1rem;
    height: 5.5rem;
    position: absolute;
}

.dv-topo-right-panel-div-subTopologyBox {
    margin-left: -1rem;
}

.dv-topo-right-panel-div-bottomPanel {
    margin-left: -1.9rem;
}

.dv-topo-common-focus:focus {
    outline: 2px dotted #186fc2;
    outline-offset: 2px;
}

.topo_right_container {
    overflow: auto;
    height: 100%;
}

#topo_right_container {
    height: 100%;
}

.right-panel-container {
    height: 100%;
}
.topo-common-panel-close-vertical-right {
    width: 100%;
    height: 100%;
    background: url("../../../../apps/businesstopo/assets/close.svg") no-repeat;
    background-size: 100% 100%;
}

.topo-common-panel-expend-vertical-right {
    width: 100%;
    height: 100%;
    background: url("../../../../apps/businesstopo/assets/open.svg") no-repeat;
    background-size: 100% 100%;
}


.dv-topo-right-panel-div-btn {
    top: calc(50% - 2.75rem);
    width: 1rem;
    height: 5.5rem;
    position: fixed;
}

.dv-topo-right-panel-div-subTopologyBox {
    margin-left: -0.9rem;
}

.dv-topo-right-panel-div-bottomPanel {
    margin-left: -0.9rem;
}

.dv-topo-common-focus:focus {
    outline: 2px dotted #186fc2;
    outline-offset: 2px;
}


/* 可选的样式 */
.custom-menu {
    background-color: #191919;
    z-index: 1000;
    min-width: 90px;
    box-shadow: 2px 2px 5px rgba(0,0,0,0.5);
    text-align: center;
}
.custom-menu-item {
    padding: 6px;
    cursor: pointer;
    border: 2px solid #4B4B4B;
}
.custom-menu-item:hover {
    background-color: #191919;
}


.region-section {
    margin-bottom: 20px;
}

.region-section h3 {
    margin: 0;
    font-size: 18px;
}

error-item {
    margin-bottom: 10px; /* Add space between error items if needed */
}

.error-item-content {
    display: flex;
    align-items: center; /* Vertically center content */
    margin-bottom: 5px; /* Space between label and value */
}

.error-item-label {
    margin-right: 10px; /* Space between label and value */
    font-weight: bold; /* Optional: make label bold */
    width: 170px;
}

.ellipsis {
    display: block; /* Ensure ellipsis applies */
    max-width: 250px; /* Set a max width to trigger ellipsis */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.error-item-value {
    /* Additional styles for the value if needed */
}

.edit_site_name_title {
  font-size : 16px ;
  color : #F5F5F5 ;
}

.edit_site_name_split_line {
  height: 1px;
  background-color: #353333;
  margin: 16px 0;
}

#dvtopoedit{
    height: calc(100vh - 4rem);
    overflow: auto;

}
#dvtopoedit::-webkit-scrollbar {
    width: 0;
    background-color: #272727;
}
