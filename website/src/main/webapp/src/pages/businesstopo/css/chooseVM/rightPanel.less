@rightPanelBgColor: #272727;
@rightPanelFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@rightPanelGrayFontColor: #BBBBBB;
@rightPanelSuccessFontColor: #00A874;
@tooltipStatBorderColor: #676767;


#chooseVMRightPanel {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 4rem;

  height: calc(100vh - 4rem);
  width: 400px;
  z-index: 999;
  overflow: hidden;

  > div {
    width: 400px;
    padding: 24px 16px;
    padding-bottom: 0;
    box-sizing: border-box;
  }

  &:hover {
    overflow-y: scroll;
  }

  color: @rightPanelFontColor;

  background: @rightPanelBgColor;
  box-sizing: border-box;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.50);
  backdrop-filter: blur(0px);

  .header {
    position: relative;
    display: flex;
    align-items: center;

    .title {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .close {
      position: absolute;
      right: 0;
      display: inline-block;
      height: 16px;
      width: 16px;
      background-repeat: no-repeat;
      cursor: pointer;
    }
  }

  .baseInfo {
    .baseInfoTitle {
      font-size: 16px;
      padding-top: 24px;
      padding-bottom: 24px;
    }

    .baseInfoTable {
      padding-top: 32px;

      .baseInfoTableSpec {
        display: inline-block;
        width: 100px;
        padding-bottom: 24px;
        white-space: nowrap;

        &:not(:nth-child(3n)) {
          margin-right: 34px;
        }

        .status {
          font-size: 14px;
          color: @rightPanelFontColor;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .success {
          color: @rightPanelSuccessFontColor;
        }
        .error {
          color: @tooltipErrFontColor;
        }

        .desc {
          padding-top: 2px;
          font-size: 12px;
          text-overflow: ellipsis;
          overflow: hidden;
          color: @rightPanelGrayFontColor;
          width: 114px;
        }
      }
    }

    .baseInfoLine {
      padding-top: 10px;
      font-size: 12px;
      color: @rightPanelFontColor;

      .baseInfoLineText {
        display: flex;
        position: relative;

        .baseInfoLineStat {
          display: flex;
          position: absolute;
          right: 0;
          gap: 4px;
          font-size: 14px;

          .baseInfoLineTotal {
            color: @rightPanelGrayFontColor;

          }
        }

        .area {
          display: flex;
          position: absolute;
          top: 0;
          right: 0;
          gap: 16px;
          font-size: 12px;
          color: @rightPanelFontColor;

          .error {
            &:before {
              content: " ";
              width: 10px;
              height: 10px;
              background: #E54545;
              border-radius: 50%;
              display: inline-block;
              margin-right: 8px;
            }
          }

          .normal {
            &:before {
              content: " ";
              width: 10px;
              height: 10px;
              background: #2070F3;
              border-radius: 50%;
              display: inline-block;
              margin-right: 8px;
            }
          }
        }
      }

      .baseInfoLineChart {
        margin-top: 9px;
        padding-bottom: 32px;
        position: relative;

        &:before {
          position: absolute;
          display: inline-block;
          content: " ";
          width: 100%;
          height: 4px;
          opacity: 0.2;
          background: #ffffff;
          border-radius: 2px;
        }

        .baseInfoLineChartNum {
          position: absolute;
          display: inline-block;
          width: 0;
          height: 4px;
          background: #2070F3;
          border-radius: 2px;
        }
      }

      .baseInfoLineDetail {
        font-size: 12px;
        color: #BBBBBB;
        padding-bottom: 32px;
      }

      .progress {
        margin-top: 9px;
        padding-bottom: 32px;
        display: flex;
        width: 100%;
        gap: 3px;

        .progressFirst {
          flex: 20;
          height: 4px;
          background: #E54545;
          border-radius: 2px;
        }

        .progressSecond {
          flex: 80;
          height: 4px;
          background: #2070F3;
          border-radius: 2px;
        }
      }
    }
  }

  .alarmStat {
    .alarmStatTitle {
      font-size: 16px;
      padding-bottom: 16px;
    }

    .alarmStatDesc {
      font-size: 0;

      > div {
        display: inline-block;
        position: relative;
        height: 70px;
        width: 180px;
        margin-bottom: 8px;
        padding-left: 16px;

        &:nth-child(2n) {
          margin-left: 8px;
        }

        background: rgba(25, 25, 25, 0.50);
        border-radius: 4px;
        box-sizing: border-box;

        &:before {
          display: inline-block;
          position: absolute;
          content: " ";
          width: 50px;
          height: 50px;
          top: 15px;
          right: 13px;
          background-repeat: no-repeat;
          background-size: cover;
          background-image: var(--lineIcon);
        }
      }

      .progress {
        padding-top: 16px;

        &:before {
          position: absolute;
          display: inline-block;
          content: " ";
          width: 80px;
          height: 4px;
          opacity: 0.2;
          background: var(--lineColor);
          border-radius: 1px;
        }

        .progressNum {
          position: absolute;
          display: inline-block;
          width: var(--lineNum);
          height: 4px;
          background: var(--lineColor);
          border-radius: 2px;
        }
      }

      .alarmStatDescDetail {
        padding-top: 12px;
        display: flex;
        gap: 3px;
        align-items: baseline;

        .alarmStatDescNum {
          font-size: 20px;
          color: @rightPanelFontColor;
        }

        .alarmStatDescText {
          font-size: 10px;
          color: @rightPanelGrayFontColor;

        }
      }
    }
  }

  .resourceUse {
    //padding-top: 24px;
    padding-bottom: 16px;

    .resourceUseTitle {
      font-size: 16px;
      color: @rightPanelFontColor;
    }

    .resourceUseTitleMM {
      font-size: 16px;
      color: rgb(201, 201, 201);
    }

    .chart {
      height: 260px;
      width: 100%;

    }
  }

  .alarmComponent {
    .alarm_event_panel {
      padding-top: 0!important;
      margin-top: 0!important;
    }

    .eui_tab_title {
      margin-left: 0!important;
      margin-right: 24px!important;
      line-height: 26px!important;
    }

    #alarmEventDiv {
      margin-top: 10px!important;
      padding-right: 0px!important;
      button {
        margin-left: 0!important;
      }
    }

    .detail_panel {
      margin-left: 0!important;
      margin-right: 0!important;
    }
  }

}
