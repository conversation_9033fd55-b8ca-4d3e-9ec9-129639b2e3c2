.add{
    background : #393939 ;
    box-shadow : 0px 8px 32px 0px #00000080 ;
    width:  416px;
    height: 376px;
    position: absolute;
    z-index: 999;
    top: 20px;
    left: 30%;
    border-radius: 4px;
}

.add-title{
    display: flex;
    justify-content: space-between;
    padding: 20px;
    fontSize : 16 ;
    color : #F5F5F5 ;
    fontFamily : 鸿蒙黑体 ;
    fontWeight : bold ;
    lineHeight : 16 ;
}

.close_icon{
    display: inline-block; /* 使得span元素可以设置宽高 */
    width: 20px; /* 设置宽度 */
    height: 20px; /* 设置高度 */
    background-image: url('../../../../apps/businesstopo/assets/closeicon.svg') ; /* 设置背景图像的URL */
    background-size: contain; /* 控制背景图像大小 */
    /* 如果需要设置背景图像的位置，可以使用 background-position 属性 */
    /* 如果需要重复背景图像，可以使用 background-repeat 属性 */
}

.add-content{
    padding: 0 20px 10px 20px;
}
.add-buttons{
    display: flex;
    justify-content: center;
    align-items: center;
}


.add-buttons .cancel-button,
.add-buttons .confirm-button {
    padding: 10px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 32px;
    width: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-buttons .cancel-button {
    background-color: #6c757d;
    color: white;
}

.add-buttons .confirm-button {
    background-color: #0067D1;
    color: white;
}