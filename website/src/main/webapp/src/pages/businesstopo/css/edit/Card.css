.card {
    width: 360px;
    height: 200px;
    border-radius : 4px ;
    background : #313131 ;
    margin: auto;
    padding: 10px;
    margin-top: 10px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    margin-bottom: 20px;
}

.title-name {
    fontSize : 16 ;
    color : #F5F5F5 ;
    fontFamily : 鸿蒙黑体 ;
    fontWeight : medium ;
    lineHeight : 20 ;
}


.card-content {
    display: flex;
    flex-direction: column;
}

.content-row {
    padding: 10px 0;
    display: flex;
}

.row-name {
    fontSize : 14 ;
    color : #BBBBBB ;
    fontFamily : 鸿蒙黑体 ;
    fontWeight : regular ;
    lineHeight : 16 ;
    flex: 0 0 35%;
}

.row-value {
    font-size : 14px ;
    color : #BBBBBB ;
    font-family : 鸿蒙黑体 ;
    flex: 0 0 66% ;
    white-space: nowrap;        /* 防止文本换行 */
    overflow: hidden;           /* 隐藏溢出的内容 */
    text-overflow: ellipsis;    /* 使用省略号表示溢出的文本 */
    white-space: nowrap;
}
.edit {
    width: 14px;
    height: 14px;
    background: url('../../../../apps/businesstopo/assets/edit/edit.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: bottom;
    display: inline-block;
    cursor: pointer;
    margin-right: 10px;

}

.delete {
    width: 14px;
    height: 14px;
    background: url('../../../../apps/businesstopo/assets/edit/delete.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: bottom;
    display: inline-block;
    cursor: pointer;
}
