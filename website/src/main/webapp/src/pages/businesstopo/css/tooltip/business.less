@tooltipBgColor: #393939;
@tooltipFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@tooltipGrayFontColor: #BBBBBB;
@tooltipSuccessFontColor: #09AA71;
@tooltipStatBorderColor: #676767;

#business {
  display: inline-block;
  position: absolute;

  height: 246px;
  width: 440px;
  padding: 24px 24px 24px 24px;
  margin: 10px;
  z-index: 99999;

  background: @tooltipBgColor;
  box-shadow: 0 16px 48px 0 rgba(0, 0, 0, 0.50);
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(0px);

  font-family: HarmonyHeiTi;
  font-size: 16px;
  letter-spacing: 1px;
  color: @tooltipFontColor;
  line-height: 16px;

  .triangle, .triangle2 {
    position: absolute;
    left: -10px;
    top: 22px;

    width: 0;
    height: 0;

    border-right: 10px solid @tooltipBgColor;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;

  }

  .triangle2 {
    border-right: 11px solid rgba(255, 255, 255, 0.3);
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;
    left: -12px;
    top: 21px;
  }

  .header {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 12px;

    .title {
      //font-weight: bold;
    }

    .status {
      padding: 5px 8px;
      margin-left: 6px;

      border: 1px solid @tooltipSuccessFontColor;
      border-radius: 16px;

      font-size: 12px;
      color: @tooltipSuccessFontColor;
      text-align: center;
      line-height: 14px;
    }

    .close {
      position: absolute;
      right: 0;
      display: inline-block;
      height: 16px;
      width: 16px;
      background-repeat: no-repeat;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .statList {
    display: flex;
    margin-bottom: 20px;

    .stat {
      display: flex;

      font-size: 14px;
      color: @tooltipGrayFontColor;
      line-height: 16px;
    }
  }

  .chart {
    display: inline-block;
    width: 100%;
    height: 180px;
  }
}
