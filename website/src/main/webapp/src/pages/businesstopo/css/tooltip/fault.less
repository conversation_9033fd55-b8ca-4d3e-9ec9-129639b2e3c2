@tooltipBgColor: #393939;
@tooltipFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@tooltipGrayFontColor: #BBBBBB;
@tooltipStatBorderColor: #676767;

#fault {
  display: inline-block;
  position: absolute;
  //z-index: 100000;

  max-height: 390px;
  width: 420px;
  //padding: 24px 24px 24px 24px;

  background: @tooltipBgColor;
  background-image: radial-gradient(560px circle at 50% -25%, #ff4c4c6e 4%, rgba(255, 54, 27, 0) 44%, rgba(255, 54, 27, 0) 100%);
  box-shadow: 0 16px 48px 0 rgba(0,0,0,0.50);
  box-sizing: border-box;
  border-radius: 4px;
  //backdrop-filter: blur(0px);

  font-family: HarmonyHeiTi;
  font-size: 16px;
  letter-spacing: 1px;
  color: @tooltipFontColor;
  line-height: 16px;
  user-select: none;

  .triangle {
    position: absolute;
    left: -10px;

    width: 0;
    height: 0;

    border-right: 10px solid @tooltipBgColor;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
  }

  .triangle2 {
    position: absolute;
    right: -10px;

    width: 0;
    height: 0;

    border-left: 10px solid @tooltipBgColor;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
  }


  .header {
    display: flex;
    align-items: center;
    position: relative;
    //margin-bottom: 24px;
    margin: 24px;

    .title {
      font-weight: bold;
    }

    .close {
      position: absolute;
      right: 0;
      display: inline-block;
      height: 16px;
      width: 16px;
      background-repeat: no-repeat;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .statScroll {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 50px;
    overflow: hidden;
  }

  .statList {
    display: flex;
    margin-bottom: 20px;
    margin-left: 24px;
    margin-right: 24px;
    padding-bottom: 30px;
    overflow-x: scroll;
    overflow-y: hidden;

    .stat {
      padding-right: 16px;
      position: relative;
      &:not(:first-child) {
        padding-left: 16px;
      }
      &:not(:last-child) {
        border-right: 1px solid @tooltipStatBorderColor;
      }

      .statAfter {
        user-select: text!important;
        display: inline-block;
        position: absolute;
        left: 0;
        top: 0;
        height: 60px;
        width: 100%;
      }

      .statNum {
        text-align: center;
        margin-bottom: 2px;
        user-select: none;
      }

      .statDesc {
        font-size : 12px ;
        color : @tooltipGrayFontColor;
        line-height : 16px ;
        text-align: center;
        min-width: 32px;
        user-select: none;
      }
    }

  }

  .hiddenScroll {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .contentList {
    display: inline-block;
    position: relative;
    width: 100%;
    max-height: 270px;
    //overflow-y: scroll;
    overflow: hidden;
    padding-right: 50px; /**隐藏滚动条*/

    .clickBackground {
      background: rgba(0,0,0,0.10);
    }

    .content {
      padding-top: 12px;
      padding-left: 24px;
      padding-right: 24px;
      position: relative;
      border-bottom: 1px solid rgba(238,238,238,0.10);
      padding-bottom: 8px;
      box-sizing: border-box;
      width: 100%;

      &:hover {
        background: rgba(0,0,0,0.10);
      }

      .contentLeft {

        .contentTitle {
          font-size: 14px;
          color: @tooltipFontColor;
          margin-bottom: 8px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 280px;
        }

        .contentText {
          display: flex;
          margin-bottom: 8px;

          font-size: 12px;
          color: @tooltipGrayFontColor;
        }

        .contentTime {
          display: flex;
          margin-bottom: 8px;

          font-size: 12px;
          color: @tooltipGrayFontColor;
        }
      }

      .contentRight {
        padding: 4px 15px;
        position: absolute;
        right: 24px;
        top: 20px;

        border: 1px solid @tooltipGrayFontColor;
        border-radius: 4px;
        box-sizing: border-box;

        font-size: 12px;
        color: @tooltipGrayFontColor;

        &:active:hover {
          transform: scale(1.03, 0.97);
        }

        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .more {
    font-size:14px;
    color: #2E94FF;
    text-align: center;

    display: inline-block;
    padding-bottom: 18px;
    padding-top: 16px;
    width: 100%;

    &:hover {
      cursor: pointer;
    }

    &:active:hover {
      transform: scale(1.03, 0.97);
    }
  }
}
