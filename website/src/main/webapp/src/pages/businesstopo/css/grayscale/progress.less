@tooltipBgColor: #393939;
@tooltipFontColor: #F5F5F5;
@tooltipErrFontColor: #F43146;
@tooltipGrayFontColor: #BBBBBB;
@tooltipSuccessFontColor: #09AA71;
@tooltipStatBorderColor: #676767;


#progress {
  &:hover{
    #progressDetail {
      transform: translateX(0);
      opacity: 1;
    }

    #smallProgress {
      transform: translateX(calc(660px - 88px));
      opacity: 0;
    }

    #smallProgressRollBack {
      transform: translateX(calc(660px - 88px));
      opacity: 0;
      height: 200px;
    }
    width: calc(660px);

  }
  transition: all 0.2s ease-in-out;

  display: inline-block;
  width: 88px;

  position: absolute;
  top: calc(4rem + 16px);
  right: 100px;
  overflow: hidden;
  font-family: HarmonyOSHans;
  box-sizing: border-box;

  background: @tooltipBgColor;
  box-shadow: 0 4px 12px 0 rgba(0,0,0,0.15);
  border-radius: 4px;
  backdrop-filter: blur(4px);
  z-index: 99999;

  #smallProgress {
    display: inline-block;
    width: 88px;
    height: 280px;
    transition: all 0.2s ease-in-out;

    & > .title {
      font-size: 14px;
      color: @tooltipFontColor;
      padding: 12px 0;
      text-align: center;
      border-bottom: 1px solid rgba(238,238,238,0.05);
    }

    & > .titleProgress {
      position: relative;


      .titleRing {
        display: inline-block;
        position: absolute;
        top: 18px;
        left: 18px;
        width: 52px;
        height: 52px;

        background: conic-gradient(
                transparent 0deg, transparent var(--ringdeg0, 0deg),
                #745EEF var(--ringdeg0, 0deg), #745EEF var(--ringdeg, 0deg),
                transparent var(--ringdeg, 0deg), transparent var(--ringdeg4, 0deg),
                #6E6E6E var(--ringdeg4, 0deg), #6E6E6E 360deg);
        border-radius: 50%;
      }


      .titleProgressNum {
        display: inline-block;
        position: relative;
        width: 46px;
        height: 46px;
        background: @tooltipBgColor;

        margin: 21px 0 0 21px;

        text-align: center;
        font-size: 16px;
        color: @tooltipFontColor;
        line-height: 40px;
        font-weight: normal;

        border: 3px solid transparent;
        box-sizing: border-box;
        border-radius: 50%;
      }

      .titleProgressDesc {
        display: inline-block;
        padding: 12px 0 0 0;
        width: 100%;

        font-size: 12px;
        color: @tooltipGrayFontColor;
        text-align: center;
      }

      .titleProgressDescNum {
        display: inline-block;
        width: 100%;

        font-size: 12px;
        color: @tooltipGrayFontColor;
        text-align: center;
      }
    }

    & > .user {
      position: relative;

      .userRing {
        display: inline-block;
        position: absolute;
        top: 18px;
        left: 18px;
        width: 52px;
        height: 52px;

        background: conic-gradient(
                transparent 0deg, transparent var(--ringdeg0, 0deg),
                #745EEF var(--ringdeg0, 0deg), #745EEF var(--ringdeg, 0deg),
                transparent var(--ringdeg, 0deg), transparent var(--ringdeg4, 0deg),
                #6E6E6E var(--ringdeg4, 0deg), #6E6E6E 360deg);
        border-radius: 50%;
      }

      .userNum {
        display: inline-block;
        position: relative;
        width: 46px;
        height: 46px;
        background: @tooltipBgColor;

        margin: 21px 0 0 21px;

        text-align: center;
        font-size: 16px;
        color: @tooltipFontColor;
        line-height: 40px;
        font-weight: normal;

        border: 3px solid transparent;
        box-sizing: border-box;
        border-radius: 50%;

        #progressUserUnit {
          font-size: 10px;
          color: @tooltipGrayFontColor;
          margin-left: 2px;
        }
      }

      .userDesc {
        display: inline-block;
        width: 100%;
        padding: 12px 0 0 0;

        font-size: 12px;
        color: @tooltipGrayFontColor;
        text-align: center;
      }
    }
  }

  #smallProgressRollBack {
    display: inline-block;
    width: 88px;
    height: 160px;
    transition: all 0.2s ease-in-out;

    & > .title {
      font-size: 14px;
      color: @tooltipFontColor;
      padding: 12px 0;
      text-align: center;
      border-bottom: 1px solid rgba(238,238,238,0.05);
    }

    & > .titleProgress {
      position: relative;


      .titleRing {
        display: inline-block;
        position: absolute;
        top: 18px;
        left: 18px;
        width: 52px;
        height: 52px;

        background: conic-gradient(
                transparent 0deg, transparent var(--ringdeg0),
                #745EEF var(--ringdeg0), #745EEF var(--ringdeg),
                transparent var(--ringdeg), transparent var(--ringdeg4),
                #6E6E6E var(--ringdeg4), #6E6E6E 360deg);
        border-radius: 50%;
      }


      .titleProgressNum {
        display: inline-block;
        position: relative;
        width: 46px;
        height: 46px;
        background: @tooltipBgColor;

        margin: 21px 0 0 21px;

        text-align: center;
        font-size: 16px;
        color: @tooltipFontColor;
        line-height: 40px;
        font-weight: normal;

        border: 3px solid transparent;
        box-sizing: border-box;
        border-radius: 50%;
      }

      .titleProgressDesc {
        display: inline-block;
        padding: 12px 0 0 0;
        width: 100%;

        font-size: 12px;
        color: @tooltipGrayFontColor;
        text-align: center;
      }

      .titleProgressDescNum {
        display: inline-block;
        width: 100%;

        font-size: 12px;
        color: @tooltipGrayFontColor;
        text-align: center;
      }
    }
  }
}

.close_icon {
  width:12px;
  height: 12px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+6Z2i5p2/5YWz6ZetaWNvbjwvdGl0bGU+CiAgICA8ZyBpZD0i6aG16Z2iLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSLliIflm74y77yI5oyB57ut5pu05paw77yJIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMTMuMDAwMDAwLCAtNDQ5LjAwMDAwMCkiIGZpbGw9IiNCQkJCQkIiPgogICAgICAgICAgICA8ZyBpZD0ieuWGhemDqC9pYy9jbG9zZS0yIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMy4wMDAwMDAsIDQ0OS4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zLjQ1NTU4MjYyLDIuNjk2Njk5MTQgTDcuODc0NjQwODgsNy4xMTU2NDA4OCBMMTIuMjk0NDE3NCwyLjY5NjY5OTE0IEMxMi41MTEzNzUzLDIuNDc5NzQxMiAxMi44NDgxNTU4LDIuNDU1NjM0NzYgMTMuMDkxNzQxLDIuNjI0Mzc5ODMgTDEzLjE3ODMwMDksMi42OTY2OTkxNCBDMTMuMzk1MjU4OCwyLjkxMzY1NzA4IDEzLjQxOTM2NTIsMy4yNTA0Mzc1OCAxMy4yNTA2MjAyLDMuNDk0MDIyNzYgTDEzLjE3ODMwMDksMy41ODA1ODI2MiBMOC43NTc2NDA4OCw3Ljk5OTY0MDg4IEwxMy4xNzgzMDA5LDEyLjQxOTQxNzQgQzEzLjQyMjM3ODUsMTIuNjYzNDk1MSAxMy40MjIzNzg1LDEzLjA1OTIyMzIgMTMuMTc4MzAwOSwxMy4zMDMzMDA5IEMxMi45MzQyMjMyLDEzLjU0NzM3ODUgMTIuNTM4NDk1MSwxMy41NDczNzg1IDEyLjI5NDQxNzQsMTMuMzAzMzAwOSBMNy44NzQ2NDA4OCw4Ljg4MjY0MDg4IEwzLjQ1NTU4MjYyLDEzLjMwMzMwMDkgQzMuMjM4NjI0NjgsMTMuNTIwMjU4OCAyLjkwMTg0NDE4LDEzLjU0NDM2NTIgMi42NTgyNTksMTMuMzc1NjIwMiBMMi41NzE2OTkxNCwxMy4zMDMzMDA5IEMyLjM1NDc0MTIsMTMuMDg2MzQyOSAyLjMzMDYzNDc2LDEyLjc0OTU2MjQgMi40OTkzNzk4MywxMi41MDU5NzcyIEwyLjU3MTY5OTE0LDEyLjQxOTQxNzQgTDYuOTkwNjQwODgsNy45OTk2NDA4OCBMMi41NzE2OTkxNCwzLjU4MDU4MjYyIEMyLjMyNzYyMTQ2LDMuMzM2NTA0OTQgMi4zMjc2MjE0NiwyLjk0MDc3NjgyIDIuNTcxNjk5MTQsMi42OTY2OTkxNCBDMi44MTU3NzY4MiwyLjQ1MjYyMTQ2IDMuMjExNTA0OTQsMi40NTI2MjE0NiAzLjQ1NTU4MjYyLDIuNjk2Njk5MTQgWiIgaWQ9Iui3r+W+hCI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  cursor: pointer;
  position: absolute;
  top: calc(4rem + 16px);
  z-index: 99999;
}