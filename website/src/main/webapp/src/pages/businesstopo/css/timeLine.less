/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

body {
  overflow-x: hidden;
}

.close_details_icon {
  width: 24px;
  height: 24px;
  background: url('../../../apps/businesstopo/assets/timeLine/closeSingle.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  cursor: pointer;
  position: absolute!important;
  right: -12px;
  top: -12px;
}

.carousel_close_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/closeDialog.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: right;
  cursor: pointer;
}

.details_occurTime_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/occurTime.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: left;
  cursor: pointer;
  margin-top: 3px;
  margin-right: 3px;
}
.details_alarmSource_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/alarmSource.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: left;
  cursor: pointer;
  margin-top: 3px;
  margin-right: 3px;
}

.details_eventDetails_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/details.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: left;
  cursor: pointer;
  margin-top: 3px;
  margin-right: 3px;
}

.page_left_icon {
  width: 32px;
  height: 32px;
  background: url('../../../apps/businesstopo/assets/timeLine/page_left.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  cursor: pointer;
  position: absolute;
  top: 50%;
  z-index: 10001;
  left: 0.5%;
}
.page_left_icon:hover {
  background: url('../../../apps/businesstopo/assets/timeLine/page_left_hover.svg') no-repeat;
}
.page_left_icon:active {
  background: url('../../../apps/businesstopo/assets/timeLine/page_left_click.svg') no-repeat;
}

.page_right_icon {
  width: 32px;
  height: 32px;
  background: url('../../../apps/businesstopo/assets/timeLine/page_right.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 0.5%;
  z-index: 10001;
}
.page_right_icon:hover {
  background: url('../../../apps/businesstopo/assets/timeLine/page_right_hover.svg') no-repeat;
}
.page_right_icon:active {
  background: url('../../../apps/businesstopo/assets/timeLine/page_right_click.svg') no-repeat;
}

.time_line {
  position: absolute;
  display: flex;
  height: 5rem;
  background: rgba(0, 0, 0, 0.9);
}

.btn_back_time {
  width: 96px;
  height: 5rem;
  background-color: #575757;
  vertical-align: top;
  cursor: pointer;
  text-align: center;
}

.time_line_chart {
  height: 100%;
}

.single_detail_container {
  background-color: #393939;
  position: fixed;
  bottom: 5.6rem;
  left: 32%;
  padding: 24px 0 24px 24px;
  border-radius: 10px;
  box-shadow: 0 16px 48px 0 rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(0px);
  z-index: 99999;
  display: block;
  width: 398px;
  height: 195px;

  & > div {
    z-index: 3;
    position: relative;
  }
}

.multiple_detail_container {
  vertical-align: top;
  background-color: #393939;
  padding: 24px 0 24px 24px;
  border-radius: 10px;
  backdrop-filter: blur(0px);
  z-index: 1000;
  display: inline-block;
  width: 398px;
  height: 195px;
  position: relative;
  overflow: hidden;

  & > div {
    z-index: 3;
    position: relative;
  }
}

.single_detail_container_child {
  display: inline-block;
  position: absolute!important;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border-radius: 10px;

}

.single_detail_container_child, .multiple_detail_container {
  overflow: hidden;
  &:after {
    content: ' ';
    display: inline-block;
    position: absolute;
    left: 2px;
    top: 2px;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    z-index: 2;
    border-radius: 6px;
    background-color: #393939;
  }
}

.detailsAlarmLabel2 {
  position: absolute!important;
  left: 0;
  top: 0;
  display: inline-block;
  height: 32px;
  line-height: 20px;
  width: 150px;
  opacity: 0.9;
  font-family: HarmonyOSHans;
  font-size: 14px;
  color: #FFFFFF;
  background-image: linear-gradient(180deg, rgba(255, 218, 195, 0) 0%, rgba(255, 138, 64, 0.12) 100%);
  img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    padding: 0 10px 0 16px;
  }
}
.detailsAlarmLabel {
  position: absolute!important;
  left: 0;
  top: 0;
  display: inline-block;
  height: 42px;
  line-height: 42px;
  width: 360px;
  opacity: 0.9;
  font-family: HarmonyOSHans;
  font-size: 14px;
  color: #FFFFFF;
  background-image: linear-gradient(180deg, rgba(255,218,195,0.00) 0%, rgba(255,138,64,0.12) 100%);

  img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    padding: 0 10px 0 16px;
  }
}

.detailsTitle {
  font-size: 16px;
  color: #f5f5f5;
  display: inline-block;
  margin-left: 6px;
  font-weight: bolder;
  vertical-align: top;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 260px;
}

.detailsInfo {
  font-size: 14px;
  color: #f5f5f5;
  line-height: 21px;
}

.detailsValue {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
}

.detailBottomContainer {
  margin-top: 16px;
  height: 46px;
  display: inline-flex;
  flex-direction: column;
}

.carousel_container {
  position: fixed;
  bottom: 5.6rem;
  left: 36%;
  background-color: #272727;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  z-index: 90000;
  display: block;
  height: 278px;
}

.carousel_tab {
  display: inline;
  font-size: 14px;
  color: #ffffff;
  text-align: left;
  font-weight: bolder;
  cursor: pointer;
}

.carousel_tab_active {
  display: inline;
  font-size: 14px;
  color: #2e94ff;
  text-align: left;
  font-weight: bolder;
  cursor: pointer;
  padding-bottom: 1px;
  border-bottom: 1px solid #2e94ff;
}

.btn_event_backtrack {
  margin-top: 24px;
  width: 96%;
  border-radius: 4px;
  background-color: #2070f3 !important;
}

.btn_event_backtrack:hover {
  background-color: #2E94FF !important;
}

.alarm_level_tag_critical {
  width: 56px;
  height: 26px;
  background: url('../../../apps/businesstopo/assets/timeLine/criticalTag.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: right;
  color: #e54545;
  font-size: 12px;
  text-align: center;
  line-height: 26px;
}

.alarm_level_tag_major {
  width: 56px;
  height: 26px;
  background: url('../../../apps/businesstopo/assets/timeLine/majorTag.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: right;
  color: #ff8000;
  font-size: 12px;
  text-align: center;
  line-height: 26px;
}

.alarm_level_tag_minor {
  width: 56px;
  height: 26px;
  background: url('../../../apps/businesstopo/assets/timeLine/minorTag.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: right;
  color: #ffbb33;
  font-size: 12px;
  text-align: center;
  line-height: 26px;
}

.alarm_level_tag_warning {
  width: 56px;
  height: 26px;
  background: url('../../../apps/businesstopo/assets/timeLine/warningTag.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
  float: right;
  color: #2e94ff;
  font-size: 12px;
  text-align: center;
  line-height: 26px;
}

.event_title_icon {
  width: 24px;
  height: 24px;
  background: url('../../../apps/businesstopo/assets/timeLine/eventIcon.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
}

.exit_track_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/exitTrack.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  left: 43%;
  top: 25%;
  position: relative;
}

.tooltipTimeLineDiv {
  color: #F5F5F5;
  font-size: 14px;
}

.tooltip_time_icon {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/timeLine/time.svg') no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  position: relative;
  display: inline-block;
  top: -2px;
}

.tipCircle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 9px;
}
