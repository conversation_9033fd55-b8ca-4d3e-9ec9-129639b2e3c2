/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

#dvtips {
  width: 100px;
  min-height: 60px;
  border-radius: 8px;
  background: rgb(57, 57, 57);
  box-shadow: rgba(0, 0, 0, 0.5) 0px 4px 16px 0px;
  backdrop-filter: blur(0px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f5f5f5;
  -webkit-font-smoothing: antialiased; /* Safari, Chrome */
  -moz-osx-font-smoothing: grayscale; /* Firefox */
}

#sitetips {
  min-width: 120px;
  height: 60px;
  border-radius: 8px;
  background: rgb(57, 57, 57);
  box-shadow: rgba(0, 0, 0, 0.5) 0px 4px 16px 0px;
  backdrop-filter: blur(0px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f5f5f5;
  -webkit-font-smoothing: antialiased; /* Safari, Chrome */
  -moz-osx-font-smoothing: grayscale; /* Firefox */
}


#pod {
  min-width: 200px;
  height: 106px;
  border-radius: 8px;
  background: rgb(57, 57, 57);
  box-shadow: rgba(0, 0, 0, 0.5) 0px 4px 16px 0px;
  backdrop-filter: blur(0px);
  padding-left: 1em;
  padding-right: 1em;
  padding-top: 1em;
  color: #f5f5f5;
  -webkit-font-smoothing: antialiased; /* Safari, Chrome */
  -moz-osx-font-smoothing: grayscale; /* Firefox */
}

.pod-left {
  margin-right: 0.5em;
  font-size: 16px;
  line-height: 16px;
  padding-top:5px
}

.pod-min-font {
  font-size: 14px;
  line-height: 16px;
  height: 16px;
}

.pod-left-sec {
  margin-right: 4em;
}

.supoort-gray {
  white-space: nowrap;
  line-height: 12px;
  display: inline-block;
  font-size: 12px;
  color: #2E94FF;
  box-sizing: content-box;
  height: 12px;
  padding: 2px 4px;
  border: 1px solid #2E94FF;
  border-radius: 2px;
}

.supoort-not-gray {
  white-space: nowrap;
  vertical-align: middle;
  line-height: 14px;
  display: inline-block;
  font-size: 12px;
  box-sizing: content-box;
  height: 14px;
  padding: 2px 4px;
  border: 1px solid #676767;
  background: #676767;
  borderRadius: 2px;
}

.image-container {
  width: 32px;
  height: 32px;
  background-size: cover;
  background-image: url("../../../../apps/businesstopo/assets/site_info.svg");
  background-position: -3px 0;
}

.image-container_error {
  width: 32px;
  height: 32px;
  background-size: cover;
  background-image: url("../../../../apps/businesstopo/assets/site_major.svg");
  background-position: -4px 0;
}

.text-container {
  align-self: flex-end;
  margin-left: 0.5em;
}

.text-container-error {
  align-self: flex-end;
  margin-left: 0.5em;
  color: #E54545;
}