/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
#business_topo {
  ::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }
  font-family: HarmonyOSHans;
  background: #191919;
}

#hsmtypeDrillTopo{
  background: #191919;
}

#siteDrillTopo{
  ::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }
}
#motypeDrillTopo {
  ::-webkit-scrollbar {
    width: 0px !important;
    height: 12px !important;
  }
  font-family: HarmonyOSHans;
  background: #191919;
}

#hsmtypeDrillTopo {
  ::-webkit-scrollbar {
    width: 0px !important;
    height: 12px !important;
  }
  font-family: HarmonyOSHans;
  background: #191919;
}

.topology3d_main_container {
  position: relative;
  height: calc(100vh - 9rem);
  width: calc(100% - 400px);
  z-index: 9998;
  background: #272727;
}

.topology3d_main_container_full {
  position: relative;
  height: calc(100vh - 9rem);
  width: 100%;
  background: #272727;
}

.over_view_select {
  cursor: pointer;
  background: #272727;
  border: 1px solid #5CACFF;
  border-radius: 2px;
  height: 40px;
  width: 40px;
  margin-top: 16px;
  margin-left: 20px;
}

.over_view_icon {
  display: inline-block;

  width: 3rem;
  height: 3rem;
  background: url("../../../apps/businesstopo/assets/overview_blue.svg") no-repeat;
  margin-top: 8px;
  margin-left: 8px;

}

.database_notselect {
  cursor: pointer;
  white-space: nowrap;
  border-radius: 4px;

  margin-top: 14px;
  margin-left: 20px;
  height: 40px;
  width: 40px;
  z-index: 99999;

  &:hover {
    .database_icon {
      background-color: rgba(103, 103, 103, 0.2);
      border-radius: 0.125rem;
    }
  }
}

.database_icon {
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
  height: 40px;
  width: 40px;
  background: url("../../../apps/businesstopo/assets/database.svg") no-repeat;
  background-position: 8px 8px;
  padding: 0px;
}

.toggle_text {
  text-align: center;
  margin-top: 10px;
  font-family: HarmonyHeiTi;
  font-size: 14px;
  color: #BBBBBB;
  line-height: 16px;
}

.view_toggle {
  position: absolute;
  left: 16px;
  bottom: 90px;
  z-index: 9999;
  background: #393939;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  backdrop-filter: blur ( 4px );
  width: 80px;
  height: 180px;
}

.switch {
  position: absolute;
  left: 16px;
  top: 70px;
  z-index: 9999;
  border-radius: 4px;
}
.switch_icon {
  display: inline-block;
  width: 3rem;
  height: 3rem;
  background: url("../../../apps/businesstopo/assets/switch.svg") no-repeat;
  margin-top: 8px;
  margin-left: 8px;
  cursor: pointer;
}
.switch_content{
  position:relative;
  left: 30px;
  bottom: 30px;
  z-index: 9999;
  background: #393939;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  backdrop-filter: blur ( 4px );
  width: 100px;
  height: 100px;
}

.switch_content_item{
  padding-left:16px ;
  padding-top: 10px;
  font-size: 18px;
  color: #f5f5f5;
}
.switch_content_item_selected{
  padding-left:16px ;
  padding-top: 10px;
  font-size: 18px;
  color: #1a70de;
}
.topo-common-panel-close-vertical-right {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/close.svg") no-repeat;
  background-size: 100% 100%;
}

.topo-common-panel-expend-vertical-right {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/open.svg") no-repeat;
  background-size: 100% 100%;
}

.topo-mm-common-panel-close-vertical-right {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/close.svg") no-repeat;
  background-size: 100% 100%;
}

.topo-mm-common-panel-expend-vertical-right {
  width: 100%;
  height: 100%;
  background: url("../../../apps/businesstopo/assets/open.svg") no-repeat;
  background-size: 100% 100%;
}

.kpi_title_text {
  margin-left: 16px;
  margin-right: 16px;
  font-size: 20px;
  color: #F5F5F5;
  font-weight: 500;
  display: inline-block;
  padding-top: 4px;
}


.service_select {
  // border : 1px solid #676767 ;
  border-radius: 4px;
  background: #272727;
}

.service_select_select {
  background: #272727;
  border: 1px solid #676767;
  border-radius: 4px;

}


.service_select_dropdown {
  background: #272727;
  border: 1px solid #676767;
}

.business-right-panel {
  top: 4rem;
  right: 0;
  position: absolute;
  z-index: 999;
  width: 400px;
  height: calc(100vh - 4rem);
  box-sizing: border-box;
  padding-top: 60px;
  float: right;
  background: #272727;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.50);
  backdrop-filter: blur ( 0px );

  overflow: hidden;

  &:hover {
    overflow-y: auto;
  }
}

.dv-topo-right-panel-div-btn {
  top: calc(50% - 2.75rem);
  width: 1rem;
  height: 5.5rem;
  position: fixed;
}

.dv-topo-right-panel-div-subTopologyBox {
  margin-left: -0.9rem;
}

.dv-topo-right-panel-div-bottomPanel {
  margin-left: -0.9rem;
}

.dv-topo-common-focus:focus {
  outline: 2px dotted #186fc2;
  outline-offset: 2px;
}

.topo_right_container {
  overflow: auto;
  height: 100%;
}

#topo_right_container {
  height: 100%;
}

.right-panel-container {
  height: 100%;
}

.alarmCls {
  width: 98%;
  height: 100%;
  min-height: 156px;
  height: calc(100% - 26px);
  display: inline-block;
}

.detail_panel {
  position: relative;
  background: #313131;
  //box-shadow : 0 8px 24px 0 rgba( 0,0,0,0.50 )  ;
  //backdrop-filter : blur ( 0px ) ;
  height: 130px;
  width: 368px;
  margin-top: 8px;
  //margin-right: 16px;
  //margin-left: 16px;
  //bottom: 10px;
  border-radius: 8px;

  &:hover {
    background: #313131;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.50);
    border-radius: 8px;
    cursor: pointer;
  }

  transition: all 0.2s linear;
}

.detail_content {
  padding-top: 24px;
  padding-left: 24px;


}

.detail_title {
  color: white;
  display: inline-block;
  margin-left: 4px;
  width: 225px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail_urgent_btn {
  border: none;
  color: #E54545;
  font-size: 12px;
  float: right;
  display: inline-block;
  background: rgba(229, 77, 80, 0.10);
  border-radius: 16px 0 0 16px;

}

.detail_level_1_btn {
  border: none;
  color: #e54545;
  background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/urgent_tag.svg);
  font-size: 12px;
  float: right;
  border-radius: 16px 0 0 16px;
  display: flex;
  background-size: 100% 100%;
  align-items: center;
  justify-content: center;
}

.detail_level_2_btn {
  border: none;
  color: #ff8000;
  background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/minor_tag.svg);
  font-size: 12px;
  float: right;
  border-radius: 16px 0 0 16px;
  display: flex;
  background-size: 100% 100%;
  align-items: center;
  justify-content: center;
}

.detail_level_3_btn {
  border: none;
  color: #ffbb33;
  background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/important_tag.svg);
  font-size: 12px;
  float: right;
  border-radius: 16px 0 0 16px;
  display: flex;
  background-size: 100% 100%;
  align-items: center;
  justify-content: center;
}

.detail_level_4_btn {
  border: none;
  color: #2e94ff;
  background-image: url(~@pages/businesstopo/a3dPages/assets/image/svg/hints_tag.svg);
  font-size: 12px;
  float: right;
  border-radius: 16px 0 0 16px;
  display: flex;
  background-size: 100% 100%;
  align-items: center;
  justify-content: center;
}

.detail_sub_title {
  margin-top: 16px;
  line-height: 18px;
  font-size: 14px;
  color: #BBBBBB;
  font-family: HarmonyHeiTi;
  width: 320px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.detail_alarm_key {
  display: inline-block;
  margin-top: 10px;
  line-height: 16px;
  font-size: 14px;
  color: #BBBBBB;
  width: 120px;
}

.detail_alarm_value {
  display: inline-block;
  margin-left: 10px;
  line-height: 16px;
  font-family: HarmonyHeiTi;
  font-size: 14px;
  color: #F5F5F5;
}

.chart_panel {
  background: #272727;
  height: 220px;
  margin-top: 15px;
  margin-right: 16px;
  margin-left: 16px;
  border-radius: 10px;
}

.site_chart_panel {
  height: 220px;
}

.indicator_select_btn {
  .eui-btn-icon {
    height: 1.5rem;
    width: 1.2rem;
  }

}

.piechart_panel {
  background: #272727;
  height: 290px;
  margin-top: 25px;
  margin-right: 16px;
  margin-left: 16px;
  border-radius: 10px;
}

.eui_tab_title {
  color: #BBBBBB;
}

.line_chart_tab {

  background: #272727;
  height: 100%;

  .eui_tab {
    background: #272727;
  }

  .eui_tab_content.active {
    height: 80%;
  }

  .eui_tab_bar {
    display: none;
  }

  .eui_tab_title {
    padding-left: 0rem;
    font-size: 16px;

    .active:focus {
      outline: none;
    }
  }

  .eui_tab_title.active {
    margin-bottom: -0.0625rem;
    height: 1.875rem;
    line-height: 1.875rem;
    color: #2E94FF;
    border-bottom: 0.125rem solid #2E94FF;
    flex-shrink: 0;
  }

  .eui_tab_title:hover {
    color: #2E94FF;
  }


  .eui_tab_title.active.lastActive {
    padding-right: 0rem
  }

}

.alarm_event_panel {
  margin-top: 1rem;

  .eui_tab {
    height: 100%;
    background: #272727;
    position: sticky;
    //top: -10px;
    z-index: 2;
  }

  .eui_tab_body.top {
    height: 100%;
  }

  .eui_tab_content.active {
    height: 80%;
  }

  .eui_tab_bar {
    display: none;
  }

  .eui_tab_title {
    padding-left: 0rem;
    font-size: 20px;
    font-weight: 500;

    .active:focus {
      outline: none;
    }
  }

  .eui_tab_title.active.lastActive {
    padding-right: 0rem
  }

  .eui_tab_title.active {
    margin-bottom: -0.0625rem;
    height: 1.875rem;
    line-height: 1.875rem;
    color: #2E94FF;
    border-bottom: 0.125rem solid #2E94FF;
    flex-shrink: 0;
  }

}

.alarm_event_title {
  margin-left: 10px;
}

.alarm_title_text {
  font-size: 20px;
  color: #BBBBBB;
  cursor: pointer;

}

.alarm_title_text :active {
  color: #2E94FF;
  text-decoration: underline #ff6600;
}

.alarm_title_text_click {
  font-size: 20px;
  color: #2E94FF;
  cursor: pointer;
}

.alarm_level_btn {
  //margin-left: 20px;
  color: #F5F5F5;
  border: none;
  border-radius: 16px;
  background: #272727;
  outline: none;
}

.alarm_level_btn:hover {
  color: #2E94FF;
  background: rgba(46, 148, 255, 0.10);
  outline: none;
}


.alarm_level_btn .active {
  color: #2E94FF;
  background: rgba(46, 148, 255, 0.10);
  outline: none;
}


.alarm_level_btn_blue {
  //margin-left: 20px;
  color: #2E94FF;
  border: none;
  border-radius: 16px;
  background: rgba(46, 148, 255, 0.10);
}

.alarm_level_btn_blue:hover {
  color: #2E94FF;
  background: rgba(46, 148, 255, 0.10);
  outline: none;
}


.alarm_level_btn_blue .active {
  color: #2E94FF;
  background: rgba(46, 148, 255, 0.10);
  outline: none;
}

.event_icon {
  width: 22px;
  height: 22px;
  background: url("../../../apps/businesstopo/assets/event_icon.svg") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.alarm_level_1_icon {
  width: 20px;
  height: 20px;
  background: url("../../../apps/businesstopo/assets/critical_icon.svg") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.alarm_level_2_icon {
  width: 20px;
  height: 20px;
  background: url("../../../apps/businesstopo/assets/major_icon.svg") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.alarm_level_3_icon {
  width: 20px;
  height: 20px;
  background: url("../../../apps/businesstopo/assets/minor_icon.svg") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.alarm_level_4_icon {
  width: 20px;
  height: 20px;
  background: url("../../../apps/businesstopo/assets/info_icon.svg") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.time_icon {
  width: 15px;
  height: 15px;
  background: url("../../../apps/businesstopo/assets/ic_timer_lined.svg") no-repeat;
  background-size: 100% 100%;
  vertical-align: bottom;
  display: inline-block;
}


#lineChartDiv,
#pieChartDiv {
  height: 100%;
}

.testcontainer .up {
  height: 50px;
  width: 100%;
}

.testcontainer .up .ul {
  list-style-type: none;
  text-align: center;
  color: #BBBBBB;
}

.testcontainer .up .ul .li {
  float: left;
  width: 50px;
  height: 50px;
  line-height: 50px;
}

.select {
  color: #1677ff;
  border-bottom: 2px solid #1677ff;
}

#indicator_select_panel {
  background: #393939;
  box-shadow: 0 16px 48px 0 rgba(0, 0, 0, 0.50);
  border-radius: 8px;
  backdrop-filter: blur ( 0px );
  width: 168px;
  max-height: 300px;
  z-index: 999;
  position: absolute;
  margin-top: -12px;
  right: 16px;
  overflow-y: auto;
  overflow-x: hidden;

  .eui_checkbox_span {
    outline: none;
    background-color: #393939;
    border: 1px solid #BBBBBB;
    border-radius: 3px;


  }

  .eui_checkbox_span.checked {
    border-color: #2E94FF;
    background: url("../../../apps/businesstopo/assets/checkbox_blue2.svg");
    background-clip: border-box;
    background-position: -5px -5px;
  }

  .eui_label {
    color: #BBBBBB;
    width: 100px;
    display: inline-block;
  }
}

.indicator_select_tip {
  font-family: HarmonyOSHans_Medium;
  font-size: 16px;
  color: #BBBBBB;
  line-height: 16px;
  margin: 16px;
  margin-bottom: 7px;
}

.site-right-panel {
  //top:4rem;
  right: 0;
  position: absolute;
  z-index: 999;
  width: 400px;
  height: calc(100vh - 4rem);
  float: right;
  background: #272727;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(0px);
  overflow-y: auto;
  padding: 25px 10px 0px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.site-right-panel-text {
  // margin-top: 25px;
  // margin-left: 16px;
  // margin-right: 1rem;
  font-size: 20px;
  margin-bottom: 24px;
  color: #f5f5f5;
  font-weight: 500;
  position: relative;
}

.strip-site-right-panel-text {
  font-size: 20px;
  color: #f5f5f5;
  font-weight: 500;
  position: relative;
}

.right-panel-text-status {
  margin-top: 1rem;
  margin-left: 1rem;
  margin-right: 1rem;
  font-size: 10px;
  color: #f5f5f5;
  font-weight: 500;
}


.right-panel-text-content {
  display: inline-block;
  font-size: 14px;
  margin-left: 0.2rem;
}


.right-panel-alarm-sub-text {
  font-size: 15px;
  color: #f5f5f5;
  font-weight: 500;
}

.right-panel-alarm-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-row-gap: 9px;
  grid-column-gap: 8px;
}

.pod-detail-right-panel-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-row-gap: 9px;
  grid-column-gap: 32px;
}

.right-panel-alarm-sub-container {
  border-radius: 5px;
  background-color: #202020;
  height: 50px;
  padding: 16px 16px 15px 16px;
}

.right-panel-alarm-position-common {
  position: relative;
}

.right-panel-alarm-critical {
  .right-panel-alarm-position-common
}

.right-panel-alarm-major {
  .right-panel-alarm-position-common
}

.right-panel-alarm-minor {
  .right-panel-alarm-position-common
}

.right-panel-alarm-info {
  .right-panel-alarm-position-common
}

.right-panel-alarm-common {
  content: "";
  display: inline-block;
  width: 63px;
  height: 48px;
  background-size: cover;
  position: absolute;
  top: 50%;
  right: 0px;
  transform: translateY(-50%);
}

.right-panel-alarm-critical::after {
  .right-panel-alarm-common;
  background-image: url("../../../apps/businesstopo/assets/site_critical.svg");
}

.right-panel-alarm-major::after {
  .right-panel-alarm-common;
  background-image: url("../../../apps/businesstopo/assets/site_major.svg");
}

.right-panel-alarm-minor::after {
  .right-panel-alarm-common;
  background-image: url("../../../apps/businesstopo/assets/site_minor.svg");
}

.right-panel-alarm-info::after {
  .right-panel-alarm-common;
  background-image: url("../../../apps/businesstopo/assets/site_info.svg");
}

.eui_process_bar_label_after {
  display: none;
}

.right-panel-alarm-container > span {
  display: inline-block;
}


.icon_div_close {
  display: inline-block;
  cursor: pointer;
  position: absolute;
  top: 6px;
  right: 0px;
  width: 16px;
  height: 16px;
  background: url("../../../apps/businesstopo/assets/pannel_close.svg");
}


.right-panel-kpi-child {
  display: inline-block;
  margin-top: 16px;
  margin-right: 10px;
  line-height: 14px;
  margin-left: 5px;
}

.right-panel-kpi-cricle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.right-panel-kpi-green {
  .right-panel-kpi-cricle;
  background-color: #00a874;
}

.right-panel-kpi-orange {
  .right-panel-kpi-cricle;
  background-color: #FF8000;
}

.right-panel-kpi-red {
  .right-panel-kpi-cricle;
  background-color: #E54545;
}

.siteChatCls {
  width: 100%;
  min-height: 156px;
  height: calc(100% - 20px);
  display: inline-block;
}


.kpi_title_samll_text {
  margin-left: 0.8rem;
  margin-top: 1rem;
  font-size: 16px;
  color: #F5F5F5;
  font-weight: 500;
}

.line_chart_tab {
  .eui_tab_title.display {
    display: inline-block;
    max-width: 100px;
  }
}



.topo-click-scale {
  &:active:hover {
    transform: scale(1.03, 0.97);
  }

  &:hover {
    cursor: pointer;
  }

  user-select: none;
}

.site-right-panel {
  .progress {
    margin-top: 9px;
    //  padding-bottom: 32px;
    display: flex;
    width: 100%;
    margin-left: 5px;
    //gap: 3px;

    .progressFirst {
      flex: 20;
      height: 4px;
      background: #E54545;
      border-radius: 2px;
      margin-right: -5px;
    }

    .progressSecond {
      flex: 80;
      height: 4px;
      background: #676767;
      border-radius: 2px;
    }
  }
}

.mo_pod_text {
  font-size: 14px;
  color: #F5F5F5;
  display: inline-block;
}

.icon_moType_back_btn {
  width: 16px;
  height: 16px;
  background: url('../../../apps/businesstopo/assets/moTypeDrill/back.svg') no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: inline-block;
  top: 2px;
  cursor: pointer;
}

.moType_back_btn {
  position: absolute;
  display: inline-block;
  top: 80px;
  left: 16px;
  z-index: 999;
}

canvas {
  outline: none;
}

.no_data_tip {
  font-size: 20px;
  font-weight: bold;
  white-space: normal;
  word-wrap: break-word;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin-top: 20%;

  p {
    max-width: 800px;
    color: #131313;
  }
}

.eui_Crumbs a[class=eui_Crumbs_link] {
  color: #c1bebe
}

.eui_Crumbs ul li:last-child a {
  color: #ffffff
}

#dvtopoLoad{
  z-index: 999999;
  position: relative;
  .euiGlobal_loading_content_cls{
    top:45%
  }
}
