/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import {registerResource} from '../../../commonUtil';
import { jumpToPMHistoryDataPage } from '@util/tools';
import i18n from '../locales';

registerResource(i18n, 'businessTopo');

export const updateStorage = businessId => {
  let {businessData, mainBussiness} = JSON.parse(sessionStorage.getItem('topoSession'));
  let nowBusiness = businessData.find(item => item.businessId === parseInt(businessId));
  mainBussiness[nowBusiness.businessType] = nowBusiness;
  setSessionData({
    mainBussiness,
    currentBusinessId: businessId,
  });
};

export const getAlarmDataBySiteId = (alarmDataList, id) => {
  const res = [];

  for (let alarm of alarmDataList) {
    let alarmData = {
      category: alarm.category,
      alarmTotalCount: 0,
      alarmLevelList: [
        {
          level: 1,
          count: 0,
        },
        {
          level: 2,
          count: 0,
        },
        {
          level: 3,
          count: 0,
        },
        {
          level: 4,
          count: 0,
        },
      ],
      alarmRecordList: [],
    };
    for (let record of alarm.alarmRecordList) {
      if (record.siteName === id) {
        alarmData.alarmRecordList.push(record);
        alarmData.alarmTotalCount++;
        let alarmLevel = alarmData.alarmLevelList.find(item => item.level === parseInt(record.alarmLevel));
        alarmLevel.count++;
      }
    }
    res.push(alarmData);
  }
  return res;
};

export const getUrlParam = key => {
  let urlParam = location.href.match(/&(\S*)/g)[0];
  let arr = urlParam.split('&');
  let allParam = {};
  arr.map(param => {
    let index = param.indexOf('=');
    if (index > 0) {
      let key = param.slice(0, index);
      allParam[key] = param.slice(index + 1, param.length);
    }
  });
  return allParam[key] || '';
};

export const deepClone = obj => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  let result;
  if (Array.isArray(obj)) {
    result = [];
    for (let i = 0; i < obj.length; i++) {
      result.push(deepClone(obj[i]));
    }
  } else {
    result = {};
    Object.keys(obj).forEach(key => result[key] = deepClone(obj[key]));
  }
  return result;
};

export const setSessionData = param => {
  sessionStorage.setItem(
    'topoSession',
    JSON.stringify({
      ...JSON.parse(sessionStorage.getItem('topoSession') || '{}'),
      ...param,
    })
  );
};

export const getSessionData = key => {
  let obj = JSON.parse(sessionStorage.getItem('topoSession') || '{}');
  return obj[key];
};

let timeout = null;
let lastRun = 0;
export const throttle = (func, wait) => {
  return function (...args) {
    const now = Date.now();
    if (now - lastRun >= wait) {
      func(...args);
      lastRun = now;
    } else {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func(...args);
        lastRun = Date.now();
      }, wait - (now - lastRun));
    }
  };
};

// 处理跳转性能页面的数据
export const handleJumpPageData = dataArr => {
  const indicatorArr = [];

  const inputArr = dataArr.map(item => ({
    dnList: item.dnList,
    measTypeKey: item.measTypeKey,
    measUnitKey: item.measUnitKey,
    originalValue: item.originalValue,
    moType: item.moType,
  }));

  for (let i = 0; i < inputArr.length; i++) {
    let indicator = inputArr[i];
    if (indicator.dnList?.length === 0) {
      continue;
    }

    if (indicator.dnList.length > 1) {
      let tempData = JSON.parse(JSON.stringify(indicator));
      indicator.dnList.map(item => {
        tempData.dn = item;
        indicatorArr.push(JSON.parse(JSON.stringify(tempData)));
      });
    } else {
      indicator.dn = indicator.dnList[0];
      indicatorArr.push(indicator);
    }
  }

  jumpToPMHistoryDataPage(indicatorArr, dataArr[0].startTime, dataArr[0].endTime);
};
