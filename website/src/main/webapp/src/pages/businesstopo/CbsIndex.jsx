/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import React, {useEffect, useContext, useState, useRef} from 'react';
import {BUSINESS_TOPO_CONTEXT} from './const';
import RightPanel from './components/RightPanel';
import i18n from './locales';
import {registerResource, setHelpId, setTabTitle} from '../../commonUtil';
import TimeLinePanel from '@pages/businesstopo/components/timeline/TimeLinePanel';
import {OVER_VIEW} from './const/timeLine';
import ViewToggle from './components/ViewToggle';
import Toolbar from './components/toolbar/Toolbar';
import Progress from './components/grayscale/progress';
import {$t} from '@util';
import {
  destroyW3D, initW3D,
  resizeW3D,
  getInitData,
  update,
} from '@pages/businesstopo/components/OverviewPage/overview3D';
import {setSessionData} from '@pages/businesstopo/util';
import {getSolutionData, getAllBussinessData} from '@pages/businesstopo/api';
import eventBus from '@pages/businesstopo/a3dPages/bus';

function CBSTopo({nowSolutionData, showGray, setShowGray}) {
  registerResource(i18n, 'businessTopo');
  setHelpId('com.huawei.dvtopo.business');
  setTabTitle($t('business_layer_title'));
  const {state, dispatch} = useContext(BUSINESS_TOPO_CONTEXT);
  const intervalId = useRef(0);
  const initFlag = useRef(0);
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [show, setShow] = useState(true);
  let overViewData = useRef({});
  const w3dContainerRef = useRef();
  const {showKPIPanel} = state;
  useEffect(() => {
    if (!show) {
      location.replace('/eviewwebsite/index.html#path=/businesstopo/database');
    }
  }, [show]);
  useEffect(async() => {
    if (initFlag.current !== 0) {
      let param = {timestamp: 0};
      let realTimesolutionData = await getSolutionData(param, res => res);
      if (realTimesolutionData.data.length > 0) {
        const oldState = state.isImporting;
        const oldSlutionId = state.solutionId;
        dispatch({
          solutionId: realTimesolutionData.data[0].solutionId,
          dataShow: true,
          dataDaseShow: realTimesolutionData.data[0].displayDatabaseView !== 'false',
          isImporting: realTimesolutionData.data[0].isImporting === true ? true : false,
        });
        const solutionEqual = oldSlutionId !== realTimesolutionData.data[0].solutionId;
        const stateEqual = (oldState !== realTimesolutionData.data[0].isImporting && realTimesolutionData.data[0].isImporting === false);
        if (solutionEqual || stateEqual) {
          location.reload();
        }
        setShowGray(realTimesolutionData.data[0]?.solutionType === 1);
      } else {
        dispatch({
          isImporting: false,
          dataShow: false,
        });
      }
    }
    const updateData = await getInitData(initFlag.current, state.solutionId);
    if (initFlag.current !== 0 && JSON.stringify(overViewData.current) !== JSON.stringify(updateData)) {
      overViewData.current = updateData;
      update(updateData);
    }
  }, [refreshFlag]);

  useEffect(() => {
    eventBus.addListener('overview_manualRefresh', () => {
      let topoSession = JSON.parse(
        sessionStorage.getItem('topoSession') || '{}',
      );
      if (topoSession.selectedTime > 0) {
        return;
      }
      setRefreshFlag(new Date().getTime());
    });
    destroyW3D();
    stopRefresh();
    let topoSession = JSON.parse(
      sessionStorage.getItem('topoSession') || '{}',
    );
    initFlag.current = 0;
    if (!topoSession.selectedTime) {
      setSessionData({
        selectedTime: 0,
        isTimeTrack: false,
      });
    }
    if (topoSession.selectedTime > 0) {
      return;
    }
    let param = {
      timestamp: topoSession.selectedTime ? topoSession.selectedTime : 0,
    };
    if (nowSolutionData.length > 0) {
      if (nowSolutionData[0].solutionType === 3) {
        return;
      }
      param.solutionId = nowSolutionData[0].solutionId;
      getAllBussinessData(param, res2 => {
        setSessionData({
          solutionId: nowSolutionData[0].solutionId,
          selectSiteId: -1,
        });
        let dataValue = {
          solutionData: nowSolutionData,
          businessDataResult: res2.data,
        };
        initW3D(w3dContainerRef, dispatch, param, dataValue);
        dispatch({
          solutionId: nowSolutionData[0].solutionId,
          solutionName: nowSolutionData[0].solutionName,
          dataShow: true,
          dataDaseShow: nowSolutionData[0].displayDatabaseView !== 'false',
          selectedTime: topoSession.selectedTime,
          isTimeTrack: topoSession.isTimeTrack,
          solutionData: nowSolutionData,
          businessData: res2.data,
        });
        initFlag.current = 1;
      });
    } else {
      dispatch({
        showMessage: false,
      });
      return;
    }
    startRefresh();
    return () => {
      clearInterval(intervalId.current);
      destroyW3D();
    };
  }, [nowSolutionData]);
  // 时间回溯
  const timeTrack = time => {
    let param = {timestamp: time};
    setSessionData({
      isTimeTrack: true,
      selectedTime: time,
    });
    stopRefresh();
    getSolutionData(param, res => {
      if (res.data.length > 0) {
        param.solutionId = res.data[0].solutionId;
        setShowGray(res.data[0]?.solutionType === 1);
        getAllBussinessData(param, res2 => {
          setSessionData({
            solutionId: res.data[0].solutionId,
            selectSiteId: -1,
            isTimeTrack: true,
            selectedTime: time,
          });
          dispatch({
            currentBusinessId: -1,
            solutionId: res.data[0].solutionId,
            dataShow: true,
            dataDaseShow: res.data[0].displayDatabaseView !== 'false',
            isTimeTrack: true,
            solutionData: res.data,
            businessData: res2.data,
            selectedTime: time,
          });
          destroyW3D();
          let dataValue = {
            solutionData: res.data,
            businessDataResult: res2.data,
          };
          initW3D(w3dContainerRef, dispatch, param, dataValue);
        });
      } else {
        destroyW3D();
        dispatch({
          isTimeTrack: true,
          selectedTime: time,
          showMessage: true,
        });
      }
    });
  };

  // 退出时间回溯模式
  const backTimeTrack = () => {
    startRefresh();
    let param = {timestamp: 0};
    getSolutionData(param, res => {
      if (res.data.length > 0) {
        param.solutionId = res.data[0].solutionId;
        getAllBussinessData(param, res2 => {
          setSessionData({
            solutionId: res.data[0].solutionId,
            selectSiteId: -1,
            isTimeTrack: false,
            selectedTime: 0,
          });
          dispatch({
            currentBusinessId: -1,
            solutionId: res.data[0].solutionId,
            dataShow: true,
            dataDaseShow: res.data[0].displayDatabaseView !== 'false',
            isTimeTrack: false,
            solutionData: res.data,
            businessData: res2.data,
            selectedTime: 0,
          });
          destroyW3D();
          let dataValue = {
            solutionData: res.data,
            businessDataResult: res2.data,
          };
          initW3D(w3dContainerRef, dispatch, param, dataValue);
        });
      } else {
        destroyW3D();
        dispatch({
          isTimeTrack: false,
          selectedTime: 0,
          showMessage: false,
        });
      }
    });
  };

  const startRefresh = () => {
    let id = setInterval(() => {
      setRefreshFlag(new Date().getTime());
    }, 30000);
    intervalId.current = id;
  };

  useEffect(() => {
    resizeW3D();
  }, [showKPIPanel]);

  const stopRefresh = () => {
    clearInterval(intervalId.current);
  };

  return (
    <BUSINESS_TOPO_CONTEXT.Provider value={{state, dispatch}}>
      <div id="business_topo" style={{height: '100%', width: '100%', 'backgroud-color': 'black'}}>
        <div
          id="topology3d_main_container"
          className={
            showKPIPanel ?
              'topology3d_main_container w3d_container' :
              'topology3d_main_container_full w3d_container'
          }
          ref={w3dContainerRef}
        />
        <Toolbar showRightPanel={showKPIPanel} />
        <TimeLinePanel
          pageType={OVER_VIEW}
          renderTopology={timeTrack}
          backTimeTrack={backTimeTrack}
        />
        <RightPanel
          refreshFlag={refreshFlag}
        />
        {
          showGray && <Progress />
        }
        {(state.dataDaseShow && !state.forceDataBaseHide) && <ViewToggle show={show} setShow={setShow} />}
      </div>
    </BUSINESS_TOPO_CONTEXT.Provider>
  );
}

export default CBSTopo;
