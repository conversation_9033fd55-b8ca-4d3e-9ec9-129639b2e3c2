import { request } from '@util';
import { appendUrlFromParam } from '../util';

const getCrossTreeUrl = '/rest/dvtopowebsite/v1/topo/cross/tree';
const getTopologyUrl = '/rest/dvtopowebsite/v1/topo/cross/solution';
const getLinksUrl = '/rest/dvtopowebsite/v1/topo/cross/links';

export const getCrossTree = (success, error) => request.get(getCrossTreeUrl, success, error);

export const getTopologyData = (params, success, error) => {
  let url = appendUrlFromParam(getTopologyUrl, params);
  return request.get(url, success, error);
};

export const getLinks = (params, success) => {
  let url = appendUrlFromParam(getLinksUrl, params);
  return request.get(url, success);
};
