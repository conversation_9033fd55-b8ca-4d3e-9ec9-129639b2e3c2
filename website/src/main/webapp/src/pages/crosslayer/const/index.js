/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React from 'react';
import { $t, registerResource } from '@util';
import i18n from '../locales';
import solutionNe from '@images/solution_ne.svg';
import appNe from '@images/business_ne.svg';
import vmNe from '@images/vm_ne.svg';
import hostNe from '@images/host_ne.svg';

registerResource(i18n, 'crossLayer');

export const CrossLayerContext = React.createContext(null);

export const NE_WIDTH = 30;

export const NE_HEIGHT = 30;
export const PAGE_SIZE = 10;

export const LAYER_MAX_NE_NUM = 75;
export const LAYER_MAX_NE_NUM_50 = 50;

export const MAX_LINE_NE_NUM_25 = 25;
export const MAX_LINE_NE_NUM_17 = 17;

// 网元纵坐标间距
export const NE_ROW_GAP = 60;

// 网元横坐标间距
export const NE_COL_GAP = 80;

// 网元y坐标修正
export const REMEDIATE_NE_Y = 20;

// 网元x坐标修正
export const REMEDIATE_NE_X = 40;

// 钻取App层级
export const DRILL_APP_LEVELS = [3, 4, 5, 6];

// App层级
export const APP_LEVELS = [2, 3, 4, 5, 6];

export const LEGEND_INFO = [
  { text: $t('solution'), img: solutionNe },
  { text: $t('business'), img: appNe },
  { text: $t('vm'), img: vmNe },
  { text: $t('host_group'), img: hostNe },
];

export const LEVEL_IMAGE_MAP = {
  solution_ne: 'DV_image_1',
  business_ne: [
    'DV_image_2',
    'DV_image_3',
    'DV_image_4',
    'DV_image_5',
    'DV_image_6',
    'DV_image_7',
  ],
  vm_ne: 'DV_image_8',
  host_ne: 'DV_image_9',
};

export const ALARM_LEVELS = ['critical', 'major', 'minor', 'warning'];

export const CLIENT_SCALE_OPTION = [
  { text: '200%', value: 2 },
  { text: '180%', value: 1.8 },
  { text: '160%', value: 1.6 },
  { text: '140%', value: 1.4 },
  { text: '120%', value: 1.2 },
  { text: '100%', value: 1 },
  { text: '80%', value: 0.8 },
  { text: '60%', value: 0.6 },
  { text: '40%', value: 0.4 },
];

export const MAX_ZOOM = 2;
export const MIN_ZOOM = 0.4;
export const ZOOM_STEP = 0.2;

export const DEFAULT_SCENE_OPTION = { text: $t('default_scene'), value: 0 };

export const MIN_PANEL_SIZE = 48;

export const MAX_PANEL_SIZE = 1470;

export const DEFAULT_PANEL_SIZE = 320;

export const DEFAULT_SCALE = 0.8;

export const SUB_DEFAULT_SCALE = 1;

export const VIRTUAL = 'VIRTUAL';
export const VIRTUAL_NODE_HOST = 'VIRTUAL_NODE_HOST';
