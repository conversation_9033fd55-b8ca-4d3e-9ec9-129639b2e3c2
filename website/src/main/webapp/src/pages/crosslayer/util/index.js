import _initTopology, {
  setTopologyContainerId as _setTopologyContainerId,
} from './initTopology';

export const initTopology = _initTopology;

export const setTopologyContainerId = _setTopologyContainerId;

export const clearTopology = eleId => {
  const bottomPanel = document.getElementById(eleId);
  bottomPanel.innerHTML = '';
};

export const appendUrlFromParam = (url, params) => {
  let _url = url;
  Object.keys(params).forEach((key, index) => {
    if (index === 0) {
      _url += `?${key}=${params[key]}`;
    } else {
      _url += `&${key}=${params[key]}`;
    }
  });
  return _url;
};

export const debounce = (callback, wait) => {
  let timeout = null;
  return function() {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      callback();
      timeout = null;
    }, wait);
  };
};

export const compareFun = (a, b) => {
  if (a > b) {
    return 1;
  } else if (a === b) {
    return 0;
  } else {
    return -1;
  }
};

export const formatTopologyData = _topologyInfo => {
  const topologyInfo = _topologyInfo || {};
  const nodes = topologyInfo.nodes || [];
  const alarms = topologyInfo.alarms || [];
  const scenes = topologyInfo.scenes || [];
  const aggregateAlarm = topologyInfo.aggregateAlarm || [];
  const groups = topologyInfo.groups || [];
  const links = topologyInfo.links || [];
  return { nodes, alarms, scenes, aggregateAlarm, groups, links };
};

export const deepCopyFun = data => {
  let copyDta = {};
  $.extend(true, copyDta, data);
  return copyDta;
};

export const createNewNode = (id, image, parentId, name) => {
  let virtualNode = {
    id: id,
    image: image,
    isOpen: false,
    name,
    properties: {
      parentId: parentId,
    },
  };
  return virtualNode;
};

export const splitLayeToNumber = layer => Number(layer.slice(layer.lastIndexOf('_') + 1));

const getCookie = name => {
  const reg = new RegExp(`(^| )${name}=([^;]*)(;|$)`);
  let arr = document.cookie.match(reg);
  if (arr) {
    return unescape(arr[2]);
  } else {
    return null;
  }
};

// 根据系统设置的格式对时间戳格式化
export const dateTimeFormat = timestamp => {
  if (!timestamp) {
    return null;
  }

  let formatTimeStamp = parseInt(timestamp);
  let timezoneOffset = getCookie('timezoneoffset');

  // 消除客户端与UTC时间的误差
  formatTimeStamp += new Date().getTimezoneOffset() * 60 * 1000;

  // UTC时间+服务器时区偏差
  formatTimeStamp += timezoneOffset * 60 * 1000;
  let date = new Date(formatTimeStamp);

  // 从cookie中获取时间格式并做处理
  let format = getCookie('format');
  let time_lang = getCookie('user_time_a_lang');
  if (!format) {
    format = 'yyyy-MM-dd HH:mm:ss';
  } else {
    format = decodeURIComponent(format).substr(0, 19);
  }
  let result = format;

  const o = {
    'M+': date.getMonth() + 1, 
    'd+': date.getDate(),
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
  };
  if (/(y+)/.test(result)) {
    result = result.replace(
      RegExp.$1,
      (`${date.getFullYear()}`).substr(4 - RegExp.$1.length),
    );
  }
  for (let k in o) {
    if (new RegExp(`(${k})`).test(result)) {
      result = result.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : (`00${o[k]}`).substr((`${o[k]}`).length),
      );
    }
  }
  if (format.indexOf('HH') !== -1) {
    return result;
  }

  if (date.getHours() > 12) {
    result = `${result} ${DATE_TIME_FORMAT[time_lang].PM}`;
  } else {
    result = `${result} ${DATE_TIME_FORMAT[time_lang].AM}`;
  }
  return result;
};
