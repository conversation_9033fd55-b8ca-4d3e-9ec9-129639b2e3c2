import { LAYER_WIDTH } from '../const';

class LayerLabelAttachment {
  name = 'layerLabelAttach';

  position = 'auto';

  label = '';

  width = LAYER_WIDTH - 50;

  height = 20;

  textAlign = 'left';

  isVisible = function() {
    return true;
  };

  setTextAlign = function(textAlign) {
    this.textAlign = textAlign;
  };

  setLabel = function(label) {
    this.label = label;
  };

  paint = function(cv, rect, ele) {
    cv.save();
    const layerPosition = ele.getPosition();
    const attachRect = {
      x: layerPosition.x + 30,
      y: layerPosition.y + ele.getHeight() - this.height - 15,
      width: rect.width,
      height: rect.height,
    };
    let font = '16px Microsoft YaHei, arial, tahoma, helvetica, sans-serif';
    window.DrawUtil.drawText(
      cv,
      this.label,
      attachRect,
      font,
      '#0e0e0e',
      this.textAlign,
      1,
      0,
      'rgba(255,255,255,0.12)',
    );
    cv.restore();
  };
}

export default LayerLabelAttachment;

