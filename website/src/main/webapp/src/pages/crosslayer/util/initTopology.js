/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React from 'react';
import ReactDOM from 'react-dom';
import { Menu } from 'eview-ui';
import { $t } from '@util';
import {
  compareFun,
  deepCopyFun,
  createNewNode,
  splitLayeToNumber,
  dateTimeFormat,
} from './index';
import imageTypesConstants from './imageTypesConstants';
import {
  ALARM_LEVELS,
  LAYER_MAX_NE_NUM,
  LAYER_MAX_NE_NUM_50,
  MAX_LINE_NE_NUM_25,
  MAX_LINE_NE_NUM_17,
  LEVEL_IMAGE_MAP,
  NE_COL_GAP,
  NE_HEIGHT,
  NE_ROW_GAP,
  NE_WIDTH,
  REMEDIATE_NE_X,
  REMEDIATE_NE_Y,
  VIRTUAL,
} from '../const';

let networkInfo;
let mianPageData = {};
let onClickBubble;
let goDetailHandler;
let containerId;
let clickNodeHandler;
let handleVirtualNodeClick;
let dataInfo;
let subDataInfo;
let refresh = true;
let selectedNode = null;
let topWidth = 1200;
let bottomWidth = 1600;
let singleRowNum = 17;
let themeChange = null;
let replcaeData = {};
let isAllDataLoading = false;

const showTips = params => {
  if (menuContextExist()) {
    return new Promise((resolve, reject) => {
      resolve();
    });
  }
  return new Promise((resolve, reject) => {
    let tipsData = networkInfo;
    if (getTopologyContainerId() !== 'subTopologyBox') {
      tipsData = mianPageData;
    }
    const {
      name,
      ipAddress,
      moTypeName,
      runningStatus,
      adminStatus,
      properties,
      createTime,
      hostInfo,
    } = tipsData.nodes.find(item => item.id === params.id);
    let level = splitLayeToNumber(properties.parentId);
    let alarmState = { critical: 0, major: 0, minor: 0, warning: 0 };
    let aggregateAlarm = { critical: 0, major: 0, minor: 0, warning: 0 };
    const alarmExists = tipsData.alarms.some(obj => obj.id === params.id);
    if (alarmExists) {
      alarmState = tipsData.alarms.find(item => item.id === params.id)
        .alarmState;
    }
    const aggreExists = tipsData.aggregateAlarm.some(
      obj => obj.id === params.id,
    );
    if (aggreExists) {
      aggregateAlarm = tipsData.aggregateAlarm.find(
        item => item.id === params.id,
      ).alarmState;
    }

    // 只有APP第一层才展示子告警信息
    const aggregateAlarmInfo =
      level === 2 && aggregateAlarm ? aggregateAlarm : null;
    let fullNameInfo = [$t('tooltip_name'), name];
    let ipAddressInfo = [$t('tooltip_ip'), ipAddress];
    let moTypeNameInfo = [$t('tooltip_mo_type'), moTypeName];
    let statusInfo = [$t('tooltip_status'), runningStatus];
    let adminStatusInfo = [$t('tooltip_admin_status'), adminStatus];
    let createTimeInfo = [
      $t('tooltip_create_time'),
      dateTimeFormat(createTime),
    ];
    const allContentStr = getAllContentStr([
      fullNameInfo,
      ipAddressInfo,
      moTypeNameInfo,
      statusInfo,
      adminStatusInfo,
      createTimeInfo,
    ]);
    let tips = `<div class="tooltip_box">
        ${allContentStr}
        ${getAlarmStr($t('tooltip_alarm'), alarmState)}
        ${getAlarmStr($t('tooltip_sub_alarm'), aggregateAlarmInfo)}
        ${getHostStr($t('tooltip_host_info'), hostInfo)}
      </div>`;
    setTimeout(() => resolve(tips), 10);
  });
};
const getIcons = () => {
  let icons = [];
  for (const key of Object.keys(imageTypesConstants)) {
    let newkey = key;
    if (newkey.startsWith('up')) {
      newkey = 'up';
      if (key !== `up_${piu.get('theme')}`) {
        continue;
      }
    }
    if (newkey.startsWith('down')) {
      newkey = 'down';
      if (key !== `down_${piu.get('theme')}`) {
        continue;
      }
    }
    const icon = {
      className: newkey,
      idata: {
        data: imageTypesConstants[key],
      },
    };
    icons.push(icon);
  }
  return icons;
};

const getAllContentStr = arr => {
  let result = '';
  arr.forEach(item => {
    const content = getContentStr(item);
    result += content;
  });
  return result;
};

const getContentStr = contentInfo => {
  const [title, name] = contentInfo;
  if (!name) {
    return '';
  }
  return `
    <div class="tooltip_title">${title}:</div>
    <div class="tooltip_name">${name}</div>`;
};

const getHostStr = (title, hostInfo) => {
  if (!hostInfo || $.isEmptyObject(hostInfo)) {
    return '';
  }
  let tooltip_name = $t('tooltip_name');
  let tooltip_ip = $t('tooltip_ip');
  let tooltip_mo_type = $t('tooltip_mo_type');
  return `
    <div class="tooltip_title">${title}:</div>
    <ul class="host_content">
         <li class="tooltip_name"><span>${tooltip_name}=</span><span>${hostInfo.name}</span></li>
        <li class="tooltip_name"><span>${tooltip_ip}=</span><span>${hostInfo.ip}</span></li>
        <li class="tooltip_name"><span>${tooltip_mo_type}=</span><span>${hostInfo.type}</span></li>
    </ul>`;
};

const getAlarmStr = (title, alarmInfo) => {
  if (!alarmInfo || $.isEmptyObject(alarmInfo)) {
    return '';
  }
  const { critical, major, minor, warning } = alarmInfo;
  let criticalStr = $t('critical');
  let majorStr = $t('major');
  let minorStr = $t('minor');
  let warningStr = $t('warning');
  return `
    <div class="tooltip_title">${title}:</div>
    <ul class="alarm_content">
        <li class="alarm_item"><span>${criticalStr}=</span><span>${critical}</span></li>,
        <li class="alarm_item"><span>${majorStr}=</span><span>${major}</span></li>,
        <li class="alarm_item"><span>${minorStr}=</span><span>${minor}</span></li>,
        <li class="alarm_item"><span>${warningStr}=</span><span>${warning}</span></li>
    </ul>`;
};

const getLinks = data => {
  const links = [];
  data.forEach(item => {
    const link = {
      id: item.id,
      resId: item.id,
      fromNodeId: item.fromNodeId,
      toNodeId: item.toNodeId,
      properties: {
        'com.huawei.topo.direction': 4,
      },
    };
    links.push(link);
  });
  return links;
};

const getLayersByGroups = groups => {
  let isLightDay = piu.get('theme') === 'lightday';
  const layers = [];
  let height = getTopologyContainerId() === 'subTopologyBox' ? 80 : 200;

  groups.forEach((item, index) => {
    const layerInfo = {
      id: item.id,
      x: 50,
      y: index * (height + 32),
      name: item.name,
      styles: {
        // // 上边宽
        topWidth,

        // 下边宽
        bottomWidth,
        height,

        // 面板底座颜色
        baseColor: isLightDay ? '#ffffff' : '#4d4343',

        // 底边宽度，建议取值[0, 30]
        baseHeight: 2,

        // 面板颜色
        fillColor: isLightDay ? '#ffffff' : '#4d4343',

        // 面板透明度
        fillAlpha: isLightDay ? 1 : 0.8,

        // 是否显示边框
        isShowBorder: true,

        // 面板边框颜色
        borderColor: isLightDay ? '#ffffff' : '#000000',
      },
    };
    layers.push(layerInfo);
  });
  return layers;
};

export const getCurImage = imageType => Object.keys(LEVEL_IMAGE_MAP).find(key => {
  const curImageType = LEVEL_IMAGE_MAP[key];
  return (
    (curImageType instanceof Array && curImageType.includes(imageType)) ||
    (typeof curImageType === 'string' && curImageType === imageType)
  );
});

// 每层最多展示75个网元
const filterNodes = (nodes, alarms, aggregateAlarm) => {
  const idSet = new Set();
  alarms.forEach(obj => {
    idSet.add(obj.id);
  });
  aggregateAlarm.forEach(obj => {
    idSet.add(obj.id);
  });
  let filterNodeArr = [];
  const overflowGroups = [];
  const groupedNodes = nodes.reduce((groups, obj) => {
    const id = obj.properties.parentId;
    if (!groups[id]) {
      groups[id] = [];
    }
    groups[id].push(obj);
    return groups;
  }, {});
  Object.keys(groupedNodes).forEach(id => {
    let newNodes = groupedNodes[id];
    const curNodeLevel = Number(id.slice(id.lastIndexOf('_') + 1));
    let lastNode;
    if (replcaeData[curNodeLevel]) {
      lastNode = newNodes.find(item => item.id === replcaeData[curNodeLevel]);
    }
    sortNodes(newNodes);
    if (newNodes.length > LAYER_MAX_NE_NUM) {
      overflowGroups.push(id);

      // 根据有没有告警排序
      newNodes = newNodes.sort((a, b) => {
        if (idSet.has(a.id) && !idSet.has(b.id)) {
          return -1;
        } else if (idSet.has(a.id) && idSet.has(b.id)) {
          return 0;
        } else {
          return 1;
        }
      });
      newNodes = newNodes.slice(0, LAYER_MAX_NE_NUM - 1);

      if (replcaeData[curNodeLevel]) {
        sortNodes(newNodes);
        newNodes[newNodes.length - 1] = lastNode;
      }
      newNodes.push(
        createNewNode(VIRTUAL + id, newNodes[0].image, id, $t('topo_show_all')),
      );
    }
    filterNodeArr = filterNodeArr.concat(newNodes);
  });
  return { filterNodeArr, overflowGroups };
};

function setLayerSize(nodes) {
  const groupedData = nodes.reduce((acc, item) => {
    const key = item.image;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});

  let isOver = false;

  Object.keys(groupedData).forEach(key => {
    if (groupedData[key].length > LAYER_MAX_NE_NUM_50) {
      isOver = true;
    }
  });
  if (isOver) {
    topWidth = 1850;
    bottomWidth = 2200;
    singleRowNum = MAX_LINE_NE_NUM_25;
  } else {
    topWidth = 1200;
    bottomWidth = 1600;
    singleRowNum = MAX_LINE_NE_NUM_17;
  }
}

const parseNetworkData = isSub => {
  const { links, groups, nodes, alarms, aggregateAlarm } = networkInfo;
  const { filterNodeArr } = filterNodes(nodes, alarms, aggregateAlarm);
  setLayerSize(nodes);
  const layers = getLayersByGroups(groups);
  const newLinks = getLinks(links);
  let newNodes = parseNodes(filterNodeArr, alarms, aggregateAlarm, layers);
  const icons = getIcons();
  let data = {
    globalStyles: {},
    layers,
    nodes: newNodes,
    links: newLinks,
    icons,
  };
  if (isSub) {
    subDataInfo = data;
  } else {
    dataInfo = data;
  }
};

const addOverlay = () => {
  let { nodes } = dataInfo;
  nodes.forEach(item => {
    let image = item.bubbleImage === undefined ? 'down' : item.bubbleImage;
    if (item.isOpen) {
      piu.emit('DynamicMap.addOverlay', {
        overlayId: item.id,
        nodes: [
          {
            resId: item.id,
            render: [
              {
                type: 'EXTENDATTACHMENT',
                value: JSON.stringify({
                  attachName: 'ImageAttachment',
                  attachParam: {
                    position: 'autoRight',
                    width: 16,
                    height: 16,
                    yoffset: 0,
                    xoffset: 0,
                    image,
                    name: 'ImageAttachment',
                  },
                }),
              },
            ],
          },
        ],
      });
    }
  });
};

const clickMenuOne = id => {
  $('#msgContextMenu').hide();
  setAllEleLoding(false);
  let clickData = networkInfo;
  if (getTopologyContainerId() !== 'subTopologyBox') {
    clickData = mianPageData;
  }
  let node = clickData.nodes.find(item => item.id === id);
  const curNodeLevel = Number(
    node.image.slice(node.image.lastIndexOf('_') + 1),
  );
  goDetailHandler(id, curNodeLevel);
};

const clickMenuTwo = nodeId => {
  const alarmUrl = `/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&json=true&&nativeMoDn=${encodeURIComponent(
    `value=${nodeId}&operation=in`,
  )}`;
  const alarmWindowJump = window.open(alarmUrl);
  alarmWindowJump.opener = null;
};

const clickMenuThree = nodeId => {
  const configManaUrl = `/moui/business.action?BMEBusiness=moui&moDn=${nodeId}`;
  const configManaJump = window.open(configManaUrl);
  configManaJump.opener = null;
};

// 同一层的网元放在一个数组里
const getNodeLayers = filterNodeArr => {
  const { groups } = networkInfo;
  return groups.map(item => filterNodeArr.filter(node => node.properties.parentId === item.id));
};

// 分配网元，每SINGLE_ROW_MAX_NE_NUM一组
const assignNodes = nodes => {
  const result = [];
  let steps;
  if (MAX_LINE_NE_NUM_17 === singleRowNum) {
    if (nodes.length <= singleRowNum) {
      steps = [singleRowNum];
    } else if (nodes.length <= 2 * singleRowNum - 2) {
      steps = [singleRowNum, singleRowNum - 2];
    } else {
      steps = [singleRowNum - 1, singleRowNum, singleRowNum + 1];
    }
  } else {
    if (nodes.length <= singleRowNum) {
      steps = [singleRowNum];
    } else if (nodes.length <= 2 * singleRowNum + 1) {
      steps = [singleRowNum, singleRowNum + 1];
    } else {
      steps = [singleRowNum - 1, singleRowNum, singleRowNum + 1];
    }
  }
  let start = 0;
  for (let i = 0; i < steps.length; i++) {
    result.push(nodes.slice(start, start + steps[i]));
    start += steps[i];
  }
  return result;
};

// 网元按照名称升序排列
const sortNodes = nodes => {
  nodes.sort((a, b) => compareFun(a.name, b.name));
};

const parseNodes = (filterNodeArr, alarms, aggregateAlarms, layersInfo) => {
  let layMap = {};
  layersInfo.forEach(info => {
    layMap[info.id] = info.y;
  });
  let retNodes = [];
  const layers = getNodeLayers(filterNodeArr);
  const defaultAlarmState = { critical: 0, major: 0, minor: 0, warning: 0 };
  layers.forEach(nodes => {
    const newNodes = assignNodes(nodes);
    const rowHeight = newNodes.length * NE_ROW_GAP + NE_HEIGHT;
    let containerWidth = bottomWidth;
    let containerHeight =
      getTopologyContainerId() === 'subTopologyBox' ? 80 : 200;
    newNodes.forEach((nodeArr, nodeArrIndex) => {
      const rowWidth = NE_COL_GAP * nodeArr.length + NE_WIDTH;
      nodeArr.forEach((item, index) => {
        const alarmState =
          alarms.find(alarm => alarm.id === item.id)?.alarmState ||
          defaultAlarmState;
        const aggregateAlarm =
          aggregateAlarms.find(alarm => alarm.id === item.id)?.alarmState ||
          defaultAlarmState;
        const curAlarmLevel =
          ALARM_LEVELS.find(alarm => (
            Number(alarmState[alarm]) > 0 || Number(aggregateAlarm[alarm]) > 0
          )) || 'warning';
        const curNodeLevel = Number(
          item.properties.parentId.slice(
            item.properties.parentId.lastIndexOf('_') + 1,
          ),
        );
        const image =
          curAlarmLevel === 'warning' ?
            getCurImage(item.image) :
            `${getCurImage(item.image)}_${curAlarmLevel}`;
        const { x: containerX, y: containerY } = {
          x: 50,
          y: layMap[nodes[0].properties.parentId],
        };
        const x =
          containerX +
          index * NE_COL_GAP +
          containerWidth / 2 -
          rowWidth / 2 +
          REMEDIATE_NE_X;
        const y =
          containerY +
          nodeArrIndex * NE_ROW_GAP +
          containerHeight / 2 -
          rowHeight / 2 +
          REMEDIATE_NE_Y;
        const retNode = {
          id: item.id,
          resId: item.id,
          image,
          x,
          y,
          name: item.name,
          bubbleImage: item.bubbleImage,
          isOpen: item.isOpen,
          alarmState,
          fullName: item.name,
          ipAddress: item.ipAddress,
          moTypeName: item.moTypeName,
          runningStatus: item.runningStatus,
          level: curNodeLevel,
          aggregateAlarm,
          properties: {
            layerId: item.properties.parentId,
          },
        };
        retNodes.push(retNode);
      });
    });
  });
  return retNodes;
};

function subThemeHandler(theme) {
  dataInfo.icons = getIcons();
  for (let i = 0; i < dataInfo.layers.length; i++) {
    let layer = dataInfo.layers[i];
    if (theme === 'lightday') {
      layer.styles.borderColor = '#ffffff';
      layer.styles.fillColor = '#ffffff';
      layer.styles.baseColor = '#ffffff';
      layer.styles.fillAlpha = 1;
    } else {
      layer.styles.borderColor = '#000000';
      layer.styles.fillColor = '#4d4343';
      layer.styles.baseColor = '#4d4343';
      layer.styles.fillAlpha = 0.8;
    }
  }
  Prel.autoLoad('topoShowcasePiu', '*').then(() => {
    piu.emit('DynamicMap.render', {
      viewId: 'dvtopoViewId',
      domId: 'bottomPanel',
      topoType: 'DVCrossLayer',
      life: 'create',
      situation: 'DV',
      theme,
      data: {},
      customDataFunc: () => deepCopyFun(dataInfo),
    });
    light();
  });
}

function nodeClickHandler(params) {
  let nodeClickData = networkInfo;
  networkInfo.selectNode = params.id;
  if (getTopologyContainerId() !== 'subTopologyBox') {
    nodeClickData = mianPageData;
  }
  $('#msgContextMenu').hide();
  if (params.id.startsWith(VIRTUAL)) {
    handleVirtualNodeClick(params.id);
    return;
  }
  let node = nodeClickData.nodes.find(item => item.id === params.id);
  const curNodeLevel = Number(
    params.layerId.slice(params.layerId.lastIndexOf('_') + 1),
  );
  if (params.attachName !== 'ImageAttachment') {
    clickNodeHandler(params.id, curNodeLevel, node.bubbleImage);
  } else {
    $('#msgContextMenu').hide();
    let image = node.bubbleImage ? node.bubbleImage : 'down';
    if (image === 'down') {
      networkInfo.nodes.forEach(item => {
        const { parentId } = item.properties;
        const nodeLevel = Number(parentId.slice(parentId.lastIndexOf('_') + 1));
        if (nodeLevel > curNodeLevel) {
          // 将同级的其他网元图标设置为下
          item.bubbleImage = 'down';
        }
      });
      nodeClickData.nodes = networkInfo.nodes;
    }
    onClickBubble(node.bubbleImage, node.id, curNodeLevel);
  }
}

async function allElementHandler(viewId) {
  isAllDataLoading = true;

  //  注册右下角的卫星
  if (viewId === 'dvtopoViewId') {
    piu.emit('DynamicMap.registerToolTipHandler', showTips);
    addOverlay();
  } else {
    piu.emit(
      'DynamicMap.registerToolTipHandler',
      '',
      showTips,
      'dvtopoSubViewId',
    );
  }
  let node =
    networkInfo.selectNode === undefined ?
      selectedNode :
      networkInfo.selectNode;
  if (node !== undefined) {
    piu.emit('DynamicMap.optElements', {
      action: 'select',
      nodes: [
        {
          resId: node,
        },
      ],
      viewId,
      isScrollToVisibleArea: false,
    });
  }
  if (refresh) {
    window.DynamicMap.setZoom('SET', 1, viewId);
    await DynamicMap.refresh({ viewId });
    window.DynamicMap.setZoom('SET', networkInfo.scale, viewId);
    DynamicMap.moveView(
      Math.floor(400 * (1 - networkInfo.scale)),
      Math.floor(100 * (1 - networkInfo.scale)) - 10,
      viewId,
    );
    refresh = false;
  }
}

function themeChangeHandler(theme) {
  let { links } = window.DynamicMap.optElements({
    viewId: 'dvtopoViewId',
    action: 'queryAllEles',
  });
  refresh = true;
  getIcons();
  let isSub = containerId === 'subTopologyBox';
  let showData = isSub ? subDataInfo : dataInfo;
  setLayerSize(showData.nodes);
  showData.layers = getLayersByGroups(showData.layers);
  showData.icons = getIcons();
  Prel.autoLoad('topoShowcasePiu', '*').then(() => {
    piu.emit('DynamicMap.render', {
      viewId: isSub ? 'dvtopoSubViewId' : 'dvtopoViewId',
      domId: containerId,
      topoType: 'DVCrossLayer',
      life: 'create',
      situation: 'DV',
      theme,
      data: {},
      customDataFunc: () => deepCopyFun(showData),
    });
    setTimeout(() => {
      piu.emit('DynamicMap.optElements', {
        action: 'add',
        links,
      });
    }, 100);
    light();
  });

  if (isSub) {
    subThemeHandler(theme);
  }
  themeChange(theme);
}

const attach = () => {
  piu.attach(piu, {
    $stateChange: {
      theme: theme => {
        themeChangeHandler(theme);
      },
    },

    'DynamicMap.backgroundClick':  (params, viewId) =>{
      $('#msgContextMenu').hide();
      networkInfo.selectNode = null;
    },
    'DynamicMap.nodeClick':  (params, viewId) => {
      nodeClickHandler(params);
    },
    'DynamicMap.rightClick':  (params, viewId)=> {
      let clickData = networkInfo;
      if (getTopologyContainerId() !== 'subTopologyBox') {
        clickData = mianPageData;
      }
      if (!params.data.id) {
        $('#msgContextMenu').hide();
        return;
      }
      if (params.data.id.startsWith(VIRTUAL)) {
        $('#msgContextMenu').hide();
        return;
      }
      const position = document.fullscreenElement ? 'fixed' : 'absolute';

      let showMenu1 = false;
      if (viewId !== 'dvtopoSubViewId') {
        let node = clickData.nodes.find(item => item.id === params.data.id);
        const curNodeLevel = Number(
          node.image.slice(node.image.lastIndexOf('_') + 1),
        );
        showMenu1 = curNodeLevel >= 2 && curNodeLevel <= 8;
      }
      $('#msgContextMenu')
        .css('position', position)
        .css('z-index', 11)
        .css('display', 'block')
        .css('left', params.position.clientX + 10)
        .css('top', params.position.clientY + 20);
      let app = document.getElementById('msgContextMenu');
      ReactDOM.render(
        <Menu style={{ width: 256, border: '1px solid #f0f0f0' }}>
          {showMenu1 && (
            <Menu.Item
              key="1"
              onClick={clickMenuOne.bind(null, params.data.id)}
            >
              {$t('view_detail')}
            </Menu.Item>
          )}
          <Menu.Item key="2" onClick={clickMenuTwo.bind(null, params.data.id)}>
            {$t('alarm_management')}
          </Menu.Item>
          <Menu.Item
            key="3"
            onClick={clickMenuThree.bind(null, params.data.id)}
          >
            {$t('config_management')}
          </Menu.Item>
        </Menu>,
        app,
      );
    },

    'DynamicMap.allElement': async(params, viewId) => {
      await allElementHandler(viewId);
    },
  });
};

export default (
  id,
  networkData,
  clickBubble,
  goDetailPage,
  _clickNodeHandler,
  _handleVirtualNodeClick,
  _selectedNode,
  _themeChange,
  _replcaeData,
) => {
  refresh = true;
  selectedNode = _selectedNode;
  $('#msgContextMenu').hide();
  let isSub = true;
  if (clickBubble) {
    isSub = false;
  }
  if (_themeChange) {
    themeChange = _themeChange;
  }
  if (_replcaeData) {
    replcaeData = _replcaeData;
  }
  networkInfo = {};
  containerId = id;
  $.extend(true, networkInfo, networkData);
  if (!isSub) {
    onClickBubble = clickBubble;
    goDetailHandler = goDetailPage;
    clickNodeHandler = _clickNodeHandler;
    $.extend(true, mianPageData, networkData);
  }
  handleVirtualNodeClick = _handleVirtualNodeClick;
  parseNetworkData(isSub);
  Prel.autoLoad('topoShowcasePiu', '*').then(() => {
    let theme = piu.get('theme');
    piu.emit('DynamicMap.render', {
      viewId: isSub ? 'dvtopoSubViewId' : 'dvtopoViewId',
      domId: id,
      topoType: 'DVCrossLayer',
      life: 'create',
      situation: 'DV',
      theme,
      data: {},
      customDataFunc: () => deepCopyFun(isSub ? subDataInfo : dataInfo),
    });
    light();
    attach();
  });
};

const light = () => {
  if (piu.get('theme') !== 'lightday') {
    if (document.body.classList.contains('eviewui_aui3_1_evening')) {
      document.body.classList.replace('eviewui_aui3_1_evening', 'eviewui_aui3_1_default');
    } else {
      setTimeout(light, 50);
    }
  }
};

const menuContextExist = () => $('#msgContextMenu').css('display') === 'block';
export const setTopologyContainerId = id => {
  containerId = id;
  $('#msgContextMenu').css('display', 'none');
};

export const resizeView = id => {
  const container = $(`#${id}`);
  piu.emit('DynamicMap.resize', {
    x: container.offset().left,
    y: container.offset().top,
    width: container.width(),
    height: container.height(),
  });
};

export const getDataInfo = () => {
  let isSub = containerId === 'subTopologyBox';
  return isSub ? subDataInfo : dataInfo;
};

export const getTopologyContainerId = () => containerId;

export const setScale = scale => {
  networkInfo.scale = scale;
};

export const setAllEleLoding = loadedRes => {
  isAllDataLoading = loadedRes;
};

export const getAllEleLoding = () => isAllDataLoading;
