export default {
  'zh-cn': {
    cross_layer_title: '分层拓扑',
    solution: '解决方案',
    business: '业务app',
    pod: 'Pod',
    vm: '主机层',
    host_group: '分组',
    legend: '图例',
    tooltip_alarm: '告警信息',
    tooltip_sub_alarm: '子节点告警汇聚信息',
    tooltip_name: '名称',
    critical: '紧急',
    major: '重要',
    minor: '次要',
    warning: '提示',
    default_scene: '默认',
    refresh: '刷新',
    search: '搜索',
    plus: '放大',
    minus: '缩小',
    expand: '进入全屏',
    collapse: '退出全屏',
    view_detail: '查看子拓扑',
    service_app: '业务app',
    tree_title: '解决方案实例树',
    topology_no_data_tip:
      '请导入网元包并接入网元，如果导入后还无法查看，可能是当前产品暂不支持该功能，请参考联机帮助说明。',
    tooltip_ip: 'IP地址',
    tooltip_mo_type: '网元类型',
    alarm_management: '告警管理',
    config_management: '配置管理',
    layer_node_overflow: '每层最多展示{0}个网元',
    tooltip_status: '运行状态',
    tooltip_admin_status: '管理状态',
    tooltip_create_time: '创建时间',
    tooltip_host_info: '部署主机信息',
    topoSearchToolBar_button: '搜索',
    topoSearchPop_name: '名称',
    topoSearchPop_type: '类型',
    topoSearchPop_ipAddress: 'IP地址',
    topoSearchPop_searchHolder: '请输入...',
    topoSearchPop_previousPage: '上一页',
    topoSearchPop_nextPage: '下一页',
    topoSearchPop_searchResult_fail: '查询失败！',
    topoSearchPop_firstPage: '已是第一页',
    topoSearchPop_lastPage: '已是最后一页',
    topoSearch_inputValidFail: '输入不能包含& # % + = ; < > / \\ 特殊字符。',
    topoSearch_inputValidFail_title: '输入无效',
    topo_show_more: '全部',
    topo_show_all: '查看全部',
  },
  'en-us': {
    cross_layer_title: 'Cross Layer Topology',
    solution: 'Solution',
    business: 'Service app',
    pod: 'Pod',
    vm: 'Host Layer',
    host_group: 'Group',
    legend: 'Legend',
    tooltip_alarm: 'AlarmInfo',
    tooltip_sub_alarm: 'SubNodeAlarmInfo',
    tooltip_name: 'Name',
    tooltip_status: 'OperationalStatus',
    tooltip_admin_status: 'AdminStatus',
    tooltip_create_time: 'CreateTime',
    tooltip_host_info: 'DeployingHost',
    critical: 'Critical',
    major: 'Major',
    minor: 'Minor',
    warning: 'Warning',
    default_scene: 'Default',
    refresh: 'Refresh',
    search: 'Search',
    plus: 'Zoom in',
    minus: 'Zoom out',
    expand: 'Go to full screen',
    collapse: 'Exit Full Screen',
    view_detail: 'View SubTopology',
    service_app: 'Service app',
    tree_title: 'Solution Tree',
    topology_no_data_tip:
      'Import the NE package and connect the NE. If you cannot view the NE after the import, the current product may not support this function. For details, see the online help.',
    tooltip_ip: 'IpAddress',
    tooltip_mo_type: 'NeType',
    alarm_management: 'Alarm Management',
    config_management: 'Configuration Management',
    layer_node_overflow: 'A maximum of {0} NEs can be displayed at each layer.',
    topoSearchToolBar_button: 'Search',
    topoSearchPop_name: 'Name',
    topoSearchPop_type: 'Type',
    topoSearchPop_ipAddress: 'IpAddress',
    topoSearchPop_searchHolder: 'please enter...',
    topoSearchPop_previousPage: 'Previous',
    topoSearchPop_nextPage: 'Next',
    topoSearchPop_searchResult_fail: 'Searching failed!',
    topoSearchPop_firstPage: 'Already the first page.',
    topoSearchPop_lastPage: 'Already the last page.',
    topoSearch_inputValidFail:
      'The input cannot contain special characters& # % + = ; < >  /',
    topoSearch_inputValidFail_title: 'input invalid',
    topo_show_more: 'All',
    topo_show_all: 'Show All',
  },
};
