/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext } from 'react';
import { Dialog, Table } from 'eview-ui';
import { $t } from '@util';
import { CrossLayerContext, VIRTUAL } from '../const';
import { compareFun } from '../util';
const showData = (showId, nodes) => {
  let level = Number(showId.slice(showId.lastIndexOf('_') + 1));
  let data = [];
  nodes.forEach(node => {
    const curNodeLevel = Number(
      node.image.slice(node.image.lastIndexOf('_') + 1),
    );
    if (showId.startsWith(VIRTUAL) && curNodeLevel === level) {
      data.push([node.id, node.name, node.moTypeName, node.ipAddress]);
    }
  });
  return data;
};

function More() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const { showMore, topologyOriginData } = state;
  if (!showMore.open) {
    return <div />;
  }
  let data;
  if (topologyOriginData !== undefined && topologyOriginData !== null) {
    data = showData(showMore.showId, topologyOriginData.nodes);
    sortNodes(data);
  }
  const columns = [
    {
      title: 'dn',
      key: 'dn',
      display: false,
    },
    {
      title: $t('topoSearchPop_name'),
      key: 'name',
      allowSort: true,
    },
    {
      title: $t('topoSearchPop_type'),
      key: 'type',
      allowSort: true,
    },
    {
      title: $t('topoSearchPop_ipAddress'),
      key: 'ipAddress',
      allowSort: true,
    },
  ];

  return (
    <div id="topo_more_container">
      <Dialog
        title={$t('topo_show_more')}
        size={[800, 500]}
        isOpen={showMore.open}
        onClose={() => {
          dispatch({
            showMore: {
              ...showMore,
              open: false,
            },
          });
        }}
        style={{ bottom: '0', left: '40px' }}
        modal={false}
        movable={true}
        children={
          <div id="more_container">
            <div>
              <Table columns={columns} dataset={data} />
            </div>
          </div>
        }
        mountId="topo_more_container"
        resizable={false}
        buttonStyle={{ textAlign: 'right', marginRight: '10px' }}
      />
    </div>
  );
}

const sortNodes = nodes => {
  nodes.sort((a, b) => compareFun(a[1], b[1]));
};
export default More;
