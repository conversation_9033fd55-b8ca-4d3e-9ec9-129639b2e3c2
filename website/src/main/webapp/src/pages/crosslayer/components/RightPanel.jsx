/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext } from 'react';
import SearchResult from './SearchResult';
import { CrossLayerContext } from '../const';
import { resizeView, getTopologyContainerId } from '../util/initTopology';

function RightPanel() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const { showRightPanel, searchResShow } = state;
  let containerId = getTopologyContainerId();

  const handleShowHideClick = () => {
    dispatch({
      searchResShow: !searchResShow,
    });
    setTimeout(() => {
      resizeView('bottomPanel');
    }, 100);
  };

  return (
    <div
      className="right-panel"
      style={{ display: showRightPanel ? 'block' : 'none' }}
    >
      <div
        className={`dv-topo-right-panel-div-btn dv-topo-common-focus dv-topo-right-panel-div-${containerId}`}
        onClick={handleShowHideClick}
      >
        <div
          className={`topo-common-panel-${
            searchResShow ? 'close' : 'expend'
          }-vertical-right`}
        />
      </div>
      <div
        className="right-panel-container"
        style={{ display: searchResShow ? 'block' : 'none' }}
      >
        <SearchResult />
      </div>
    </div>
  );
}

export default RightPanel;
