/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useEffect, useContext } from 'react';
import { IconButton, Tree } from 'eview-ui';
import treePic from '@images/tree.svg';
import businessPic from '@images/business_ne.svg';
import customizedPic from '@images/ne_customized.svg';
import { $t } from '@util';
import {
  CrossLayerContext,
  DEFAULT_PANEL_SIZE,
  MIN_PANEL_SIZE,
} from '../const';
import { getCrossTree } from '../api';
import { compareFun } from '../util';
import { resizeView } from '../util/initTopology';

function LeftTree({ showSideRef }) {
  const { state, dispatch } = useContext(CrossLayerContext);
  const { leftTreeData, selectedKeys, expandedKeys } = state;

  const handleSelect = (selectedKeys, node) => {
    // 只有叶子节点可以选中
    if (!node.props.children) {
      dispatch({ selectedKeys });
    }
    dispatch({
      showRightPanel: false,
      topoSearchData: '',
      searchResShow: false,
      replcaeData: {},
      selectedNode: '',
    });
  };

  const handleExpand = keys => {
    dispatch({ expandedKeys: keys });
  };

  const changePanelSize = () => {
    let { pane1 } = showSideRef.current.getPanelSize();
    let panelSize =
      pane1.width <= MIN_PANEL_SIZE ? DEFAULT_PANEL_SIZE : MIN_PANEL_SIZE;
    showSideRef.current.setPanelSize(panelSize);
    setTimeout(() => {
      resizeView('bottomPanel');
    }, 100);
  };

  // 左树按照名称升序排列
  const sortTreeData = treeData => {
    treeData.forEach(item => {
      if (item.children?.length > 0) {
        item.children.sort((a, b) => compareFun(a.text, b.text));
        sortTreeData(item.children);
      }
    });
  };

  useEffect(() => {
    const payload = { showNoDataTip: true };
    let panelSize = MIN_PANEL_SIZE;
    getCrossTree(
      res => {
        const leftTreeData = res.data || [];
        if (leftTreeData.length > 0) {
          sortTreeData(leftTreeData);
          payload.leftTreeData = leftTreeData;
          payload.showNoDataTip = false;
          payload.expandedKeys = [leftTreeData[0].id];
          if (leftTreeData[0].children.length > 0) {
            payload.selectedKeys = [leftTreeData[0].children[0].id];
          }
          panelSize = DEFAULT_PANEL_SIZE;
        }
        showSideRef.current.setPanelSize(panelSize);
        dispatch(payload);
      },
      () => {
        showSideRef.current.setPanelSize(panelSize);
        dispatch(payload);
      },
    );
  }, []);

  return (
    <div className="left_tree">
      <div
        className="expand_bar"
        style={{
          'box-shadow': '0.0625rem 0 0.375rem 0 rgba(0,0,0,.08)',
        }}
      >
        <div className="iconBlockChoose">
          <IconButton
            id="menuListDashboard"
            tipData={{
              direction: 'right',
            }}
            size={['1.5rem', '1.5rem']}
            iconUrl={treePic}
            onClick={changePanelSize}
          />
        </div>
      </div>
      <div id="left_tree_id">
        <div style={{ 'padding-left': '1.5rem', 'margin-top': '30px' }}>
          <div style={{ 'border-bottom': '.0625rem solid rgba(25,25,25,.1)' }}>
            <div
              style={{
                'border-bottom': '0.18rem solid #0067d1',
                'padding-bottom': '5px',
                width: '80%',
              }}
            >
              <span
                style={{ 'font-weight': 'bold', color: 'rgb(0, 103, 209)' }}
              >
                {$t('tree_title')}
              </span>
            </div>
          </div>
        </div>
        <Tree
          data={leftTreeData}
          nodeKey="id"
          checkable
          onSelect={handleSelect}
          onExpand={handleExpand}
          style={{ overflow: 'auto', height: 'calc(100% - 80px)' }}
          iconLeaf={businessPic}
          iconExpanded={customizedPic}
          iconCollapsed={customizedPic}
          selectedKeys={selectedKeys}
          expandedKeys={expandedKeys}
        />
      </div>
    </div>
  );
}

export default LeftTree;
