import React, { useContext, useEffect, useMemo } from 'react';
import { IconButtonGroup, Select, Crumbs, SearchInput } from 'eview-ui';
import plus from '@images/plus.svg';
import minus from '@images/minus.svg';
import refresh from '@images/refresh.svg';
import collapseIcon from '@images/collapse.svg';
import expandIcon from '@images/expand.svg';
import { $t } from '@util';
import { setScale, resizeView, getAllEleLoding } from '../util/initTopology';
import {
  CLIENT_SCALE_OPTION,
  CrossLayerContext,
  MAX_ZOOM,
  MIN_ZOOM,
  SUB_DEFAULT_SCALE,
  ZOOM_STEP,
} from '../const';
import { debounce, setTopologyContainerId } from '../util';

function Header() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const {
    sceneOptions,
    scene,
    scale,
    subScale,
    pageType,
    expand,
    topologyData,
    subTopologyData,
    topoSearchData,
  } = state;
  const shrinkIcon = expand ? collapseIcon : expandIcon;
  const shrinkText = expand ? $t('collapse') : $t('expand');
  const viewId = pageType === 'detail' ? 'dvtopoSubViewId' : 'dvtopoViewId';
  const showHeader =
    (pageType === 'home' && topologyData) ||
    (pageType === 'detail' && subTopologyData);
  const scaleValue = pageType === 'home' ? scale : subScale;
  const scaleKey = pageType === 'home' ? 'scale' : 'subScale';
  const plusDisabled = scaleValue >= MAX_ZOOM;
  const minusDisabled = scaleValue <= MIN_ZOOM;
  const topoSearchValue = pageType === 'home' ? topoSearchData : '';

  const crumbData = useMemo(() => {
    const result = [{ title: $t('cross_layer_title') }];
    if (pageType === 'detail') {
      result.push({ title: $t('service_app') });
    }
    return result;
  }, [pageType]);

  const refreshMethod = () => {
    location.reload();
  };

  const plusMethod = () => {
    const value = Number((scaleValue + ZOOM_STEP).toFixed(1));
    dispatch({ [scaleKey]: value });
    setScale(value);
  };

  const minusMethod = () => {
    const value = Number((scaleValue - 0.2).toFixed(1));
    dispatch({ [scaleKey]: value });
    setScale(value);
  };

  const expandHandler = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.body.requestFullscreen();
    }
  };

  const iconButtonData = [
    {
      tipText: $t('refresh'),
      iconUrl: refresh,
      onClick: refreshMethod,
    },
    {
      disabled: plusDisabled,
      tipText: $t('plus'),
      iconUrl: plus,
      onClick: plusMethod,
    },
    {
      disabled: minusDisabled,
      tipText: $t('minus'),
      iconUrl: minus,
      onClick: minusMethod,
    },
    {
      tipText: shrinkText,
      iconUrl: shrinkIcon,
      onClick: expandHandler,
    },
  ];

  const changeScenesHandler = v => {
    dispatch({ scene: v });
  };

  const changeScaleHandler = v => {
    const key = pageType === 'home' ? 'scale' : 'subScale';
    dispatch({ [key]: v });
    setScale(v);
  };

  const clickCrumb = ({ title }) => {
    dispatch({
      showRightPanel: false,
      searchResShow: false,
    });
    if (title === $t('cross_layer_title')) {
      setTopologyContainerId('bottomPanel');
      dispatch({ pageType: 'home', subScale: SUB_DEFAULT_SCALE });
      setScale(scale);
    } else {
      setTopologyContainerId('subTopologyBox');
      dispatch({ pageType: 'detail' });
    }
  };

  const resizeCallback = () => {
    dispatch({ expand: Boolean(document.fullscreenElement) });
  };

  const keyDownCallback = e => {
    if (e.keyCode === 122) {
      e.preventDefault();
      expandHandler();
    }
  };

  useEffect(async () => {
    if (window.DynamicMap && getAllEleLoding()) {
      window.DynamicMap.setZoom('SET', 1, viewId);
      await DynamicMap.refresh({ viewId: viewId });
      window.DynamicMap.setZoom('SET', scaleValue, viewId);
      DynamicMap.moveView(
        Math.floor(400 * (1 - scaleValue)),
        Math.floor(100 * (1 - scaleValue)) - 10,
        viewId,
      );
    }
  }, [scaleValue, pageType]);

  useEffect(() => {
    window.addEventListener('resize', debounce(resizeCallback, 100));
    document.addEventListener('keydown', keyDownCallback);
    return () => {
      window.removeEventListener('resize', debounce(resizeCallback, 100));
      document.removeEventListener('keydown', keyDownCallback);
    };
  }, []);

  return (
    <div className="header">
      <Crumbs
        data={crumbData}
        onClick={clickCrumb}
        className="header_crumb"
      />
      {showHeader && (
        <div className="header_right">
          <IconButtonGroup
            data={iconButtonData}
            id="IconButtonGroupId"
            itemStyle={{ 'margin-right': '1.1rem' }}
          />
          <Select
            options={CLIENT_SCALE_OPTION}
            selectStyle={{ width: '6rem' }}
            onChange={changeScaleHandler}
            value={scaleValue}
          />
          <div
            style={{
              'border-left': '1px solid #e8e8e8',
              height: '30px',
              'margin-right': '15px',
            }}
          />
          {pageType === 'home' && (
            <Select
              style={{ marginRight: '1rem' }}
              selectStyle={{ width: '8rem' }}
              options={sceneOptions}
              onChange={changeScenesHandler}
              value={scene}
            />
          )}
          <SearchInput
            placeholder={$t('topoSearchPop_searchHolder')}
            maxLengthInput={128}
            style={{ width: '20rem' }}
            onSearch={value => {
              dispatch({
                showRightPanel: true,
                topoSearchData: value,
                searchResShow: true,
              });
              setTimeout(() => {
                resizeView('bottomPanel');
              }, 1);
            }}
            onClear={value => {
              dispatch({
                showRightPanel: false,
                topoSearchData: '',
                searchResShow: false,
              });
              setTimeout(() => {
                resizeView('bottomPanel');
              }, 1);
            }}
            value={topoSearchValue}
          />
        </div>
      )}
    </div>
  );
}

export default Header;
