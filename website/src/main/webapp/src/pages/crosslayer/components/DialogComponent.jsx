import React, { useContext, useEffect } from 'react';
import { DialogPro, IconButtonGroup } from 'eview-ui';
import { $t, isZh } from '@util';
import recovery_legend from '@images/recovery_legend.svg';
import { CrossLayerContext, LEGEND_INFO } from '../const';

function DialogComponent() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const { iframeOpen } = state;
  const handleClick = () => {
    dispatch({ iframeOpen: true });
  };
  const dialogWidth = isZh ? 280 : 320;
  let bakckStyle = { backgroundColor: '#4d4343', color: 'white' };
  if (piu.get('theme') === 'lightday') {
    bakckStyle = {};
  }
  const iframeData = [
    {
      title: $t('legend'),
      tipText: $t('legend'),
      iconUrl: recovery_legend,
      onClick: handleClick,
    },
  ];

  let legendClass = `legend_content_${piu.get('theme')}`;
  return (
    <div>
      {!iframeOpen && (
        <IconButtonGroup
          data={iframeData}
          id="IconButtonGroupId3"
          itemStyle={{
            position: 'fixed',
            right: '30px',
            bottom: '100px',
            'z-index': '9999',
            padding: 8,
            backgroundColor: '#fff',
          }}
        />
      )}
      {iframeOpen && (
        <DialogPro
          style={{
            minWidth: dialogWidth,
            maxWidth: dialogWidth,
            ...bakckStyle,
          }}
          id="legend_box"
          title={$t('legend')}
          isOpen={iframeOpen}
          onClose={() => {
            dispatch({ iframeOpen: false });
          }}
          contentStyle={{ overflow: 'hidden', padding: '1rem' }}
          position={['auto', 'auto', 20, 60]}
          modal={false}
          movable={true}
          resizable={false}
        >
          <div className={legendClass}>
            {LEGEND_INFO.map((legend, index) => (
              <div key={index} className="legend_item">
                <img src={legend.img} className="legend_img" />
                <span className="legend_text" title={legend.text}>
                  {legend.text}
                </span>
              </div>
            ))}
          </div>
        </DialogPro>
      )}
    </div>
  );
}

export default DialogComponent;
