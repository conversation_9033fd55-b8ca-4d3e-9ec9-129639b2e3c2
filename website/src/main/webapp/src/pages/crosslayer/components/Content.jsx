import React, { useContext, useRef } from 'react';
import { Split } from 'eview-ui';
import { debounce } from '../util';
import LeftTree from './LeftTree';
import { resizeView } from '../util/initTopology';
import LayerComponent from './LayerComponent';

import {
  CrossLayerContext,
  MAX_PANEL_SIZE,
  MIN_PANEL_SIZE,
  DEFAULT_PANEL_SIZE,
} from '../const';
import ServiceApp from './ServiceApp';

function Content() {
  const { state } = useContext(CrossLayerContext);
  const { pageType } = state;
  const showSideRef = useRef();

  const changePanelSize = debounce(() => {
    resizeView('bottomPanel');
  }, 100);

  return (
    <div className="content" id="topology_container">
      {pageType === 'detail' && <ServiceApp />}
      <div style={{ visibility: pageType === 'detail' ? 'hidden' : 'visible' }}>
        <Split
          id="split_box"
          key="dv-topo-split"
          split="vertical"
          splitResizerWidth={0.1}
          defaultSize={DEFAULT_PANEL_SIZE}
          allowResize={true}
          ref={node => {
            showSideRef.current = node;
          }}
          minSize={MIN_PANEL_SIZE}
          maxSize={MAX_PANEL_SIZE}
          minSplitClickSize={MIN_PANEL_SIZE}
          maxSplitClickSize={DEFAULT_PANEL_SIZE}
          onChange={changePanelSize}
        >
          <div
            id="leftMenuBar"
            style={{ background: '#ffffff', height: '100%', display: 'flex' }}
          >
            <LeftTree showSideRef={showSideRef} />
          </div>
          <div id="rightMenuBar">
            <LayerComponent />
          </div>
        </Split>
      </div>
    </div>
  );
}

export default Content;
