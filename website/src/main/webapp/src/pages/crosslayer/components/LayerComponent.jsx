import React, { useEffect, useContext } from 'react';
import { $t } from '@util';
import { initTopology, formatTopologyData, deepCopyFun } from '../util';
import {
  APP_LEVELS,
  CrossLayerContext,
  DEFAULT_SCALE,
  DEFAULT_SCENE_OPTION,
  DRILL_APP_LEVELS,
  LAYER_MAX_NE_NUM,
} from '../const';
import { getLinks, getTopologyData } from '../api';

let linksIdsCache = [];
let linksCache = [];

const dynamicAddLinks = (links, nodes) => {
  piu.emit('DynamicMap.optElements', {
    action: 'remove',
    links: linksIdsCache,
  });
  linkCache(links, nodes);
  piu.emit('DynamicMap.optElements', {
    action: 'add',
    links: linksCache,
  });
};

const linkCache = (links, nodes) => {
  let newLinks = [];
  for (let i = 0; i < links.length; i++) {
    if (
      nodes.find(item => item.id === links[i].fromNodeId) &&
      nodes.find(item => item.id === links[i].toNodeId)
    ) {
      newLinks.push({
        id: links[i].id,
        resId: links[i].id,
        fromNodeId: links[i].fromNodeId,
        toNodeId: links[i].toNodeId,
        properties: {
          'com.huawei.topo.direction': 4,
        },
        styles: { width: 2, color: '#5ccbb1' },
      });
    }
  }
  linksIdsCache = [];
  newLinks.forEach(link => {
    linksIdsCache.push({
      resId: link.resId,
    });
  });
  linksCache = [];
  linksCache = newLinks;
};

const needDynamicReplacement = nodes => {
  let vmNodes = [];
  nodes.forEach(node => {
    if (node.image === 'DV_image_8') {
      vmNodes.push(node);
    }
  });
  if (vmNodes.length > LAYER_MAX_NE_NUM) {
    return true;
  }
  return false;
};

function LayerComponent() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const {
    topologyOriginData,
    topologyData,
    selectedKeys,
    scene,
    showNoDataTip,
    scale,
    selectedNode,
    pageType,
    replcaeData,
  } = state;
  const handleVirtualNodeClick = id => {
    dispatch({
      showMore: {
        open: true,
        showId: id,
      },
    });
  };

  const themeChange = theme => {
    dispatch({
      theme: theme,
    });
  };

  const mergeData = async (data, level, nodeId) => {
    const res = await getTopologyData({
      solutionDn: selectedKeys[0],
      level,
      masterView: 0,
      clickDn: nodeId,
    });
    const {
      nodes,
      alarms,
      scenes,
      aggregateAlarm,
      groups,
    } = formatTopologyData(res.data);
    const newGroups = [...data.groups, ...groups].sort((a, b) => {
      let levelA = a.id.slice(a.id.lastIndexOf('_') + 1);
      let levelB = b.id.slice(b.id.lastIndexOf('_') + 1);
      return Number(levelA) - Number(levelB);
    });
    return {
      nodes: [...data.nodes, ...nodes],
      alarms: [...data.alarms, ...alarms],
      aggregateAlarm: [...data.aggregateAlarm, ...aggregateAlarm],
      scenes: [...data.scenes, ...scenes],
      groups: newGroups,
      links: [],
    };
  };

  const getNewNodes = (imageType, nodeId, nodes, level) => nodes.map(item => {
    const bubbleImage = imageType === 'up' ? 'down' : 'up';
    const { parentId } = item.properties;
    const curLevel = Number(parentId.slice(parentId.lastIndexOf('_') + 1));
    if (item.id === nodeId) {
      return { ...item, bubbleImage };
    } else if (curLevel === level) {
      // 将同级的其他网元图标设置为下
      return { ...item, bubbleImage: 'down' };
    } else {
      return item;
    }
  });

  const filterAppData = (id, level) => {
    const curLevel = Number(id.slice(id.lastIndexOf('_') + 1));
    if (DRILL_APP_LEVELS.includes(curLevel)) {
      return level >= curLevel;
    }
    return true;
  };

  const filterDrillAppData = (data, level) => {
    const { nodes, groups, links } = data;
    const newNodes = nodes.filter(item => filterAppData(item.properties.parentId, level));
    const newGroups = groups.filter(item => filterAppData(item.id, level));
    const nodeIdsSet = new Set(newNodes.map(item => item.id));
    const newLinks = links.filter(item => nodeIdsSet.has(item.fromNodeId) && nodeIdsSet.has(item.toNodeId));
    return { ...data, nodes: newNodes, links: newLinks, groups: newGroups };
  };

  const dynamicReplacement = (nodeId, data) => {
    let vmNodes = [];
    let newVmNode = [];
    let hostNodes = [];
    let newHostNode = [];
    let nodes = [];
    topologyOriginData.nodes.forEach(node => {
      if (node.image === 'DV_image_8') {
        vmNodes.push(node);
      }
      if (node.image === 'DV_image_9') {
        hostNodes.push(node);
      }
    });
    for (let i = 0; i < vmNodes.length; i++) {
      if (vmNodes[i].appNodeIds.indexOf(nodeId) !== -1) {
        newVmNode.push(vmNodes[i]);
      }
    }
    for (let i = 0; i < vmNodes.length; i++) {
      if (vmNodes[i].appNodeIds.indexOf(nodeId) === -1) {
        newVmNode.push(vmNodes[i]);
      }
    }

    for (let i = 0; i < hostNodes.length; i++) {
      if (hostNodes[i].appNodeIds.indexOf(nodeId) !== -1) {
        newHostNode.push(hostNodes[i]);
      }
    }
    for (let i = 0; i < hostNodes.length; i++) {
      if (hostNodes[i].appNodeIds.indexOf(nodeId) === -1) {
        newHostNode.push(hostNodes[i]);
      }
    }
    nodes = data.nodes.filter(
      e => e.image !== 'DV_image_8' && e.image !== 'DV_image_9',
    );
    const newNodes = [...newVmNode, ...newHostNode, ...nodes];
    data.nodes = newNodes;
    return data;
  };

  const clickBubble = async (imageType, nodeId, level) => {
    let networkData;
    let image = imageType ? imageType : 'down';
    let networkInfo = filterDrillAppData(topologyData, level);
    if (image === 'down') {
      networkInfo = await mergeData(networkInfo, level, nodeId);
    }
    const newNodes = getNewNodes(image, nodeId, networkInfo.nodes, level);
    networkData = { ...networkInfo, nodes: newNodes };
    linksIdsCache = [];
    linksCache = [];
    dispatch({ topologyData: networkData, selectedNode: '' });
  };

  const goDetailPage = (nodeId, level) => {
    dispatch({ pageType: 'detail', detailData: { nodeId, level } });
  };

  const setLowlightNodes = (nodes, dns) => nodes.map(item => {
    const result = { ...item };
    result.lowlight = !(dns.includes(item.id) || dns.length === 0);
    return result;
  });

  const getTopologyDataHandler = () => {
    const payload = {
      topologyData: null,
      topologyOriginData: null,
      sceneOptions: [DEFAULT_SCENE_OPTION],
      scene: 0,
      scale: DEFAULT_SCALE,
    };
    getTopologyData(
      { solutionDn: selectedKeys[0], level: 0, masterView: 0 },
      res => {
        const topologyData = formatTopologyData(res.data);
        const sceneOptions = topologyData.scenes.map(item => ({
          text: item.name,
          value: item.id,
        }));
        payload.topologyData = topologyData;
        payload.topologyOriginData = deepCopyFun(topologyData);
        payload.sceneOptions = [DEFAULT_SCENE_OPTION, ...sceneOptions];
        dispatch(payload);
      },
      () => {
        dispatch(payload);
      },
    );
  };

  const getNodeInfoById = id => {
    const nodes = topologyData.nodes || [];
    return nodes.find(item => item.id === id) || {};
  };

  const getLastOpenGroupResId = () => {
    const groups = topologyData.groups || [];
    const appResIds = groups
      .map(item => item.resId)
      .filter(item => {
        const level = Number(item.slice(item.lastIndexOf('_') + 1));
        return APP_LEVELS.includes(level);
      });
    if (appResIds.length > 1) {
      return appResIds[appResIds.length - 2];
    } else {
      return undefined;
    }
  };

  // 点击虚拟机层时需要把最后一个展开的app层网元id传给后台
  const getAppLastOpenDn = (level, params) => {
    if (level === 8) {
      const lastOpenResId = getLastOpenGroupResId();
      if (lastOpenResId !== undefined) {
        const lastOpenNode = topologyData.nodes.find(node => (
          lastOpenResId === node.properties.parentId &&
            node.bubbleImage === 'up'
        ));
        if (lastOpenNode) {
          params.lastOpenDn = lastOpenNode.id;
        }
      }
    }
  };

  const clickNodeHandler = (nodeId, level, imageType) => {
    const open = imageType === 'down' || imageType === undefined ? 0 : 1;
    const params = {
      solutionDn: selectedKeys[0],
      level,
      open,
      clickDn: nodeId,
    };

    if (level === 8) {
      const { deployNeInfo } = topologyOriginData.nodes.find(
        item => item.id === nodeId,
      );
    }
    getAppLastOpenDn(level, params);
    getLinks(params, res => {
      const { links } = res.data || { links: [] };
      if (links.length > 0) {
        const filterLinks = links.filter(item => {
          if (level === 8) {
            const { fromNodeId } = item;
            const nodeInfo = getNodeInfoById(fromNodeId);
            return !(nodeInfo.bubbleImage === 'up');
          } else {
            return true;
          }
        });
        let newData = topologyData;
        if (level === 2) {
          if (needDynamicReplacement(topologyOriginData.nodes)) {
            newData = dynamicReplacement(nodeId, newData);
            dispatch({
              topologyData: { ...newData, links: filterLinks },
              selectedNode: nodeId,
            });
            linkCache(links, newData.nodes);
          } else {
            dynamicAddLinks(links, newData.nodes);
          }
        } else {
          dynamicAddLinks(links, topologyData.nodes);
        }
      }
    });
  };
  useEffect(() => {
    if (topologyData) {
      topologyData.scale = scale;
      initTopology(
        'bottomPanel',
        topologyData,
        clickBubble,
        goDetailPage,
        clickNodeHandler,
        handleVirtualNodeClick,
        selectedNode,
        themeChange,
        replcaeData,
      );
    }
  }, [topologyData]);

  useEffect(() => {
    if (pageType === 'home') {
      setTimeout(() => {
        piu.emit('DynamicMap.optElements', {
          action: 'add',
          links: linksCache,
        });
      }, 100);
    }
  }, [pageType]);

  useEffect(() => {
    if (selectedKeys.length > 0) {
      getTopologyDataHandler();
    }
  }, [selectedKeys]);

  useEffect(() => {
    let topologyInfo;
    if (scene === 0) {
      if (topologyData) {
        const { nodes } = topologyData;
        const newNodes = setLowlightNodes(nodes, []);
        topologyInfo = { ...topologyData, links: [], nodes: newNodes };
      }
    } else {
      const { scenes, nodes } = topologyData;
      const curSceneInfo = scenes.find(item => item.id === scene) || {};
      const newNodes = setLowlightNodes(nodes, curSceneInfo.dns);
      topologyInfo = {
        ...topologyData,
        links: curSceneInfo.links,
        nodes: newNodes,
      };
    }
    dispatch({
      topologyData: topologyInfo,
      topologyOriginData: deepCopyFun(topologyInfo),
    });
  }, [scene]);

  return (
    <div id="bottomPanel" key="bottomPanel" style={{ height: '100%' }}>
      {showNoDataTip && (
        <div className="no_data_tip">
          <p>{$t('topology_no_data_tip')}</p>
        </div>
      )}
    </div>
  );
}

export default LayerComponent;
