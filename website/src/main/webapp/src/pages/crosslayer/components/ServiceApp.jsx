import React, { useContext, useEffect } from 'react';
import { CrossLayerContext } from '../const';
import lightBackground from '@images/light_background.png';
import { initTopology, clearTopology, formatTopologyData } from '../util';
import { getTopologyData } from '../api';

function ServiceApp() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const { detailData, subTopologyData, selectedKeys, subScale } = state;
  const { nodeId, level } = detailData;

  const initSubData = () => {
    dispatch({ detailData: {}, subTopologyData: null });
  };

  useEffect(() => {
    const groups = subTopologyData.groups || [];
    if (groups.length > 0) {
      clearTopology('subTopologyBox');
      subTopologyData.scale = subScale;
      subTopologyData.selectNode = nodeId;
      initTopology('subTopologyBox', subTopologyData);
    }
  }, [subTopologyData]);

  useEffect(() => {
    dispatch({
      showRightPanel: false,
      searchResShow: false,
    });
    getTopologyData(
      { solutionDn: selectedKeys[0], level, masterView: 1, clickDn: nodeId },
      res => {
        const subTopologyData = formatTopologyData(res.data);
        dispatch({ subTopologyData });
      },
    );
    return () => {
      initSubData();
    };
  }, []);

  return (
    <div
      id="subTopologyBox"
      key="subTopologyBox"
      style={{ backgroundImage: `url(${lightBackground})` }}
    />
  );
}

export default ServiceApp;
