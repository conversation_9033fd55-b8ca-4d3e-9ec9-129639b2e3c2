/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext, useEffect } from 'react';
import { Table } from 'eview-ui';
import { $t } from '@util';
import {
  getDataInfo,
  getTopologyContainerId,
  getCurImage,
} from '../../crosslayer/util/initTopology';
import { CrossLayerContext, ALARM_LEVELS } from '../const';
import { compareFun } from '../util';

function getUniqueArr(topologyOriginData) {
  let { nodes } = getDataInfo();
  let mergedArray = nodes;
  if (getTopologyContainerId() !== 'subTopologyBox') {
    mergedArray = nodes.concat(topologyOriginData.nodes);
  }
  const uniqueArr = mergedArray.filter((item, index, self) => index === self.findIndex(t => t.id === item.id));
  return uniqueArr;
}

const escape = str => str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

function SearchResult() {
  const { state, dispatch } = useContext(CrossLayerContext);
  const {
    topoSearchData,
    topoShowData,
    topologyOriginData,
    replcaeData,
  } = state;
  const defaultAlarmState = { critical: 0, major: 0, minor: 0, warning: 0 };
  useEffect(() => {
    const rows = [];
    const uniqueArr = getUniqueArr(topologyOriginData);
    const escapedString = escape(topoSearchData.trim());
    const pattern = new RegExp(escapedString, 'i');
    uniqueArr.forEach(item => {
      if (
        topoSearchData.length !== 0 &&
        (pattern.test(item.name) || pattern.test(item.ipAddress))
      ) {
        rows.push([
          item.id,
          editName(item.name),
          item.moTypeName,
          item.ipAddress,
        ]);
      }
    });
    sortNodes(rows);
    dispatch({
      topoShowData: rows,
    });
  });

  const columns = [
    {
      title: 'dn',
      key: 'dn',
      display: false,
    },
    {
      title: $t('topoSearchPop_name'),
      key: 'name',
      allowSort: true,
    },
    {
      title: $t('topoSearchPop_type'),
      key: 'type',
      allowSort: true,
    },
    {
      title: $t('topoSearchPop_ipAddress'),
      key: 'ipAddress',
      allowSort: true,
    },
  ];

  const editName = name => (
    <span
      style={{
        cursor: 'pointer',
        color: '#0af',
        textDecoration: 'none',
        paddingBottom: '2px',
        borderBottom: '1px solid',
      }}
    >
      {name}
    </span>
  );

  function addOverlay(parentId, bubbleImage) {
    piu.emit('DynamicMap.addOverlay', {
      overlayId: parentId,
      nodes: [
        {
          resId: parentId,
          render: [
            {
              type: 'EXTENDATTACHMENT',
              value: JSON.stringify({
                attachName: 'ImageAttachment',
                attachParam: {
                  position: 'autoRight',
                  width: 16,
                  height: 16,
                  yoffset: 0,
                  xoffset: 0,
                  image: bubbleImage,
                  name: 'ImageAttachment',
                },
              }),
            },
          ],
        },
      ],
    });
  }

  function replaceNode(nodes, parentId, viewId) {
    let newNodes = nodes.filter(obj => obj.layerId === 'DVTOPO_LEVEL_2');
    const sortedArr = newNodes.sort((a, b) => {
      if (a.y > b.y) {
        return -1;
      } else if (a.y === b.y && a.x > b.x) {
        return 1;
      } else if (a.y === b.y && a.x < b.x) {
        return -1;
      } else {
        return 1;
      }
    });
    const uniqueArr = getUniqueArr(topologyOriginData);
    const parentNode = uniqueArr.find(item => item.id === parentId);
    const alarmState =
      topologyOriginData.alarms.find(alarm => alarm.id === parentId)
        ?.alarmState || defaultAlarmState;
    const aggregateState =
      topologyOriginData.aggregateAlarm.find(alarm => alarm.id === parentId)
        ?.alarmState || defaultAlarmState;
    const curAlarmLevel =
      ALARM_LEVELS.find(item => Number(alarmState[item]) > 0 || Number(aggregateState[item]) > 0) || 'warning';
    let { image } = parentNode;
    if (parentNode.image.startsWith('DV_')) {
      image =
        curAlarmLevel === 'warning' ?
          getCurImage(parentNode.image) :
          `${getCurImage(parentNode.image)}_${curAlarmLevel}`;
    }
    const secondLargestValue = sortedArr[sortedArr.length - 2];
    piu.emit('DynamicMap.optElements', {
      action: 'remove',
      nodes: [
        {
          resId: secondLargestValue.id,
        },
      ],
      viewId: viewId,
    });
    piu.emit('DynamicMap.optElements', {
      action: 'add',
      nodes: [
        {
          id: parentId,
          resId: parentId,
          name: parentNode.name,
          image,
          x: secondLargestValue.x,
          y: secondLargestValue.y,
          properties: secondLargestValue.properties,
        },
      ],
      viewId: viewId,
    });
    const secondNodeLevel = Number(
      secondLargestValue.properties.layerId.slice(
        secondLargestValue.properties.layerId.lastIndexOf('_') + 1,
      ),
    );

    replcaeData[secondNodeLevel] = parentId;
    dispatch({
      replcaeData: replcaeData,
    });
    if (parentNode.isOpen) {
      let bubbleImage =
        parentNode.bubbleImage === undefined ? 'down' : parentNode.bubbleImage;
      addOverlay(parentId, bubbleImage);
    }
  }

  const clickNameAction = (cell, row, event) => {
    if (cell.key === 'name') {
      let viewId =
        getTopologyContainerId() === 'subTopologyBox' ?
          'dvtopoSubViewId' :
          'dvtopoViewId';
      let parentId = row.data[0].value;
      let { nodes } = window.DynamicMap.optElements({
        viewId: viewId,
        action: 'queryAllEles',
      });
      const exists = nodes.some(obj => obj.id === parentId);
      if (!exists) {
        replaceNode(nodes, parentId, viewId);
      }
      piu.emit('DynamicMap.optElements', {
        action: 'select',
        nodes: [
          {
            resId: parentId,
          },
        ],
        viewId: viewId,
      });
    }
  };

  return (
    <div id="topo_right_container">
      <Table
        columns={columns}
        dataset={topoShowData}
        onCellClick={(cell, row, event) => {
          clickNameAction(cell, row, event);
        }}
      />
    </div>
  );
}

const sortNodes = nodes => {
  nodes.sort((a, b) => compareFun(a[1].props.children, b[1].props.children));
};
export default SearchResult;
