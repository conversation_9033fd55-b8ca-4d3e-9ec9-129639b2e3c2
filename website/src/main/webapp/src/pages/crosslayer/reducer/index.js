import {
  DEFAULT_SCALE,
  DEFAULT_SCENE_OPTION,
  SUB_DEFAULT_SCALE,
} from '../const';

export const initState = {
  iframeOpen: false,
  leftTreeData: [],
  selectedKeys: [],
  expandedKeys: [],
  topologyData: null,
  topologyOriginData: null,
  sceneOptions: [DEFAULT_SCENE_OPTION],
  scene: 0,
  subScale: SUB_DEFAULT_SCALE,
  scale: DEFAULT_SCALE,
  expand: false,
  pageType: 'home',
  detailData: {},
  subTopologyData: null,
  showNoDataTip: false,
  selectedNode: null,
  piu: null,
  theme: '',
  topoSearchData: '',
  topoShowData: [],
  showMore: {
    open: false,
    showId: '',
  },
  showRightPanel: false,
  searchResShow: true,
  replcaeData: {},
};

export const reducer = (state, action) => ({ ...state, ...action });
