#cross_layer {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    position: fixed;
    width: 100vw;
    background-color: #fff;
    z-index: 9999;

    .header_crumb {
      margin-left: 48px;
      font-size: 20px;

      .eui_Crumbs_span,
      a {
        cursor: pointer;
      }

      .eui_Crumbs_span:hover,
      a:hover {
        color: #2e94ff;
      }
    }

    .header_right {
      display: flex;
    }
  }

  .content {
    height: calc(100vh - 130px);
    padding-top: 60px;
    position: relative;

    #split_box {
      height: calc(100vh - 124px) !important;
      overflow-y: scroll !important;

      .Pane1 {
        overflow-x: hidden !important;
      }
    }

    .left_tree {
      display: flex;
      background-color: #fff;
      width: 100%;
      min-width: 260px;

      #left_tree_id {
        width: calc(100% - 80px);
      }

      .iconBlockChoose {
        border-left: 0.25rem solid #0067d1;
        background-color: rgba(25, 25, 25, 0.1);
        padding: 1rem 1rem 1rem 0.75rem;
      }

      .iconBlock {
        padding: 1rem;
      }
    }

    #rightMenuBar {
      height: 100%;
      background-size: 1920px 1080px;
      background-repeat: repeat;
      overflow-y: scroll;
      position: relative;
    }

    #subTopologyBox {
      height: calc(100vh - 125px);
      background-size: 1920px 1080px;
      background-repeat: repeat;
      position: absolute;
      z-index: 10;
      width: 100%;
      top: 60px;
    }

    #bottomPanel {
      .no_data_tip {
        font-size: 20px;
        font-weight: bold;
        white-space: normal;
        word-wrap: break-word;
        color: #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        p {
          max-width: 800px;
        }
      }
    }
  }
}

#legend_box {
  height: 200px !important;
  position: fixed !important;

  .legend_content_lightday {
    background-color: #f1f1f1;
    display: flex;
    flex-wrap: wrap;
    padding: 10px;

    .legend_item {
      display: flex;
      align-items: center;
      width: 50%;
      height: 50px;
      font-size: 14px;

      .legend_img {
        width: 1.2rem;
        height: 1.2rem;
      }

      .legend_text {
        margin-left: 5px;
      }
    }
  }

  .legend_content_evening {
    background-color: #393939;
    display: flex;
    flex-wrap: wrap;
    padding: 10px;

    .legend_item {
      display: flex;
      align-items: center;
      width: 50%;
      height: 50px;
      font-size: 14px;
      color: #eeeeee;

      .legend_img {
        width: 1.2rem;
        height: 1.2rem;
      }

      .legend_text {
        margin-left: 5px;
      }
    }
  }
}

#tooltipId {
  position: absolute;
  padding: 4px 8px;
  border: 1px solid #e6e6e6 !important;
  z-index: 100000;
  background-color: white;
  white-space: normal;
  word-wrap: break-word;
  max-width: 400px;
  font-size: 14px;

  .tooltip_box {
    .tooltip_title {
      font-weight: bold;
    }

    .tooltip_name {
      margin-left: 10px;
      word-break: break-all;
  }

  .alarm_content {
    display: flex;
    padding-left: 10px;
  }
}
}

.topo-common-panel-close-vertical-right {
width: 100%;
height: 100%;
background: url("data:image/svg+xml;base64,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")
    no-repeat;
  background-size: 100% 100%;
}

.topo-common-panel-expend-vertical-right {
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml;base64,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")
    no-repeat;
  background-size: 100% 100%;
}

.right-panel {
  position: relative;
  z-index: 999;
  max-width: 600px;
  height: calc(100vh - 130px);
  padding-top: 60px;
  float: right;
  background-color: white;
}

.dv-topo-right-panel-div-btn {
  top: calc(50% - 2.75rem);
  width: 1rem;
  height: 5.5rem;
  position: absolute;
}

.dv-topo-right-panel-div-subTopologyBox {
  margin-left: -1rem;
}

.dv-topo-right-panel-div-bottomPanel {
  margin-left: -1.9rem;
}

.dv-topo-common-focus:focus {
  outline: 2px dotted #186fc2;
  outline-offset: 2px;
}

.topo_right_container {
  overflow: auto;
  height: 100%;
}

#topo_right_container {
  height: 100%;
}

.right-panel-container {
  height: 100%;
}
