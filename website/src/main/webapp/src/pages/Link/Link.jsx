import React, { useContext, useState } from 'react';
import { Dialog, TextField } from 'eview-ui';
import { GlobalContext } from '../Context';
import { Store, CREATE_LINK_TYPE } from '../../utils/Common';
import i18n from '../../utils/locales';
import Http from '../../utils/Http';
import Message from '../message/Message';

function Link(props) {
  const [state, dispatch] = useContext(GlobalContext);

  const [message, setMessage] = useState({
    isOpen: false,
  });

  const closeSuccessPanel = () => {
    setMessage({
      isOpen: false,
    });
  };

  const successPanle = () => {
    setMessage({
      type: 'success',
      isOpen: true,
      content: i18n.translate('topology.create.link.success'),
    });
  };
  const errorPanle = () => {
    setMessage({
      type: 'error',
      isOpen: true,
      content: i18n.translate('topology.create.link.fail'),
    });
  };

  const cancelButtonText = i18n.translate('topo.link.cancel');
  const okButtonText = i18n.translate('topo.link.confirm');
  let linkNameValidation = true;
  const validator = value => {
    let validationReturnMap = {};
    linkNameValidation = true;
    validationReturnMap.result = true;
    if (value.length > 128) {
      linkNameValidation = false;
      validationReturnMap.result = false;
      validationReturnMap.message = i18n.translate(
        'topology.link.nameInputValidFail',
      );
    }

    // 链路名称白名单校验，匹配中文，英文字母大小写，数字，下滑线，横杠
    const rule = /^[\u4e00-\u9fa5_a-zA-Z0-9\-]+$/;
    if (value.trim() === '' || !rule.test(value)) {
      linkNameValidation = false;
      validationReturnMap.result = false;
      validationReturnMap.message = i18n.translate(
        'topology.link.inputValidFail',
      );
    }
    return validationReturnMap;
  };

  const createLink = (topoId, leftId, rightId, fromObjId, toObjId) => {
    Http.post(true, {
      url: '/rest/dvtopowebsite/v1/topo/link',
      timeout: 1000 * 3,
      data: {
        label: textFieldValue.getValue(),
        leftId: leftId,
        rightId: rightId,
        topoId: topoId,
        topoType: topoId === 101 ? CREATE_LINK_TYPE.resource : CREATE_LINK_TYPE.custom,
        leftObjId: fromObjId,
        rightObjId: toObjId,
      },
      success: data => {
        if (
          data &&
          data.data &&
          data.resultMessage &&
          data.resultMessage === '200'
        ) {
          successPanle();
        } else {
          errorPanle();
        }
      },
      error: e => {
        errorPanle();
      },
    });
  };

  let style = { width: '430px' };
  let labelStyle = { display: 'block', paddingLeft: '0px' };
  let divStyle = { margin: '10px 20px', display: 'inline-block' };
  let textFieldValue;
  return (
    <>
      <Message {...message} closeSuccessPanel={closeSuccessPanel} />
      <Dialog
        title={i18n.translate('topo.link.title')}
        size={[640, 230]}
        style={{
          maxHeight: 'unset',
          minHeight: 'unset',
          maxWidth: '640px',
          minWidth: '640px',
        }}
        isOpen={state.isopen}
        onClose={() => {
          dispatch({ type: 'hidden' });
          Store.linkMenuRefresh = false;
        }}
        modal={false}
        movable={false}
        children={
          <div style={divStyle}>
            <TextField
              label={`${i18n.translate('topo.link.content')} :`}
              labelStyle={labelStyle}
              required={true}
              hideRequiredMark={true}
              hintType="tip"
              validator={validator}
              autoComplete="off"
              inputStyle={style}
              ref={textField => (textFieldValue = textField)}
            />
          </div>
        }
        resizable={false}
        buttons={[
          {
            text: cancelButtonText,
            onClick: () => {
              dispatch({ type: 'hidden' });
              Store.linkMenuRefresh = false;
            },
          },
          {
            text: okButtonText,
            onClick: () => {
              if (
                textFieldValue.getValue().trim() === '' ||
                !linkNameValidation
              ) {
                textFieldValue.validate();
                textFieldValue.focus();
                return;
              }
              createLink(
                props.data.fromNode._clientMap.topoId,
                props.data.fromNode._clientMap.nativeId,
                props.data.toNode._clientMap.nativeId,
                props.data.fromNode._clientMap.objId,
                props.data.toNode._clientMap.objId,
              );
              dispatch({ type: 'hidden' });
              Store.linkMenuRefresh = false;
            },
          },
        ]}
      />
    </>
  );
}
export default Link;
