<svg width="56.000000" height="56.000000" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_1_3115_dd" x="27.558838" y="29.022461" width="16.303711" height="13.087585" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.20392 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3116_dd" x="36.662598" y="29.022461" width="7.200195" height="7.806763" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3117_dd" x="34.912598" y="30.072449" width="7.200195" height="7.806763" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3118_dd" x="27.558838" y="31.472473" width="12.453613" height="10.637573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3119_dd" x="30.133789" y="25.515198" width="18.605469" height="27.214111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="1.6689" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_1_3128_dd" x="27.558838" y="20.622437" width="16.303711" height="13.087585" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.20392 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3129_dd" x="36.662598" y="20.622437" width="7.200195" height="7.806763" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3130_dd" x="34.912598" y="21.672485" width="7.200195" height="7.806763" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3131_dd" x="27.558838" y="23.072449" width="12.453613" height="10.637573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_3132_dd" x="30.133789" y="17.115234" width="18.605469" height="27.214111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="1.6689" result="effect_layerBlur_1"/>
		</filter>
		<linearGradient x1="14.791174" y1="35.323433" x2="41.854931" y2="35.323433" id="paint_linear_1_3114_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#2F54A5"/>
			<stop offset="0.400101" stop-color="#73BDFF"/>
			<stop offset="0.565808" stop-color="#3F59BF"/>
			<stop offset="1.000000" stop-color="#425EAC"/>
		</linearGradient>
		<linearGradient x1="27.959486" y1="35.036301" x2="27.959486" y2="15.976225" id="paint_linear_1_3120_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0C277F"/>
			<stop offset="1.000000" stop-color="#6DB3F7"/>
		</linearGradient>
		<linearGradient x1="27.959488" y1="28.782858" x2="27.959488" y2="36.691605" id="paint_linear_1_3120_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#B7FBFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#B7FBFF"/>
		</linearGradient>
		<linearGradient x1="20.756046" y1="28.371944" x2="35.611088" y2="28.371944" id="paint_linear_1_3122_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#282E52"/>
			<stop offset="0.400101" stop-color="#5289C1"/>
			<stop offset="0.565808" stop-color="#283472"/>
			<stop offset="1.000000" stop-color="#374A79"/>
		</linearGradient>
		<linearGradient x1="14.791174" y1="26.923470" x2="41.854931" y2="26.923470" id="paint_linear_1_3127_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#2F54A5"/>
			<stop offset="0.400101" stop-color="#73BDFF"/>
			<stop offset="0.565808" stop-color="#3F59BF"/>
			<stop offset="1.000000" stop-color="#425EAC"/>
		</linearGradient>
		<linearGradient x1="27.959484" y1="27.373013" x2="27.959484" y2="12.194398" id="paint_linear_1_3133_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#7CBFFF"/>
			<stop offset="1.000000" stop-color="#2E4F9B"/>
		</linearGradient>
		<linearGradient x1="27.959486" y1="20.347755" x2="27.959486" y2="28.222565" id="paint_linear_1_3133_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#BBFFFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#BBFFFF"/>
		</linearGradient>
		<linearGradient x1="21.129889" y1="19.986296" x2="35.214951" y2="19.986296" id="paint_linear_1_3135_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#364482"/>
			<stop offset="0.407087" stop-color="#72BDFF"/>
			<stop offset="0.565808" stop-color="#2F3F87"/>
			<stop offset="1.000000" stop-color="#40578D"/>
		</linearGradient>
		<linearGradient x1="28.034025" y1="19.877136" x2="28.034025" y2="24.022192" id="paint_linear_1_3136_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#BAD6F4"/>
			<stop offset="1.000000" stop-color="#FFFFFF"/>
		</linearGradient>
	</defs>
	<rect id="应用网元备份 32" width="56.000000" height="56.000000" fill="#2F2F2F" fill-opacity="0"/>
	<mask id="mask1_3114" mask-type="alpha" maskUnits="userSpaceOnUse" x="14.012451" y="28.724243" width="27.842529" height="14.125732">
		<path id="蒙版" d="M14.01 28.92L25.52 35.67C26.86 36.45 28.51 36.45 29.86 35.68L41.85 28.72L41.85 34.29C41.85 34.76 41.59 35.2 41.18 35.42L29.41 41.9C28.34 42.49 27.03 42.48 25.97 41.88L14.67 35.54C14.26 35.31 14.01 34.88 14.01 34.42L14.01 28.92Z" fill="url(#paint_linear_1_3114_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</mask>
	<path id="蒙版" d="M14.01 28.92L25.52 35.67C26.86 36.45 28.51 36.45 29.86 35.68L41.85 28.72L41.85 34.29C41.85 34.76 41.59 35.2 41.18 35.42L29.41 41.9C28.34 42.49 27.03 42.48 25.97 41.88L14.67 35.54C14.26 35.31 14.01 34.88 14.01 34.42L14.01 28.92Z" fill="url(#paint_linear_1_3114_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g mask="url(#mask1_3114)">
		<g filter="url(#filter_1_3115_dd)">
			<mask id="mask_1_3115" fill="white">
				<path id="形状结合" d="M39.6624 32.7452L40.8625 32.0225L40.8625 33.1065L39.6624 33.8292L39.6624 32.7452ZM37.9124 33.7952L39.1125 33.0724L39.1125 34.1566L37.9124 34.8793L37.9124 33.7952ZM37.0125 34.4725L30.5588 38.026L30.5588 39.11L37.0125 35.5565L37.0125 34.4725Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
			</mask>
			<path id="形状结合" d="M39.6624 32.7452L40.8625 32.0225L40.8625 33.1065L39.6624 33.8292L39.6624 32.7452ZM37.9124 33.7952L39.1125 33.0724L39.1125 34.1566L37.9124 34.8793L37.9124 33.7952ZM37.0125 34.4725L30.5588 38.026L30.5588 39.11L37.0125 35.5565L37.0125 34.4725Z" clip-rule="evenodd" fill="#34FFFF" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_1_3115)"/>
			<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
		</g>
	</g>
	<g mask="url(#mask1_3114)">
		<g opacity="0.328780" filter="url(#filter_1_3119_dd)">
			<path id="矩形" d="M41.85 30.52L43.73 35.64L43.73 47.72L35.14 47.72L36.82 47.24C36.82 47.24 39.25 43.42 40.34 40.94C41.42 38.47 41.85 30.52 41.85 30.52Z" fill="#00C5F9" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
		</g>
	</g>
	<path id="矩形" d="M14.35 28.22L25.92 21.57C26.98 20.96 28.29 20.94 29.37 21.53L41.49 28.08C41.98 28.34 41.98 29.03 41.5 29.3L29.83 35.75C28.46 36.51 26.8 36.5 25.45 35.73L14.35 29.43C13.89 29.16 13.88 28.49 14.35 28.22Z" fill="url(#paint_linear_1_3120_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="矩形" d="M14.35 28.22L25.92 21.57C26.98 20.96 28.29 20.94 29.37 21.53L41.49 28.08C41.98 28.34 41.98 29.03 41.5 29.3L29.83 35.75C28.46 36.51 26.8 36.5 25.45 35.73L14.35 29.43C13.89 29.16 13.88 28.49 14.35 28.22ZM14.6 28.65L26.17 22.01C26.39 21.88 26.63 21.78 26.88 21.71C27.12 21.64 27.38 21.61 27.64 21.6C27.91 21.6 28.16 21.63 28.41 21.69C28.66 21.75 28.9 21.84 29.13 21.97L41.26 28.52C41.29 28.54 41.32 28.56 41.33 28.59C41.35 28.62 41.36 28.65 41.36 28.69C41.36 28.73 41.35 28.76 41.33 28.79C41.32 28.82 41.29 28.84 41.26 28.86L29.58 35.32C29.28 35.48 28.97 35.61 28.64 35.69C28.32 35.77 27.98 35.81 27.64 35.81C27.29 35.81 26.96 35.77 26.63 35.68C26.31 35.59 26 35.46 25.7 35.29L14.6 28.99C14.57 28.97 14.54 28.95 14.53 28.92C14.51 28.89 14.5 28.86 14.5 28.82C14.5 28.79 14.51 28.75 14.53 28.72C14.54 28.7 14.57 28.67 14.6 28.65Z" fill="url(#paint_linear_1_3120_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<mask id="mask1_3122" mask-type="alpha" maskUnits="userSpaceOnUse" x="20.328613" y="24.430237" width="15.282471" height="8.437317">
		<path id="蒙版" d="M21.39 28.1L26.25 25.31C27.21 24.76 28.39 24.74 29.36 25.27L34.47 28.03C34.96 28.29 34.96 28.99 34.47 29.26L29.33 32.03C28.37 32.55 27.22 32.54 26.27 32.02L21.4 29.32C20.93 29.05 20.92 28.37 21.39 28.1Z" fill="url(#paint_linear_1_3122_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</mask>
	<path id="蒙版" d="M21.39 28.1L26.25 25.31C27.21 24.76 28.39 24.74 29.36 25.27L34.47 28.03C34.96 28.29 34.96 28.99 34.47 29.26L29.33 32.03C28.37 32.55 27.22 32.54 26.27 32.02L21.4 29.32C20.93 29.05 20.92 28.37 21.39 28.1Z" fill="url(#paint_linear_1_3122_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g mask="url(#mask1_3122)">
		<path id="矩形" d="M22.98 31.3L26.9 28.95C27.59 28.53 28.46 28.53 29.16 28.95L33.08 31.3L33.08 34.54L22.98 34.54L22.98 31.3Z" fill="#2EEBFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<mask id="mask1_3127" mask-type="alpha" maskUnits="userSpaceOnUse" x="14.012451" y="20.324280" width="27.842529" height="14.125732">
		<path id="蒙版" d="M14.01 20.52L25.52 27.27C26.86 28.05 28.51 28.05 29.86 27.28L41.85 20.32L41.85 25.89C41.85 26.36 41.59 26.8 41.18 27.02L29.41 33.5C28.34 34.09 27.03 34.08 25.97 33.48L14.67 27.14C14.26 26.91 14.01 26.48 14.01 26.02L14.01 20.52Z" fill="url(#paint_linear_1_3127_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</mask>
	<path id="蒙版" d="M14.01 20.52L25.52 27.27C26.86 28.05 28.51 28.05 29.86 27.28L41.85 20.32L41.85 25.89C41.85 26.36 41.59 26.8 41.18 27.02L29.41 33.5C28.34 34.09 27.03 34.08 25.97 33.48L14.67 27.14C14.26 26.91 14.01 26.48 14.01 26.02L14.01 20.52Z" fill="url(#paint_linear_1_3127_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g mask="url(#mask1_3127)">
		<g filter="url(#filter_1_3128_dd)">
			<mask id="mask_1_3128" fill="white">
				<path id="形状结合" d="M39.6624 24.3452L40.8625 23.6224L40.8625 24.7065L39.6624 25.4292L39.6624 24.3452ZM37.9124 25.3951L39.1125 24.6724L39.1125 25.7565L37.9124 26.4792L37.9124 25.3951ZM37.0125 26.0724L30.5588 29.626L30.5588 30.71L37.0125 27.1565L37.0125 26.0724Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
			</mask>
			<path id="形状结合" d="M39.6624 24.3452L40.8625 23.6224L40.8625 24.7065L39.6624 25.4292L39.6624 24.3452ZM37.9124 25.3951L39.1125 24.6724L39.1125 25.7565L37.9124 26.4792L37.9124 25.3951ZM37.0125 26.0724L30.5588 29.626L30.5588 30.71L37.0125 27.1565L37.0125 26.0724Z" clip-rule="evenodd" fill="#34FFFF" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_1_3128)"/>
			<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
		</g>
	</g>
	<g mask="url(#mask1_3127)">
		<g opacity="0.328780" filter="url(#filter_1_3132_dd)">
			<path id="矩形" d="M41.85 22.12L43.73 27.24L43.73 39.32L35.14 39.32L36.82 38.84C36.82 38.84 39.25 35.02 40.34 32.54C41.42 30.07 41.85 22.12 41.85 22.12Z" fill="#00C5F9" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
		</g>
	</g>
	<path id="矩形" d="M14.36 19.82L25.92 13.17C26.98 12.56 28.29 12.54 29.37 13.13L41.49 19.67C41.97 19.94 41.98 20.63 41.49 20.9L29.81 27.3C28.46 28.04 26.81 28.03 25.46 27.28L14.36 21.03C13.89 20.76 13.89 20.09 14.36 19.82Z" fill="url(#paint_linear_1_3133_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="矩形" d="M14.36 19.82L25.92 13.17C26.98 12.56 28.29 12.54 29.37 13.13L41.49 19.67C41.97 19.94 41.98 20.63 41.49 20.9L29.81 27.3C28.46 28.04 26.81 28.03 25.46 27.28L14.36 21.03C13.89 20.76 13.89 20.09 14.36 19.82ZM14.61 20.25L26.17 13.61C26.39 13.48 26.63 13.38 26.88 13.31C27.12 13.24 27.38 13.21 27.64 13.2C27.91 13.2 28.16 13.23 28.41 13.29C28.66 13.35 28.9 13.44 29.13 13.57L41.25 20.11C41.28 20.13 41.31 20.16 41.33 20.19C41.34 20.21 41.35 20.25 41.35 20.29C41.35 20.33 41.35 20.36 41.33 20.39C41.31 20.42 41.29 20.44 41.25 20.46L29.57 26.86C29.27 27.03 28.96 27.15 28.64 27.23C28.31 27.31 27.98 27.35 27.64 27.35C27.29 27.35 26.96 27.31 26.64 27.22C26.32 27.14 26.01 27.01 25.71 26.84L14.61 20.59C14.57 20.57 14.55 20.55 14.53 20.52C14.52 20.49 14.51 20.46 14.51 20.42C14.51 20.38 14.52 20.35 14.53 20.32C14.55 20.29 14.57 20.27 14.61 20.25Z" fill="url(#paint_linear_1_3133_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<mask id="mask1_3135" mask-type="alpha" maskUnits="userSpaceOnUse" x="20.724609" y="16.248901" width="14.490234" height="8.000000">
		<path id="蒙版" d="M21.79 19.7L26.26 17.13C27.22 16.58 28.4 16.56 29.37 17.09L34.08 19.63C34.56 19.89 34.56 20.59 34.08 20.86L29.34 23.41C28.38 23.93 27.23 23.92 26.28 23.4L21.8 20.91C21.32 20.65 21.32 19.97 21.79 19.7Z" fill="url(#paint_linear_1_3135_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</mask>
	<path id="蒙版" d="M21.79 19.7L26.26 17.13C27.22 16.58 28.4 16.56 29.37 17.09L34.08 19.63C34.56 19.89 34.56 20.59 34.08 20.86L29.34 23.41C28.38 23.93 27.23 23.92 26.28 23.4L21.8 20.91C21.32 20.65 21.32 19.97 21.79 19.7Z" fill="url(#paint_linear_1_3135_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g mask="url(#mask1_3135)">
		<path id="矩形" d="M22.98 22.9L26.9 20.55C27.59 20.13 28.46 20.13 29.16 20.55L33.08 22.9L33.08 26.14L22.98 26.14L22.98 22.9Z" fill="url(#paint_linear_1_3136_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
</svg>
