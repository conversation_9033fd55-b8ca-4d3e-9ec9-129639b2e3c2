<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 4</title>
    <defs>
        <filter x="-22.2%" y="-29.6%" width="144.4%" height="159.3%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="83.925659%" y1="50%" x2="0.765584643%" y2="50%" id="linearGradient-2">
            <stop stop-color="#274D9E" offset="0%"></stop>
            <stop stop-color="#3172B5" offset="58.0462262%"></stop>
            <stop stop-color="#3167BD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.1222888%" id="linearGradient-3">
            <stop stop-color="#3464DA" offset="0%"></stop>
            <stop stop-color="#5CACFF" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="47.0469649%" cy="100%" fx="47.0469649%" fy="100%" r="88.3060015%" gradientTransform="translate(0.470470,1.000000),scale(0.666667,1.000000),rotate(106.003095),translate(-0.470470,-1.000000)" id="radialGradient-4">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.4" offset="22.0703433%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图2（持续更新）" transform="translate(-72.000000, -586.000000)">
            <g id="编组-5备份" transform="translate(72.000000, 586.000000)">
                <g id="编组-3" transform="translate(2.000000, 6.000000)">
                    <g id="编组" filter="url(#filter-1)" transform="translate(0.000000, -0.000000)">
                        <path d="M18,27 C27.9411255,27 36,21.627417 36,15 C36,10.581722 36,9.24838867 36,11 L0,12 C0,13.3836854 0,14.3836854 0,15 C0,21.627417 8.0588745,27 18,27 Z" id="椭圆形备份-2" fill="url(#linearGradient-2)"></path>
                        <ellipse id="椭圆形备份-3" stroke="url(#radialGradient-4)" stroke-width="0.5" fill="url(#linearGradient-3)" cx="18" cy="12" rx="17.75" ry="11.75"></ellipse>
                    </g>
                    <g id="编组-2" transform="translate(5.000000, 3.500000)">
                        <path d="M13,0 C20.1797017,0 26,3.80557963 26,8.5 C26,13.1944204 20.1797017,17 13,17 C5.82029825,17 0,13.1944204 0,8.5 C0,3.80557963 5.82029825,0 13,0 Z M13,6 C10.6067661,6 8.66666667,7.11928813 8.66666667,8.5 C8.66666667,9.88071187 10.6067661,11 13,11 C15.3932339,11 17.3333333,9.88071187 17.3333333,8.5 C17.3333333,7.11928813 15.3932339,6 13,6 Z" id="形状结合" fill=""></path>
                        <path d="M13.0337984,5.4594951 C15.0468382,5.47229102 16.8095415,6.23851225 17.5772536,7.31381871 L25.8071879,8.60920892 C25.9455823,8.63099467 26.0513089,8.70182827 26.0733065,8.78750028 C26.1027241,8.90207009 25.9749209,9.00557401 25.7878501,9.01868275 L17.6378647,9.59036546 C16.9468073,10.6122843 15.2980146,11.3180528 13.3373371,11.3055897 C10.6874869,11.2887459 8.47140951,9.96639846 8.38758961,8.35204401 C8.30376971,6.73768955 10.3839482,5.44265129 13.0337984,5.4594951 Z M13.1205237,7.12980786 C11.9848736,7.12258908 11.0933686,7.67760548 11.1292914,8.36947167 C11.1652142,9.06133787 12.1149617,9.62805821 13.2506117,9.63527698 C14.3862618,9.64249576 15.2777669,9.08747936 15.2418441,8.39561317 C15.2059212,7.70374697 14.2561738,7.13702663 13.1205237,7.12980786 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>