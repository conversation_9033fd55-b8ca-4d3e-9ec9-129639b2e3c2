<svg width="112.000000" height="112.000000" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_238_53646_dd" x="57.592163" y="71.620667" width="26.541504" height="20.246796" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.20392 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53643_dd" x="75.409790" y="71.620667" width="8.723877" height="9.911316" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53644_dd" x="71.984009" y="73.675568" width="8.723877" height="9.911316" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53645_dd" x="57.592102" y="76.415649" width="19.006409" height="15.451843" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53647_dd" x="64.426758" y="66.551544" width="27.455750" height="44.304962" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="1.77321" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_238_53659_dd" x="57.592163" y="55.179871" width="26.541504" height="20.247009" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.20392 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53656_dd" x="75.409790" y="55.179871" width="8.723877" height="9.911316" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53657_dd" x="71.984009" y="57.234985" width="8.723877" height="9.911316" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53658_dd" x="57.592102" y="59.975037" width="19.006409" height="15.451843" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.0625"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.91373 0 0 0 0 0.99608 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_238_53660_dd" x="64.426758" y="50.110809" width="27.455750" height="44.304962" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="1.77321" result="effect_layerBlur_1"/>
		</filter>
		<clipPath id="clip238_53560">
			<rect id="第三方渠道" width="76.000000" height="76.000000" transform="translate(18.000000 28.000000)" fill="white" fill-opacity="0"/>
		</clipPath>
		<clipPath id="clip238_53754">
			<rect id="Thrid" width="112.000000" height="112.000000" fill="white" fill-opacity="0"/>
		</clipPath>
		<linearGradient x1="29.918549" y1="81.268837" x2="82.888466" y2="81.268837" id="paint_linear_238_53642_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#2F54A5"/>
			<stop offset="0.400101" stop-color="#73BDFF"/>
			<stop offset="0.565808" stop-color="#3F59BF"/>
			<stop offset="1.000000" stop-color="#425EAC"/>
		</linearGradient>
		<linearGradient x1="55.691849" y1="80.706863" x2="55.691849" y2="43.401962" id="paint_linear_238_53649_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0C277F"/>
			<stop offset="1.000000" stop-color="#6DB3F7"/>
		</linearGradient>
		<linearGradient x1="55.691849" y1="68.467453" x2="55.691849" y2="83.946671" id="paint_linear_238_53649_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#B7FBFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#B7FBFF"/>
		</linearGradient>
		<linearGradient x1="41.592995" y1="67.663116" x2="70.667686" y2="67.663116" id="paint_linear_238_53650_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#282E52"/>
			<stop offset="0.400101" stop-color="#5289C1"/>
			<stop offset="0.565808" stop-color="#283472"/>
			<stop offset="1.000000" stop-color="#374A79"/>
		</linearGradient>
		<linearGradient x1="29.918547" y1="64.828102" x2="82.888466" y2="64.828102" id="paint_linear_238_53655_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#2F54A5"/>
			<stop offset="0.400101" stop-color="#73BDFF"/>
			<stop offset="0.565808" stop-color="#3F59BF"/>
			<stop offset="1.000000" stop-color="#425EAC"/>
		</linearGradient>
		<linearGradient x1="55.691856" y1="65.707993" x2="55.691856" y2="35.999996" id="paint_linear_238_53662_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#7CBFFF"/>
			<stop offset="1.000000" stop-color="#2E4F9B"/>
		</linearGradient>
		<linearGradient x1="55.691853" y1="51.957973" x2="55.691853" y2="67.370766" id="paint_linear_238_53662_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#BBFFFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#BBFFFF"/>
		</linearGradient>
		<linearGradient x1="42.324776" y1="51.250542" x2="69.892441" y2="51.250542" id="paint_linear_238_53663_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#364482"/>
			<stop offset="0.407087" stop-color="#72BDFF"/>
			<stop offset="0.565808" stop-color="#2F3F87"/>
			<stop offset="1.000000" stop-color="#40578D"/>
		</linearGradient>
		<linearGradient x1="55.838066" y1="51.036896" x2="55.838066" y2="59.149712" id="paint_linear_238_53664_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#BAD6F4"/>
			<stop offset="1.000000" stop-color="#FFFFFF"/>
		</linearGradient>
	</defs>
	<rect id="Thrid" width="112.000000" height="112.000000" fill="#FFFFFF" fill-opacity="0"/>
	<g clip-path="url(#clip238_53754)">
		<rect id="第三方渠道" width="76.000000" height="76.000000" transform="translate(18.000000 28.000000)" fill="#FFFFFF" fill-opacity="0"/>
		<g clip-path="url(#clip238_53560)">
			<mask id="mask238_53642" mask-type="alpha" maskUnits="userSpaceOnUse" x="28.394409" y="68.352722" width="54.494080" height="27.647278">
				<path id="蒙版" d="M28.39 68.75L52.86 83.08C54.28 83.91 56.04 83.92 57.47 83.09L82.88 68.35L82.88 79.94C82.88 80.44 82.61 80.9 82.17 81.14L56.99 94.99C55.85 95.61 54.47 95.61 53.33 94.97L29.09 81.37C28.66 81.13 28.39 80.67 28.39 80.17L28.39 68.75Z" fill="url(#paint_linear_238_53642_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</mask>
			<path id="蒙版" d="M28.39 68.75L52.86 83.08C54.28 83.91 56.04 83.92 57.47 83.09L82.88 68.35L82.88 79.94C82.88 80.44 82.61 80.9 82.17 81.14L56.99 94.99C55.85 95.61 54.47 95.61 53.33 94.97L29.09 81.37C28.66 81.13 28.39 80.67 28.39 80.17L28.39 68.75Z" fill="url(#paint_linear_238_53642_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			<g mask="url(#mask238_53642)">
				<g filter="url(#filter_238_53646_dd)">
					<mask id="mask_238_53646" fill="white">
						<path id="形状结合" d="M78.5974 76.2227L80.9462 74.8082L80.9462 76.93L78.5974 78.3445L78.5974 76.2227ZM75.1716 78.2776L77.5204 76.8631L77.5204 78.9848L75.1716 80.3994L75.1716 78.2776ZM73.4111 79.6031L60.7797 86.5582L60.7797 88.68L73.4111 81.7249L73.4111 79.6031Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
					</mask>
					<path id="形状结合" d="M78.5974 76.2227L80.9462 74.8082L80.9462 76.93L78.5974 78.3445L78.5974 76.2227ZM75.1716 78.2776L77.5204 76.8631L77.5204 78.9848L75.1716 80.3994L75.1716 78.2776ZM73.4111 79.6031L60.7797 86.5582L60.7797 88.68L73.4111 81.7249L73.4111 79.6031Z" clip-rule="evenodd" fill="#34FFFF" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_238_53646)"/>
					<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
				</g>
			</g>
			<g mask="url(#mask238_53642)">
				<g opacity="0.328780" filter="url(#filter_238_53647_dd)">
					<path id="矩形" d="M82.88 71.87L86.56 81.89L86.56 105.53L69.74 105.53L73.03 104.6C73.03 104.6 77.8 97.12 79.92 92.28C82.04 87.43 82.88 71.87 82.88 71.87Z" fill="#00C5F9" fill-opacity="1.000000" fill-rule="evenodd"/>
					<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
				</g>
			</g>
			<path id="矩形" d="M28.12 67.91L53.23 53.48C54.36 52.83 55.75 52.81 56.9 53.43L83.19 67.63C83.7 67.91 83.71 68.65 83.19 68.93L57.39 83.2C55.94 84 54.17 83.99 52.73 83.17L28.12 69.2C27.62 68.91 27.62 68.2 28.12 67.91Z" fill="url(#paint_linear_238_53649_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="矩形" d="M28.12 67.91L53.23 53.48C54.36 52.83 55.75 52.81 56.9 53.43L83.19 67.63C83.7 67.91 83.71 68.65 83.19 68.93L57.39 83.2C55.94 84 54.17 83.99 52.73 83.17L28.12 69.2C27.62 68.91 27.62 68.2 28.12 67.91ZM28.38 68.37L53.5 53.94Q54.28 53.49 55.07 53.48Q55.85 53.47 56.65 53.9L82.94 68.1Q82.99 68.13 83.02 68.18Q83.04 68.22 83.04 68.28Q83.04 68.34 83.02 68.39Q82.99 68.44 82.94 68.47L57.13 82.73Q56.09 83.31 55.06 83.3Q54.03 83.3 53 82.71L28.38 68.73Q28.33 68.7 28.3 68.66Q28.28 68.61 28.28 68.55Q28.28 68.49 28.3 68.45Q28.33 68.4 28.38 68.37Z" fill="url(#paint_linear_238_53649_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<mask id="mask238_53650" mask-type="alpha" maskUnits="userSpaceOnUse" x="40.756409" y="59.948303" width="29.911255" height="16.513763">
				<path id="蒙版" d="M41.89 67.69L53.74 60.88C54.76 60.3 56 60.28 57.04 60.84L69.46 67.55C69.98 67.83 69.98 68.57 69.46 68.85L57.01 75.58C55.99 76.12 54.76 76.12 53.75 75.56L41.9 68.98C41.39 68.7 41.39 67.98 41.89 67.69Z" fill="url(#paint_linear_238_53650_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</mask>
			<path id="蒙版" d="M41.89 67.69L53.74 60.88C54.76 60.3 56 60.28 57.04 60.84L69.46 67.55C69.98 67.83 69.98 68.57 69.46 68.85L57.01 75.58C55.99 76.12 54.76 76.12 53.75 75.56L41.9 68.98C41.39 68.7 41.39 67.98 41.89 67.69Z" fill="url(#paint_linear_238_53650_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			<g mask="url(#mask238_53650)">
				<path id="矩形" d="M45.95 73.41L54.63 68.19C55.37 67.75 56.29 67.75 57.03 68.19L65.71 73.41L65.71 79.74L45.95 79.74L45.95 73.41Z" fill="#2EEBFF" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</g>
			<mask id="mask238_53655" mask-type="alpha" maskUnits="userSpaceOnUse" x="28.394409" y="51.911987" width="54.494080" height="27.647278">
				<path id="蒙版" d="M28.39 52.31L52.86 66.64C54.28 67.47 56.04 67.48 57.47 66.65L82.88 51.91L82.88 63.5C82.88 64 82.61 64.46 82.17 64.7L56.99 78.55C55.85 79.17 54.47 79.17 53.33 78.53L29.09 64.93C28.66 64.69 28.39 64.23 28.39 63.73L28.39 52.31Z" fill="url(#paint_linear_238_53655_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</mask>
			<path id="蒙版" d="M28.39 52.31L52.86 66.64C54.28 67.47 56.04 67.48 57.47 66.65L82.88 51.91L82.88 63.5C82.88 64 82.61 64.46 82.17 64.7L56.99 78.55C55.85 79.17 54.47 79.17 53.33 78.53L29.09 64.93C28.66 64.69 28.39 64.23 28.39 63.73L28.39 52.31Z" fill="url(#paint_linear_238_53655_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			<g mask="url(#mask238_53655)">
				<g filter="url(#filter_238_53659_dd)">
					<mask id="mask_238_53659" fill="white">
						<path id="形状结合" d="M78.5974 59.7819L80.9462 58.3674L80.9462 60.4892L78.5974 61.9037L78.5974 59.7819ZM75.1716 61.837L77.5204 60.4225L77.5204 62.5443L75.1716 63.9588L75.1716 61.837ZM73.4111 63.1625L60.7797 70.1176L60.7797 72.2394L73.4111 65.2843L73.4111 63.1625Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
					</mask>
					<path id="形状结合" d="M78.5974 59.7819L80.9462 58.3674L80.9462 60.4892L78.5974 61.9037L78.5974 59.7819ZM75.1716 61.837L77.5204 60.4225L77.5204 62.5443L75.1716 63.9588L75.1716 61.837ZM73.4111 63.1625L60.7797 70.1176L60.7797 72.2394L73.4111 65.2843L73.4111 63.1625Z" clip-rule="evenodd" fill="#34FFFF" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_238_53659)"/>
					<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
				</g>
			</g>
			<g mask="url(#mask238_53655)">
				<g opacity="0.328780" filter="url(#filter_238_53660_dd)">
					<path id="矩形" d="M82.88 55.43L86.56 65.44L86.56 89.09L69.74 89.09L73.03 88.16C73.03 88.16 77.8 80.68 79.92 75.83C82.04 70.99 82.88 55.43 82.88 55.43Z" fill="#00C5F9" fill-opacity="1.000000" fill-rule="evenodd"/>
					<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
				</g>
			</g>
			<path id="矩形" d="M28.12 51.47L53.23 37.04C54.36 36.39 55.75 36.37 56.9 36.99L83.18 51.19C83.7 51.47 83.7 52.21 83.19 52.49L57.37 66.64C55.93 67.43 54.18 67.42 52.75 66.61L28.13 52.75C27.63 52.47 27.63 51.75 28.12 51.47Z" fill="url(#paint_linear_238_53662_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="矩形" d="M28.12 51.47L53.23 37.04C54.36 36.39 55.75 36.37 56.9 36.99L83.18 51.19C83.7 51.47 83.7 52.21 83.19 52.49L57.37 66.64C55.93 67.43 54.18 67.42 52.75 66.61L28.13 52.75C27.63 52.47 27.63 51.75 28.12 51.47ZM28.39 51.93L53.5 37.5Q54.28 37.05 55.07 37.04Q55.85 37.03 56.65 37.46L82.93 51.66Q82.98 51.69 83.01 51.73Q83.04 51.78 83.04 51.84Q83.04 51.9 83.01 51.95Q82.99 51.99 82.93 52.02L57.11 66.17Q56.09 66.74 55.06 66.73Q54.03 66.73 53.01 66.15L28.39 52.29Q28.34 52.26 28.31 52.22Q28.28 52.17 28.28 52.11Q28.28 52.05 28.31 52Q28.33 51.96 28.39 51.93Z" fill="url(#paint_linear_238_53662_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<mask id="mask238_53663" mask-type="alpha" maskUnits="userSpaceOnUse" x="41.531555" y="43.935608" width="28.360901" height="15.657806">
				<path id="蒙版" d="M42.66 51.25L53.76 44.87C54.77 44.29 56.02 44.27 57.05 44.83L68.68 51.11C69.2 51.39 69.2 52.13 68.68 52.41L57.03 58.71C56.01 59.26 54.78 59.25 53.77 58.69L42.67 52.53C42.17 52.25 42.16 51.53 42.66 51.25Z" fill="url(#paint_linear_238_53663_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</mask>
			<path id="蒙版" d="M42.66 51.25L53.76 44.87C54.77 44.29 56.02 44.27 57.05 44.83L68.68 51.11C69.2 51.39 69.2 52.13 68.68 52.41L57.03 58.71C56.01 59.26 54.78 59.25 53.77 58.69L42.67 52.53C42.17 52.25 42.16 51.53 42.66 51.25Z" fill="url(#paint_linear_238_53663_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			<g mask="url(#mask238_53663)">
				<path id="矩形" d="M45.95 56.97L54.63 51.75C55.37 51.31 56.29 51.31 57.03 51.75L65.71 56.97L65.71 63.3L45.95 63.3L45.95 56.97Z" fill="url(#paint_linear_238_53664_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
				<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
			</g>
		</g>
	</g>
</svg>
