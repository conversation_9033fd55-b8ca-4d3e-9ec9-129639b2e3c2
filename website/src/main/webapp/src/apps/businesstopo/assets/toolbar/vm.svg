<svg width="65.569092" height="75.999756" viewBox="0 0 65.5691 75.9998" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_1_1130_dd" x="20.000000" y="20.000000" width="25.569092" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="0"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 0.54 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1149_dd" x="0.000000" y="0.000000" width="65.569092" height="63.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="6.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 0.54 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1168_dd" x="6.895996" y="23.126892" width="52.207520" height="31.746216" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="3.62437" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_1_1173_dd" x="15.000000" y="22.586426" width="35.569092" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.11373 0 0 0 0 0.18039 0 0 0 0 0.22353 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1174_dd" x="15.000000" y="22.586365" width="12.932617" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1175_dd" x="19.797119" y="22.586365" width="12.833252" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1176_dd" x="24.305664" y="22.586365" width="12.733887" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1177_dd" x="37.636475" y="22.586365" width="12.932617" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1178_dd" x="32.938721" y="22.586365" width="12.833252" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1179_dd" x="28.529053" y="22.586365" width="12.733887" height="28.413513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="2"/>
			<feGaussianBlur stdDeviation="1.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_1_1188_dd" x="22.713867" y="21.000000" width="1.082764" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1191_dd" x="27.510742" y="21.000000" width="0.590332" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1194_dd" x="32.019775" y="21.000000" width="0.098145" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1198_dd" x="44.486084" y="21.000000" width="1.082764" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1201_dd" x="40.181641" y="21.000000" width="0.590332" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1204_dd" x="36.164795" y="21.000000" width="0.098145" height="23.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="1.01482"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_backgroundBlur_1" result="shape"/>
		</filter>
		<filter id="filter_1_1207_dd" x="16.999756" y="45.999756" width="31.000000" height="21.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_1_1208_dd" x="16.999756" y="45.999756" width="20.408203" height="20.987610" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_1_1209_dd" x="26.369385" y="45.999756" width="21.630371" height="21.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_1_1210_dd" x="16.999756" y="45.999756" width="20.408203" height="20.987610" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_1_1211_dd" x="26.369385" y="45.999756" width="21.630371" height="21.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_1_1213_dd" x="16.999756" y="45.999756" width="20.408203" height="20.987610" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_1_1214_dd" x="26.369385" y="45.999756" width="21.630371" height="21.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="5.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.34902 0 0 0 0 0.56863 0 0 0 0 1 0 0 0 0.41 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_1_1216_dd" x="9.999756" y="38.999756" width="34.408203" height="34.987610" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="4.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
		</filter>
		<filter id="filter_1_1217_dd" x="19.369385" y="38.999756" width="35.630371" height="35.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feGaussianBlur in="BackgroundImage" stdDeviation="0"/>
			<feComposite in2="SourceAlpha" operator="in" result="effect_backgroundBlur_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="4.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.44314 0 0 0 0 0.69412 0 0 0 0 1 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_backgroundBlur_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
		</filter>
		<linearGradient x1="1.418854" y1="43.772739" x2="53.281410" y2="58.388100" id="paint_linear_1_1125_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#575757" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#141825" stop-opacity="0.501961"/>
		</linearGradient>
		<linearGradient x1="16.447760" y1="47.631493" x2="51.458748" y2="47.332298" id="paint_linear_1_1125_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#C8C8C8" stop-opacity="0.000000"/>
			<stop offset="0.045523" stop-color="#C7C7C7" stop-opacity="0.184314"/>
			<stop offset="0.215380" stop-color="#C5C5C5" stop-opacity="0.215686"/>
			<stop offset="0.796960" stop-color="#C4C4C4" stop-opacity="0.231373"/>
			<stop offset="0.885144" stop-color="#BEBEBE" stop-opacity="0.180392"/>
			<stop offset="1.000000" stop-color="#979797" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(27.3067 45.4492) rotate(100.682) scale(8.86832 8.86832)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1125_2" gradientUnits="userSpaceOnUse">
			<stop stop-color="#E6E5E5" stop-opacity="0.301961"/>
			<stop offset="1.000000" stop-color="#000000" stop-opacity="0.000000"/>
		</radialGradient>
		<linearGradient x1="51.520126" y1="52.645180" x2="13.905698" y2="52.680611" id="paint_linear_1_1128_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#282E35"/>
			<stop offset="0.041732" stop-color="#2A3039"/>
			<stop offset="0.081977" stop-color="#333B4A"/>
			<stop offset="0.686152" stop-color="#5B657B"/>
			<stop offset="0.894982" stop-color="#5B6776"/>
			<stop offset="0.960769" stop-color="#32373D"/>
			<stop offset="1.000000" stop-color="#282E36"/>
		</linearGradient>
		<linearGradient x1="1.139368" y1="34.189930" x2="27.629803" y2="67.622070" id="paint_linear_1_1128_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0C4CB2" stop-opacity="0.501961"/>
			<stop offset="1.000000" stop-color="#102672" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="16.447758" y1="47.876682" x2="51.458755" y2="47.597775" id="paint_linear_1_1128_2" gradientUnits="userSpaceOnUse">
			<stop stop-color="#C8C8C8" stop-opacity="0.000000"/>
			<stop offset="0.045523" stop-color="#C7C7C7" stop-opacity="0.184314"/>
			<stop offset="0.215380" stop-color="#C5C5C5" stop-opacity="0.215686"/>
			<stop offset="0.796960" stop-color="#C4C4C4" stop-opacity="0.231373"/>
			<stop offset="0.885144" stop-color="#BEBEBE" stop-opacity="0.180392"/>
			<stop offset="1.000000" stop-color="#979797" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(28.9643 58.7627) rotate(97.7583) scale(5.47765 5.47765)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1128_3" gradientUnits="userSpaceOnUse">
			<stop stop-color="#E6E5E5" stop-opacity="0.701961"/>
			<stop offset="1.000000" stop-color="#000000" stop-opacity="0.000000"/>
		</radialGradient>
		<radialGradient gradientTransform="translate(32.9998 41.1713) rotate(-71.7753) scale(15.7454 15.7454)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1129_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#5378B1"/>
			<stop offset="1.000000" stop-color="#141A20"/>
		</radialGradient>
		<linearGradient x1="12.216198" y1="37.713200" x2="51.458752" y2="34.443790" id="paint_linear_1_1129_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#C8C8C8" stop-opacity="0.000000"/>
			<stop offset="0.045523" stop-color="#C7C7C7" stop-opacity="0.184314"/>
			<stop offset="0.215380" stop-color="#C5C5C5" stop-opacity="0.215686"/>
			<stop offset="0.796960" stop-color="#C4C4C4" stop-opacity="0.231373"/>
			<stop offset="0.885144" stop-color="#BEBEBE" stop-opacity="0.180392"/>
			<stop offset="1.000000" stop-color="#979797" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(27.0869 40.7199) rotate(101.594) scale(3.67929 3.67929)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1129_2" gradientUnits="userSpaceOnUse">
			<stop stop-color="#E6E5E5" stop-opacity="0.701961"/>
			<stop offset="1.000000" stop-color="#000000" stop-opacity="0.000000"/>
		</radialGradient>
		<linearGradient x1="32.784512" y1="43.000000" x2="32.784512" y2="19.999998" id="paint_linear_1_1130_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="32.784512" y1="43.000000" x2="32.784512" y2="19.999998" id="paint_linear_1_1149_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="18.994276" y1="35.253101" x2="48.618969" y2="35.110607" id="paint_linear_1_1168_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#C8C8C8" stop-opacity="0.000000"/>
			<stop offset="0.045523" stop-color="#C7C7C7" stop-opacity="0.184314"/>
			<stop offset="0.215380" stop-color="#C5C5C5" stop-opacity="0.215686"/>
			<stop offset="0.796960" stop-color="#C4C4C4" stop-opacity="0.231373"/>
			<stop offset="0.885144" stop-color="#BEBEBE" stop-opacity="0.180392"/>
			<stop offset="1.000000" stop-color="#979797" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(29.5852 40.8139) rotate(102.717) scale(2.8422 2.8422)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1168_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#E6E5E5" stop-opacity="0.701961"/>
			<stop offset="1.000000" stop-color="#000000" stop-opacity="0.000000"/>
		</radialGradient>
		<linearGradient x1="45.569023" y1="43.999928" x2="19.121788" y2="25.586432" id="paint_linear_1_1173_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#006DDA"/>
			<stop offset="1.000000" stop-color="#11BDFF"/>
		</linearGradient>
		<linearGradient x1="28.005764" y1="16.986649" x2="45.569031" y2="44.658348" id="paint_linear_1_1173_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#8574FF" stop-opacity="0.501961"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(20 21.8684) rotate(29.144) scale(15.4525 15.4525)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_1_1173_2" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</radialGradient>
		<linearGradient x1="45.569016" y1="26.589775" x2="20.854599" y2="12.941788" id="paint_linear_1_1181_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#116AF1"/>
			<stop offset="1.000000" stop-color="#3DD1FF" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="24.439970" y1="42.845543" x2="22.967751" y2="26.268555" id="paint_linear_1_1188_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#173268"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="28.899895" y1="42.845543" x2="27.540361" y2="26.268555" id="paint_linear_1_1191_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#173268"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="33.162838" y1="42.845543" x2="31.803301" y2="26.268555" id="paint_linear_1_1194_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0040C5"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="43.842747" y1="42.845543" x2="45.314964" y2="26.268555" id="paint_linear_1_1198_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#173268"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="39.382816" y1="42.845543" x2="40.742352" y2="26.268555" id="paint_linear_1_1201_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#173268"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="35.119877" y1="42.845543" x2="36.479412" y2="26.268555" id="paint_linear_1_1204_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0040C5"/>
			<stop offset="1.000000" stop-color="#2270C7" stop-opacity="0.988235"/>
		</linearGradient>
		<linearGradient x1="32.499756" y1="60.999756" x2="32.499756" y2="51.999756" id="paint_linear_1_1207_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="27.203812" y1="60.987343" x2="27.203812" y2="51.999756" id="paint_linear_1_1210_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="37.184601" y1="60.999756" x2="37.184601" y2="51.999756" id="paint_linear_1_1211_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="27.203812" y1="60.987343" x2="27.203812" y2="51.999756" id="paint_linear_1_1213_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="37.184601" y1="60.999756" x2="37.184601" y2="51.999756" id="paint_linear_1_1214_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="27.203812" y1="60.987343" x2="27.203812" y2="51.999756" id="paint_linear_1_1216_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
		<linearGradient x1="37.184601" y1="60.999756" x2="37.184601" y2="51.999756" id="paint_linear_1_1217_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#107DE6"/>
			<stop offset="1.000000" stop-color="#66B1FF"/>
		</linearGradient>
	</defs>
	<g opacity="0.800030">
		<rect id="矩形" x="14.999756" y="45.000000" width="36.000000" height="21.000000" fill="#1F252E" fill-opacity="1.000000"/>
		<rect id="矩形" x="14.999756" y="45.000000" width="36.000000" height="21.000000" fill="url(#paint_linear_1_1125_0)" fill-opacity="0"/>
		<rect id="矩形" x="15.499756" y="45.500000" width="35.000000" height="20.000000" stroke="url(#paint_linear_1_1125_1)" stroke-opacity="0" stroke-width="1.000000"/>
		<rect id="矩形" x="15.499756" y="45.500000" width="35.000000" height="20.000000" stroke="url(#paint_radial_1_1125_2)" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<path id="路径 3" d="M14.99 45.42L50.99 45.42L50.99 63C50.99 64.1 50.1 65 48.99 65L16.99 65C15.89 65 14.99 64.1 14.99 63L14.99 45.42Z" fill="url(#paint_linear_1_1128_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="路径 3" d="M14.99 45.42L50.99 45.42L50.99 63C50.99 64.1 50.1 65 48.99 65L16.99 65C15.89 65 14.99 64.1 14.99 63L14.99 45.42Z" fill="url(#paint_linear_1_1128_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="路径 3" d="" fill="url(#paint_linear_1_1128_2)" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径 3" d="" fill="url(#paint_radial_1_1128_3)" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 1" d="M17.11 46C16.79 46 16.49 45.98 16.22 45.94C15.96 45.91 15.72 45.86 15.53 45.79C15.34 45.73 15.2 45.66 15.1 45.58C15.01 45.49 14.98 45.4 15 45.31L17.66 33.42C17.68 33.36 17.75 33.3 17.86 33.25C17.96 33.2 18.11 33.16 18.29 33.12C18.46 33.08 18.67 33.05 18.89 33.03C19.11 33.01 19.35 33 19.6 33L46.39 33C46.64 33 46.88 33.01 47.1 33.03C47.32 33.05 47.53 33.08 47.7 33.12C47.88 33.16 48.02 33.2 48.13 33.25C48.24 33.3 48.31 33.36 48.33 33.42L50.99 45.31C51.01 45.4 50.98 45.49 50.89 45.58C50.79 45.66 50.65 45.73 50.46 45.79C50.27 45.86 50.03 45.91 49.77 45.94C49.5 45.98 49.2 46 48.88 46L17.11 46Z" fill="url(#paint_radial_1_1129_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 1" d="M15 45.31L17.66 33.42C17.68 33.36 17.75 33.3 17.86 33.25C17.96 33.2 18.11 33.16 18.29 33.12C18.46 33.08 18.67 33.05 18.89 33.03C19.11 33.01 19.35 33 19.6 33L46.39 33C46.64 33 46.88 33.01 47.1 33.03C47.32 33.05 47.53 33.08 47.7 33.12C47.88 33.16 48.02 33.2 48.13 33.25C48.24 33.3 48.31 33.36 48.33 33.42L50.99 45.31C51.01 45.4 50.98 45.49 50.89 45.58C50.79 45.66 50.65 45.73 50.46 45.79C50.27 45.86 50.03 45.91 49.77 45.94C49.5 45.98 49.2 46 48.88 46L17.11 46C16.79 46 16.49 45.98 16.22 45.94C15.96 45.91 15.72 45.86 15.53 45.79C15.34 45.73 15.2 45.66 15.1 45.58C15.01 45.49 14.98 45.4 15 45.31ZM47.87 33.69C47.8 33.66 47.71 33.63 47.6 33.61C47.44 33.57 47.26 33.55 47.05 33.53C46.84 33.51 46.62 33.5 46.39 33.5L19.6 33.5C19.37 33.5 19.15 33.51 18.94 33.53C18.73 33.55 18.55 33.57 18.39 33.61C18.28 33.63 18.19 33.66 18.12 33.69L15.53 45.26C15.57 45.28 15.62 45.3 15.69 45.32C15.85 45.37 16.05 45.41 16.29 45.45C16.54 45.48 16.82 45.5 17.11 45.5L48.88 45.5C49.17 45.5 49.45 45.48 49.7 45.45C49.94 45.41 50.14 45.37 50.3 45.32C50.37 45.3 50.42 45.28 50.46 45.26L47.87 33.69Z" fill="url(#paint_linear_1_1129_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 1" d="M15 45.31L17.66 33.42C17.68 33.36 17.75 33.3 17.86 33.25C17.96 33.2 18.11 33.16 18.29 33.12C18.46 33.08 18.67 33.05 18.89 33.03C19.11 33.01 19.35 33 19.6 33L46.39 33C46.64 33 46.88 33.01 47.1 33.03C47.32 33.05 47.53 33.08 47.7 33.12C47.88 33.16 48.02 33.2 48.13 33.25C48.24 33.3 48.31 33.36 48.33 33.42L50.99 45.31C51.01 45.4 50.98 45.49 50.89 45.58C50.79 45.66 50.65 45.73 50.46 45.79C50.27 45.86 50.03 45.91 49.77 45.94C49.5 45.98 49.2 46 48.88 46L17.11 46C16.79 46 16.49 45.98 16.22 45.94C15.96 45.91 15.72 45.86 15.53 45.79C15.34 45.73 15.2 45.66 15.1 45.58C15.01 45.49 14.98 45.4 15 45.31ZM47.87 33.69C47.8 33.66 47.71 33.63 47.6 33.61C47.44 33.57 47.26 33.55 47.05 33.53C46.84 33.51 46.62 33.5 46.39 33.5L19.6 33.5C19.37 33.5 19.15 33.51 18.94 33.53C18.73 33.55 18.55 33.57 18.39 33.61C18.28 33.63 18.19 33.66 18.12 33.69L15.53 45.26C15.57 45.28 15.62 45.3 15.69 45.32C15.85 45.37 16.05 45.41 16.29 45.45C16.54 45.48 16.82 45.5 17.11 45.5L48.88 45.5C49.17 45.5 49.45 45.48 49.7 45.45C49.94 45.41 50.14 45.37 50.3 45.32C50.37 45.3 50.42 45.28 50.46 45.26L47.87 33.69Z" fill="url(#paint_radial_1_1129_2)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_1_1130_dd)">
		<path id="形状结合" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8804 38.59L22.9326 42.9999L20.282 42.9999L20 24.5867L20 24.5865L20 24.5865L21.3989 20.0001L23.7969 20.0001L22.7139 24.5865L22.7141 24.5865L23.7971 20ZM28.1011 20L28.1011 20.0001L28.1013 20.0001L28.1011 20.0022L28.1011 32.7933L27.6301 42.9954L27.6301 42.9999L24.9797 42.9999L24.7969 24.5865L24.7971 24.5865L25.7034 20.0001L28.1008 20.0001L28.1011 20ZM32.1179 20.0123L32.1179 32.7933L32.0398 43L32.0198 24.5867L32.0188 24.5867L32.0383 42.9999L29.3877 42.9999L29.3044 24.5865L29.3062 24.5865L29.7202 20.0001L32.1182 20.0001L32.1179 20.0123ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0009L33.4507 20.0001L35.8484 20.0001L36.2627 24.5865L36.2627 24.5865L36.1792 42.9999L33.5288 42.9999L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.0041L37.467 20.0001L39.865 20.0001L40.771 24.5865L40.7715 24.5865L40.5889 42.9999L37.9382 42.9999L37.9382 42.9954ZM41.7717 20L42.8547 24.5865L42.8547 24.5865L41.772 20.0001L44.1697 20.0001L45.5686 24.5865L45.5691 24.5865L45.2871 42.9999L42.6365 42.9999L42.7988 29.2953L42.6362 43L41.7717 32.7933L41.7717 20ZM32.1179 20.0001L32.1179 20L32.1179 20.0001ZM33.4507 20.0001L33.4507 20L33.4507 20.0001ZM37.4675 20.0001L37.4675 20L37.4675 20.0001Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="0.003922" fill-rule="evenodd"/>
		<path id="形状结合" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8804 38.59L22.9326 42.9999L20.282 42.9999L20 24.5867L20 24.5865L20 24.5865L21.3989 20.0001L23.7969 20.0001L22.7139 24.5865L22.7141 24.5865L23.7971 20ZM28.1011 20L28.1011 20.0001L28.1013 20.0001L28.1011 20.0022L28.1011 32.7933L27.6301 42.9954L27.6301 42.9999L24.9797 42.9999L24.7969 24.5865L24.7971 24.5865L25.7034 20.0001L28.1008 20.0001L28.1011 20ZM32.1179 20.0123L32.1179 32.7933L32.0398 43L32.0198 24.5867L32.0188 24.5867L32.0383 42.9999L29.3877 42.9999L29.3044 24.5865L29.3062 24.5865L29.7202 20.0001L32.1182 20.0001L32.1179 20.0123ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0009L33.4507 20.0001L35.8484 20.0001L36.2627 24.5865L36.2627 24.5865L36.1792 42.9999L33.5288 42.9999L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.0041L37.467 20.0001L39.865 20.0001L40.771 24.5865L40.7715 24.5865L40.5889 42.9999L37.9382 42.9999L37.9382 42.9954ZM41.7717 20L42.8547 24.5865L42.8547 24.5865L41.772 20.0001L44.1697 20.0001L45.5686 24.5865L45.5691 24.5865L45.2871 42.9999L42.6365 42.9999L42.7988 29.2953L42.6362 43L41.7717 32.7933L41.7717 20ZM32.1179 20.0001L32.1179 20L32.1179 20.0001ZM33.4507 20.0001L33.4507 20L33.4507 20.0001ZM37.4675 20.0001L37.4675 20L37.4675 20.0001Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="0.000000" fill-rule="evenodd"/>
		<path id="形状结合" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8804 38.59L22.9326 42.9999L20.282 42.9999L20 24.5867L20 24.5865L20 24.5865L21.3989 20.0001L23.7969 20.0001L22.7139 24.5865L22.7141 24.5865L23.7971 20ZM28.1011 20L28.1011 20.0001L28.1013 20.0001L28.1011 20.0022L28.1011 32.7933L27.6301 42.9954L27.6301 42.9999L24.9797 42.9999L24.7969 24.5865L24.7971 24.5865L25.7034 20.0001L28.1008 20.0001L28.1011 20ZM32.1179 20.0123L32.1179 32.7933L32.0398 43L32.0198 24.5867L32.0188 24.5867L32.0383 42.9999L29.3877 42.9999L29.3044 24.5865L29.3062 24.5865L29.7202 20.0001L32.1182 20.0001L32.1179 20.0123ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0009L33.4507 20.0001L35.8484 20.0001L36.2627 24.5865L36.2627 24.5865L36.1792 42.9999L33.5288 42.9999L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.0041L37.467 20.0001L39.865 20.0001L40.771 24.5865L40.7715 24.5865L40.5889 42.9999L37.9382 42.9999L37.9382 42.9954ZM41.7717 20L42.8547 24.5865L42.8547 24.5865L41.772 20.0001L44.1697 20.0001L45.5686 24.5865L45.5691 24.5865L45.2871 42.9999L42.6365 42.9999L42.7988 29.2953L42.6362 43L41.7717 32.7933L41.7717 20ZM32.1179 20.0001L32.1179 20L32.1179 20.0001ZM33.4507 20.0001L33.4507 20L33.4507 20.0001ZM37.4675 20.0001L37.4675 20L37.4675 20.0001Z" clip-rule="evenodd" fill="url(#paint_linear_1_1130_0)" fill-opacity="0.000000" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1149_dd)">
		<path id="形状结合备份" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8809 38.6446L22.9326 42.9998L20.282 42.9998L20 24.5865L20 24.5864L20 24.5864L21.3989 20L23.7969 20L22.7139 24.5864L22.7141 24.5864L23.7971 20ZM28.1011 20.0021L28.1011 32.7933L27.6301 42.9954L27.6301 42.9998L24.9797 42.9998L24.7969 24.5864L24.7971 24.5864L25.7034 20L28.1013 20L28.1011 20.0021ZM32.1179 20.0122L32.1179 32.7933L32.0398 43L32.0198 24.5865L32.0188 24.5865L32.0383 42.9998L29.3877 42.9998L29.3044 24.5864L29.3062 24.5864L29.7202 20L32.1182 20L32.1179 20.0122ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0008L33.4507 20L35.8484 20L36.2627 24.5864L36.2627 24.5864L36.1792 42.9998L33.5288 42.9998L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.004L37.467 20L39.865 20L40.771 24.5864L40.7715 24.5864L40.5889 42.9998L37.9382 42.9998L37.9382 42.9954ZM41.7717 20L42.8545 24.5864L42.8547 24.5864L41.772 20L44.1697 20L45.5686 24.5864L45.5691 24.5864L45.2871 42.9998L42.6365 42.9998L42.7981 29.3501L42.6362 43L41.7717 32.7933L41.7717 20ZM27.6299 43L27.6299 42.9998L27.6299 43ZM33.5288 43L33.5288 42.9998L33.5288 43ZM37.9385 43L37.9385 42.9998L37.9385 43Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="0.003922" fill-rule="evenodd"/>
		<path id="形状结合备份" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8809 38.6446L22.9326 42.9998L20.282 42.9998L20 24.5865L20 24.5864L20 24.5864L21.3989 20L23.7969 20L22.7139 24.5864L22.7141 24.5864L23.7971 20ZM28.1011 20.0021L28.1011 32.7933L27.6301 42.9954L27.6301 42.9998L24.9797 42.9998L24.7969 24.5864L24.7971 24.5864L25.7034 20L28.1013 20L28.1011 20.0021ZM32.1179 20.0122L32.1179 32.7933L32.0398 43L32.0198 24.5865L32.0188 24.5865L32.0383 42.9998L29.3877 42.9998L29.3044 24.5864L29.3062 24.5864L29.7202 20L32.1182 20L32.1179 20.0122ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0008L33.4507 20L35.8484 20L36.2627 24.5864L36.2627 24.5864L36.1792 42.9998L33.5288 42.9998L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.004L37.467 20L39.865 20L40.771 24.5864L40.7715 24.5864L40.5889 42.9998L37.9382 42.9998L37.9382 42.9954ZM41.7717 20L42.8545 24.5864L42.8547 24.5864L41.772 20L44.1697 20L45.5686 24.5864L45.5691 24.5864L45.2871 42.9998L42.6365 42.9998L42.7981 29.3501L42.6362 43L41.7717 32.7933L41.7717 20ZM27.6299 43L27.6299 42.9998L27.6299 43ZM33.5288 43L33.5288 42.9998L33.5288 43ZM37.9385 43L37.9385 42.9998L37.9385 43Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="0.000000" fill-rule="evenodd"/>
		<path id="形状结合备份" d="M23.7971 20L23.7971 32.7933L22.9326 43L22.8809 38.6446L22.9326 42.9998L20.282 42.9998L20 24.5865L20 24.5864L20 24.5864L21.3989 20L23.7969 20L22.7139 24.5864L22.7141 24.5864L23.7971 20ZM28.1011 20.0021L28.1011 32.7933L27.6301 42.9954L27.6301 42.9998L24.9797 42.9998L24.7969 24.5864L24.7971 24.5864L25.7034 20L28.1013 20L28.1011 20.0021ZM32.1179 20.0122L32.1179 32.7933L32.0398 43L32.0198 24.5865L32.0188 24.5865L32.0383 42.9998L29.3877 42.9998L29.3044 24.5864L29.3062 24.5864L29.7202 20L32.1182 20L32.1179 20.0122ZM33.5288 42.9937L33.4507 32.7933L33.4507 20.0008L33.4507 20L35.8484 20L36.2627 24.5864L36.2627 24.5864L36.1792 42.9998L33.5288 42.9998L33.5288 42.9937ZM37.9382 42.9954L37.4675 32.7933L37.4675 20.004L37.467 20L39.865 20L40.771 24.5864L40.7715 24.5864L40.5889 42.9998L37.9382 42.9998L37.9382 42.9954ZM41.7717 20L42.8545 24.5864L42.8547 24.5864L41.772 20L44.1697 20L45.5686 24.5864L45.5691 24.5864L45.2871 42.9998L42.6365 42.9998L42.7981 29.3501L42.6362 43L41.7717 32.7933L41.7717 20ZM27.6299 43L27.6299 42.9998L27.6299 43ZM33.5288 43L33.5288 42.9998L33.5288 43ZM37.9385 43L37.9385 42.9998L37.9385 43Z" clip-rule="evenodd" fill="url(#paint_linear_1_1149_0)" fill-opacity="0.000000" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1168_dd)">
		<path id="Fill 1" d="M46.43 44L19.56 44C19.28 44 19.03 43.98 18.8 43.95C18.58 43.93 18.38 43.89 18.22 43.84C18.06 43.79 17.93 43.74 17.86 43.67C17.78 43.61 17.75 43.54 17.77 43.47L20.87 34.32C20.88 34.28 20.94 34.23 21.03 34.19C21.12 34.15 21.25 34.12 21.4 34.09C21.54 34.06 21.72 34.04 21.91 34.02C22.09 34 22.3 34 22.51 34L43.48 34C43.69 34 43.9 34 44.08 34.02C44.27 34.04 44.45 34.06 44.59 34.09C44.74 34.12 44.87 34.15 44.96 34.19C45.05 34.23 45.11 34.28 45.12 34.32L48.22 43.47C48.24 43.54 48.21 43.61 48.13 43.67C48.06 43.74 47.93 43.79 47.77 43.84C47.61 43.89 47.41 43.93 47.19 43.95C46.96 43.98 46.71 44 46.43 44Z" fill="#1A2236" fill-opacity="0.188235" fill-rule="evenodd"/>
		<path id="Fill 1" d="" fill="url(#paint_linear_1_1168_0)" fill-opacity="0" fill-rule="evenodd"/>
		<path id="Fill 1" d="" fill="url(#paint_radial_1_1168_1)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1173_dd)">
		<mask id="mask_1_1173" fill="white">
			<path id="形状结合" d="M20.282 43.9999L22.9326 43.9999L22.7146 25.5864L20 25.5864L20.282 43.9999ZM24.9797 43.9999L27.6301 43.9999L27.5115 25.5864L24.7969 25.5864L24.9797 43.9999ZM32.0398 43.9999L29.3892 43.9999L29.3059 25.5864L32.0203 25.5864L32.0398 43.9999ZM36.1794 43.9999L33.5291 43.9999L33.5483 25.5864L36.2629 25.5864L36.1794 43.9999ZM37.9387 43.9999L40.5894 43.9999L40.772 25.5864L38.0576 25.5864L37.9387 43.9999ZM45.2871 43.9999L42.6365 43.9999L42.8545 25.5864L45.5691 25.5864L45.2871 43.9999Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
		</mask>
		<path id="形状结合" d="M20.282 43.9999L22.9326 43.9999L22.7146 25.5864L20 25.5864L20.282 43.9999ZM24.9797 43.9999L27.6301 43.9999L27.5115 25.5864L24.7969 25.5864L24.9797 43.9999ZM32.0398 43.9999L29.3892 43.9999L29.3059 25.5864L32.0203 25.5864L32.0398 43.9999ZM36.1794 43.9999L33.5291 43.9999L33.5483 25.5864L36.2629 25.5864L36.1794 43.9999ZM37.9387 43.9999L40.5894 43.9999L40.772 25.5864L38.0576 25.5864L37.9387 43.9999ZM45.2871 43.9999L42.6365 43.9999L42.8545 25.5864L45.5691 25.5864L45.2871 43.9999Z" clip-rule="evenodd" fill="url(#paint_linear_1_1173_0)" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_1_1173)"/>
		<path id="形状结合" d="M20.282 43.9999L22.9326 43.9999L22.7146 25.5864L20 25.5864L20.282 43.9999ZM24.9797 43.9999L27.6301 43.9999L27.5115 25.5864L24.7969 25.5864L24.9797 43.9999ZM32.0398 43.9999L29.3892 43.9999L29.3059 25.5864L32.0203 25.5864L32.0398 43.9999ZM36.1794 43.9999L33.5291 43.9999L33.5483 25.5864L36.2629 25.5864L36.1794 43.9999ZM37.9387 43.9999L40.5894 43.9999L40.772 25.5864L38.0576 25.5864L37.9387 43.9999ZM45.2871 43.9999L42.6365 43.9999L42.8545 25.5864L45.5691 25.5864L45.2871 43.9999Z" clip-rule="evenodd" fill="url(#paint_linear_1_1173_1)" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_1_1173)"/>
		<path id="形状结合" d="M20.282 43.9999L20.0077 26.0864L20 25.5864L22.7146 25.5864L22.9267 43.4999L22.9326 43.9999L20.282 43.9999ZM22.4266 43.4999L22.2204 26.0864L20.5078 26.0864L20.7744 43.4999L22.4266 43.4999ZM45.2871 43.9999L42.6365 43.9999L42.6424 43.4999L42.8545 25.5864L45.5691 25.5864L45.5614 26.0864L45.2871 43.9999ZM45.0613 26.0864L43.3486 26.0864L43.1425 43.4999L44.7946 43.4999L45.0613 26.0864ZM24.9797 43.9999L24.8018 26.0864L24.7969 25.5864L27.5115 25.5864L27.6269 43.4999L27.6301 43.9999L24.9797 43.9999ZM27.1269 43.4999L27.0147 26.0864L25.3019 26.0864L25.4748 43.4999L27.1269 43.4999ZM38.4387 43.9999L37.9387 43.9999L37.9419 43.4999L38.0576 25.5864L40.772 25.5864L40.767 26.0864L40.5894 43.9999L38.4387 43.9999ZM38.442 43.4999L40.0943 43.4999L40.2672 26.0864L38.5544 26.0864L38.442 43.4999ZM32.0393 43.4999L32.0398 43.9999L29.3892 43.9999L29.3082 26.0864L29.3059 25.5864L32.0203 25.5864L32.0393 43.4999ZM31.5393 43.4999L29.8869 43.4999L29.8082 26.0864L31.5208 26.0864L31.5393 43.4999ZM36.1794 43.9999L33.5291 43.9999L33.5296 43.4999L33.5483 25.5864L36.2629 25.5864L36.2607 26.0864L36.1794 43.9999ZM35.7607 26.0864L34.0478 26.0864L34.0296 43.4999L35.6817 43.4999L35.7607 26.0864Z" clip-rule="evenodd" fill="url(#paint_radial_1_1173_2)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<mask id="mask_1_1181" fill="white">
		<path id="形状结合" d="M22.7139 25.5863L23.7969 20.9998L21.3989 20.9998L20 25.5863L22.7139 25.5863ZM27.511 25.5863L28.1013 20.9998L25.7034 20.9998L24.7971 25.5863L27.511 25.5863ZM32.1182 20.9998L32.02 25.5863L29.3062 25.5863L29.7202 20.9998L32.1182 20.9998ZM33.5488 25.5863L33.4507 20.9998L35.8484 20.9998L36.2627 25.5863L33.5488 25.5863ZM37.4678 20.9998L38.0579 25.5863L40.772 25.5863L39.8657 20.9998L37.4678 20.9998ZM42.855 25.5863L41.7722 20.9998L44.1699 20.9998L45.5691 25.5863L42.855 25.5863Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<path id="形状结合" d="M22.7139 25.5863L23.7969 20.9998L21.3989 20.9998L20 25.5863L22.7139 25.5863ZM27.511 25.5863L28.1013 20.9998L25.7034 20.9998L24.7971 25.5863L27.511 25.5863ZM32.1182 20.9998L32.02 25.5863L29.3062 25.5863L29.7202 20.9998L32.1182 20.9998ZM33.5488 25.5863L33.4507 20.9998L35.8484 20.9998L36.2627 25.5863L33.5488 25.5863ZM37.4678 20.9998L38.0579 25.5863L40.772 25.5863L39.8657 20.9998L37.4678 20.9998ZM42.855 25.5863L41.7722 20.9998L44.1699 20.9998L45.5691 25.5863L42.855 25.5863Z" clip-rule="evenodd" fill="url(#paint_linear_1_1181_0)" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_1_1181)"/>
	<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_1_1188_dd)">
		<path id="路径" d="M23.79 21L23.79 33.79L22.93 44L22.71 25.58L23.79 21Z" fill="url(#paint_linear_1_1188_0)" fill-opacity="0.810000" fill-rule="evenodd"/>
		<path id="路径" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1191_dd)">
		<path id="路径 2" d="M28.1 21L28.1 33.79L27.63 44L27.51 25.58L28.1 21Z" fill="url(#paint_linear_1_1191_0)" fill-opacity="0.810000" fill-rule="evenodd"/>
		<path id="路径 2" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1194_dd)">
		<path id="路径 4" d="M32.11 21L32.11 33.79L32.03 44L32.01 25.58L32.11 21Z" fill="url(#paint_linear_1_1194_0)" fill-opacity="0.630000" fill-rule="evenodd"/>
		<path id="路径 4" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1198_dd)">
		<path id="路径" d="M44.48 21L44.48 33.79L45.35 44L45.56 25.58L44.48 21Z" fill="url(#paint_linear_1_1198_0)" fill-opacity="0.810000" fill-rule="evenodd"/>
		<path id="路径" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1201_dd)">
		<path id="路径 2" d="M40.18 21L40.18 33.79L40.65 44L40.77 25.58L40.18 21Z" fill="url(#paint_linear_1_1201_0)" fill-opacity="0.810000" fill-rule="evenodd"/>
		<path id="路径 2" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1204_dd)">
		<path id="路径 4" d="M36.16 21L36.16 33.79L36.24 44L36.26 25.58L36.16 21Z" fill="url(#paint_linear_1_1204_0)" fill-opacity="0.630000" fill-rule="evenodd"/>
		<path id="路径 4" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1207_dd)">
		<path id="形状结合" d="M22.9998 51.9998L24.6912 51.9998L26.9753 58.2936C27.0166 58.4136 27.0681 58.579 27.1296 58.7901C27.1914 59.0012 27.241 59.1915 27.2778 59.3611C27.4219 58.8605 27.531 58.5046 27.605 58.2936L29.8398 51.9998L31.408 51.9998L27.9692 60.9874L26.4382 60.9874L22.9998 51.9998ZM33.709 53.6074C33.9312 54.3356 34.1389 54.9481 34.3325 55.4446L36.5178 60.9998L37.6907 60.9998L39.8513 55.7053C40.0818 55.1301 40.3186 54.4308 40.5613 53.6074C40.5037 54.4598 40.4749 55.186 40.4749 55.7859L40.4749 60.9874L41.9998 60.9874L41.9998 51.9998L39.9316 51.9998L37.8081 57.2446C37.5569 57.8694 37.3472 58.4818 37.1785 59.0818C37.0591 58.5729 36.8574 57.9604 36.5735 57.2446L34.5115 51.9998L32.3694 51.9998L32.3694 60.9874L33.7769 60.9874L33.7769 55.7859C33.7769 54.917 33.7542 54.1908 33.709 53.6074Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="形状结合" d="M22.9998 51.9998L24.6912 51.9998L26.9753 58.2936C27.0166 58.4136 27.0681 58.579 27.1296 58.7901C27.1914 59.0012 27.241 59.1915 27.2778 59.3611C27.4219 58.8605 27.531 58.5046 27.605 58.2936L29.8398 51.9998L31.408 51.9998L27.9692 60.9874L26.4382 60.9874L22.9998 51.9998ZM33.709 53.6074C33.9312 54.3356 34.1389 54.9481 34.3325 55.4446L36.5178 60.9998L37.6907 60.9998L39.8513 55.7053C40.0818 55.1301 40.3186 54.4308 40.5613 53.6074C40.5037 54.4598 40.4749 55.186 40.4749 55.7859L40.4749 60.9874L41.9998 60.9874L41.9998 51.9998L39.9316 51.9998L37.8081 57.2446C37.5569 57.8694 37.3472 58.4818 37.1785 59.0818C37.0591 58.5729 36.8574 57.9604 36.5735 57.2446L34.5115 51.9998L32.3694 51.9998L32.3694 60.9874L33.7769 60.9874L33.7769 55.7859C33.7769 54.917 33.7542 54.1908 33.709 53.6074Z" clip-rule="evenodd" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="形状结合" d="M22.9998 51.9998L24.6912 51.9998L26.9753 58.2936C27.0166 58.4136 27.0681 58.579 27.1296 58.7901C27.1914 59.0012 27.241 59.1915 27.2778 59.3611C27.4219 58.8605 27.531 58.5046 27.605 58.2936L29.8398 51.9998L31.408 51.9998L27.9692 60.9874L26.4382 60.9874L22.9998 51.9998ZM33.709 53.6074C33.9312 54.3356 34.1389 54.9481 34.3325 55.4446L36.5178 60.9998L37.6907 60.9998L39.8513 55.7053C40.0818 55.1301 40.3186 54.4308 40.5613 53.6074C40.5037 54.4598 40.4749 55.186 40.4749 55.7859L40.4749 60.9874L41.9998 60.9874L41.9998 51.9998L39.9316 51.9998L37.8081 57.2446C37.5569 57.8694 37.3472 58.4818 37.1785 59.0818C37.0591 58.5729 36.8574 57.9604 36.5735 57.2446L34.5115 51.9998L32.3694 51.9998L32.3694 60.9874L33.7769 60.9874L33.7769 55.7859C33.7769 54.917 33.7542 54.1908 33.709 53.6074Z" clip-rule="evenodd" fill="url(#paint_linear_1_1207_0)" fill-opacity="0.000000" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1210_dd)">
		<path id="路径备份 2" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径备份 2" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径备份 2" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="url(#paint_linear_1_1210_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1211_dd)">
		<path id="路径备份" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径备份" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径备份" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="url(#paint_linear_1_1211_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1213_dd)">
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="url(#paint_linear_1_1213_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1214_dd)">
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="url(#paint_linear_1_1214_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1216_dd)">
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="#ECFBFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M22.99 51.99L24.69 51.99L26.97 58.29C27.01 58.41 27.06 58.57 27.12 58.79C27.19 59 27.24 59.19 27.27 59.36C27.42 58.86 27.53 58.5 27.6 58.29L29.83 51.99L31.4 51.99L27.96 60.98L26.43 60.98L22.99 51.99Z" fill="url(#paint_linear_1_1216_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_1_1217_dd)">
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="#ECFBFF" fill-opacity="1.000000" fill-rule="evenodd"/>
		<path id="路径" d="M34.33 55.44C34.13 54.94 33.93 54.33 33.7 53.6C33.75 54.19 33.77 54.91 33.77 55.78L33.77 60.98L32.36 60.98L32.36 51.99L34.51 51.99L36.57 57.24C36.85 57.96 37.05 58.57 37.17 59.08C37.34 58.48 37.55 57.86 37.8 57.24L39.93 51.99L41.99 51.99L41.99 60.98L40.47 60.98L40.47 55.78C40.47 55.18 40.5 54.45 40.56 53.6C40.31 54.43 40.08 55.13 39.85 55.7L37.69 60.99L36.51 60.99L34.33 55.44Z" fill="url(#paint_linear_1_1217_0)" fill-opacity="0" fill-rule="evenodd"/>
	</g>
</svg>
