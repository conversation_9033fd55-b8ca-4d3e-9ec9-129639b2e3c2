import { createContext } from 'react';

/**
 * 变化时无需触发UI重新渲染的全局数据
 */
const STATE = {
  piu: {} as Record<string, any>,
  csrfToken: '',
  copilotExpandPanelId: '',
  language: 'en',
  theme: 'evening',
  menuTheme: 'evening', // 菜单栏的主题
  slotEndDomId: '',
};

export function setGlobalState(newState: Partial<typeof STATE>): void {
  Object.assign(STATE, newState);
}

export function getGlobalState(): typeof STATE {
  return { ...STATE };
}

/**
 * 变化时需要触发UI重新渲染的全局数据
 */
export const GlobalContext = createContext({
  theme: STATE.theme,
});

/**
 * febs的主题
 */
export const Theme = {
  lightDay: 'lightday',
  evening: 'evening',
};
