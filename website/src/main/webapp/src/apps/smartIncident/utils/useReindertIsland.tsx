import React, { useCallback, useEffect, useRef } from 'react';
import { createRoot } from '@cloudsop/horizon';
import { baseEventMgr, BaseEvents } from './EventMgr';
import { getGlobalState } from './globalState';
import { getTheme4Container } from './pageUtils';
import { color_hand } from '../assets/const';
import styles from '../components/page.module.less';

/**
 * 用于创建一个灵动岛组件并挂载到菜单栏右上角
 * @param cardDomId 卡片的 DOM ID
 * @param islandContent 灵动岛的显示内容
 * @returns 一个呼出灵动岛的方法
 */
export default function useReindertIsland(
  cardDomId: string,
  islandContent: string,
): { showReindertIsland: () => void } {
  const islandRef = useRef<ReturnType<typeof createRoot>>();
  const containerRef = useRef<HTMLDivElement>(null);

  const changeContainerCls = useCallback((curTheme: string) => {
    if (containerRef.current) {
      containerRef.current.className = getContainCls(curTheme);
    }
  }, []);

  const showReindertIsland = useCallback(async() => {
    const slotEndDom = document.getElementById(getGlobalState().slotEndDomId);
    const cardDom = document.getElementById(cardDomId);
    if (!cardDom || !slotEndDom || !slotEndDom.parentElement) {
      return;
    }
    // 隐藏卡片
    cardDom.classList.add(styles['smart-cockpit-reindeer-island-origin-card-hidden']);
    // 创建灵动岛
    let container = containerRef.current;
    if (!container) {
      container = document.createElement('div');
      container.id = 'smart-cockpit-reindeer-island';
      container.className = getContainCls(getGlobalState().menuTheme);
      slotEndDom.parentElement.appendChild(container);
      containerRef.current = container;
    }
    islandRef.current = createRoot(container);
    islandRef.current.render(
      <div
        onClick={() => {
          cardDom.classList.remove(styles['smart-cockpit-reindeer-island-origin-card-hidden']);
          islandRef.current?.unmount();
          baseEventMgr.off(BaseEvents.menuThemeChanged, changeContainerCls);
        }}
      >
        <img src={color_hand} />
        <span title={islandContent}>{islandContent}</span>
      </div>,
    );
    baseEventMgr.on(BaseEvents.menuThemeChanged, changeContainerCls);
  }, [changeContainerCls, islandContent, cardDomId]);

  useEffect(() => {
    document.getElementById(cardDomId)?.classList.add(styles['smart-cockpit-reindeer-island-origin-card']);
  }, [cardDomId]);

  // 父组件被销毁时，卸载菜单栏上的灵动岛
  useEffect(() => {
    return () => {
      islandRef.current?.unmount();
      containerRef.current?.remove();
      baseEventMgr.off(BaseEvents.menuThemeChanged, changeContainerCls);
    };
  }, [changeContainerCls]);

  return { showReindertIsland };
}

function getContainCls(curTheme: string): string {
  return `${styles['smart-cockpit-reindeer-island']} ${getTheme4Container(curTheme)}`;
}
