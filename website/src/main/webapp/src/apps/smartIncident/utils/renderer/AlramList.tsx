import React, { useState} from 'react';
import { Tooltip } from 'eview-ui';
import { formatDate2SysSetting } from '@digitalview/fe-utils';
import { $t, registerResource } from '@util';
import { jumpAlarmLogPage, jumpViewMoreHistoryPage } from '@util/tools';
import '@pages/businesstopo/css/tooltip/fault.less';
import { I_EventMO, I_EventInfo } from '../../apis/incident';
import i18nText from '../../locales';

const AlramList: React.FC<{ moEvents: I_EventMO & { eventList: I_EventInfo[] } }> = props => {
  registerResource(i18nText, 'smartIncident');

  const [chooseAlarm, setChooseAlarm] = useState(null);
  const { moEvents } = props;

  return (
    <div
      id="fault"
      style={{ left: 0, top: -30, width: '26.25rem', zIndex: 1001, pointerEvents: 'initial' }}
      onClick={e => {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
      }}
    >
      <div className='triangle' style={{ top: '1.375rem' }} />
      <div className="header">
        <div className="title">{moEvents.moName}</div>
      </div>
      <div className="hiddenScroll">
        <div className="contentList">
          {
            moEvents.eventList.slice(0, 4).map((evt, evtIndex) => (
              <div key={evt.alarmCsn} className={`content ${chooseAlarm === evtIndex ? 'clickBackground' : ''}`}>
                <div className="contentLeft">
                  <Tooltip
                    placement="topLeft"
                    content={evt.name}
                    color="#393939"
                    overlayStyle={{color: '#FFF'}}
                  >
                    <div className="contentTitle">
                      {evt.name}
                    </div>
                  </Tooltip>
                  <div className="contentTime">{formatDate2SysSetting(evt.occurTime)}</div>
                </div>
                <div
                  className="contentRight"
                  onClick={e => {
                    e.stopPropagation();
                    e.nativeEvent.stopImmediatePropagation();
                    setChooseAlarm(evtIndex);
                    jumpAlarmLogPage(evt.alarmCsn, 0);
                  }}
                >
                  {$t('smart.incident.detail.steps.perception.tooltip.view')}
                </div>
              </div>
            ))
          }
        </div>
      </div>
      {/* 待完成，这里需要构造一个 conditionId */}
      <div
        className="more"
        onClick={e => {
          e.stopPropagation();
          e.nativeEvent.stopImmediatePropagation();
          jumpViewMoreHistoryPage('');
        }}
      >
        {$t('smart.incident.detail.steps.perception.tooltip.view.more')}
      </div>
    </div>
  );
};

export default AlramList;
