import React from '@cloudsop/horizon';

import { thumbnail_cases, thumbnail_topo } from '../assets/const';
import LoadingIcon from '../assets/ic_public_loading_clockwise_round_bottom2.svg';
import Loading2Icon from '../assets/ic_gts_loading.svg';
import SuccessIcon from '../assets/success.svg';
import Success2Icon from '../assets/success2.svg';
import ErrorIcon from '../assets/close_circle_filled.svg';
import NotStartIcon from '../assets/not_start.svg';
import Error2Icon from '../assets/error.svg';
import { $t, registerResource } from '@util';
import {E_DiagnosticStatus, E_Severity, I_AggregateStatics, I_IncidentDetail, I_Relationresult} from '../apis/incident';
import i18nText from '../locales';
import { SEVERITY_COLOR_MAP, SEVERITY_INTL } from '../consts';
import { I_Step, E_DIAGNOSE_STEPS, I_IncidentDetail4UI, E_DIAGNOSE_STEP_STATUS } from '../interface';
import { AbstractStageRenderer, PerceptionRenderer, AnalysisRenderer, DecisionRenderer } from './renderer/renderer';
import { getGlobalState } from './globalState';

registerResource(i18nText, 'smartIncident');

let stageRender: AbstractStageRenderer;

/**
 * 渲染当前步骤的中心区域
 * @param domId 中间区域的 DOM ID，用于页面卸载或切换阶段时销毁中间区域组件，避免内存泄漏
 * @param incidentDetail incident详情
 * @param currentStepInfo 当前步骤
 * @param subStepIndex 当前步骤的子步骤
 */
export function renderStepUI(
  domId: string,
  incidentDetail: I_IncidentDetail4UI,
  currentStepInfo: I_Step,
  subStepIndex?: number,
): void {
  if (
    stageRender &&
    [E_DIAGNOSE_STEPS.ANALYSIS, E_DIAGNOSE_STEPS.DECISION].includes(stageRender.getStage()) &&
    [E_DIAGNOSE_STEPS.ANALYSIS, E_DIAGNOSE_STEPS.DECISION].includes(currentStepInfo.stage)
  ) {
    // 使用同一个渲染器的阶段，切换时不再重新绘制
    return;
  }

  stageRender?.destroy();
  stageRender = null;

  const centralPartRootElem = document.getElementById(domId);
  switch (currentStepInfo.stage) {
    case E_DIAGNOSE_STEPS.PERCEPTION:
      stageRender = new PerceptionRenderer(centralPartRootElem, currentStepInfo.stage);
      break;
    case E_DIAGNOSE_STEPS.ANALYSIS:
    case E_DIAGNOSE_STEPS.DECISION:
      stageRender = new AnalysisRenderer(centralPartRootElem, currentStepInfo.stage);
      break;
    default:
      break;
  }

  const commonConfig = {
    theme: getGlobalState().theme,
    version: 'aui3-1',
    locale: getGlobalState().language,
  };
  stageRender?.render(commonConfig, incidentDetail);
}

/**
 * 销毁当前步骤的中心区域UI
 * 用于详情页直接跳出到其他页面的场景
 */
export function destroyStepUI(): void {
  stageRender?.destroy();
}

/**
 * 根据故障详情构造steps
 */
function genStepsInfo(incidentDetail: I_IncidentDetail, fromCockpit: boolean): I_IncidentDetail4UI['steps'] {
  const steps: I_Step[] = [
    {
      status: E_DIAGNOSE_STEP_STATUS.SUCCESS,
      stage: E_DIAGNOSE_STEPS.PERCEPTION,
      description: $t('smart.incident.detail.steps.perception.desc'),
      appendDescription: incidentDetail.name,
    },
    {
      status: E_DIAGNOSE_STEP_STATUS.SUCCESS,
      stage: E_DIAGNOSE_STEPS.ANALYSIS,
      description: $t('smart.incident.detail.steps.analysis.desc'),
      appendDescription: $t('smart.incident.detail.steps.analysis.sub.desc'),
    },
  ];
  const subSteps = fromCockpit ? _genAlarmIncidentSubSteps() : [];
  if (incidentDetail.diagnosisStatus === E_DiagnosticStatus.FAILED) {
    steps.push({
      status: E_DIAGNOSE_STEP_STATUS.FAILED,
      stage: E_DIAGNOSE_STEPS.DECISION,
      description: $t('smart.incident.detail.steps.decision.desc'),
      appendDescription: $t('smart.incident.detail.steps.decision.sub.desc.failed'),
      subSteps,
    });
  } else if (incidentDetail.diagnosisStatus === E_DiagnosticStatus.SUCCESS) {
    steps.push({
      status: E_DIAGNOSE_STEP_STATUS.SUCCESS,
      stage: E_DIAGNOSE_STEPS.DECISION,
      description: $t('smart.incident.detail.steps.decision.desc'),
      appendDescription: $t('smart.incident.detail.steps.decision.sub.desc.finished'),
      subSteps,
    });
  } else if (incidentDetail.diagnosisStatus === E_DiagnosticStatus.PROCESSING) {
    steps.push({
      status: E_DIAGNOSE_STEP_STATUS.RUNNING,
      stage: E_DIAGNOSE_STEPS.DECISION,
      description: $t('smart.incident.detail.steps.decision.desc'),
      appendDescription: $t('smart.incident.detail.steps.decision.sub.desc.running'),
      subSteps,
    });
  } else {
    steps.push({
      status: E_DIAGNOSE_STEP_STATUS.NOT_START,
      stage: E_DIAGNOSE_STEPS.DECISION,
      description: $t('smart.incident.detail.steps.decision.desc'),
      appendDescription: $t('smart.incident.detail.steps.decision.sub.desc.notStart'),
      subSteps,
    });
  }

  return steps;
}

function _genAlarmIncidentSubSteps(): I_Step['subSteps'] {
  return [
    {
      type: 'cases',
      img: thumbnail_cases,
      name: $t('smart.incident.detail.steps.analysis.cases'),
    },
    {
      type: 'businessTopo',
      img: thumbnail_topo,
      name: $t('smart.incident.detail.steps.analysis.businessTopo'),
    },
  ];
}

function genStatisticsInfo(incidentDetail: I_IncidentDetail, incidentStatistics: I_AggregateStatics): I_IncidentDetail4UI['statistics'] {
  if (incidentStatistics.eventCountTrend.length === 0) {
    return {};
  }
  return {
    latestCount: incidentStatistics.eventCountTrend.slice(-1)[0],
    aggrLineData: _genAggrLineData(incidentDetail, incidentStatistics),
    moTypeAttribution: _genMoTypeAttribution(incidentDetail, incidentStatistics),
    alarmIdAttribution: _genAlarmIdAttribution(incidentDetail, incidentStatistics),
  };
}

function _genAggrLineData(incidentDetail: I_IncidentDetail, incidentStatistics: I_AggregateStatics): I_IncidentDetail4UI['statistics']['aggrLineData'] {
  const eventCountTrendSorted = incidentStatistics.eventCountTrend.sort((a, b) => a.timestamp - b.timestamp);
  const aggrCount = eventCountTrendSorted.length;
  return {
    id: `${incidentDetail.csn}_eventTrend`,
    name: '',
    config: {
      diagramType: 'PiuChart',
      type: 'line',
      legendShow: true,
      legendPosition: '0', // top
      showPoints: true,
      pointSize: 4,
      timeRange: {
        startTimestartTime: Number(eventCountTrendSorted[0].timestamp),
        endTime: Number(eventCountTrendSorted[aggrCount - 1].timestamp),
      },
    },
    fields: _genAggrLineFields(eventCountTrendSorted),
    length: aggrCount,
    unitTypes: 1,
  };
}

function _genAggrLineFields(eventCountTrendSorted: I_AggregateStatics['eventCountTrend']): any[] {
  return [
    {
      id: 'total',
      rawName: $t('smart.incident.list.status.total'),
      name: $t('smart.incident.list.status.total'),
      type: 'number',
      config: {
        type: {
          isX: false,
        },
        customColor: '#2070F3',
        show: true,
        unit: $t('smart.incident.detail.aggr.trend.unit'),
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.detail.total),
    },
    {
      id: E_Severity[E_Severity.URGENT],
      rawName: SEVERITY_INTL[E_Severity.URGENT],
      name: SEVERITY_INTL[E_Severity.URGENT],
      type: 'number',
      config: {
        type: {
          isX: false,
        },
        customColor: SEVERITY_COLOR_MAP[E_Severity.URGENT],
        show: true,
        unit: $t('smart.incident.detail.aggr.trend.unit'),
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.detail.critical),
    },
    {
      id: E_Severity[E_Severity.IMPORTANT],
      rawName: SEVERITY_INTL[E_Severity.IMPORTANT],
      name: SEVERITY_INTL[E_Severity.IMPORTANT],
      type: 'number',
      config: {
        type: {
          isX: false,
        },
        customColor: SEVERITY_COLOR_MAP[E_Severity.IMPORTANT],
        show: true,
        unit: $t('smart.incident.detail.aggr.trend.unit'),
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.detail.major),
    },
    {
      id: E_Severity[E_Severity.MINOR],
      rawName: SEVERITY_INTL[E_Severity.MINOR],
      name: SEVERITY_INTL[E_Severity.MINOR],
      type: 'number',
      config: {
        type: {
          isX: false,
        },
        customColor: SEVERITY_COLOR_MAP[E_Severity.MINOR],
        show: true,
        unit: $t('smart.incident.detail.aggr.trend.unit'),
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.detail.warning),
    },
    {
      id: E_Severity[E_Severity.TIPS],
      rawName: SEVERITY_INTL[E_Severity.TIPS],
      name: SEVERITY_INTL[E_Severity.TIPS],
      type: 'number',
      config: {
        type: {
          isX: false,
        },
        customColor: SEVERITY_COLOR_MAP[E_Severity.TIPS],
        show: true,
        unit: $t('smart.incident.detail.aggr.trend.unit'),
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.detail.info),
    },
    {
      id: 'timestamp',
      rawName: 'Time',
      name: 'Time',
      type: 'time',
      config: {
        type: {
          isX: true,
        },
        show: false,
        unit: null,
        yIndex: 0,
      },
      values: eventCountTrendSorted.map(item => item.timestamp),
    },
  ];
}

function _genMoTypeAttribution(incidentDetail: I_IncidentDetail, incidentStatistics: I_AggregateStatics): I_IncidentDetail4UI['statistics']['moTypeAttribution'] {
  return {
    id: `${incidentDetail.csn}_moTypeAttribution`,
    name: '',
    config: {
      diagramType: 'PiuChart',
      type: 'pie',
      legendShow: true,
      pieType: 'PIE',
      maxDataDimension: 20,
      showPercentage: false,
    },
    fields: incidentStatistics.moTypeDistribute.map(item => ({
      id: item.moType,
      rawName: item.moTypeName,
      name: item.moTypeName,
      type: 'number',
      values: [item.count],
      config: {
        show: true,
        yIndex: 0,
        type: {
          isX: false,
        },
      },
    })),
    length: 1,
  };
}

function _genAlarmIdAttribution(incidentDetail: I_IncidentDetail, incidentStatistics: I_AggregateStatics): I_IncidentDetail4UI['statistics']['alarmIdAttribution'] {
  return {
    id: `${incidentDetail.csn}_alarmIdAttribution`,
    name: '',
    config: {
      diagramType: 'PiuChart',
      type: 'pie',
      legendShow: true,
      pieType: 'PIE',
      maxDataDimension: 20,
      showPercentage: false,
    },
    fields: incidentStatistics.alarmIdDistribute.map(item => ({
      id: item.alarmId,
      rawName: item.name,
      name: item.name,
      type: 'number',
      values: [item.count],
      config: {
        show: true,
        yIndex: 0,
        type: {
          isX: false,
        },
      },
    })),
    length: 1,
  };
}

export function genIncidentData4UI(
    incidentData: I_IncidentDetail,
    incidentStatistics: I_AggregateStatics,
    relationresult: I_Relationresult,
    fromCockpit: boolean,
): I_IncidentDetail4UI {
    return {
        ...incidentData,
        steps: genStepsInfo(incidentData, fromCockpit),
        statistics: genStatisticsInfo(incidentData, incidentStatistics),
        moRelation: relationresult,
    };
}

/**
 * 根据诊断状态构造对应的状态图标
 */
export function genStatusIcon(status: E_DIAGNOSE_STEP_STATUS): React.JSX.Element {
  switch (status) {
    case E_DIAGNOSE_STEP_STATUS.NOT_START:
      return null;
    case E_DIAGNOSE_STEP_STATUS.RUNNING:
      return <LoadingIcon width='0.875rem' height='0.875rem' />;
    case E_DIAGNOSE_STEP_STATUS.FAILED:
      return <ErrorIcon color='#EB171F' width='0.875rem' height='0.875rem' />;
    case E_DIAGNOSE_STEP_STATUS.SUCCESS:
    default:
      return <SuccessIcon width='0.875rem' height='0.875rem' />;
  }
}

/**
 * 根据诊断状态构造对应的状态图标
 */
export function genCloseStatusIcon(status: E_DIAGNOSE_STEP_STATUS): React.JSX.Element {
  switch (status) {
    case E_DIAGNOSE_STEP_STATUS.NOT_START:
      return <NotStartIcon />;
    case E_DIAGNOSE_STEP_STATUS.RUNNING:
      return <Loading2Icon />;
    case E_DIAGNOSE_STEP_STATUS.FAILED:
      return <Error2Icon />;
    case E_DIAGNOSE_STEP_STATUS.SUCCESS:
    default:
      return <Success2Icon />;
  }
}

/**
 * 获取当前主题的样式scope
 * @param theme 当前主题，取值 evening 或 lightday
 */
export const getTheme4Container = (theme: string): string => {
  return theme === 'evening' ? 'dv-ai-agent-aui3-1 dv-ai-agent-aui3-1-evening' : 'dv-ai-agent-aui3-1 dv-ai-agent-aui3-1-lightday';
};
