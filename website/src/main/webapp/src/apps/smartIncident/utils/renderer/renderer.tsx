import ReactDOM from 'react-dom';
import React from '@cloudsop/horizon';
import { $t, modal } from '@src/commonUtil';
import eventBus from '@pages/businesstopo/a3dPages/bus';
import { eventBusHandler, getRouteQuery } from '@pages/businesstopo/a3dPages/components/utils';
import IncidentViewScene from '@pages/businesstopo/a3dPages/w3d/scenes/incidentView';
import { E_MO_LAYER, I_EventInfo, I_EventMO } from '../../apis/incident';
import { E_DIAGNOSE_STEPS, I_IncidentDetail4ThreeD, I_IncidentDetail4UI } from '../../interface';
import styles from './renderer.module.less';
import AlramList from './AlramList';

export abstract class AbstractStageRenderer {
  /**
   * 用于页面卸载或更新时销毁
   */
  protected centralPartElem: HTMLElement;

  protected stage: E_DIAGNOSE_STEPS;

  protected scene: IncidentViewScene;

  protected events: Array<{ eventName: string; fn: (settings) => any }> = [];

  public constructor(
    centralPartElem: HTMLElement,
    stage: E_DIAGNOSE_STEPS,
  ) {
    this.centralPartElem = centralPartElem;
    this.stage = stage;
  }

  public getStage(): E_DIAGNOSE_STEPS {
    return this.stage;
  }

  public destroy(): void {
    destroyW3D(this.scene, this.events);
    this.scene = null;
    this.events = [];
  }

  public render(
    commonConfig: { theme: string; version: string; locale: string },
    incidentDetail: I_IncidentDetail4UI,
  ): void {
    // 具体渲染由子类实现
  }
}

/**
 * 感知阶段渲染器
 */
export class PerceptionRenderer extends AbstractStageRenderer {
  protected stage = E_DIAGNOSE_STEPS.PERCEPTION;

  public async render(
    commonConfig: { theme: string; version: string; locale: string },
    incidentDetail: I_IncidentDetail4UI,
  ): Promise<void> {
    /**
     * 将moList按照站点实例进行分组，同时将eventList挂到对应的网元实例下
     * 方便快速查找：根据网元查看告警、根据关键告警查看发生网元
     */
    const alarmId2DnsMap: Record<string, string[]> = {};
    const dn2EventsMap: Record<string, I_EventInfo[]> = {};
    for (const evt of incidentDetail.eventList) {
      if (!alarmId2DnsMap[evt.alarmId]) {
        alarmId2DnsMap[evt.alarmId] = [];
      }
      alarmId2DnsMap[evt.alarmId].push(evt.dn);
      if (!dn2EventsMap[evt.dn]) {
        dn2EventsMap[evt.dn] = [];
      }
      dn2EventsMap[evt.dn].push(evt);
    }
    const keyEventWithDnsMap: I_IncidentDetail4ThreeD['keyEventWithDnsMap'] = {};
    for (const evt of incidentDetail.keyEventList) {
      if (!keyEventWithDnsMap[evt.alarmId]) {
        keyEventWithDnsMap[evt.alarmId] = {
          ...evt,
          dnList: alarmId2DnsMap[evt.alarmId],
        };
      }
    }
    const dataGroupBySite: I_IncidentDetail4ThreeD['dataGroupBySite'] = incidentDetail.moList.site.map(siteInstance => {
      const siteId = siteInstance.siteId;
      const appList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};
      const podList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};
      const vmList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};
      for (const instance of incidentDetail.moList.app) {
        if (instance.siteId === siteId) {
          appList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
        }
      }
      for (const instance of incidentDetail.moList.pod) {
        if (instance.siteId === siteId) {
          podList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
        }
      }
      for (const instance of incidentDetail.moList.host) {
        if (instance.siteId === siteId) {
          vmList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
        }
      }
      return {
        siteInfo: { ...siteInstance, eventList: dn2EventsMap[siteInstance.dn] },
        appList,
        podList,
        vmList,
        appCount: Object.keys(appList).length,
        podCount: Object.keys(podList).length,
        vmCount: Object.keys(vmList).length,
      };
    });
    const dn2Site = {}; // 感知阶段不需要这个映射关系
    try {
      this.events = initA3dEvents({ keyEventWithDnsMap, dataGroupBySite, dn2Site });
      this.scene = await initW3D(
        this.centralPartElem,
        { keyEventWithDnsMap, dataGroupBySite, dn2Site },
        this.events,
        { isMM: false, stage: this.stage },
      );
    } catch {
      modal.error($t('smart.incident.fe.error'));
    }
  }
}

/**
 * 分析阶段渲染器
 */
export class AnalysisRenderer extends AbstractStageRenderer {
  protected stage = E_DIAGNOSE_STEPS.ANALYSIS;

  public async render(
    commonConfig: { theme: string; version: string; locale: string },
    incidentDetail: I_IncidentDetail4UI,
  ): Promise<void> {
    // 遍历所有网元，找到每个网元与其所属站点实例的映射关系
    const dnMap = this.preHandleMoList(incidentDetail);

    // 遍历所有告警，找到每个告警的发生网元
    const { alarmId2DnsMap, dn2EventsMap } = this.preHandleEventList(incidentDetail);

    // 遍历所有关键告警，找到每个关键告警的发生网元，以及与发生网元 存在父子关系或部署关系的网元
    const { keyEventWithDnsMap, keyEventDns } = this.preHandleKeyEventList(incidentDetail, alarmId2DnsMap, dnMap);

    // 过滤出与关键告警有关系的网元
    const dataGroupBySite = this.filterKeyEventRelatedMoList(incidentDetail, keyEventDns, dn2EventsMap);

    try {
      this.events = initA3dEvents({ keyEventWithDnsMap, dataGroupBySite, dn2Site: dnMap });
      this.scene = await initW3D(
        this.centralPartElem,
        { keyEventWithDnsMap, dataGroupBySite, dn2Site: dnMap, moRelation: incidentDetail.moRelation },
        this.events,
        { isMM: false, stage: this.stage },
      );
    } catch {
      modal.error($t('smart.incident.fe.error'));
    }
  }

  /**
   * 遍历所有网元，找到每个网元与其所属站点实例的映射关系
   */
  private preHandleMoList(incidentDetail: I_IncidentDetail4UI): Record<string, I_EventMO & { siteDn: string }> {
    const dnMap: Record<string, I_EventMO & { siteDn: string }> = {};

    const siteId2SiteDn: Record<number, string> = {};
    for (const instance of incidentDetail.moList.site) {
      siteId2SiteDn[instance.siteId] = instance.dn;
      dnMap[instance.dn] = { ...instance, siteDn: instance.dn };
    }
    for (const instance of incidentDetail.moList.app) {
      dnMap[instance.dn] = { ...instance, siteDn: siteId2SiteDn[instance.siteId] };
    }
    for (const instance of incidentDetail.moList.pod) {
      dnMap[instance.dn] = { ...instance, siteDn: siteId2SiteDn[instance.siteId] };
    }
    for (const instance of incidentDetail.moList.host) {
      dnMap[instance.dn] = { ...instance, siteDn: siteId2SiteDn[instance.siteId] };
    }

    return dnMap;
  }

  /**
   * 遍历所有告警，找到每个告警的发生网元
   */
  private preHandleEventList(incidentDetail: I_IncidentDetail4UI): {
    alarmId2DnsMap: Record<string, string[]>;
    dn2EventsMap: Record<string, I_EventInfo[]>;
  } {
    const alarmId2DnsMap: Record<string, string[]> = {};
    const dn2EventsMap: Record<string, I_EventInfo[]> = {};

    for (const evt of incidentDetail.eventList) {
      if (!alarmId2DnsMap[evt.alarmId]) {
        alarmId2DnsMap[evt.alarmId] = [];
      }
      alarmId2DnsMap[evt.alarmId].push(evt.dn);
      if (!dn2EventsMap[evt.dn]) {
        dn2EventsMap[evt.dn] = [];
      }
      dn2EventsMap[evt.dn].push(evt);
    }

    return { alarmId2DnsMap, dn2EventsMap };
  }

  /**
   * 遍历所有关键告警，找到每个关键告警的发生网元，以及与发生网元 存在父子关系或部署关系的网元
   */
  private preHandleKeyEventList(
    incidentDetail: I_IncidentDetail4UI,
    alarmId2DnsMap: Record<string, string[]>,
    dnMap: Record<string, I_EventMO & { siteDn: string }>,
  ): {
    keyEventWithDnsMap: I_IncidentDetail4ThreeD['keyEventWithDnsMap'];
    keyEventDns: Set<string>;
  } {
    const keyEventWithDnsMap: I_IncidentDetail4ThreeD['keyEventWithDnsMap'] = {};
    const keyEventDns: Set<string> = new Set();

    for (const evt of incidentDetail.keyEventList) {
      if (!keyEventWithDnsMap[evt.alarmId]) {
        keyEventWithDnsMap[evt.alarmId] = {
          ...evt,
          dnList: alarmId2DnsMap[evt.alarmId],
        };
  
        for (const dn of alarmId2DnsMap[evt.alarmId]) {
          keyEventDns.add(dn);
          const dnObj = dnMap[dn];
          keyEventDns.add(dnObj.siteDn); // 站点网元必须加上，因为需要按照站点网元切换查看发生在其下的网元
          switch (dnObj.layer) {
            case E_MO_LAYER.APP: {
              (dnObj.childDNs ?? []).forEach(childDn => keyEventDns.add(childDn));
              break;
            }
            case E_MO_LAYER.POD: {
              (dnObj.sourceDeployMoDNs ?? []).forEach(deployDn => keyEventDns.add(deployDn));
              if (dnObj.parentDN) {
                keyEventDns.add(dnObj.parentDN);
              }
              break;
            }
            case E_MO_LAYER.HOST: {
              (dnObj.targetDeployMoDNs ?? []).forEach(deployDn => keyEventDns.add(deployDn));
              break;
            }
            default:
              break;
          }
        }
      }
    }

    return { keyEventWithDnsMap, keyEventDns };
  }

  /**
   * 过滤出与关键告警有关系的网元
   */
  private filterKeyEventRelatedMoList(
    incidentDetail: I_IncidentDetail4UI,
    keyEventDns: Set<string>,
    dn2EventsMap: Record<string, I_EventInfo[]>,
  ): I_IncidentDetail4ThreeD['dataGroupBySite'] {
    return incidentDetail.moList.site
      .map(siteInstance => {
        const siteId = siteInstance.siteId;
        const appList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};
        const podList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};
        const vmList: Record<string, I_EventMO & { eventList?: I_EventInfo[] }> = {};

        for (const instance of incidentDetail.moList.app) {
          if (instance.siteId === siteId && keyEventDns.has(instance.dn)) {
            appList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
          }
        }
        for (const instance of incidentDetail.moList.pod) {
          if (instance.siteId === siteId && keyEventDns.has(instance.dn)) {
            podList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
          }
        }
        for (const instance of incidentDetail.moList.host) {
          if (instance.siteId === siteId && keyEventDns.has(instance.dn)) {
            vmList[instance.dn] = { ...instance, eventList: dn2EventsMap[instance.dn] };
          }
        }

        return {
          siteInfo: { ...siteInstance, eventList: dn2EventsMap[siteInstance.dn] },
          appList,
          podList,
          vmList,
          appCount: Object.keys(appList).length,
          podCount: Object.keys(podList).length,
          vmCount: Object.keys(vmList).length,
          isKeyEventRelated: keyEventDns.has(siteInstance.dn), // 如果 keyEventDns 里面有当前站点的 dn，说明有关键告警发生在该站点，反之说明没有
        };
      })
      .filter(site => site.isKeyEventRelated);
  }
}

/**
 * 诊断阶段渲染器
 */
export class DecisionRenderer extends AbstractStageRenderer {
  protected stage = E_DIAGNOSE_STEPS.DECISION;

  private subStage: string;

  public constructor(
    centralPartElem: HTMLElement,
    stage: E_DIAGNOSE_STEPS,
    subStage: string,
  ) {
    super(centralPartElem, stage);
    this.subStage = subStage;
  }

  public render(
    commonConfig: { theme: string; version: string; locale: string },
    incidentDetail: I_IncidentDetail4UI,
  ): void {
    // 待实现
  }
}

/**
 * 初始化3D模型中需要响应的交互事件，以及如何处理交互
 * @param initData 业务数据
 * @param isVMScenario 是否为虚机？
 * @returns 返回需要响应的交互事件，用于后续销毁模型时同步清理事件监听。
 */
function initA3dEvents(initData: I_IncidentDetail4ThreeD): Array<{ eventName: string; fn: (settings) => any }> {
  const siteCount = initData.dataGroupBySite.length;
  return [
    {
      // 悬浮事件，显示tooltip
      eventName: 'from3d_showCommonTip',
      fn: (setting) => {
        setting.dom.innerHTML = '';
        if (setting.data.eventList && setting.data.eventList.length > 0) {
          ReactDOM.render(<AlramList moEvents={setting.data} />, setting.dom);
        }
      },
    },
    {
      // 面板左侧展示详情
      eventName: 'from3d_showIncidentPanelInfo',
      fn: (setting) => {
        let dom;
        if (setting.type === 'site') {
          dom = document.createElement('div');
          dom.classList.add(styles['incident-detail-panel-info']);
          dom.innerText = `${$t('smart.incident.detail.layer.site')} ${siteCount}`;
        }
        if (setting.type === 'app') {
          dom = document.createElement('div');
          dom.classList.add(styles['incident-detail-panel-info']);
          dom.innerText = `${$t('smart.incident.detail.layer.app')} ${setting.data.appCount}`;
        }
        if (setting.type === 'pod') {
          dom = document.createElement('div');
          dom.classList.add(styles['incident-detail-panel-info']);
          dom.innerText = `${$t('smart.incident.detail.layer.pod')} ${setting.data.podCount}`;
        }
        if (setting.type === 'vm') {
          dom = document.createElement('div');
          dom.classList.add(styles['incident-detail-panel-info']);
          dom.innerText = `${$t('smart.incident.detail.layer.host')} ${setting.data.vmCount}`;
        }
        if (dom) {
          setting.dom.append(dom);
        }
      },
    },
  ];
}

/**
 * 初始化及绘制一个3D场景
 * @param container 3D绘制的容器
 * @param setting 3D绘制的配置、数据等
 */
async function initW3D(
  container: HTMLElement,
  initData: I_IncidentDetail4ThreeD,
  events: Array<{ eventName: string; fn: (settings) => any }>,
  setting: { isMM: boolean; stage: E_DIAGNOSE_STEPS },
): Promise<IncidentViewScene> {
  // 添加事件监听
  for (const evt of events) {
    eventBus.addListener(evt.eventName, evt.fn);
  }

  // 2D-3D对接，将数据转化成3D需要的数据
  const data = {
    ...initData,
    stage: setting.stage,
    podCount: 0,
    productionProportion: 0,
    grayProportion: 0,
    applicationType: 1,
    environmentType: 0,
    i18n: {},
  };

  const scene3D = new IncidentViewScene(eventBusHandler);
  const routeQuery = getRouteQuery();
  routeQuery.isMM = setting.isMM;
  await scene3D.init(container, routeQuery, { incidentView: data }); // 这里的 incidentView 是用于在 handler 中获取对应的处理器
  return scene3D;
}

/**
 * 销毁3D场景
 */
function destroyW3D(scene3D: IncidentViewScene, events: Array<{ eventName: string; fn: (settings) => any }>): void {
  for (const evt of events) {
    eventBus.removeListener(evt.eventName, evt.fn);
  }
  scene3D?.destroy();
}

/**
 * resize 3D场景
 */
function resizeW3D(scene3D: IncidentViewScene): void {
  if (scene3D?.resize) {
    scene3D.resize();
  }
}
