type TEventHandler = (data?: unknown) => any;

class EventMgr {
  /**
   * 存放事件的订阅者
   */
  private _eventList: Record<string, TEventHandler[]> = {};

  /**
   * 缓存发布时还未被订阅的消息，用于在订阅时接收之前发布的消息并执行回调，解决发布先于订阅的场景。
   */
  private _pastMsgList: Record<string, unknown[]> = {};

  public on(event: string, callback: TEventHandler, recievePastMsg: boolean = false): void {
    if (!this._eventList[event]) {
      this._eventList[event] = [];
    }
    this._eventList[event].push(callback);

    // recievePastMsg 为 true，用于先发布后订阅 订阅者也需要收到消息的场景
    if (recievePastMsg && this._pastMsgList[event]) {
      callback(...this._pastMsgList[event]);
      delete this._pastMsgList[event];
    }
  }

  public off(event: string, callback: TEventHandler): void {
    if (!this._eventList[event]) {
      return;
    }
    let index = this._eventList[event].indexOf(callback);
    if (index > -1) {
      this._eventList[event].splice(index, 1);
    }
    if (this._eventList[event].length === 0) {
      delete this._eventList[event];
    }
  }

  public emit(event: string, ...data: unknown[]): void {
    if (!this._eventList[event] || this._eventList[event].length === 0) {
      // 如果还没有订阅者，就把消息缓存起来
      this._pastMsgList[event] = data;
      return;
    }
    for (let callback of this._eventList[event]) {
      try {
        callback(...data);
      } catch {
        // 捕获异常，确保能继续通知其他订阅者
      }
    }
  }
}

export const baseEventMgr = new EventMgr();

export default EventMgr;

export const BaseEvents = {
  themeChanged: 'themeChanged',
  menuThemeChanged: 'menuThemeChanged', // 仅菜单栏发生主题切换时触发，用于只有黑夜主题的页面
};
