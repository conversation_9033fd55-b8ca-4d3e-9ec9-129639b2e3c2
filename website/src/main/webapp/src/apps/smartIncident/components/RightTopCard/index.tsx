import React, { useContext, useMemo, useState } from 'react';
import { Tooltip } from '@cloudsop/eview-ui';

import { aicoLogo } from '../../assets/const';
import ExpandIcon from '../../assets/ic_public_expand_border.svg';
import ZoomOutIcon from '../../assets/ic_ict_exit_full_screen_border.svg';

import { $t } from '@util';
import { GlobalContext, Theme } from '../../utils/globalState';
import useReindertIsland from '../../utils/useReindertIsland';
import { STEP_2_ICON, STEP_2_TITLE } from '../../consts';
import { E_DIAGNOSE_STEPS, I_IncidentDetail4UI, I_Step } from '../../interface';

import DiagnosisReport from './DiagnosisReport';
import KeyEvents from './KeyEvents';
import styles from '../page.module.less';
import StatisticView from './StatisticView';

interface I_RightTopCardProps {
  currentStep: I_Step;
  incidentDetail: I_IncidentDetail4UI;
  fromCockpit: boolean;
}

/**
 * 故障详情-右上角悬浮卡片
 */
const RightTopCard: React.FC<I_RightTopCardProps> = props => {
  const { theme } = useContext(GlobalContext);
  const { currentStep, incidentDetail, fromCockpit } = props;

  const { showReindertIsland } = useReindertIsland('cockpit-right-top-card', incidentDetail.name);
  const [expandView, setExpandView] = useState(false);

  const contentDetail = useMemo(() => {
    if (currentStep.stage === E_DIAGNOSE_STEPS.PERCEPTION) {
      return <StatisticView incidentDetail={incidentDetail} />;
    } else if (currentStep.stage === E_DIAGNOSE_STEPS.ANALYSIS) {
      return <KeyEvents incidentDetail={incidentDetail} />;
    } else {
      return fromCockpit ? null : <DiagnosisReport incidentDetail={incidentDetail} />;
    }
  }, [currentStep, incidentDetail]);

  return (
    <div id='cockpit-right-top-card' className={styles['cockpit-right-top-card']} style={expandView ? { width: '50vw' } : undefined}>
      <Tooltip content={$t('smart.incident.common.hide.panel')} placement='top' hideArrow trigger={['hover']}>
        <span
          onClick={() => showReindertIsland()}
          className={styles['cockpit-right-top-card-handler']}
        />
      </Tooltip>
      <div className={styles['cockpit-right-top-card-step-title']}>
        <div><img src={aicoLogo} /></div>
        <p>
          <span>{STEP_2_TITLE[currentStep.stage]}</span>
          <span>{incidentDetail.name}</span>
        </p>
      </div>
      <div className={styles['cockpit-right-top-card-step-content']}>
        <div className={styles['cockpit-right-top-card-step-content-detail']}>
          {contentDetail}
        </div>
      </div>
    </div>
  );
};

export default RightTopCard;
