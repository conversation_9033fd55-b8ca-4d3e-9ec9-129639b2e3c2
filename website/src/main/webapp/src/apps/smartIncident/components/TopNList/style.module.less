.topNIncidentsContainer {
  position: relative;
  width: 100%;
  padding: 1rem;
  margin: -4rem 0 4rem 0;
  box-sizing: border-box;

  .commonOneLineStyle {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .collapsableTab {
    width: 100%;
    height: 2.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    padding: 0 1rem 0 0;
    box-sizing: border-box;
    .collapsableTitle {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      img {
        width: 1.25rem;
        height: 1.25rem;
      }
      span {
        font-size: 1rem;
        font-weight: 600;
        color: rgba(138, 190, 243, 1);
        line-height: 1.375rem;
      }
    }
    .collapsableIcon {
      cursor: pointer;
      width: 1rem;
      height: 1rem;
    }
  }

  .content {
    margin-top: 0.875rem;

    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .incidentCard {
      width: 100%;
      border-radius: 0.75rem;
      // 在绘制时，图像以 z 方向堆叠的方式进行。先指定的图像会在之后指定的图像上面绘制。因此指定的第一个图像“最接近用户”。
      background-image:
        url(../../assets/top_smart_incident_tbg.png),
        linear-gradient(
          232.13deg,
          rgba(42, 42, 42, 1),
          rgba(57, 57, 57, 1) 100%
        );
      background-size: contain;
      background-position: right top;
      background-repeat: no-repeat;
      padding: 0.875rem 1rem 1rem 1rem;
      box-sizing: border-box;

      display: flex;
      flex-direction: column;
      gap: 0.375rem;

      cursor: pointer;

      .incidentHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        .incidentHeaderLeft {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          .commonOneLineStyle();
          svg {
            flex-shrink: 0;
          }
          .incidentName {
            font-size: 1rem;
            color: rgba(255, 255, 255, 1);
            line-height: 1.5;
            .commonOneLineStyle();
          }
        }
        .incidentHeaderRight {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          gap: 0.375rem;
          .incidentDiagnoseStatusCircle {
            display: inline-block;
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
          }
          .incidentDiagnoseStatusIntl {
            font-size: 0.75rem;
            color: rgba(201, 201, 201, 1);
          }
        }
      }

      .incidentBody {
        font-size: 0.875rem;
        color: rgba(201, 201, 201, 1);
        /* 最多显示 3 行 */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .viewAll {
      text-align: center;
    }
  }
}
