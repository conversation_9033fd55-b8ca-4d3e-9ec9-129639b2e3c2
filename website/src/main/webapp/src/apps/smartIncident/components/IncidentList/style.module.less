.stateCountsCellsContainer {
  margin-top: 0;
  margin-bottom: 1rem;
  display: flex;
  gap: 2.5rem;
  .leftStateCountsCells {
    flex-shrink: 0;
  }
  .rightStateCountsCells {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem 2.5rem;
  }
  .stateCountsCell {
    height: 1.5rem;
    .circle {
      width: 0.5rem;
      height: 0.5rem;
      margin-right: 0.375rem;
      border-radius: 50%;
      display: inline-block;
    }
    .stateText {
      line-height: 1.5rem;
    }
    .selectedNumber {
      width: fit-content;
      height: 1.375rem;
      padding-left: 0.625rem;
      padding-right: 0.625rem;
      margin-left: 0.25rem;
      text-align: center;
      line-height: 1.375rem;
      border-radius: 1.25rem;
      background: #0067d11a;
      border: 0.0625rem solid #2e86de;
      display: inline-block;
      cursor: pointer;
    }
    .underlinedNumber {
      line-height: 1.5rem;
      margin-left: 0.5rem;
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

.incidentListOperation {
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: space-between;
  .incidentListLeftFilter {
    padding: 0.5rem 0.5rem;
    margin-bottom: 0;
  }
  .incidentListRightBtns {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
}

.incidentListFilterArea {
  position: relative;
  background: #eeeeee0d;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 1.5rem 1rem 0.5rem;
  border-radius: 0.25rem;
  margin-top: 0.6875rem;
  margin-bottom: 1rem;
  .incidentListFilterItem {
    height: 2rem;
    width: 33%;
    line-height: 2rem;
    margin-bottom: 1.3rem;
    display: flex;
    justify-content: center;
    gap: 1.375rem;
  }
}

.incidentListTableContainer {
  :global {
    .eui-table-container {
      background: #2a2a2a !important;
    }
  }
}

.priorityCell {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.diagnoseCellIcon {
  cursor: pointer;
  &:nth-child(n+2) {
    margin-left: 0.5rem;
  }     
}
.statusCellCircle {
  width: auto;
  border-radius: 0.25rem;
  padding: 0.125rem 0.5rem;
}
.diagnoseStatusCellCircle {
  width: 0.5rem;
  height: 0.5rem;
  margin-right: 0.25rem;
  flex-shrink: 0;
  display: inline-block;
  border-radius: 50%;
}

.tableStretch {
  height: 10rem;
}
.emptyNodeContainer {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 5.5rem;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  .emptyNodeIcon {
    width: 3rem;
    height: 3rem;
    flex-shrink: 0;
  }
  .emptyNodeText {
    padding-top: 0.6875rem;
    font-size: 0.875rem;
    color: #fff;
  }
}
