import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { formatDate2SysSetting } from '@digitalview/fe-utils';
import { STEP_2_TITLE } from '../../consts';
import {genCloseStatusIcon, genStatusIcon} from '../../utils/pageUtils';
import styles from '../page.module.less';
import { I_StepBarItemProps } from './interface';

/**
 * 步骤条中的单个步骤
 */
const StepsBarItem: React.FC<I_StepBarItemProps> = props => {
  const { displaySteps, selfStep, isSelected, onStepClick, portalRef, stepsBarStatus} = props;
  const stepBarDate = selfStep.startTime ? formatDate2SysSetting(selfStep.startTime) : null;
  const stepBorderColorSelectClass = isSelected ? styles['cockpit-step-title-with-border-color-select'] : '';
  const stepTitleClassName = `${styles['cockpit-step-title-with-border']} ${stepBorderColorSelectClass}`;
  const stepTitleIconClass = styles[`cockpit-sub-step-bar-item-title-status-icon-${selfStep.status}`];
  const containerBorderSelect = isSelected ? styles['cockpit-sub-step-bar-item-content-container-border-select'] : '';

  const [selectedSubStep, setSelectedSubStep] = useState(0);

  return (
    <div className={styles['cockpit-step-bar-item']}>
      {
        displaySteps.has(selfStep.stage) &&
            <div
              className={styles['cockpit-sub-step-bar-item']}
              data-key={selfStep.stage}
              onClick={() => onStepClick(selfStep, (selfStep.subSteps ?? []).length > 0 ? selectedSubStep : undefined)}
            >
              <div className={`${styles['cockpit-sub-step-bar-item-container']} ${isSelected ? styles.selected : ''}`}>
                { stepsBarStatus &&
                <div className={stepTitleClassName}>
                  {STEP_2_TITLE[selfStep.stage]}
                </div>}
                <div className={`${styles['cockpit-sub-step-bar-item-content-container']} ${containerBorderSelect}`}>
                  {
                    stepsBarStatus &&
                      <div className={styles['cockpit-sub-step-bar-item-content']}>
                        <div className={styles['cockpit-sub-step-bar-item-title']}>
                          <div className={`${styles['cockpit-sub-step-bar-item-title-status-icon']} ${stepTitleIconClass}`}>
                            {genStatusIcon(selfStep.status)}
                          </div>
                          <div className={styles['cockpit-sub-step-bar-item-title-info']} title={selfStep.description}>
                            {selfStep.description}
                          </div>
                        </div>
                        <div className={styles['cockpit-sub-step-bar-item-desc']} title={selfStep.appendDescription}>
                          {selfStep.appendDescription}
                        </div>
                      </div>
                  }
                  {
                    !stepsBarStatus &&
                      <div className={[styles['cockpit-sub-step-bar-item-content2'], styles[`cockpit-sub-step-bar-item-content2-${selfStep.status}`]].join(' ')}>
                        <span>{genCloseStatusIcon(selfStep.status)}</span>
                        <span>{STEP_2_TITLE[selfStep.stage]}</span>
                      </div>
                  }
                  <div className={styles['cockpit-sub-step-status-bar']}>
                    <div className={styles[`step-bar-${selfStep.status}`]} />
                    { stepsBarStatus && <div className={styles['step-bar-time']}>16:50</div>}
                  </div>
                </div>
              </div>
              {
                stepBarDate !== null ?
                  <div className={styles['cockpit-sub-step-start-time']}>
                    <div>{stepBarDate.split(' ')[1]}</div>
                    <div>{stepBarDate.split(' ')[0]}</div>
                  </div> :
                  null
              }
            </div>
      }
      {
        // 存在子步骤
        (isSelected && (selfStep.subSteps ?? []).length > 0) &&
            createPortal(
              <div className={styles['cockpit-step-bar-overview-sub-panel']}>
                {
                  selfStep.subSteps.map((subStep, subStepIndex) => (
                    <div
                      key={subStepIndex}
                      className={`${styles['cockpit-step-bar-overview-sub-panel-item']} ${selectedSubStep === subStepIndex ? styles.selected : ''}`}
                      onClick={() => {
                        setSelectedSubStep(subStepIndex);
                        onStepClick(selfStep, subStepIndex);
                      }}
                      title={subStep.name}
                    >
                      <img src={subStep.img} />
                      <span>{subStep.name}</span>
                    </div>
                  ))
                }
              </div>,
              portalRef.current,
            )
      }
    </div>
  );
};

export default StepsBarItem;
