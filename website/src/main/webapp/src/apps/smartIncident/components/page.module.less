/* 默认取值为白天主题的页面级滚动条样式 */
.customScrollbar(@bgColor: transparent, @barColor: #dfdfdf, @barActiveColor: #aeaeae, @barWidth: 0.875rem, @thumbBorderW: 0.25rem, @thumbHoverBorderW: 0.125rem) {
  /* 这两个属性是兼容火狐的写法 */
  @-moz-document url-prefix() {
    scrollbar-width: thin;
    scrollbar-color: @barColor @bgColor;
  }

  /* 高宽分别对应横竖滚动条的尺寸 */
  &::-webkit-scrollbar {
    height: @barWidth;
    width: @barWidth;
  }
  /* Track */
  &::-webkit-scrollbar-track {
    background: @bgColor;
    border-radius: 0.625rem;
  }
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: @barColor;
    border: @thumbBorderW solid transparent;
    border-radius: 0.625rem;
    background-clip: content-box;
    cursor: pointer;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: @barActiveColor;
    border: @thumbHoverBorderW solid transparent;
    background-clip: content-box;
  }
  /* 滚动条两端按钮 */
  &::-webkit-scrollbar-button:vertical {
    display: none;
  }
  &::-webkit-scrollbar-button:horizontal {
    display: none;
  }
  /* 当同时有垂直滚动条和水平滚动条时交汇的部分 */
  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.detailsPage * {
  .customScrollbar();
  font-family: "Microsoft Yahei", Arial, "PingFang SC", "San Francisco";
}
:global(.dv-smart-incident-theme-evening) {
  .detailsPage * {
    .customScrollbar(transparent, #595959, #939393);
  }
}

:global {
  .lil-gui.autoPlace {
    right: 2rem !important;
    top: 4rem !important;
  }
}

.detailsPage {
  position: relative;
  height: calc(100vh - 4rem);
  overflow: hidden;
  background-color: #191919;
  background: url("../assets/smart_cockpit_detail_bg.png");
  background-repeat: repeat;
  background-size: cover;
  .leftTopCardContainer {
    position: absolute;
    top: 1rem;
    left: 2rem;
    z-index: 3;
  }
  .rightTopCardContainer {
    position: absolute;
    top: 1rem;
    right: 2rem;
  }
  .centralContainer {
    position: relative;
    z-index: 1;
    width: 100%;
    height: calc(100% - 6rem);
    margin-top: 0;
    margin-bottom: 6rem;
    margin-left: 0;
    margin-right: 0;
    box-sizing: border-box;
    overflow-y: hidden;
    background: transparent;
    &.hasSubStep {
      height: calc(100% - 12rem);
      margin-bottom: 12rem;
      padding-bottom: 2.5rem;
      padding-right: 1.5rem;
      background-image: url("../assets/smart_cockpit_detail_bg3.png");
      background-position: 0 bottom;
      background-repeat: no-repeat;
      background-size: 100%;
    }
  }
  .disgnoseStepsContainer {
    position: absolute;
    left: 0;
    bottom: 0rem;
    width: 100%;
    z-index: 2;
  }
}

// 诊断步骤条，默认关闭状态
.cockpit-step-bar-overview-default {
  --stepBarSelectedBgColor-rgb: 92, 162, 233;
  --stepBarHoverBgColor-rgb: 147, 147, 147;
  --stepBarBorderBgColor: rgba(243, 243, 243, 0.2);
  --stepBarBorderBgColorSelect: rgba(46, 134, 222, 1);

  width: 100%;
  height: 88px;
  position: relative;
  z-index: 100;
  box-shadow: 0px 16px 48px 0px rgba(0, 0, 0, 0.56);
  backdrop-filter: blur(50px);
  background: linear-gradient(-0.41deg, rgba(38, 37, 37, 1),rgba(94, 94, 94, 0.45) 100%),linear-gradient(177.99deg, rgba(238, 90, 59, 0.12),rgba(25, 25, 25, 0) 100%),linear-gradient(-7.64deg, rgba(59, 78, 238, 0.12),rgba(25, 25, 25, 0) 100%);
  .cockpit-step-bar-overview-default-bar{
    width: 32px;
    position: absolute;
    top: 5%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
  }

  .cockpit-step-bar-overview-default-container {
    border-radius: 24px;
    height: 44px;
    width: 99%;
    background: rgba(243, 243, 243, 0.1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .cockpit-step-bar-overview-default-leftSide {
      display: flex;
      align-items: center; /* 垂直居中 */
      gap: 16px; /* 元素间间距 */
      font-size: 14px;
      height: 44px;
      margin-left: 2rem;
      .cockpit-step-bar-overview-default-leftSide-dark{
        color: rgba(201, 201, 201, 1);
        font-family: HarmonyHeiTi;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
      }
      .cockpit-step-bar-overview-default-leftSide-light{
        color: rgba(255, 255, 255, 1);
        font-family: HarmonyHeiTi;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
      }
    }
    .cockpit-step-bar-overview-content-line{
      position: absolute;  /* 在父容器内绝对定位 */
      height: 1px;
      z-index: 10;
      width: 50%;
      margin-top: -13px;
      left: 22%;
      border-radius: 10px;
      background: rgba(89, 89, 89, 1);
    }
    .cockpit-step-bar-overview-content {
      position: absolute;
      top: 0;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      padding-left: 55%;
      transform: translateX(-37rem);
      z-index: 1000;
      display: flex;
      .cockpit-step-bar-item {
        display: flex;
        flex: 0 0 auto;

        .cockpit-sub-step-bar-item {
          width: 18.5rem;
          font-size: 0.875rem;

          .cockpit-sub-step-bar-item-container {
            cursor: pointer;

            .cockpit-step-title-with-border {
              height: 1.375rem;
              line-height: 1.375rem;
              padding-top: 0.25rem;
              padding-left: 0.5rem;
              font-weight: bold;
              border-left: 0.0625rem solid var(--stepBarBorderBgColor);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 16.5rem;
            }

            .cockpit-step-title {
              height: 1.375rem;
              line-height: 1.375rem;
              padding-top: 0.25rem;
            }

            .cockpit-sub-step-bar-item-content-container {
              //border-left: 0.0625rem solid var(--stepBarBorderBgColor);

              .cockpit-sub-step-bar-item-content2{
                display: inline-flex; /* 或 flex，看布局需要 */
                gap: 8px; /* 间距 4px */
                margin-bottom: 4px;
                color: rgba(201, 201, 201, 1)
              }
              .cockpit-sub-step-bar-item-content2-running{
                color: rgba(92, 162, 233, 1);
              }
              padding-top: 6px;
              padding-bottom: 4px;
              .cockpit-sub-step-bar-item-content {
                padding-left: 0.5rem;
                padding-bottom: 0.5rem;

                .cockpit-sub-step-bar-item-title {
                  display: flex;
                  height: 1.375rem;
                  align-items: center;

                  .cockpit-sub-step-bar-item-title-status-icon {
                    width: 0.875rem;
                    height: 0.875rem;

                    &.cockpit-sub-step-bar-item-title-status-icon-running {
                      animation: runningRotate 1s linear infinite;
                      @keyframes runningRotate {
                        0% {
                          transform: rotate(0deg);
                        }
                        100% {
                          transform: rotate(360deg);
                        }
                      }
                    }
                  }

                  .cockpit-sub-step-bar-item-title-info {
                    padding-left: 0.5rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 16rem;
                  }
                }

                .cockpit-sub-step-bar-item-desc {
                  padding-left: 1.5rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 16rem;
                  height: 1.25rem;
                  line-height: 1.25rem;
                  font-size: 0.75rem;
                  color: var(--color-text-secondary);
                }
              }

              .cockpit-sub-step-status-bar {
                div:nth-of-type(1).step-bar-not_start {
                  height: 4px;
                  background: var(--color-tag-text-none);
                  border-radius: 10px;
                }

                div:nth-of-type(1).step-bar-running {
                  height: 4px;
                  animation: loadStep 2s ease-in-out;
                  animation-fill-mode: forwards;
                  background: var(--brand-30);
                  border-radius: 5px;
                }

                div:nth-of-type(1).step-bar-success {
                  height: 4px;
                  background: var(--color-tag-text-success);
                  border-radius: 10px;
                  margin-right: 0.5rem;
                }

                div:nth-of-type(1).step-bar-failed {
                  height: 4px;
                  background: var(--color-error);
                  border-radius: 5px;
                }

                /* 动画效果 */
                @keyframes loadStep {
                  from {
                    width: 0;
                  }
                  to {
                    width: 80%;
                  }
                }

                div:nth-of-type(2) {
                  height: 0.375rem;
                }
              }
            }
          }

          .cockpit-sub-step-bar-item-container:hover {
            background: linear-gradient(to left,
            rgba(var(--stepBarHoverBgColor-rgb), 0.05),
            rgba(var(--stepBarHoverBgColor-rgb), 0.25));
          }

          .cockpit-sub-step-bar-item-container.selected {
            background: linear-gradient(180.00deg, rgba(184, 217, 249, 0.15),rgba(184, 217, 249, 0) 100%),radial-gradient(56.00% 20.00% at 28.000000000000004% 0%,rgba(0, 78, 168, 0.2),rgba(0, 78, 168, 0) 100%),radial-gradient(45.00% 25.00% at 68% 110.00000000000001%,rgba(28, 148, 164, 0.1),rgba(28, 148, 164, 0) 100%),radial-gradient(29.00% 29.00% at 5% 0%,rgba(85, 49, 235, 0.1),rgba(85, 49, 235, 0) 100%);
          }

          .cockpit-sub-step-start-time {
            width: 5rem;
            margin-left: -2.5rem;
            text-align: center;
            font-size: 0.75rem;
            color: var(--color-text-secondary);
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// 诊断步骤条
.cockpit-step-bar-overview {
  --stepBarSelectedBgColor-rgb: 92, 162, 233;
  --stepBarHoverBgColor-rgb: 147, 147, 147;
  --stepBarBorderBgColor: rgba(243, 243, 243, 0.2);
  --stepBarBorderBgColorSelect: rgba(46, 134, 222, 1);

  .cockpit-step-bar-overview-toolbar-container{
    background: linear-gradient(-0.41deg, rgba(38, 37, 37, 1),rgba(94, 94, 94, 0.45) 100%),linear-gradient(177.99deg, rgba(238, 90, 59, 0.12),rgba(25, 25, 25, 0) 100%),linear-gradient(-7.64deg, rgba(59, 78, 238, 0.12),rgba(25, 25, 25, 0) 100%);
    height: 19px;
    position: relative;
    .cockpit-step-bar-overview-toolbar{
      width: 32px;
      position: absolute;
      top: 20%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
    }
  }

  width: 100%;
  height: 10rem;
  position: relative;
  z-index: 100;
  background: var(--color-bg-3);
  box-shadow: var(--shadow-sm);

  .cockpit-step-bar-overview-panel {
    padding: 1.25rem 1.5rem 0 1.5rem;
    color: var(--color-text-primary);
    background: rgba(243, 243, 243, 0.1);
    .cockpit-step-bar-overview-title {
      height: 1.75rem;
      line-height: 1.75rem;
      font-size: 1.25rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .cockpit-step-bar-overview-container {
      position: relative;
      width: 100%;
      overflow-x: auto;
      scroll-behavior: smooth;

      .cockpit-step-bar-box-position{
        border: none;
        position: absolute;
      }

      .cockpit-step-bar-box{
        width: 164px;
        height: 48px;
        border-radius: 4px;
        background: rgba(243, 243, 243, 0.1);
        margin-bottom: 6px;
        display: flex;
        flex-direction: column;
        padding-left: 6px;
        padding-top: 3px;
        .cockpit-step-bar-box-row{
          display: flex;
          align-items: center;
        }

        .cockpit-step-bar-box-row-second{
          display: flex;
          align-items: center;
          padding-left: 18px; /* 图标宽度 + margin-right，保证对齐 */
        }
        .cockpit-step-bar-box-row-text{
          color: rgba(201, 201, 201, 1);
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0px;
          text-align: left;
          margin-left: 4px;
        }
        .cockpit-step-bar-box-row-text-second{
          color: rgba(255, 255, 255, 1);
          font-family: HarmonyOS Sans SC;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: 0px;
          text-align: center;
        }
      }


      .cockpit-step-bar-process-success{
        width: 50%;
        height: 0.75rem;
        border: none;
        margin-top: 4.75rem;
        margin-bottom: 5.5rem;
        background: linear-gradient(
                to right,
                rgba(13, 148, 88, 0.1) 0%,        /* 起点完全透明 */
                rgba(13, 148, 88, 0.5) 20%,        /* 起点完全透明 */
                rgba(13, 148, 88, 1) 100%       /* 最后 20% 全是 #0D9458 */
        );
        transform: translateX(-20rem);
        margin-left: 31rem;
        z-index: 989;
        position: absolute;

      }

      .cockpit-step-bar-process-padding {
        width: 50%;
        height: 0.75rem;
        border: none;
        margin-top: 4.75rem;
        margin-bottom: 2.5rem;
        margin-left: auto;
        background-image: linear-gradient(90deg,
        rgba(89, 144, 253, 0.3) 65%,
        transparent 35%);
        background-size: 0.375rem 100%;
      }

      .cockpit-step-bar-overview-content {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        width: 100%;
        padding-left: 55%;
        transform: translateX(-37rem);
        z-index: 1000;
        display: flex;

        .cockpit-step-bar-item {
          display: flex;
          flex: 0 0 auto;

          .cockpit-sub-step-bar-item {
            width: 18.5rem;
            height: 7.875rem;
            font-size: 0.875rem;
            .cockpit-sub-step-bar-item-container {
              cursor: pointer;
              .cockpit-step-title-with-border {
                height: 1.375rem;
                line-height: 1.375rem;
                padding-top: 0.25rem;
                padding-left: 0.5rem;
                font-weight: bold;
                border-left: 0.0625rem solid var(--stepBarBorderBgColor);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 16.5rem;
              }

              .cockpit-step-title {
                height: 1.375rem;
                line-height: 1.375rem;
                padding-top: 0.25rem;
              }

              .cockpit-sub-step-bar-item-content-container {
                border-left: 0.0625rem solid var(--stepBarBorderBgColor);
                .cockpit-sub-step-bar-item-content {
                  padding-left: 0.5rem;
                  padding-bottom: 0.5rem;
                  .cockpit-sub-step-bar-item-title {
                    display: flex;
                    height: 1.375rem;
                    align-items: center;
                    .cockpit-sub-step-bar-item-title-status-icon {
                      width: 0.875rem;
                      height: 0.875rem;
                      &.cockpit-sub-step-bar-item-title-status-icon-running {
                        animation: runningRotate 1s linear infinite;
                        @keyframes runningRotate {
                          0% {
                            transform: rotate(0deg);
                          }
                          100% {
                            transform: rotate(360deg);
                          }
                        }
                      }
                    }
                    .cockpit-sub-step-bar-item-title-info {
                      padding-left: 0.5rem;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      width: 16rem;
                    }
                  }
                  .cockpit-sub-step-bar-item-desc {
                    padding-left: 1.5rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 16rem;
                    height: 1.25rem;
                    line-height: 1.25rem;
                    font-size: 0.75rem;
                    color: var(--color-text-secondary);
                  }
                }
                .cockpit-sub-step-status-bar {
                  position: relative;
                  .step-bar-time{
                    color: rgba(201, 201, 201, 1);
                    font-family: HarmonyOS Sans SC;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                    letter-spacing: 0px;
                    text-align: left;
                    position: absolute;
                    left: -15px;
                  }
                  div:nth-of-type(1).step-bar-not_start {
                    height: 0.75rem;
                    background: var(--color-tag-text-none);
                  }
                  div:nth-of-type(1).step-bar-running {
                    height: 0.75rem;
                    animation: loadStep 2s ease-in-out;
                    animation-fill-mode: forwards;
                    background: var(--brand-30);
                  }
                  div:nth-of-type(1).step-bar-success {
                    height: 0.75rem;
                    background: var(--color-tag-text-success);
                  }
                  div:nth-of-type(1).step-bar-failed {
                    height: 0.75rem;
                    background: var(--color-error);
                  }

                  /* 动画效果 */
                  @keyframes loadStep {
                    from {
                      width: 0;
                    }
                    to {
                      width: 80%;
                    }
                  }

                  div:nth-of-type(2) {
                    height: 0.375rem;
                  }
                }
              }

            }

            .cockpit-sub-step-bar-item-container:hover {
              background: linear-gradient(to left,
              rgba(var(--stepBarHoverBgColor-rgb), 0.05),
              rgba(var(--stepBarHoverBgColor-rgb), 0.25));
            }

            .cockpit-sub-step-bar-item-container.selected {
              background: linear-gradient(180.00deg, rgba(184, 217, 249, 0.15),rgba(184, 217, 249, 0) 100%),radial-gradient(56.00% 20.00% at 28.000000000000004% 0%,rgba(0, 78, 168, 0.2),rgba(0, 78, 168, 0) 100%),radial-gradient(45.00% 25.00% at 68% 110.00000000000001%,rgba(28, 148, 164, 0.1),rgba(28, 148, 164, 0) 100%),
              radial-gradient(29.00% 29.00% at 5% 0%,rgba(85, 49, 235, 0.1),rgba(85, 49, 235, 0) 100%);
            }

            .cockpit-sub-step-start-time {
              width: 5rem;
              margin-left: -2.5rem;
              text-align: center;
              font-size: 0.75rem;
              color: var(--color-text-secondary);
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .cockpit-step-bar-overview-sub-panel {
    position: absolute;
    bottom: calc(100% + 2rem);
    left: 50%;
    transform: translateX(-50%) translateZ(0);
    padding: 0.625rem;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    background: rgba(255, 255, 255, 0.01);
    backdrop-filter: blur(3rem);
    border-radius: 0.5rem;
    @supports (not (backdrop-filter: blur(3rem))) {
      & {
        filter: blur(3rem);
        -webkit-filter: blur(3rem);
      }
    }
    .cockpit-step-bar-overview-sub-panel-item {
      display: flex;
      flex-direction: column;
      gap: 0;
      width: 5rem;
      cursor: pointer;
      transition: width 0.25s linear, background-image 0.25s linear;
      img {
        width: 100%;
        height: 2.75rem;
        transition: height 0.25s linear;
      }
      span {
        display: inline-block;
        width: 100%;
        overflow-x: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 0.75rem;
        font-weight: 400;
        line-height: 1.25rem;
        color: rgba(255, 255, 255, 0.5);
        text-align: center;
      }
      &:hover,
      &.selected {
        width: 7.125rem;
        box-shadow: 0 0 1.5rem rgb(22, 139, 253, 0.5);
        border-radius: 0 0 0.25rem 0.25rem;
        img {
          height: 3.875rem;
          box-sizing: border-box;
          border: 1px solid transparent;
          border-bottom: none;
          border-radius: 0.25rem 0.25rem 0 0;
          background: linear-gradient(to top, #0067d1ff, #a4ecf1ff);
          background-origin: border-box;
        }
        span {
          color: #ffffff;
          background: -webkit-linear-gradient(to bottom,
          #0067d1 100%,
          #0067d1 50%);
          background: linear-gradient(to bottom, #0067d1 100%, #0067d1 50%);
          border-radius: 0 0 0.25rem 0.25rem;
          box-sizing: border-box;
          padding: 0 2px;
        }
      }
    }
  }
}

// 右上悬浮卡片
.cockpit-right-top-card {
  position: relative;
  z-index: 3;
  width: 28.25rem;
  height: auto;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  box-sizing: border-box;
  border-radius: 1rem;
  background-image:
    url("../assets/right_top_card_bg.png"),
    radial-gradient(445.00% 100.00% at 50% 0%,rgba(57, 57, 57, 1),rgba(57, 57, 57, 1) 11%,rgba(42, 42, 42, 1) 18%,rgba(42, 42, 42, 0.97) 23%,rgba(42, 42, 42, 1) 29%,rgba(42, 42, 42, 1) 37%,rgba(42, 42, 42, 1) 45%,rgba(42, 42, 42, 1) 51%,rgba(42, 42, 42, 1) 100%);
  background-position: center top;
  background-size: contain;
  background-repeat: no-repeat;
  background-origin: border-box; // 将border也应用上背景
  background-color: #2a2a2a;
  box-shadow: 0rem 1rem 3rem 0rem rgba(0, 0, 0, 0.5);

  font-size: 0.875rem;
  color: var(--color-text-primary);

  .cockpit-right-top-card-handler {
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-top: 3px solid #4d5359;
    border-right: 3px solid #4d5359;
    position: absolute;
    left: 50%;
    top: 1rem;
    transform: translateX(-50%) rotate(-45deg);
    cursor: pointer;

    &:hover {
      border-color: #0067d1;
    }
  }

  .cockpit-right-top-card-step-title {
    margin-bottom: 0.75rem;
    display: flex;
    gap: 1rem;
    align-items: center;

    div {
      flex-shrink: 0;
      width: 2.8125rem;
      height: 2.8125rem;
      background: linear-gradient(180.00deg, rgba(73, 81, 94, 1), rgba(67, 71, 79, 1) 100%), radial-gradient(48.00% 47.00% at 49% 6%, rgba(184, 194, 206, 1), rgba(63, 69, 77, 1) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.5rem;
      box-sizing: border-box;

      img {
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    p {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      span {
        overflow-x: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-weight: 500;
      }

      span:nth-of-type(1) {
        font-size: 1.25rem;
        color: var(--color-text-primary);
        line-height: 1.75rem;
      }

      span:nth-of-type(2) {
        font-size: 0.75rem;
        color: rgba(201, 201, 201, 1);
        line-height: 1.25rem;
      }
    }
  }

  .cockpit-right-top-card-step-content {
    .cockpit-right-top-card-step-content-title {
      display: flex;
      gap: 0.5rem;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      .cockpit-right-top-card-step-content-title-left {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        img {
          width: 1.25rem;
          height: 1.25rem;
          flex-shrink: 0;
        }

        p {
          font-size: 0.875rem;
          color: #ffffff;
          font-weight: 600;
          line-height: 1.375rem;
        }
      }

      .cockpit-right-top-card-step-content-title-right {
        cursor: pointer;
        flex-shrink: 0;
        display: flex;
        align-items: center; // 实现自身垂直居中
      }
    }

    .cockpit-right-top-card-step-content-detail {
      height: auto;
      max-height: calc(70vh - 4.75rem);
      overflow-y: auto;
      word-break: break-word;
    }

    // 感知阶段
    .cockpit-right-top-card-perception-container {
      .cockpit-right-top-card-perception-latest {
        border-radius: 0.75rem;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.5);
        background: rgba(89, 89, 89, 0.2);
        padding: 1rem;

        .cockpit-right-top-card-perception-latest-title {
          display: flex;
          align-items: center;
          img {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
          }
          span {
            font-size: 0.875rem;
            color: #fff;
            font-weight: 500;
          }
        }
        .cockpit-right-top-card-perception-latest-level-list {
          margin-top: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: space-around;
          gap: 0.75rem;
          .cockpit-right-top-card-perception-latest-level {
            display: flex;
            flex-direction: column;
            .severity-level {
              color: rgba(201, 201, 201, 0.75);
              font-size: 0.875rem;
              font-weight: 400;
              line-height: 1.375rem;
              &::before {
                display: inline-block;
                content: ' ';
                width: 0.5rem;
                height: 0.5rem;
                border-radius: 50%;
                background-color: var(--fillColor);
                margin-right: 0.25rem;
              }
            }
            .severity-count {
              color: rgba(255, 255, 255, 1);
              font-size: 1.25rem;
              font-weight: 500;
              line-height: 1.75rem;
            }
          }
        }
      }

      .cockpit-right-top-card-perception-block {
        margin-top: 0.875rem;
        box-sizing: border-box;
        border-radius: 0.75rem;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.08);
        background: linear-gradient(180.00deg, rgba(77, 81, 87, 0.5),rgba(57, 57, 57, 0.5) 50%);
        padding: 0.75rem 0;

        .cockpit-right-top-card-perception-block-title {
          color: rgba(255, 255, 255, 1);
          font-size: 0.875rem;
          font-weight: 500;
          line-height: 1.375rem;
          padding: 0 0.75rem;
        }

        .cockpit-right-top-card-perception-block-chart {
          position: relative;
          width: 100%;
          height: 11.25rem;
        }
      }
    }

    // 分析阶段
    .cockpit-right-top-card-analysis-container {
      position: relative;

      .cockpit-right-top-card-analysis-title {
        color: rgba(255, 255, 255, 1);
        font-size: 0.875rem;
        font-weight: 500;
        line-height: 1.375rem;
      }

      .cockpit-right-top-card-analysis-container-detail-blue {
        border: 1px solid rgba(46, 134, 222, 1);
      }

      .cockpit-right-top-card-analysis-container-detail-gray {
        border: 1px solid transparent;
      }

      .cockpit-right-top-card-analysis-container-detail {
        margin-top: 0.5rem;
        box-sizing: border-box;
        border-radius: 0.75rem;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.08);
        background: linear-gradient(180.00deg, rgba(77, 81, 87, 0.5),rgba(57, 57, 57, 0.5) 50%);
        position: relative;

        .ellipsis-text {
          display: inline-block;
          max-width: 160px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
        .cockpit-right-top-card-analysis-container-view-more{
          display: inline-flex;        /* 一行内布局 */
          align-items: center;         /* 垂直居中 */
          gap: 3px;                    /* 图标和文字之间的间距 */
          height: 14px;                /* 可根据需求调整 */
          padding-top: 3px;
          .cockpit-right-top-card-analysis-container-view-more-text{
            color: rgba(92, 162, 233, 1);
            font-family: HarmonyOS Sans SC;
            font-size: 12px;
            font-weight: 400;
            line-height: 14px;
            letter-spacing: 0px;
            text-align: center;
            cursor: pointer;
          }
        }

        .cockpit-right-top-card-analysis-container-relative {
          position: absolute;
          right: 1rem;
          top: 50%;
          transform: translateY(-50%);

          .cockpit-right-top-card-analysis-container-percent {
            height: 24px;
            color: rgba(255, 255, 255, 1);
            font-family: HarmonyOS Sans SC;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: center;
          }

          .cockpit-right-top-card-analysis-container-relative-number {
            /* 容器 1557  */
            height: 18px;
            /* 自动布局 */
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0rem 1rem 0rem 1rem;
            border-radius: 9px;
            background: rgba(243, 243, 243, 0.1);
          }
        }

        .cockpit-right-top-card-analysis-container-detail-warn {
          /* 容器 8205  */
          width: auto;
          height: 1rem;
          box-sizing: border-box;
          /* 自动布局 */
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          gap: 0.25rem;
          padding: 2px 0.375rem;
          border-radius: 0.5rem;
          background: rgba(238, 105, 111, 0.15);
        }

        .cockpit-right-top-card-analysis-container-detail-warn-number {
          font-family: HarmonyHeiTi;
          font-size: 0.625rem;
          font-weight: 400;
        }
        .cockpit-right-top-card-analysis-container-detail-warn-color-1{
          color: #E7434A;
        }
        .cockpit-right-top-card-analysis-container-detail-warn-color-2{
          color: #F4840C;
        }
        .cockpit-right-top-card-analysis-container-detail-warn-color-3{
          color: #FCC800;
        }
        .cockpit-right-top-card-analysis-container-detail-warn-color-4{
          color: #5CA2E9;
        }

        .cockpit-right-top-card-analysis-container-detail-col {
          font-family: HarmonyOS Sans SC;
          font-size: 10px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0px;
          text-align: left;
          color: #c9c9c9;
        }
        .cockpit-right-top-card-analysis-container-relative-words{
          color: rgba(201, 201, 201, 1);
          font-family: HarmonyOS Sans SC;
          font-size: 10px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0px;
          text-align: left;
        }
      }

    }

    // 诊断阶段
    .cockpit-right-top-card-diagnose-container {
      position: relative;

      .cockpit-right-top-card-diagnose-container-item-text {
        width: 75%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: bottom;
        margin-right: 5px;
      }
      .cockpit-right-top-card-diagnose-container-block {
        border-radius: 0.75rem;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.5);
        background: rgba(89, 89, 89, 0.2);
        padding: 1rem;
        color: rgba(201, 201, 201, 0.75);
      }
      .cockpit-right-top-card-diagnose-container-item-12 {
        margin-bottom: 0.75rem;
      }
      .cockpit-right-top-card-diagnose-container-item-8 {
        margin-bottom: 0.5rem;
      }
      .text-indent {
        padding-left: 1.25rem;
      }
      .text-font-size-12 {
        font-size: 12px;
        color:#F5F5F5;
        margin-right: 5px;
        margin-top: 0.5rem;
      }
      .text-blue {
        color: #186fc2;
        cursor: pointer;
      }
    }
  }
}
.case-details-dialog-container{
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  .case-details-title{
    margin-bottom: 1rem;
    font-size: 18px;
    font-weight: 600;
    color: var(#bcbecc, #333333);
  }
  .case-details-dialog-content {
    white-space: pre-wrap;
    width: 100%;
    word-break: break-word;
    cursor: pointer;
    color: #186fc2;
  }
  .case-details-content {
    white-space: pre-wrap;
    width: 100%;
    word-break: break-word;
  }

}

// 左上悬浮卡片
.cockpit-left-top-card {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  width: fit-content;
  max-width: 30rem;
  height: 4.5rem;
  padding: 0.2rem 1rem;
  box-sizing: border-box;
  background-color: var(--color-bg-3);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  border: 1px solid #f3f3f326;
  font-size: 0.875rem;
  color: var(--color-text-primary);

  .cockpit-left-top-card-back {
    margin-top: 2px;
    cursor: pointer;
  }

  .cockpit-left-top-card-icon {
    width: 2rem;
    height: 2rem;
  }

  .cockpit-left-top-card-title {
    flex-shrink: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    span:nth-child(1) {
      font-size: 1rem;
      font-weight: 600;
      line-height: 1.5rem;
      overflow-x: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      background-image: linear-gradient(to right,
      #eff3fa 0%,
      #b3cfff 50%,
      #8cb6ff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 100% auto;
    }

    span:nth-child(2) {
      font-size: 0.75rem;
      line-height: 1.25rem;
      color: #ffffff;
    }
  }
}

// 分析过程中检索的案例
.cockpit-incident-retrieved-cases {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;

  .cockpit-incident-no-retrieved-cases {
    text-align: center;
    color: #f5f5f5;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
    display: flex;
    align-items: center;
  }

  .cockpit-incident-retrieved-case-item {
    width: 23rem;
    height: 13rem;
    padding: 1.75rem 1rem 1rem 1rem;
    box-sizing: border-box;
    overflow: hidden;
    background-image: url("../assets/case_bg.png");
    background-repeat: no-repeat;
    background-size: contain;

    .cockpit-incident-retrieved-case-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1.5rem;

      span:nth-child(1) {
        flex-shrink: 1;
        overflow-x: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #f5f5f5;
        font-size: 0.875rem;
        font-weight: 600;
        line-height: 1.375rem;
      }

      span:nth-child(2) {
        flex-shrink: 0;
        border-radius: 0.25rem;
        background: #aeaeae26;
        padding: 0 0.5rem;
        font-size: 0.75rem;
        color: #aeaeae;
        font-weight: 400;
        line-height: 1.25rem;
      }
    }

    .cockpit-incident-retrieved-case-item-abstract {
      margin-top: 1rem;
      margin-bottom: 0.75rem;
      border-bottom: 1px dashed #595959;
      height: calc(100% - 4rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      :global(.markdown-body) {
        background-color: transparent;
        height: calc(100% - 1rem);
        overflow: hidden;

        * {
          font-size: 12px;
          color: #939393;
          font-weight: 400;
          line-height: 1.25rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin: 0;
          border: none;
        }
      }
    }

    .cockpit-incident-retrieved-case-item-viewAll {
      text-align: right;

      :global(.eui-textButton) {
        font-size: 0.75rem;
        font-weight: 500;
      }
    }
  }
}

.cockpit-incident-retrieved-case-item-full-text {
  :global(.markdown-body) {
    background: #2a2a2a;
    border-radius: 0.5rem;
    padding: 2%;
    box-sizing: border-box;
    min-height: 100%;

    * {
      color: #c9c9c9;
    }
  }
}

// 页面中间的错误提示
.cockpit-incident-central-error-tip {
  margin-bottom: 0.5rem;
  width: auto;
  max-width: 12.5rem;
  height: auto;
  box-sizing: border-box;
  padding: 0.75rem 1.25rem;
  // 在绘制时，图像以 z 方向堆叠的方式进行。先指定的图像会在之后指定的图像上面绘制。因此指定的第一个图像“最接近用户”。
  background-image: url('../assets/smart_cockpit_topology_tip_bg.png'),
  linear-gradient(to bottom, #cd08081e 0%, transparent 100%),
  linear-gradient(to bottom, #393939, #363434ed),
  radial-gradient(ellipse at 100% 0%, #f43146d6 0%, transparent 50%),
  radial-gradient(ellipse at 0% 0%, #ff6a3baa 0%, transparent 50%),
  linear-gradient(to left, #FFFFFF1A 0%, #FFFFFF80 47%, #FFFFFF1A 100%);
  background-position: left top;
  background-size: cover;
  background-origin: border-box;
  background-clip: border-box, border-box, padding-box, padding-box, padding-box, border-box; // 第一个和最后一个背景才应用到边框上
  background-color: #2a2a2a;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  box-shadow: rgba(0, 0, 0, 0.16) 0 0.25rem 0.75rem 0.375rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;

  p {
    color: #fff;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    white-space: pre-wrap;
    text-align: left;
    width: 100%;
  }
}

// 菜单栏上的灵动岛
.smart-cockpit-reindeer-island {
  position: relative;
  top: 1rem;
  height: 4rem;
  line-height: 3.85rem;
  display: inline-block;
  overflow: hidden;
  margin-left: 1rem;

  > div {
    @keyframes borderFlash {
      0% {
        border-color: transparent;
      }
      50% {
        border-color: var(--color-info-primary-subtle);
      }
      100% {
        border-color: transparent;
      }
    }
    box-sizing: border-box;
    width: auto;
    max-width: 10.5rem;
    overflow: hidden;
    height: 2rem;
    background-color: var(--color-fill);
    border: 1px solid transparent;
    animation: borderFlash 0.8s ease-in-out 2 forwards;
    border-radius: 1rem;
    box-shadow: none;
    cursor: pointer;
    padding: 0 0.7rem 0 0.5rem;
    line-height: 1.875rem;
    color: var(--color-text-secondary);
    display: flex;
    gap: 0.2rem;

    &:hover {
      background-color: var(--color-fill-1);
    }

    img {
      width: 1.5rem;
      flex-shrink: 0;
    }

    span {
      overflow-x: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

// 需要收起成灵动岛的div
.smart-cockpit-reindeer-island-origin-card {
  // 收起或展开的动画
  transform: translate(0, 0);
  transition: all 0.5s cubic-bezier(0.39, 0.58, 0.57, 1) !important;

  &.smart-cockpit-reindeer-island-origin-card-hidden {
    transform: translate(70%, -130%);
  }
}

