import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Loader } from 'eview-ui';
import { createTimingTask, goToNewPage } from '@digitalview/fe-utils';

import { $t, registerResource } from '@util';
import downWhiteIcon from '@src/apps/businesstopo/assets/ic_chevron_up_lined_white.svg';
import upWhiteIcon from '@src/apps/businesstopo/assets/ic_chevron_down_lined_white.svg';
import { E_AggrType, E_DiagnosticStatus, E_IncidentStatus, E_Priority, E_SOLUTION_NAME, I_Incident, queryIncidentList } from '../../apis/incident';
import criticalIcon from '../../assets/ic_public_alert2_textured_border.png';
import highIcon from '../../assets/ic_public_alert_textured_border.png';
import mediumIcon from '../../assets/ic_public_warning_textured_border.png';
import lowIcon from '../../assets/ic_public_infotex_tured_border.png';
import { aicoLogo } from '../../assets/const';
import { DETAIL_PAGE_PATH, DIAGNOSTIC_STATUS_COLOR_MAP, DIAGNOSTIC_STATUS_INTL } from '../../consts';
import i18nResource from '../../locales';
import TableEmptyNode from '../IncidentList/TableEmptyNode';
import IncidentList from '../IncidentList';
import styles from './style.module.less';

const PRIORITY_ICON_MAP = {
  [E_Priority.CRITICAL]: criticalIcon,
  [E_Priority.HIGH]: highIcon,
  [E_Priority.MEDIUM]: mediumIcon,
  [E_Priority.LOW]: lowIcon,
};

interface I_TopNListProps {
  solutionName: E_SOLUTION_NAME;
}

/**
 * topN 智能事件
 */
const TopNList: React.FC<I_TopNListProps> = props => {
  registerResource(i18nResource, 'smartIncident');

  const { solutionName } = props;

  const [loading, setLoading] = useState(true);
  const [topNIncidents, setTopNIncidents] = useState<I_Incident[]>([]);

  const [showList, setShowList] = useState(false);
  const [showTopNList, setShowTopNList] = useState(true);

  let timingTaskRef = useRef<{ terminate: () => void }>();

  useEffect(() => {
    function refreshTopN(): void {
      if (!solutionName) {
        return;
      }
      queryIncidentList({
        solutionName,
        statusList: [E_IncidentStatus.INIT, E_IncidentStatus.OPENED],
        incidentType: E_AggrType.NORMAL,
        paging: {
          pageSize: 3,
          pageNumber: 1,
        },
      })
        .then(result => {
          setTopNIncidents(result.objects.slice(0, 3));
        })
        .finally(() => {
          setLoading(false);
        });
    }

    timingTaskRef.current = createTimingTask(refreshTopN, 60000, true);
    return () => {
      timingTaskRef.current?.terminate();
    };
  }, [solutionName]);

  useEffect(() => {
    return () => {
      timingTaskRef.current?.terminate();
    };
  }, []);

  return (
    <div className={styles.topNIncidentsContainer}>
      <Loader type='local' isOpen={loading} />
      <div className={styles.collapsableTab}>
        <div className={styles.collapsableTitle}>
          <img src={aicoLogo} />
          <span>{$t('smart.incident.topn.title')}</span>
        </div>
        <img
          src={showTopNList ? downWhiteIcon : upWhiteIcon}
          onClick={() => setShowTopNList(prev => !prev)}
          className={styles.collapsableIcon}
        />
      </div>
      <div className={styles.content} style={showTopNList ? undefined : { display: 'none' }}>
        {
          topNIncidents.map(item => (
            <div
              key={item.csn}
              className={styles.incidentCard}
              onClick={() => {
                // 跳转到故障详情的感知步骤
                const detailPage = `${location.origin}/eviewwebsite/index.html#path=${DETAIL_PAGE_PATH}`;
                goToNewPage(`${detailPage}&solutionName=${solutionName}&incidentId=${item.csn}`, true);
              }}
            >
              <div className={styles.incidentHeader}>
                <p className={styles.incidentHeaderLeft}>
                  <img src={PRIORITY_ICON_MAP[item.priority]} style={{ width: '1.25rem', height: '1.25rem' }} />
                  <span className={styles.incidentName} title={item.name}>{item.name}</span>
                </p>
                <p className={styles.incidentHeaderRight}>
                  <span
                    className={styles.incidentDiagnoseStatusCircle}
                    style={{ background: DIAGNOSTIC_STATUS_COLOR_MAP[item.diagnosisStatus] ?? DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PENDING] }}
                  />
                  <span className={styles.incidentDiagnoseStatusIntl}>
                    {DIAGNOSTIC_STATUS_INTL[item.diagnosisStatus] ?? DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PENDING]}
                  </span>
                </p>
              </div>
              <div className={styles.incidentBody} title={item.description}>
                { item.description ? item.description : $t('smart.incident.topn.abstract.to.be.analysed') }
              </div>
            </div>
          ))
        }
        {
          !loading && topNIncidents.length === 0 &&
          <TableEmptyNode />
        }
        {
          !loading &&
          <div className={styles.viewAll}>
            <TextButton text={$t('smart.incident.topn.viewAll')} onClick={() => setShowList(true)} />
          </div>
        }
      </div>
      { showList && <IncidentList solutionName={solutionName} hideDrawer={() => setShowList(false)} /> }
    </div>
  );
};

export default TopNList;
