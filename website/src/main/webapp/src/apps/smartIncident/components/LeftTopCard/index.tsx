import React, { useContext } from 'react';
import { goToNewPage } from '@digitalview/fe-utils';

import { $t } from '@util';
import LeftArrow from '../../assets/arrow_lined.svg';
import leftTopIcon from '../../assets/left_top_icon.png';
import { viewInformation } from '../../assets/const';

import { GlobalContext, Theme } from '../../utils/globalState';
import { I_IncidentDetail4UI } from '../../interface';
import styles from '../page.module.less';

interface I_LeftTopCardProps {
  incidentDetail: I_IncidentDetail4UI;
  fromCockpit: boolean;
}

/**
 * 故障详情-左上角悬浮卡片
 */
const LeftTopCard: React.FC<I_LeftTopCardProps> = props => {
  const { theme } = useContext(GlobalContext);

  const { incidentDetail, fromCockpit } = props;

  return (
    <div className={styles['cockpit-left-top-card']}>
      <div
        className={styles['cockpit-left-top-card-back']}
        onClick={() => {
          fromCockpit ?
            goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/dvaiagentwebsite/smartCockpit/overview`) :
            goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/businesstopo`);
        }}
      >
        <LeftArrow color={theme === Theme.evening ? '#F5F5F5' : '#191919'} width={16} height={16} />
      </div>
      <img src={leftTopIcon} className={styles['cockpit-left-top-card-icon']} />
      <div className={styles['cockpit-left-top-card-title']} title={incidentDetail.name}>
        <span>{ incidentDetail.name }</span>
        { fromCockpit && <span>{ $t('smart.incident.detail.left.top.category') }</span> }
      </div>
      <img src={viewInformation} className={styles['cockpit-left-top-card-icon']} />
    </div>
  );
};

export default LeftTopCard;
