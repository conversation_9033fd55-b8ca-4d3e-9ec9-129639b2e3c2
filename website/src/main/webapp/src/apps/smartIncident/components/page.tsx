import React, { useCallback, useEffect, useState } from 'react';
import { Loader } from 'eview-ui';
import { goToNewPage } from '@digitalview/fe-utils';

import { modal, $t, registerResource } from '@util';

import StepsBar from './StepsBar';
import RightTopCard from './RightTopCard';
import LeftTopCard from './LeftTopCard';
import CentralPart from './CentralPart';

import {E_SOLUTION_NAME, queryAggregateStatics, queryAppRelation, queryIncidentByCsn} from '../apis/incident';
import { genIncidentData4UI } from '../utils/pageUtils';
import { E_DIAGNOSE_STEPS, I_IncidentDetail4UI, I_Step } from '../interface';
import i18nText from '../locales';
import styles from './page.module.less';

interface I_DetailPage_Props {
  solutionName: E_SOLUTION_NAME;
  incidentId: string;
  fromCockpit: boolean; // 是否由驾舱进入
  defaultShowStep?: E_DIAGNOSE_STEPS;
}

/**
 * 智能 Incident 回溯页面
 * 
 * - 需要判断是否有大模型
 *   1. 无大模型场景，Incident 分析阶段没有推荐案例、决策阶段右侧面板内容为小模型的知识推荐、没有 “案例归档” 和 “接管” 的能力。
 *   2. 有大模型场景，Incident 分析阶段有推荐案例、决策阶段右侧面板内容为大模型诊断报告，Incident可接管、可归档案例。
 * - 泄洪 Incident 没有详情页
 */
const DetailPage: React.FC<I_DetailPage_Props> = props => {
  registerResource(i18nText, 'smartIncident');

  const { solutionName, incidentId, defaultShowStep, fromCockpit } = props;
  const [incidentDetail, setIncidentDetail] = useState<I_IncidentDetail4UI>();
  const [pageLoading, setPageLoading] = useState(true);
  // 记录当前选择的诊断步骤
  const [currentStep, setCurrentStep] = useState<undefined | I_Step & { selectedSubStep?: number }>();

  // 刷新中间区域数据
  const getData4CurrentStep = useCallback(async(isInit: boolean, currentStage: E_DIAGNOSE_STEPS, subStepIndex?: number) => {
    if (!isInit) {
      return;
    }
    try {
      setPageLoading(true);
      // 待完成：待处理子步骤
      const [incidentData, incidentStatistics, relationresult] = await Promise.all([
        queryIncidentByCsn(Number(incidentId), solutionName, currentStage),
        queryAggregateStatics(Number(incidentId), solutionName),
        queryAppRelation(solutionName),
      ]);
      if (!incidentData) {
        modal.error(
          $t('smart.incident.detail.queryFailed'),
          () => {
            fromCockpit ?
              goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/dvaiagentwebsite/smartCockpit/overview`) :
              goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/businesstopo`);
          },
          undefined,
          () => {
            fromCockpit ?
              goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/dvaiagentwebsite/smartCockpit/overview`) :
              goToNewPage(`${location.origin}/eviewwebsite/index.html#path=/businesstopo`);
          },
        );
        return;
      }
      const incidentData4UI = genIncidentData4UI(incidentData, incidentStatistics, relationresult, fromCockpit);
      const currentStepInfo = incidentData4UI.steps.find(step => step.stage === currentStage) ?? incidentData4UI.steps[0];
      setIncidentDetail(incidentData4UI);
      if (isInit) {
        setCurrentStep({ ...currentStepInfo, selectedSubStep: subStepIndex });
      }
    } catch {
      // ..
    } finally {
      setPageLoading(false);
    }
  }, [incidentId, solutionName, fromCockpit]);

  // 打开页面时
  useEffect(() => {
    // 设置默认展示的诊断步骤，如果未指定，默认展示感知步骤（感知步骤一定会有）
    const currentStage = defaultShowStep ?? E_DIAGNOSE_STEPS.PERCEPTION;
    getData4CurrentStep(true, currentStage);
  }, []);

  return (
    <div className={styles.detailsPage}>
      {/* 整个页面的loading */}
      <Loader type='local' isOpen={pageLoading} desc='' />
      {
        (incidentDetail !== undefined && currentStep !== undefined) &&
        <>
          {/* 中心区域 */}
          <CentralPart
            incidentDetail={incidentDetail}
            currentStep={currentStep}
            fromCockpit={fromCockpit}
          />
          {/* 左上角卡片 返回故障首页 */}
          <div className={styles.leftTopCardContainer}>
            <LeftTopCard
              incidentDetail={incidentDetail}
              fromCockpit={fromCockpit}
            />
          </div>
          {/* 右上角卡片，随下方诊断步骤的切换展示不同的诊断内容 */}
          <div className={styles.rightTopCardContainer}>
            <RightTopCard
              incidentDetail={incidentDetail}
              currentStep={currentStep}
              fromCockpit={fromCockpit}
            />
          </div>
          {/* 诊断步骤条 */}
          <div className={styles.disgnoseStepsContainer}>
            <StepsBar
              incidentDetail={incidentDetail}
              currentStep={currentStep}
              onStepClick={(stepInfo, subStepIndex) => {
                if (stepInfo.stage === currentStep.stage && subStepIndex === currentStep.selectedSubStep) {
                  return;
                }
                setCurrentStep({ ...stepInfo, selectedSubStep: subStepIndex }); // 刷新步骤条
                getData4CurrentStep(false, stepInfo.stage, subStepIndex); // 刷新中间数据
              }}
            />
          </div>
        </>
      }
    </div>
  );
};

export default DetailPage;
