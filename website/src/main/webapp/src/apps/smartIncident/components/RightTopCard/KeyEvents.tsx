import React from 'react';
import {Col, Row, Tooltip} from 'eview-ui';
import {I_IncidentDetail4UI} from '../../interface';
import styles from '../page.module.less';
import UrgentIcon from '@src/apps/smartIncident/assets/ic_public_urgent.svg';
import ImportantIcon from '@src/apps/smartIncident/assets/ic_public_important.svg';
import MinorIcon from '@src/apps/smartIncident/assets/ic_public_minor.svg';
import TipsIcon from '@src/apps/smartIncident/assets/ic_public_tips.svg';
import CircleUrgentIcon from '@src/apps/smartIncident/assets/ic_ict_management_urgent.svg';
import CircleImportantIcon from '@src/apps/smartIncident/assets/ic_ict_management_important.svg';
import CircleMinorIcon from '@src/apps/smartIncident/assets/ic_ict_management_minor.svg';
import CircleTipsIcon from '@src/apps/smartIncident/assets/ic_ict_management_tips.svg';
import FileIcon from '@src/apps/smartIncident/assets/ic_public_files.svg';
import {E_Severity, queryMoreAlarmData} from '@src/apps/smartIncident/apis/incident';

import {$t} from '@util';

interface I_DiagnosisReportProps {
    incidentDetail: I_IncidentDetail4UI;
}

/**
 * 故障详情-右上角悬浮卡片-分析阶段
 */
const KeyEvents: React.FC<I_DiagnosisReportProps> = props => {
  const {incidentDetail} = props;
  const alarmIconMap = {
    [E_Severity.URGENT]: <UrgentIcon />,
    [E_Severity.IMPORTANT]: <ImportantIcon />,
    [E_Severity.MINOR]: <MinorIcon />,
    [E_Severity.TIPS]: <TipsIcon />,
  };
  const circleIconMap = {
    [E_Severity.URGENT]: <CircleUrgentIcon />,
    [E_Severity.IMPORTANT]: <CircleImportantIcon />,
    [E_Severity.MINOR]: <CircleMinorIcon />,
    [E_Severity.TIPS]: <CircleTipsIcon />,
  };

  const viewMore = (item):void => {
    let targetAlarmIdList = item.targetAlarmIdList;
    let alarmCsn = incidentDetail.eventList.filter(alarmItem => targetAlarmIdList.includes(alarmItem.alarmId)).map(filterItem => filterItem.alarmCsn);
    queryMoreAlarmData(alarmCsn.join(',')).then(data => {
      if (!data) {
        return;
      }
      let newId = encodeURIComponent(data.toString());
      let win = window.open(`/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&conditionId=${newId}&isShowFilter=true`);
      if (win) {
        win.opener = null;
      }
    });
  };

  return (
    <div className={styles['cockpit-right-top-card-analysis-container']}>
      <p className={styles['cockpit-right-top-card-analysis-title']}>
        {$t('smart.incident.detail.steps.analysis.topn.key.events')}
      </p>
      {
        incidentDetail.keyEventList?.map((item, index) => (
          <div
            className={[styles['cockpit-right-top-card-analysis-container-detail'], styles['cockpit-right-top-card-analysis-container-detail-gray']].join(' ')}
            key={index}
          >
            <Row style={{ margin: '1rem 1rem 0 1rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              {alarmIconMap[item.severity]}
              <Tooltip placement="top" content={item.name}
                trigger={['hover', 'focus']}
              >
                <span className={styles['ellipsis-text']}>{item.name}</span>
              </Tooltip>
              <div className={styles['cockpit-right-top-card-analysis-container-detail-warn']}>
                {circleIconMap[item.severity]}
                <span
                  className={[styles['cockpit-right-top-card-analysis-container-detail-warn-number'], styles[`cockpit-right-top-card-analysis-container-detail-warn-color-${item.severity}`]].join(' ')}
                >
                  {
                    (item.targetAlarmIdList || []).length
                  }
                </span>
              </div>
            </Row>
            <Row style={{marginTop: '1rem', marginLeft: '0.5rem', paddingBottom: '0.5rem'}}
              className={styles['cockpit-right-top-card-analysis-container-detail-col']}
            >
              <Col cols={8}>
                <span style={{marginRight: '0.5rem'}}>ID:</span>
                <span>{item.alarmId}</span>
              </Col>
              <Col cols={16}>
                <div
                  className={styles['cockpit-right-top-card-analysis-container-view-more']}
                  onClick={() => {
                    viewMore(item);
                  }}
                >
                  <FileIcon />
                  <span className={styles['cockpit-right-top-card-analysis-container-view-more-text']}>{$t('smart.incident.view.more')}</span>
                </div>
              </Col>
            </Row>
            <div className={styles['cockpit-right-top-card-analysis-container-relative']}>
              <div className={styles['cockpit-right-top-card-analysis-container-percent']}>{item.score}%
              </div>
              <div
                className={styles['cockpit-right-top-card-analysis-container-relative-number']}
              >
                <div style={{
                  width: '30px',
                  height: '21px',
                  transform: 'rotate(0.39deg)',
                }}
                >
                  <span
                    className={styles['cockpit-right-top-card-analysis-container-relative-words']}
                  >{$t('smart.incident.association')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))
      }
    </div>
  );
};

export default KeyEvents;
