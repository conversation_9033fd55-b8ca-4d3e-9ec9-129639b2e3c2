import React, {useEffect, useState} from 'react';
import {I_IncidentDetail4UI} from '../../interface';
import styles from '../page.module.less';
import {Dialog, Tooltip} from 'eview-ui';
import {$t} from '@util';
import {queryCaseKnowledgeDetails} from '@src/apps/smartIncident/apis/incident';

interface I_DiagnosisReportProps {
    incidentDetail: I_IncidentDetail4UI;
}

/**
 * 故障详情-右上角悬浮卡片-诊断阶段
 */

const DiagnosisReport: React.FC<I_DiagnosisReportProps> = props => {
  const {incidentDetail} = props;
  const [caseDialog, setCaseDialog] = useState({
    isShow: false,
    caseId: 0,
    caseModele: '',
  });
  const [caseKnowledgeValue, setCaseKnowledgeValue] = useState({
    diagnosisAbstract: '',
    generalRecommendation: [],
    caseRecommendation: [],
  });
  useEffect(() => {
    queryCaseKnowledgeDetails({
      solutionName: incidentDetail.name,
      incidentCsn: incidentDetail.csn,
    }).then(result => {
      const data = {
        diagnosisAbstract: result.diagnosisAbstract,
        generalRecommendation: JSON.parse(result.generalRecommendation),
        caseRecommendation: JSON.parse(result.caseRecommendation),
      };
      setCaseKnowledgeValue(data);
    });
  }, [incidentDetail]);

  // 弹窗查看案例详情组件
  function CaseDetailsDialog({caseId, caseModele, caseKnowledge}):React.ReactNode {
    const [detailsInfoArr, setDetailsInfoArr] = useState([]);

    useEffect(() => {
      if (caseModele === 'case') {
        let caseRecommendation = caseKnowledge.caseRecommendation[caseId];
        setDetailsInfoArr([
          {title: $t('case.knowledge.table.column.caseId'), content: caseRecommendation.caseId},
          {title: $t('case.knowledge.table.column.solution'), content: caseRecommendation.solution},
          {title: $t('case.knowledge.table.column.name'), content: caseRecommendation.title},
          {title: $t('case.knowledge.table.column.faultSymptom'), content: caseRecommendation.keyword},
          {title: $t('case.knowledge.table.column.cause'), content: caseRecommendation.cause},
          {title: $t('case.knowledge.view.influence'), content: caseRecommendation.effect},
          {title: $t('case.knowledge.view.recoveryMeasures'), content: caseRecommendation.repair},
        ]);
      } else {
        let generalRecommendation = caseKnowledge.generalRecommendation[caseId];
        setDetailsInfoArr([
          {title: $t('event.knowledge.table.column.name'), content: generalRecommendation.name},
          {title: $t('event.knowledge.table.column.cause'), content: generalRecommendation.cause},
          {title: $t('event.knowledge.table.column.suggestions'), content: generalRecommendation.recoverySuggestion},
          {title: $t('event.knowledge.table.column.desc'), content: generalRecommendation.description},
        ]);
      }
    }, [caseId, caseModele, caseKnowledge]);

    const jumpToCaseKnowledge = (index:number):void => {
      if (index !== 2) {
        return;
      }
      let origin = window.location.origin;
      let pathname = window.location.pathname;

      // 深色主题跳转时需求改变html
      if (document.querySelector('[data-theme=dark]')) {
        pathname = pathname.replace(/associationAnalysisPage\.html$/, 'index.html');
      }

      let win = window.open(`${origin}${pathname}#path=/aiOpsService&subMenu=caseKnowledge?caseId=${caseId}`);
      if (win) {
        win.opener = null;
      }
    };

    return (
      <div className={styles['case-details-dialog-container']}>
        {detailsInfoArr.map((item, index) => (
          <div key={index} style={{marginBottom: '1.5rem', width: '100%'}}>
            <div className={styles['case-details-title']}>{item.title}</div>
            <div
              className={(index === 2 && caseModele === 'case') ? styles['case-details-dialog-content'] : styles['case-details-content']}
              onClick={() => jumpToCaseKnowledge(index)}
            >
              {item.content}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={styles['cockpit-right-top-card-diagnose-container']}>
      <div
        className={`
          ${styles['cockpit-right-top-card-diagnose-container-item-12']}
          ${styles['cockpit-right-top-card-diagnose-container-block']}
        `}
      >
        <div style={{fontSize: '0.875rem', color: '#fff', marginBottom: '0.5rem'}}>
          {$t('intelligent.recommendation.common.queryInfo')}
        </div>
        <div>
          {caseKnowledgeValue.diagnosisAbstract}
        </div>
      </div>

      <div
        className={`
          ${styles['cockpit-right-top-card-diagnose-container-item-12']}
          ${styles['cockpit-right-top-card-diagnose-container-block']}
        `}
      >
        <div style={{fontSize: '0.875rem', color: '#fff', marginBottom: '0.5rem'}}>
          {$t('intelligent.recommendation.common.suggestion')}
        </div>
        <div style={{color: '#c9c9c9'}}>
          {
            caseKnowledgeValue.generalRecommendation.map((item, index) => (
              <>
                <div className={[styles['text-font-size-12']].join(' ')}>
                  <span
                    className={styles['text-font-size-12']}
                  >{$t('event.knowledge.table.column.name')}{index + 1}:
                  </span>
                  <Tooltip content={item.name} trigger={['hover', 'focus']}>
                    <span
                      className={[styles['text-blue'], styles['cockpit-right-top-card-diagnose-container-item-text']].join(' ')}
                      onClick={() => {
                        setCaseDialog({
                          caseId: index,
                          isShow: true,
                          caseModele: 'general',
                        });
                      }}
                    >
                      {item.name}
                    </span>
                  </Tooltip>
                </div>
                <div className={[styles['text-indent'], styles['text-font-size-12']].join(' ')}>
                  <span className={styles['text-font-size-12']}>{$t('intelligent.recommendation.cause')}</span>
                  <Tooltip content={item.cause} trigger={['hover', 'focus']}>
                    <span
                      className={styles['cockpit-right-top-card-diagnose-container-item-text']}
                    >
                      {item.cause}
                    </span>
                  </Tooltip>
                </div>
              </>
            ))
          }
        </div>
      </div>

      <div
        className={styles['cockpit-right-top-card-diagnose-container-block']}
      >
        <div style={{fontSize: '0.875rem', color: '#fff', marginBottom: '0.5rem'}}>
          {$t('intelligent.recommendation.common.case')}
        </div>
        <div>
          {
            caseKnowledgeValue.caseRecommendation.map((item, index) => (
              <>
                <div className={styles['cockpit-right-top-card-diagnose-container-item-8']}>
                  <span className={styles['text-font-size-12']}> {$t('intelligent.recommendation.case')}{index + 1}:</span>
                  <Tooltip content={item.title} trigger={['hover', 'focus']}>
                    <span
                      className={[styles['text-blue'], styles['cockpit-right-top-card-diagnose-container-item-text']].join(' ')}
                      onClick={() => {
                        setCaseDialog({
                          caseId: index,
                          isShow: true,
                          caseModele: 'case',
                        });
                      }}
                    >
                      {
                        item.title
                      }
                    </span>
                  </Tooltip>
                </div>
                <div className={[styles['text-indent'], styles['text-font-size-12']].join(' ')}>
                  <div className={styles['cockpit-right-top-card-diagnose-container-item-8']}>
                    <span
                      className={styles['text-font-size-12']}
                    >{$t('intelligent.recommendation.cause')}
                    </span>
                    <Tooltip content={item.cause} trigger={['hover', 'focus']}>
                      <span
                        className={styles['cockpit-right-top-card-diagnose-container-item-text']}
                      >
                        {item.cause}
                      </span>
                    </Tooltip>
                  </div>
                  <div className={styles['cockpit-right-top-card-diagnose-container-item-8']}>
                    <span className={styles['text-font-size-12']}>{$t('intelligent.recommendation.suggestion')}</span>
                    <Tooltip content={item.cause} trigger={['hover', 'focus']}>
                      <span className={styles['cockpit-right-top-card-diagnose-container-item-text']}>
                        {item.repair}
                      </span>
                    </Tooltip>
                  </div>
                </div>
              </>
            ))
          }
        </div>
      </div>

      <Dialog
        title={caseDialog.caseModele === 'case' ? $t('case.knowledge.view.title') : $t('case.knowledge.event.title')}
        zIndex={99999}
        modal={true}
        closeOnEscape={false}
        isOpen={caseDialog.isShow}
        onClose={() => setCaseDialog({
          ...caseDialog,
          isShow: false,
        })}
        style={{
          height: caseDialog.caseModele === 'case' ? '45rem' : '40rem',
          width: '40rem',
        }}
      >
        <CaseDetailsDialog caseId={caseDialog.caseId} caseModele={caseDialog.caseModele}
          caseKnowledge={caseKnowledgeValue}
        />
      </Dialog>
    </div>
  );
};


export default DiagnosisReport;
