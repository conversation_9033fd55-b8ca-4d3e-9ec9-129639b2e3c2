import type { MutableRefObject } from 'react';
import { I_Step, E_DIAGNOSE_STEPS, I_IncidentDetail4UI } from '../../interface';

export interface I_StepsBarProps {
  incidentDetail: I_IncidentDetail4UI;
  currentStep: I_Step;
  /**
   * 选择某个诊断步骤的回调
   * @param stepInfo 选中的诊断步骤数据
   * @param subStepIndex 选中的诊断子步骤（如果没有就不传）
   */
  onStepClick: (stepInfo: I_Step, subStepIndex?: number) => void;
}

export interface I_StepBarItemProps {
  /** 需要展示的步骤。未完成的步骤不展示。 */
  displaySteps: Set<E_DIAGNOSE_STEPS>;
  /** 是否自动播放 */
  isAutoPlay: boolean;
  /** 步骤自身的信息 */
  selfStep: I_Step;
  /** 是否被选中 */
  isSelected: boolean;
  /** 点击步骤的回调 */
  onStepClick: (stepInfo: I_Step, subStepIndex?: number) => void;
  /** 步骤条容器ref */
  portalRef: MutableRefObject<HTMLDivElement>;
  /** 是否展开*/
  stepsBarStatus: boolean;
}
