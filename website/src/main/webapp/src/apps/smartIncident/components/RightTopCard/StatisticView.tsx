import React, { CSSProperties, useContext, useEffect, useRef, useState } from 'react';
import { Loader } from 'eview-ui';
import { $t } from '@util';

import { I_IncidentDetail4UI } from '../../interface';
import styles from '../page.module.less';
import { SEVERITY_COLOR_MAP, SEVERITY_INTL } from '../../consts';
import { E_Severity } from '../../apis/incident';
import { GlobalContext, getGlobalState } from '../../utils/globalState';
import alarmLight from '../../assets/ic_public_alarmlight1_border.png';

interface I_StatisticViewProps {
  incidentDetail: I_IncidentDetail4UI;
}

/**
 * 故障详情-右上角悬浮卡片- 感知阶段
 */
const StatisticView: React.FC<I_StatisticViewProps> = props => {
  const { incidentDetail } = props;
  const { latestCount, aggrLineData, moTypeAttribution, alarmIdAttribution } = incidentDetail.statistics;

  return (
    <div className={styles['cockpit-right-top-card-perception-container']}>
      <div className={styles['cockpit-right-top-card-perception-latest']}>
        <p className={styles['cockpit-right-top-card-perception-latest-title']}>
          <img src={alarmLight} />
          <span>{$t('smart.incident.detail.aggr.event.current.total')} {latestCount?.detail.total ?? '--'}</span>
        </p>
        <div className={styles['cockpit-right-top-card-perception-latest-level-list']}>
          <p className={styles['cockpit-right-top-card-perception-latest-level']}>
            <span className={styles['severity-level']} style={{ '--fillColor': SEVERITY_COLOR_MAP[E_Severity.URGENT] } as CSSProperties}>
              {SEVERITY_INTL[E_Severity.URGENT]}
            </span>
            <span className={styles['severity-count']}>{latestCount?.detail.critical ?? '--'}</span>
          </p>
          <p className={styles['cockpit-right-top-card-perception-latest-level']}>
            <span className={styles['severity-level']} style={{ '--fillColor': SEVERITY_COLOR_MAP[E_Severity.IMPORTANT] } as CSSProperties}>
              {SEVERITY_INTL[E_Severity.IMPORTANT]}
            </span>
            <span className={styles['severity-count']}>{latestCount?.detail.major ?? '--'}</span>
          </p>
          <p className={styles['cockpit-right-top-card-perception-latest-level']}>
            <span className={styles['severity-level']} style={{ '--fillColor': SEVERITY_COLOR_MAP[E_Severity.MINOR] } as CSSProperties}>
              {SEVERITY_INTL[E_Severity.MINOR]}
            </span>
            <span className={styles['severity-count']}>{latestCount?.detail.warning ?? '--'}</span>
          </p>
          <p className={styles['cockpit-right-top-card-perception-latest-level']}>
            <span className={styles['severity-level']} style={{ '--fillColor': SEVERITY_COLOR_MAP[E_Severity.TIPS] } as CSSProperties}>
              {SEVERITY_INTL[E_Severity.TIPS]}
            </span>
            <span className={styles['severity-count']}>{latestCount?.detail.info ?? '--'}</span>
          </p>
        </div>
      </div>
      <div className={styles['cockpit-right-top-card-perception-block']}>
        <p className={styles['cockpit-right-top-card-perception-block-title']}>
          {$t('smart.incident.detail.aggr.trend')}
        </p>
        <div className={styles['cockpit-right-top-card-perception-block-chart']}>
          { Boolean(aggrLineData) && <Chart chartType={ViewTypeEnum.LINE_SHADOW} dataFrame={aggrLineData} /> }
        </div>
      </div>
      <div className={styles['cockpit-right-top-card-perception-block']}>
        <p className={styles['cockpit-right-top-card-perception-block-title']}>
          {$t('smart.incident.detail.aggr.moType.attribution')}
        </p>
        <div className={styles['cockpit-right-top-card-perception-block-chart']}>
          { Boolean(moTypeAttribution) && <Chart chartType={ViewTypeEnum.PIE} dataFrame={moTypeAttribution} /> }
        </div>
      </div>
      <div className={styles['cockpit-right-top-card-perception-block']}>
        <p className={styles['cockpit-right-top-card-perception-block-title']}>
          {$t('smart.incident.detail.aggr.alarmId.attribution')}
        </p>
        <div className={styles['cockpit-right-top-card-perception-block-chart']}>
          { Boolean(alarmIdAttribution) && <Chart chartType={ViewTypeEnum.PIE} dataFrame={alarmIdAttribution} /> }
        </div>
      </div>
    </div>
  );
};

const THEME_MAP = {
  lightday: 'light',
  evening: 'dark',
};

enum ViewTypeEnum {
  PIE = 2,
  LINE = 4,
  LINE_SHADOW = 5,
}

/**
 * 内部实现了piu加载、尺寸resize的视图组件
 */
const Chart: React.FC<{ chartType: ViewTypeEnum; dataFrame: any }> = props => {
  const { chartType, dataFrame } = props;

  const { theme } = useContext(GlobalContext);
  const [piuReady, setPiuReady] = useState(false);
  const [errTip, setErrTip] = useState('');
  const [viewSize, setViewSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // 监听容器resize
  useEffect(() => {
    const container = containerRef.current;
    if (!container) {
      return undefined;
    }
    const resizeObserver = new window.ResizeObserver(entries => {
      // 实际 entries 只会有一项
      for (const entry of entries) {
        const containerSize = { width: 0, height: 0 };
        if (entry.contentBoxSize.length > 0) {
          const { writingMode } = getComputedStyle(entry.target);
          if (!writingMode || writingMode === 'horizontal-tb') {
            // 内容水平流动
            containerSize.width = entry.contentBoxSize[0].inlineSize;
            containerSize.height = entry.contentBoxSize[0].blockSize;
          } else {
            // 内容垂直流动
            containerSize.width = entry.contentBoxSize[0].blockSize;
            containerSize.height = entry.contentBoxSize[0].inlineSize;
          }
        } else {
          containerSize.width = entry.contentRect.width;
          containerSize.height = entry.contentRect.height;
        }
        setViewSize({ width: containerSize.width, height: containerSize.height });
      }
    });
    resizeObserver.observe(container);
    return () => {
      resizeObserver.unobserve(container);
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    Prel.autoLoad({ 'dv-common-dashboard-piu': '*' }).then(() => {
      setPiuReady(true);
    }).catch(() => {
      // 加载视图PIU失败，依赖大盘服务，可能是未装大盘特性等原因
      setErrTip($t('dvAgent.dashboard.piu.load.failed'));
    });
  }, []);

  useEffect(() => {
    if (!piuReady || viewSize.height <= 0) {
      return;
    }   
    getGlobalState().piu.emit(
      'renderChart',
      containerRef.current,
      {
        theme: THEME_MAP[theme] ?? 'light',
        size: viewSize,
        timeMode: 'server',
        chartType: chartType,
        dataFrame,
      },
      {
        theme,
        version: 'aui3-1',
        locale: getGlobalState().language,
      },
    );
  }, [theme, viewSize, piuReady, chartType, dataFrame]);

  return (
    <>
      <Loader type='local' isOpen={!piuReady || errTip !== ''} desc='' />
      { errTip !== '' && <p>{errTip}</p> }
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
    </>
  );
};

export default StatisticView;
