import React, { useEffect, useState, useMemo, useRef } from 'react';
import { <PERSON><PERSON>, TablePro, Button, MultipleSelect, TextField, Tooltip, TextButton } from 'eview-ui';
import { formatDate2SysSetting, goToNewPage } from '@digitalview/fe-utils';
import { $t, modal, registerResource } from '@util';
import {
  queryIncidentList, queryIncidentStatistic, manualDiagnose, markRetention,
  E_DiagnosticStatus, E_DIAGNOSE_STEPS, E_MarkStatus, E_AggrType, E_Priority,
} from '../../apis/incident';
import type { E_SOLUTION_NAME, I_Incident } from '../../apis/incident';
import { filterConditionIcon } from '../../assets/const';
import PriorityIcon from '../../assets/ic_public_alert_filled.svg';
import PriorityIcon2 from '../../assets/ic_public_info_filled.svg';
import DiagnoseIcon from '../../assets/ic_ict_intelligent_diagnostics_filled.svg';
import ReportIcon from '../../assets/ic_public_files_filled.svg';
import MarkIcon from '../../assets/ic_public_star_bookmark_filled.svg';
import UnMarkIcon from '../../assets/ic_public_bookmark_filled.svg';
import {
  DETAIL_PAGE_PATH, DEFAULT_PAGINATION,
  DIAGNOSTIC_TYPE_INTL, DIAGNOSTIC_STATUS_INTL, DIAGNOSTIC_STATUS_COLOR_MAP,
  PRIORITY_COLOR_MAP, PRIORITY_INTL, SORT_FIELD_MAP, SORT_DIRECTION_MAP,
  AGGR_STATUS_INTL, AGGR_STATUS_COLOR_MAP,
} from '../../consts';
import i18nResource from '../../locales';
import TableEmptyNode from './TableEmptyNode';
import styles from './style.module.less';

interface I_IncidentListProps {
  solutionName: E_SOLUTION_NAME;
  hideDrawer: () => void;
  queryTimeRange?: { startTime: number; endTime: number };
}

/**
 * 事件列表
 */
const IncidentList: React.FC<I_IncidentListProps> = props => {
  registerResource(i18nResource, 'smartIncident');

  const { solutionName, hideDrawer, queryTimeRange } = props;

  const [selectedStatus, setSelectedStatus] = useState<E_DiagnosticStatus>();

  const [filterCondition4Req, setFilterCondition4Req] = useState({ // 用户选中的筛选排序分页数据 --用于控制接口查询入参
    diagnosisStatus: selectedStatus || null,
    csn: null,
    name: null,
    priorities: null,
    status: null,
    sortField: null,
    sortType: null,
    pagination: DEFAULT_PAGINATION,
  });

  // 选中的诊断状态改变时，更新过滤条件
  useEffect(() => {
    const diagnosisStatus = selectedStatus || null;
    setFilterCondition4Req(prev => {
      if (prev.diagnosisStatus === diagnosisStatus && prev.pagination.currentPage === 1) {
        // 避免多余的rerender
        return prev;
      }
      return {
        ...prev,
        diagnosisStatus,
        pagination: {
          ...prev.pagination,
          currentPage: 1,
        },
      };
    });
  }, [selectedStatus]);

  const [showFilterArea, setShowFilterArea] = useState(false); // 筛选区域是否显示
  const filterValues = useRef<Record<string, any>>({}); // 实时保存筛选框中的值
  const [filterCondition4UI, setFilterCondition4UI] = useState({ // 存储筛选框中的值 --用于控制筛选控件的值
    name: '',
    csn: '',
    priority: [],
    status: [],
  });

  const [tableHeight, setTableHeight] = useState('calc(100vh - 20rem)'); // 展开过滤区域后，使表格高度减小

  const [incidentList, setIncidentList] = useState<{ isLoading: boolean; total: number; currentList: I_Incident[] }>({
    isLoading: true,
    total: 0,
    currentList: [],
  });

  // 初始化、筛选、排序、切换页码、解决方案变更，刷新事件列表
  useEffect(() => {
    setIncidentList(prev => ({ ...prev, isLoading: true }));
    queryIncidentList({
      solutionName,
      diagnosisStatusList: filterCondition4Req.diagnosisStatus ? [filterCondition4Req.diagnosisStatus] : undefined,
      csn: filterCondition4Req.csn ? Number(filterCondition4Req.csn) : undefined,
      name: filterCondition4Req.name,
      priorities: filterCondition4Req.priorities,
      statusList: filterCondition4Req.status,
      paging: {
        pageSize: filterCondition4Req.pagination.pageSize,
        pageNumber: filterCondition4Req.pagination.currentPage,
        sortField: filterCondition4Req.sortField,
        sortType: filterCondition4Req.sortType,
      },
      startCreateTime: queryTimeRange ? queryTimeRange.startTime : undefined,
      endCreateTime: queryTimeRange ? queryTimeRange.endTime : undefined,
    }).then(data => {
      setIncidentList({ isLoading: false, total: data.total, currentList: data.objects });
    });
    queryIncidentStatistic(solutionName).then(incidentStatistic => {
      const { diagnosisStatusStatics } = incidentStatistic;
      setStatusCounts({
        total: {
          label: $t('smart.incident.list.status.total'),
          count: Object.values(diagnosisStatusStatics).reduce((prev, cur) => prev + cur, 0),
          color: '',
        },
        [E_DiagnosticStatus.PENDING]: {
          label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PENDING],
          count: diagnosisStatusStatics.waiting,
          color:  DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PENDING],
        },
        [E_DiagnosticStatus.PROCESSING]: {
          label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PROCESSING],
          count: diagnosisStatusStatics.running,
          color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PROCESSING],
        },
        [E_DiagnosticStatus.SUCCESS]: {
          label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.SUCCESS],
          count: diagnosisStatusStatics.success,
          color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.SUCCESS],
        },
        [E_DiagnosticStatus.FAILED]: {
          label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.FAILED],
          count: diagnosisStatusStatics.failed,
          color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.FAILED],
        },
      });
    });
  }, [solutionName, queryTimeRange, filterCondition4Req]);

  const tableColumns = useMemo(() => {
    return [
      {
        key: 'operation',
        title: $t('smart.incident.list.field.operation'),
        dataIndex: 'operation',
        ellipsis: {
          tipOnOverflow: false,
        },
        width: '6rem',
        fixed: 'left',
        render: (text, record: I_Incident) => {
          return (
            <>
              <Tooltip content={$t('smart.incident.operation.manual.diagnose')} placement='top' trigger={['hover']}>
                <span
                  className={styles.diagnoseCellIcon}
                  onClick={() => {
                    modal.confirm($t('smart.incident.operation.diagnose.confirm'), () => {
                      // 小模型成功触发诊断后，跳转到回溯页面的执行阶段
                      manualDiagnose(record.csn, record.solutionName).then(isOk => {
                        if (!isOk) {
                          return;
                        }
                        modal.info(
                          $t('smart.incident.operation.diagnose.trigger.success'),
                          () => {
                            const detailPage = `${location.origin}/eviewwebsite/index.html#path=${DETAIL_PAGE_PATH}`;
                            goToNewPage(`${detailPage}&solutionName=${record.solutionName}&incidentId=${record.csn}&defaultShowStep=${E_DIAGNOSE_STEPS.DECISION}`, true);
                          },
                        );
                      });
                    });
                  }}
                >
                  <DiagnoseIcon width={14} height={14} style={{ filter: 'invert(1)' }} />
                </span>
              </Tooltip>
              {
                record.repairAdvice ?
                  <Tooltip content={$t('smart.incident.operation.viewReport')} placement='top' trigger={['hover']}>
                    <span
                      className={styles.diagnoseCellIcon}
                      onClick={() => {
                        // 跳转到故障详情的决策步骤
                        const detailPage = `${location.origin}/eviewwebsite/index.html#path=${DETAIL_PAGE_PATH}`;
                        goToNewPage(`${detailPage}&solutionName=${record.solutionName}&incidentId=${record.csn}&defaultShowStep=${E_DIAGNOSE_STEPS.DECISION}`, true);
                      }}
                    >
                      <ReportIcon width={14} height={14} />
                    </span>
                  </Tooltip> :
                  null
              }
              {
                record.markStatus === E_MarkStatus.MARKED ?
                  <Tooltip content={$t('smart.incident.operation.cancelMarkRemain')} placement='top' trigger={['hover']}>
                    <span
                      className={styles.diagnoseCellIcon}
                      onClick={() => {
                        // 取消标记留存，成功后icon更新为标记留存
                        markRetention(record.csn, record.solutionName, E_MarkStatus.CANCEL_MARK).then(isOk => {
                          if (isOk) {
                            setIncidentList(prev => {
                              const targetItemIndex = prev.currentList.findIndex(item => item.csn === record.csn);
                              if (targetItemIndex === -1) {
                                return prev;
                              }
                              const targetItem = { ...prev.currentList[targetItemIndex] };
                              targetItem.markStatus = E_MarkStatus.CANCEL_MARK;
                              return {
                                ...prev,
                                currentList: [...prev.currentList.slice(0, targetItemIndex), targetItem, ...prev.currentList.slice(targetItemIndex + 1)],
                              };
                            });
                          }
                        });
                      }}
                    >
                      <UnMarkIcon width={14} height={14} />
                    </span>
                  </Tooltip> :
                  <Tooltip content={$t('smart.incident.operation.markRemain')} placement='top' trigger={['hover']}>
                    <span
                      className={styles.diagnoseCellIcon}
                      onClick={() => {
                        // 标记留存，成功后icon更新为取消标记留存
                        markRetention(record.csn, record.solutionName, E_MarkStatus.MARKED).then(isOk => {
                          if (isOk) {
                            setIncidentList(prev => {
                              const targetItemIndex = prev.currentList.findIndex(item => item.csn === record.csn);
                              if (targetItemIndex === -1) {
                                return prev;
                              }
                              const targetItem = { ...prev.currentList[targetItemIndex] };
                              targetItem.markStatus = E_MarkStatus.MARKED;
                              return {
                                ...prev,
                                currentList: [...prev.currentList.slice(0, targetItemIndex), targetItem, ...prev.currentList.slice(targetItemIndex + 1)],
                              };
                            });
                          }
                        });
                      }}
                    >
                      <MarkIcon width={14} height={14} />
                    </span>
                  </Tooltip>
              }
            </>
          );
        },
      },
      {
        key: 'csn',
        title: $t('smart.incident.list.field.csn'),
        dataIndex: 'csn',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '9rem',
        render: (text, record: I_Incident) => {
          return record.csn ?? '--';
        },
      },
      {
        key: 'name',
        title: $t('smart.incident.list.field.incidentName'),
        dataIndex: 'name',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '9rem',
        render: (text, record: I_Incident) => {
          return (
            <TextButton
              text={record.name}
              onClick={() => {
                if (record.incidentType === E_AggrType.NORMAL) {
                  // 跳转到故障详情的感知步骤
                  const detailPage = `${location.origin}/eviewwebsite/index.html#path=${DETAIL_PAGE_PATH}`;
                  goToNewPage(`${detailPage}&solutionName=${record.solutionName}&incidentId=${record.csn}`, true);
                } else {
                  // 待完成：泄洪事件，钻取到告警日志页面
                  const alarmLogPage = `${location.origin}/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmLog`;
                  goToNewPage(`${alarmLogPage}&json=true&csn=${encodeURIComponent(`value=[${record.csn}]&operation=in`)}`, true);
                }
              }}
            />
          );
        },
      },
      {
        key: 'priority',
        title: $t('smart.incident.list.field.priority'),
        dataIndex: 'priority',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '6rem',
        sorter: {
          order: false, // 阻止默认的前端动作
        },
        render: (text, record: I_Incident) => {
          return (
            <div className={styles.priorityCell}>
              {
                [E_Priority.CRITICAL, E_Priority.HIGH].includes(record.priority) ?
                  <PriorityIcon width={16} height={16} color={PRIORITY_COLOR_MAP[record.priority]} /> :
                  <PriorityIcon2 width={16} height={16} color={PRIORITY_COLOR_MAP[record.priority]} />
              }
              <span>{PRIORITY_INTL[record.priority] ?? '--'}</span>
            </div>
          );
        },
      },
      {
        key: 'status',
        title: $t('smart.incident.list.field.status'),
        dataIndex: 'status',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '10rem',
        render: (text, record: I_Incident) => {
          return (
            <span className={styles.statusCellCircle} style={{ background: AGGR_STATUS_COLOR_MAP[record.status] }}>
              {AGGR_STATUS_INTL[record.status] ?? '--'}
            </span>
          );
        },
      },
      {
        key: 'diagnosisStatus',
        title: $t('smart.incident.list.field.diagnoseStatus'),
        dataIndex: 'diagnosisStatus',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '10rem',
        render: (text, record: I_Incident) => {
          return (
            <>
              <span
                className={styles.diagnoseStatusCellCircle}
                style={{ background: DIAGNOSTIC_STATUS_COLOR_MAP[record.diagnosisStatus] ?? DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PENDING] }}
              />
              <span>
                {/* 诊断状态可能包含5/6，统一展示为“待诊断” */}
                { DIAGNOSTIC_STATUS_INTL[record.diagnosisStatus] ?? DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PENDING] }
              </span>
            </>
          );
        },
      },
      {
        key: 'description',
        title: $t('smart.incident.list.field.description'),
        dataIndex: 'description',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '9rem',
        render: (text, record: I_Incident) => {
          return record.description ?? '--';
        },
      },
      {
        key: 'moCount',
        title: $t('smart.incident.list.field.moCount'),
        dataIndex: 'moCount',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '6rem',
        render: (text, record: I_Incident) => {
          return record.moCount ?? '--';
        },
      },
      {
        key: 'createTime',
        title: $t('smart.incident.list.field.createTime'),
        dataIndex: 'createTime',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '10rem',
        sorter: {
          order: false, // 阻止默认的前端动作
        },
        render: (text, record: I_Incident) => {
          return record.createTime ? formatDate2SysSetting(record.createTime) : '--';
        },
      },
      {
        key: 'occurTime',
        title: $t('smart.incident.list.field.occurTime'),
        dataIndex: 'occurTime',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '10rem',
        sorter: {
          order: false, // 阻止默认的前端动作
        },
        render: (text, record: I_Incident) => {
          return record.occurTime ? formatDate2SysSetting(record.occurTime) : '--';
        },
      },
      {
        key: 'endTime',
        title: $t('smart.incident.list.field.endTime'),
        dataIndex: 'endTime',
        ellipsis: {
          hintType: 'tip',
          tipOnOverflow: true,
        },
        width: '10rem',
        sorter: {
          order: false, // 阻止默认的前端动作
        },
        render: (text, record: I_Incident) => {
          return record.endTime ? formatDate2SysSetting(record.endTime) : '--';
        },
      },
    ];
  }, []);

  // 在界面可筛选的列
  const filterableFields = useMemo(() => {
    const filterableKeys = ['csn', 'name', 'priority', 'status'];
    const columnMap = new Map(tableColumns.map(c => [c.key, c]));
    // 按照 filterableKeys 的顺序
    return filterableKeys.map(key => columnMap.get(key)).map((col, colIndex) => {
      let component;
      if (['priority', 'status'].includes(col.key)) {
        const fieldOptions = {
          priority: PRIORITY_INTL,
          status: AGGR_STATUS_INTL,
        };
        component = (
          <MultipleSelect
            value={filterCondition4UI[col.key]}
            onChange={(value) => {
              filterValues.current[col.key] = value;
            }}
            options={Object.entries(fieldOptions[col.key]).map(([itemValue, itemLabel]) => ({ value: itemValue, text: itemLabel }))}
            inputStyle={{
              width: '12rem',
              height: '2rem',
              maxWidth: 'unset',
            }}
          />
        );
      } else {
        component = (
          <TextField
            onChange={(value) => {
              filterValues.current[col.key] = value.trim();
            }}
            inputStyle={{
              width: '12rem',
              height: '2rem',
              maxWidth: 'unset',
            }}
            value={filterCondition4UI[col.key]}
            useLatestValue={true}
          />
        );
      }

      return (
        <div key={colIndex} className={styles.incidentListFilterItem}>
          <span style={{ width: '6rem', textAlign: 'left' }}>{col.title}</span>
          <span>{component}</span>
        </div>
      );
    });
  }, [tableColumns, filterCondition4UI]);

  const [statusCounts, setStatusCounts] = useState<
    Record<'total' | E_DiagnosticStatus, { label: string; count: number; color: string }>
  >({
    total: {
      label: $t('smart.incident.list.status.total'),
      count: 0,
      color: '',
    },
    [E_DiagnosticStatus.PENDING]: {
      label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PENDING],
      count: 0,
      color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PENDING],
    },
    [E_DiagnosticStatus.PROCESSING]: {
      label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.PROCESSING],
      count: 0,
      color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.PROCESSING],
    },
    [E_DiagnosticStatus.SUCCESS]: {
      label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.SUCCESS],
      count: 0,
      color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.SUCCESS],
    },
    [E_DiagnosticStatus.FAILED]: {
      label: DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.FAILED],
      count: 0,
      color: DIAGNOSTIC_STATUS_COLOR_MAP[E_DiagnosticStatus.FAILED],
    },
  });

  let drawerTitle = $t('smart.incident.list.panelTitle');
  if (queryTimeRange) {
    drawerTitle = `${drawerTitle} (${formatDate2SysSetting(queryTimeRange.startTime)}~${formatDate2SysSetting(queryTimeRange.endTime)})`;
  }

  return (
    <Drawer
      visible
      placement='right'
      onClose={() => hideDrawer()}
      width='70%'
      height='100%'
      style={{ marginTop: '4rem' }}
      inContainer={false}
      footer={false}
      drawerStyle={{ background: '#2a2a2a' }}
      title={drawerTitle}
    >
      <div className={styles.stateCountsCellsContainer} style={{ display: queryTimeRange ? 'none' : 'flex' }}>
        <div className={styles.leftStateCountsCells}>
          <div className={styles.stateCountsCell}>
            <span className={styles.stateText}>{statusCounts.total.label}</span>
            <span
              onClick={() => setSelectedStatus(undefined)}
              className={selectedStatus ? styles.underlinedNumber : styles.selectedNumber}
            >
              {statusCounts.total.count}
            </span>
          </div>
        </div>
        <div className={styles.rightStateCountsCells}>
          {Object.entries(statusCounts).map(([statusKey, statusData]) => {
            if (statusKey === 'total') {
              return null;
            }
            const statusKeyEnum = Number(statusKey);
            return (
              <div key={statusKey} className={styles.stateCountsCell}>
                <span className={styles.circle} style={{ backgroundColor: statusData.color }} />
                <span className={styles.stateText}>{statusData.label}</span>
                <span
                  onClick={() => setSelectedStatus(statusKeyEnum)}
                  className={selectedStatus === statusKeyEnum ? styles.selectedNumber : styles.underlinedNumber}
                >
                  {statusData.count}
                </span>
              </div>
            );
          })}
        </div>
      </div>
      <div className={styles.incidentListOperation}>
        <div className={styles.incidentListLeftFilter}>
          <Button
            text={$t('smart.incident.list.operation.filterCondition')}
            status='default'
            leftIcon={filterConditionIcon}
            onClick={() => {
              if (showFilterArea) {
                setTableHeight('calc(100vh - 20rem)');
              } else {
                setTableHeight('calc(100vh - 30rem)');
              }
              setShowFilterArea(prev => !prev);
            }}
          />
        </div>
        <div className={styles.incidentListRightBtns} style={{ display: showFilterArea ? 'flex' : 'none' }}>
          <Button
            text={$t('smart.incident.list.operation.reset')}
            status='default'
            onClick={() => {
              filterValues.current = {};
              setFilterCondition4UI({
                name: '',
                csn: '',
                priority: [],
                status: [],
              });
            }}
          />
          <Button
            text={$t('smart.incident.list.operation.filter')}
            status='primary'
            onClick={() => {
              setFilterCondition4UI({
                csn: filterValues.current.csn,
                name: filterValues.current.name,
                priority: filterValues.current.priority,
                status: filterValues.current.status,
              });
              setFilterCondition4Req(prev => ({
                ...prev,
                csn: filterValues.current.csn,
                name: filterValues.current.name,
                priorities: filterValues.current.priority,
                status: filterValues.current.status,
                pagination: {
                  ...prev.pagination,
                  currentPage: 1,
                },
              }));
            }}
          />
        </div>
      </div>
      <div className={styles.incidentListFilterArea} style={{ display: showFilterArea ? 'flex' : 'none' }}>
        { filterableFields }
      </div>
      <div className={styles.incidentListTableContainer}>
        <TablePro
          rowKey='csn'
          columns={tableColumns}
          dataset={incidentList.currentList}
          enableZebraCrossing={false}
          scroll={{ x: '100%', y: tableHeight }}
          emptyNode={<TableEmptyNode />}
          pagination={{
            recordCount: incidentList.total,
            pageSize: filterCondition4Req.pagination.pageSize,
            currentPage: filterCondition4Req.pagination.currentPage,
            onPageChange: (currentPage: number) => {
              setFilterCondition4Req(prev => ({
                ...prev,
                pagination: {
                  ...prev.pagination,
                  currentPage,
                },
              }));
            },
            onPageSizeChange: (pageSize: number) => {
              setFilterCondition4Req(prev => ({
                ...prev,
                pagination: {
                  pageSize,
                  currentPage: 1,
                },
              }));
            },
          }}
          onChange={(value, event) => {
            if (event.action === 'sort') {
              const currentSortField = event.sort.field;
              const currentSortOrder = event.sort.order;
              // 获取映射后的排序字段
              const mappedSortField = SORT_FIELD_MAP[currentSortField];
              // 获取映射后的排序类型
              const mappedSortType = SORT_DIRECTION_MAP[currentSortOrder];
              setFilterCondition4Req(prev => ({
                ...prev,
                sortField: mappedSortField,
                sortType: mappedSortType,
                pagination: {
                  ...prev.pagination,
                  currentPage: 1,
                },
              }));
            }
          }}
        />
      </div>
    </Drawer>
  );
};

export default IncidentList;
