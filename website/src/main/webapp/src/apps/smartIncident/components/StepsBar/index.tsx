import React, {useRef, useState} from 'react';
import styles from '../page.module.less';
import StepsBarItem from './StepsBarItem';
import OperateIcon from '../../assets/panel_operation_bar.svg';
import TimeIcon from '../../assets/times.svg';
import DocIcon from '../../assets/base_ic_public_doc_clock.svg';
import {I_StepsBarProps} from './interface';

/**
 * 故障详情-诊断步骤条
 */
const StepsBar: React.FC<I_StepsBarProps> = props => {
  const {incidentDetail, currentStep, onStepClick} = props;
  const displaySteps = new Set(incidentDetail.steps.map(step => step.stage));
  const stepBarRef = useRef<HTMLDivElement>(null);
  // 记录当前步骤条的状态
  const [stepsBarStatus, setStepsBarStatus] = useState(false);
  return (
    stepsBarStatus ? (
      <div className={styles['cockpit-step-bar-overview']} ref={stepBarRef}>
        <div className={styles['cockpit-step-bar-overview-toolbar-container']} onClick={()=>{setStepsBarStatus(!stepsBarStatus);}}>
          <div className={styles['cockpit-step-bar-overview-toolbar']}>
            <OperateIcon />
          </div>
        </div>
        <div className={styles['cockpit-step-bar-overview-panel']}>
          <div className={styles['cockpit-step-bar-overview-container']}>
            <div className={styles['cockpit-step-bar-box-position']}>
              <div className={styles['cockpit-step-bar-box']}>
                <div className={styles['cockpit-step-bar-box-row']}>
                  <TimeIcon />
                  <span className={styles['cockpit-step-bar-box-row-text']}>开始时间</span>
                </div>
                <div className={styles['cockpit-step-bar-box-row-second']}>
                  <span className={styles['cockpit-step-bar-box-row-text-second']}>2024-6-26 16:50</span>
                </div>
              </div>
              <div className={styles['cockpit-step-bar-box']}>
                <div className={styles['cockpit-step-bar-box-row']}>
                  <DocIcon />
                  <span className={styles['cockpit-step-bar-box-row-text']}>累计时间</span>
                </div>
                <div className={styles['cockpit-step-bar-box-row-second']}>
                  <span className={styles['cockpit-step-bar-box-row-text-second']}>13 min</span>
                </div>
              </div>
            </div>
            <div className={styles['cockpit-step-bar-process-success']} />
            <div className={styles['cockpit-step-bar-process-padding']} />
            <div className={styles['cockpit-step-bar-overview-content']}>
              {
                incidentDetail.steps.map(step => (
                  <StepsBarItem
                    key={step.stage}
                    selfStep={step}
                    displaySteps={displaySteps}
                    isAutoPlay={false}
                    isSelected={currentStep.stage === step.stage}
                    onStepClick={onStepClick}
                    portalRef={stepBarRef}
                    stepsBarStatus={stepsBarStatus}
                  />
                ))
              }
            </div>
          </div>
        </div>
      </div>
    ) : (
      <div className={styles['cockpit-step-bar-overview-default']} ref={stepBarRef}>
        <div className={styles['cockpit-step-bar-overview-default-bar']} onClick={()=>{setStepsBarStatus(!stepsBarStatus);}}><OperateIcon /></div>
        <div className={styles['cockpit-step-bar-overview-default-container']}>
          <div className={styles['cockpit-step-bar-overview-default-leftSide']}>
            <span className={styles['cockpit-step-bar-overview-default-leftSide-dark']}>开始时间 </span>
            <span className={styles['cockpit-step-bar-overview-default-leftSide-light']}>2025/01/09 09:52:56</span>
            <span className={styles['cockpit-step-bar-overview-default-leftSide-dark']}>|</span>
            <span className={styles['cockpit-step-bar-overview-default-leftSide-dark']}>处置时长 </span>
            <span className={styles['cockpit-step-bar-overview-default-leftSide-light']}>3min</span>
          </div>
          <div className={styles['cockpit-step-bar-overview-content']}>
            {
              incidentDetail.steps.map(step => (
                <StepsBarItem
                  key={step.stage}
                  selfStep={step}
                  displaySteps={displaySteps}
                  isAutoPlay={false}
                  isSelected={currentStep.stage === step.stage}
                  onStepClick={onStepClick}
                  portalRef={stepBarRef}
                  stepsBarStatus={stepsBarStatus}
                />
              ))
            }
          </div>
          <div className={styles['cockpit-step-bar-overview-content-line']} />
        </div>
      </div>
    )
  );
};

export default StepsBar;
