import React, { useEffect } from 'react';

import { renderStepUI as renderCentalPart4CurrentStep, destroyStepUI } from '../../utils/pageUtils';
import { I_IncidentDetail4UI, I_Step } from '../../interface';
import styles from '../page.module.less';
import { CENTRAL_PART_DOM_ID } from '../../consts';

interface I_CentralPartProps {
  incidentDetail: I_IncidentDetail4UI;
  currentStep: I_Step & { selectedSubStep?: number };
  fromCockpit: boolean;
}

/**
 * 故障详情-中间区域
 */
const CentralPart: React.FC<I_CentralPartProps> = props => {
  const { incidentDetail, currentStep, fromCockpit } = props;

  useEffect(() => {
    renderCentalPart4CurrentStep(CENTRAL_PART_DOM_ID, incidentDetail, currentStep, currentStep.selectedSubStep);
  }, [incidentDetail, currentStep]);

  // 离开页面时，销毁组件
  useEffect(() => {
    return () => {
      destroyStepUI();
    };
  }, []);

  return (
    <div
      id={CENTRAL_PART_DOM_ID}
      className={`${styles.centralContainer} ${(currentStep?.subSteps ?? []).length > 0 ? styles.hasSubStep : ''}`}
    />
  );
};

export default CentralPart;
