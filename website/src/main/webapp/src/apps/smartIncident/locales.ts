export default {
  'zh-cn': {
    // TOP列表国际化
    'smart.incident.topn.title': 'TOP待处理智能事件',
    'smart.incident.topn.abstract.to.be.analysed': '待诊断分析...',
    'smart.incident.topn.viewAll': '查看全部',
  
    // 全量列表国际化
    'smart.incident.list.panelTitle': '事件列表',
    'smart.incident.list.queryFailed': '智能事件查询失败。',
    'smart.incident.list.status.total': '总计',
    'smart.incident.list.operation.filterCondition': '过滤条件',
    'smart.incident.list.operation.filter': '过滤',
    'smart.incident.list.operation.reset': '重置',
    'smart.incident.list.field.incidentId': '编号',
    'smart.incident.list.field.incidentName': '名称',
    'smart.incident.list.field.priority': '优先级',
    'smart.incident.list.field.priority.low': '低',
    'smart.incident.list.field.priority.medium': '中',
    'smart.incident.list.field.priority.high': '高',
    'smart.incident.list.field.priority.critical': '紧急',
    'smart.incident.list.field.status': '状态',
    'smart.incident.list.field.status.init': '聚合中',
    'smart.incident.list.field.status.opened': '聚合结束',
    'smart.incident.list.field.status.clsoed': '已关闭',
    'smart.incident.list.field.diagnoseType': '诊断类型',
    'smart.incident.list.field.diagnoseType.auto': '自动',
    'smart.incident.list.field.diagnoseType.manual': '手动',
    'smart.incident.list.field.diagnoseStatus': '诊断状态',
    'smart.incident.list.field.diagnoseStatus.pending': '待诊断',
    'smart.incident.list.field.diagnoseStatus.processing': '诊断中',
    'smart.incident.list.field.diagnoseStatus.success': '诊断成功',
    'smart.incident.list.field.diagnoseStatus.failed': '诊断失败',
    'smart.incident.list.field.clearStatus': '清除状态',
    'smart.incident.list.field.clearStatus.cleared': '已清除',
    'smart.incident.list.field.clearStatus.unCleared': '未清除',
    'smart.incident.list.field.ackStatus': '确认状态',
    'smart.incident.list.field.ackStatus.acked': '已确认',
    'smart.incident.list.field.ackStatus.unAcked': '未确认',
    'smart.incident.list.field.description': '摘要',
    'smart.incident.list.field.repairAdvice': '修复建议',
    'smart.incident.list.field.moCount': '网元数量',
    'smart.incident.list.field.createTime': '创建时间',
    'smart.incident.list.field.occurTime': '首次发生时间',
    'smart.incident.list.field.clearTime': '清除时间',
    'smart.incident.list.field.endTime': '关闭时间',
    'smart.incident.list.field.csn': '流水号',
    'smart.incident.list.field.eventCount': '事件数量',
    'smart.incident.list.field.operation': '操作',

    'smart.incident.operation.manual.diagnose': '诊断',
    'smart.incident.operation.diagnose.confirm': '重新诊断会覆盖已有诊断结果，请确认是否继续？',
    'smart.incident.operation.diagnose.trigger.failed': '诊断失败，请稍候重试。',
    'smart.incident.operation.diagnose.trigger.success': '触发诊断成功，即将跳转到诊断结果页面。由于诊断需要一定时间，请耐心等候...',
    'smart.incident.operation.viewReport': '查看报告',
    'smart.incident.operation.markRemain': '标记留存',
    'smart.incident.operation.cancelMarkRemain': '取消标记留存',
    'smart.incident.operation.diagnose.markRetention.success': '操作成功。',
    'smart.incident.operation.diagnose.markRetention.failed': '操作失败，请稍候重试。',

    // 事件详情页面国际化
    'smart.incident.fe.error': '出错了，请刷新页面重试。',
    'smart.incident.aggr.event.severity.critical': '紧急',
    'smart.incident.aggr.event.severity.major': '重要',
    'smart.incident.aggr.event.severity.minor': '次要',
    'smart.incident.aggr.event.severity.tips': '提示',
    'smart.incident.detail.aggr.event.current.total': '告警总计',
    'smart.incident.detail.aggr.trend': '聚合时间线',
    'smart.incident.detail.aggr.trend.unit': '次',
    'smart.incident.detail.aggr.moType.attribution': '告警网元类型分布',
    'smart.incident.detail.aggr.alarmId.attribution': '告警类型分布',
    'smart.incident.detail.queryFailed': '故障信息查询失败。',
    'smart.incident.detail.layer.site': '站点',
    'smart.incident.detail.layer.app': '业务App',
    'smart.incident.detail.layer.pod': '业务Pod',
    'smart.incident.detail.layer.host': '主机',
    'smart.incident.detail.left.top.category': '智能告警',
    'smart.incident.detail.steps.perception': '感知',
    'smart.incident.detail.steps.analysis': '分析',
    'smart.incident.detail.steps.decision': '诊断',
    'smart.incident.detail.steps.perception.desc': '故障感知',
    'smart.incident.detail.steps.analysis.desc': '故障关键告警分析',
    'smart.incident.detail.steps.decision.desc': '故障诊断结果生成',
    'smart.incident.detail.steps.analysis.sub.desc': '分析已完成',
    'smart.incident.detail.steps.decision.sub.desc.notStart': '待诊断生成故障诊断结果...',
    'smart.incident.detail.steps.decision.sub.desc.running': '故障诊断结果生成中...',
    'smart.incident.detail.steps.decision.sub.desc.failed': '故障诊断结果生成失败',
    'smart.incident.detail.steps.decision.sub.desc.finished': '故障诊断结果已生成',
    'smart.incident.detail.steps.perception.tooltip.view': '查看',
    'smart.incident.detail.steps.perception.tooltip.view.more': '查看更多',
    'smart.incident.detail.steps.analysis.topn.key.events': 'TOP关键告警',
    'smart.incident.detail.steps.analysis.businessTopo': '业务拓扑',
    'smart.incident.detail.steps.analysis.cases': '相关案例',
    'smart.incident.detail.steps.analysis.cases.viewAll': '查看全文',
    'smart.incident.detail.steps.analysis.no.cases': '没有相关的检索案例。',
    'smart.incident.detail.steps.decision.no.report': '当前故障暂无诊断结果。',
    'smart.incident.detail.steps.decision.taskover': '接管',
    'smart.incident.detail.steps.decision.taskover.ing': '接管中...',
    'smart.incident.detail.steps.decision.taskover.duplicate': '故障已被接管，无法被重复接管。',
    'smart.incident.detail.steps.decision.archive': '案例归档',

    // 案例详情
    'case.knowledge.table.column.caseId': '知识ID',
    'case.knowledge.table.column.solution': '解决方案类型',
    'case.knowledge.table.column.name': '案例名称',
    'case.knowledge.table.column.faultSymptom': '关键故障现象',
    'case.knowledge.table.column.cause': '故障原因',
    'case.knowledge.table.column.version': '版本号',
    'case.knowledge.table.column.source': '来源',
    'case.knowledge.view.influence': '故障影响',
    'case.knowledge.view.recoveryMeasures': '恢复措施',
    'case.knowledge.view.query.failed': '查询案例知识详情失败',
    'case.knowledge.view.title': '案例详情',
    'case.knowledge.event.title': '事件详情',

    // 智能推荐案例
    'intelligent.recommendation.common.suggestion': '通用处理建议',
    'intelligent.recommendation.common.case':'案例',
    'intelligent.recommendation.common.queryInfo': '关键路径摘要',
    'intelligent.recommendation.cause': '故障原因:',
    'intelligent.recommendation.suggestion': '恢复措施:',
    'intelligent.recommendation.case': '案例名称',

    // 事件名称
    'event.knowledge.table.column.name': '事件名称',
    'event.knowledge.table.column.cause': '可能原因',
    'event.knowledge.table.column.suggestions': '恢复建议',
    'event.knowledge.table.column.desc': '描述',
    // 分析页面查看更多按钮
    'smart.incident.view.more':'查看更多',
    'smart.incident.association':'关联度',
    // 事件公共国际化
    'smart.incident.common.no.data': '暂无数据',
    'smart.incident.common.hide.panel': '收起面板',
    'smart.incident.common.loading': '加载中，请稍候...',

    'smart.incident.list.queryAlarm': '查询告警信息失败',
    'smart.incident.list.NE':'查询网元连接失败',
  },
  'en-us': {
    // TOP列表国际化
    'smart.incident.topn.title': 'Top Pending Smart Incidents',
    'smart.incident.topn.abstract.to.be.analysed': 'Awaiting diagnosis and analysis...',
    'smart.incident.topn.viewAll': 'View All',

    // 全量列表国际化
    'smart.incident.list.panelTitle': 'Incident List',
    'smart.incident.list.queryFailed': 'Failed to query the smart incident.',
    'smart.incident.list.status.total': 'Total',
    'smart.incident.list.operation.filterCondition': 'Filter Condition',
    'smart.incident.list.operation.filter': 'Filter',
    'smart.incident.list.operation.reset': 'Reset',
    'smart.incident.list.field.incidentId': 'ID',
    'smart.incident.list.field.incidentName': 'Name',
    'smart.incident.list.field.priority': 'Priority',
    'smart.incident.list.field.priority.low': 'Low',
    'smart.incident.list.field.priority.medium': 'Medium',
    'smart.incident.list.field.priority.high': 'High',
    'smart.incident.list.field.priority.critical': 'Critical',
    'smart.incident.list.field.status': 'Status',
    'smart.incident.list.field.status.init': 'Aggregating',
    'smart.incident.list.field.status.opened': 'Aggregated',
    'smart.incident.list.field.status.clsoed': 'Closed',
    'smart.incident.list.field.diagnoseType': 'Diagnose type',
    'smart.incident.list.field.diagnoseType.auto': 'Auto',
    'smart.incident.list.field.diagnoseType.manual': 'Manual',
    'smart.incident.list.field.diagnoseStatus': 'Diagnose status',
    'smart.incident.list.field.diagnoseStatus.pending': 'Pending diagnosis',
    'smart.incident.list.field.diagnoseStatus.processing': 'Under diagnosis',
    'smart.incident.list.field.diagnoseStatus.success': 'Diagnostic success',
    'smart.incident.list.field.diagnoseStatus.failed': 'Diagnostic failed',
    'smart.incident.list.field.clearStatus': 'Clearance status',
    'smart.incident.list.field.clearStatus.cleared': 'Cleared',
    'smart.incident.list.field.clearStatus.unCleared': 'Uncleared',
    'smart.incident.list.field.ackStatus': 'Confirmation status',
    'smart.incident.list.field.ackStatus.acked': 'Confirmed',
    'smart.incident.list.field.ackStatus.unAcked': 'Unconfirmed',
    'smart.incident.list.field.description': 'Abstract',
    'smart.incident.list.field.repairAdvice': 'Handling suggestion',
    'smart.incident.list.field.moCount': 'MO Count',
    'smart.incident.list.field.createTime': 'Created On',
    'smart.incident.list.field.occurTime': 'First Occurred On',
    'smart.incident.list.field.clearTime': 'Cleared On',
    'smart.incident.list.field.endTime': 'Close Time',
    'smart.incident.list.field.csn': 'Csn',
    'smart.incident.list.field.eventCount': 'Event Quantity',
    'smart.incident.list.field.operation': 'Operation',

    'smart.incident.operation.manual.diagnose': 'Diagnose',
    'smart.incident.operation.diagnose.confirm': 'Rediagnosis will overwrite the existing diagnosis, please confirm whether to continue?',
    'smart.incident.operation.diagnose.trigger.failed': 'Diagnosis failed, please try again later.',
    'smart.incident.operation.diagnose.trigger.success': 'Trigger diagnosis successfully, will jump to the diagnosis result page soon. Please be patient as the diagnosis will take some time...',
    'smart.incident.operation.viewReport': 'View report',
    'smart.incident.operation.markRemain': 'Tag retention',
    'smart.incident.operation.cancelMarkRemain': 'Untag retention',
    'smart.incident.operation.diagnose.markRetention.success': 'Operation successful.',
    'smart.incident.operation.diagnose.markRetention.failed': 'Operation failed, please wait and try again.',

    // 事件详情页面国际化
    'smart.incident.fe.error': 'An error has occurred. Please refresh the page and try again.',
    'smart.incident.aggr.event.severity.critical': 'Urgent',
    'smart.incident.aggr.event.severity.major': 'Important',
    'smart.incident.aggr.event.severity.minor': 'Minor',
    'smart.incident.aggr.event.severity.tips': 'Tips',
    'smart.incident.detail.aggr.event.current.total': 'Total alarms',
    'smart.incident.detail.aggr.trend': 'Aggregation timeline', 
    'smart.incident.detail.aggr.trend.unit': 'Times', 
    'smart.incident.detail.aggr.moType.attribution': 'Distribution of alarm NE types', 
    'smart.incident.detail.aggr.alarmId.attribution': 'Distribution of alarm types',
    'smart.incident.detail.queryFailed': 'Failed to query the incident information.',
    'smart.incident.detail.layer.site': 'Site',
    'smart.incident.detail.layer.app': 'Manage Business',
    'smart.incident.detail.layer.pod': 'Service Pod',
    'smart.incident.detail.layer.host': 'Host',
    'smart.incident.detail.left.top.category': 'Smart Alarm',
    'smart.incident.detail.steps.perception': 'Perception',
    'smart.incident.detail.steps.analysis': 'Analysis',
    'smart.incident.detail.steps.decision': 'Diagnosis',
    'smart.incident.detail.steps.perception.desc': 'Fault Perception',
    'smart.incident.detail.steps.analysis.desc': 'Fault Key Events Analysis',
    'smart.incident.detail.steps.decision.desc': 'Fault Diagnosis Result Generation',
    'smart.incident.detail.steps.analysis.sub.desc': 'Analysis completed',
    'smart.incident.detail.steps.decision.sub.desc.notStart': 'Pending diagnosis to generate fault results...', 
    'smart.incident.detail.steps.decision.sub.desc.running': 'Fault results generation in progress...', 
    'smart.incident.detail.steps.decision.sub.desc.failed': 'Fault results generation failed', 
    'smart.incident.detail.steps.decision.sub.desc.finished': ' Fault results have been generated',
    'smart.incident.detail.steps.perception.tooltip.view': 'View',
    'smart.incident.detail.steps.perception.tooltip.view.more': 'View More',
    'smart.incident.detail.steps.analysis.topn.key.events': 'Top Key Event(s)',
    'smart.incident.detail.steps.analysis.businessTopo': 'Business Topology',
    'smart.incident.detail.steps.analysis.cases': 'Correlation Cases',
    'smart.incident.detail.steps.analysis.cases.viewAll': 'View Full Text',
    'smart.incident.detail.steps.analysis.no.cases': 'There are no relevant retrieved cases.',
    'smart.incident.detail.steps.decision.no.report': 'There is no diagnostic result for the current incident at this time.',
    'smart.incident.detail.steps.decision.taskover': 'Take over',
    'smart.incident.detail.steps.decision.taskover.ing': 'Taking over...',
    'smart.incident.detail.steps.decision.taskover.duplicate': 'The fault has been taken over and cannot be repeated.',
    'smart.incident.detail.steps.decision.archive': 'Archive case',


    // 案例详情
    'case.knowledge.table.column.caseId': 'Knowledge ID',
    'case.knowledge.table.column.solution': 'Solution Type',
    'case.knowledge.table.column.name': 'Case Name',
    'case.knowledge.table.column.faultSymptom': 'Symptom',
    'case.knowledge.table.column.cause': 'Cause',
    'case.knowledge.table.column.version': 'Version',
    'case.knowledge.table.column.source': 'Source',
    'case.knowledge.view.influence': 'Impact',
    'case.knowledge.view.recoveryMeasures': 'Recovery Measures',
    'case.knowledge.view.query.failed': 'Failed to query the case knowledge details.',
    'case.knowledge.view.title': 'Case Details',
    'case.knowledge.event.title': 'Event Details',

    // 智能推荐案例
    'intelligent.recommendation.common.queryInfo': 'Key Path Summary',
    'intelligent.recommendation.common.suggestion': 'General Handling Suggestions',
    'intelligent.recommendation.common.case': 'Case',
    'intelligent.recommendation.cause': 'Cause:',
    'intelligent.recommendation.suggestion': 'Recovery Measures:',
    'intelligent.recommendation.case': 'Case:',

    // 事件详细
    'event.knowledge.table.column.name': 'Event Name',
    'event.knowledge.table.column.cause': 'Cause',
    'event.knowledge.table.column.suggestions': 'Recovery Suggestions',
    'event.knowledge.table.column.desc': 'Description',

    // 查看更多
    'smart.incident.view.more':'View More',
    'smart.incident.association':'Association',

    // 事件公共国际化
    'smart.incident.common.no.data': 'No records found.',
    'smart.incident.common.hide.panel': 'Fold the panel',
    'smart.incident.common.loading': 'Loading, please wait...',

    'smart.incident.list.queryAlarm': 'Failed to query alarm information',
    'smart.incident.list.NE':'Failed to query network element connection',
  },
};

