import type { I_RestResponse } from './client';
import request from './client';
import { notification, $t, modal } from '@util';

/**
 * 查询incident列表
 */
export async function queryIncidentList(param: I_IncidentListParam): Promise<{total: number; objects: I_Incident[]}> {
  try {
    const response = await request.post<I_RestResponse<{total: number; objects: I_Incident[]}>>(
      '/rest/dvtopowebsite/v1/business/topo/incident/incidentlist',
      param,
    );
    if (response.resultCode === 0) {
      return { total: response.data.total, objects: response.data.objects ?? [] };
    } else {
      notification.error(response.resultMessage ? response.resultMessage : response.resultCode);
    }
  } catch {
    notification.error($t('smart.incident.list.queryFailed'));
  }
  return { total: 0, objects: [] };
}

/**
 * 查询 incident 某一阶段的详情
 */
export async function queryIncidentByCsn(csn: number, solutionName: E_SOLUTION_NAME, stage: E_DIAGNOSE_STEPS): Promise<I_IncidentDetail | null> {
  try {
    const response = await request.post<I_RestResponse<I_IncidentDetail | null>>(
      '/rest/dvtopowebsite/v1/business/topo/incident/detail',
      { csn, solutionName, macroSteps: stage },
    );
    if (response.resultCode === 0) {
      return response.data;
    }
  } catch {
    // 请求出错，返回null，交给UI层去处理异常
  }
  return null;
}

/**
 * 查询 Incident 状态统计
 */
export async function queryIncidentStatistic(solutionName: E_SOLUTION_NAME): Promise<null | I_IncidentStatistic> {
  try {
    const response = await request.get<I_RestResponse<I_IncidentStatistic>>(
      `/rest/dvtopowebsite/v1/business/topo/incident/statusstatics?solutionName=${solutionName}`,
    );
    if (response.resultCode === 0) {
      return response.data;
    } else {  
      notification.error(response.resultMessage ? response.resultMessage : response.resultCode);
    }
  } catch {
    notification.error($t('smart.incident.list.queryFailed'));
  }
  return {
    diagnosisStatusStatics: {
      waiting: 0,
      running: 0,
      success: 0,
      failed: 0,
    },
    incidentStatusStatics: {
      init: 0,
      opened: 0,
      closed: 0,
    },
  };
}

/**
 * 手动触发诊断
 */
export async function manualDiagnose(incidentCsn: number, solutionName: E_SOLUTION_NAME): Promise<boolean> {
  try {
    const response = await request.post<I_RestResponse<boolean>>(
      '/rest/dvtopowebsite/v1/business/topo/incident/manualdiagnosis',
      { incidentCsn, solutionName },
    );
    if (response.resultCode === 0) {
      return true;
    }
    modal.error(response.resultMessage);
  } catch {
    modal.error($t('smart.incident.operation.diagnose.trigger.failed'));
  }
  return false;
}

/**
 * 标记留存
 */
export async function markRetention(incidentCsn: number, solutionName: E_SOLUTION_NAME, markStatus: E_MarkStatus): Promise<boolean> {
  try {
    const response = await request.post<I_RestResponse<boolean>>(
      '/rest/dvtopowebsite/v1/business/topo/incident/mark',
      { solutionName, incidentCsn, markStatus },
    );
    if (response.resultCode === 0) {
      notification.success($t('smart.incident.operation.diagnose.markRetention.success'));
      return true;
    }
  } catch {
    // 不做处理
  }
  notification.error($t('smart.incident.operation.diagnose.markRetention.failed'));
  return false;
}

/**
 * 查询 incident 的告警统计数据
 */
export async function queryAggregateStatics(csn: number, solutionName: E_SOLUTION_NAME): Promise<I_AggregateStatics> {
  try {
    const response = await request.post<I_RestResponse<I_AggregateStatics | null>>(
      '/rest/dvtopowebsite/v1/business/topo/incident/aggregatestatics',
      { csn, solutionName },
    );
    if (response.resultCode === 0) {
      return response.data;
    }
  } catch {
    notification.error($t('获取聚合告警统计信息失败。'));
  }
  return {
    alarmIdDistribute: [],
    moTypeDistribute: [],
    eventCountTrend: [],
  };
}

/**
 * 解决方案名称（solutionType唯一标识一种解决方案模板，solutionName唯一标识一种解决方案的一套系统）
 */
export enum E_SOLUTION_NAME {
  CBS = 'CBS',
  BES = 'BES',
  MM = 'MM',
}

/**
 * 故障处置步骤
 */
export enum E_DIAGNOSE_STEPS {
  /**
   * 感知
   */
  PERCEPTION = 1,
  /**
   * 分析
   */
  ANALYSIS = 2,
  /**
   * 诊断
   */
  DECISION = 3,
}

/**
 * Incident优先级
 */
export enum E_Priority {
  // 紧急优先级
  CRITICAL = 'CRITICAL',
  // 高优先级
  HIGH = 'HIGH',
  // 中优先级
  MEDIUM = 'MEDIUM',
  // 低优先级
  LOW = 'LOW',
}

/**
 * 告警级别
 */
export enum E_Severity {
  // 严重问题
  URGENT = 1,
  // 重要问题
  IMPORTANT = 2,
  // 次要问题
  MINOR = 3,
  // 提示问题
  TIPS = 4,
}

export enum E_ClearStatus {
  UnCleared = 0,
  Cleared = 1,
}

export enum E_AckStatus {
  UnAcked = 0,
  Acked = 1,
}

/**
 * Incident状态
 * 0-INIT 表示聚合未结束，event:1-OPENED 表示聚合结束，2-CLOSED 表示已清除
 */
export enum E_IncidentStatus {
  // 聚合未结束
  INIT = 'INIT',
  // 聚合结束
  OPENED = 'OPENED',
  // 已关闭（CLOSED的情况有2种，一种是聚合的告警已全部清除，一种是超时关闭）
  CLOSED = 'CLOSED',
}

/**
 * 聚合类型
 */
export enum E_AggrType {
  /**
   * 正常聚合事件
   */
  NORMAL = 0,
  /**
   * 泄洪事件
   */
  FLOOD_DISCHARGE = 1,
}

/**
 * 诊断类别
 */
export enum E_DiagnosticType {
  /** 自动 */
  AUTO = 0,
  /** 手动 */
  MANUAL = 1,
}

/**
 * 诊断状态
 * 
 * 0：未诊断，1：待诊断，2：诊断中，3：诊断成功，4：诊断失败，5：资源不足，未执行诊断，6：级别升级，重诊断
 */
export enum E_DiagnosticStatus {
  /**
   * 待诊断（未到达诊断时间，或者到达诊断时间但需要排队等待诊断）
   */
  PENDING = 1,
  /**
   * 诊断中
   */
  PROCESSING = 2,
  /**
   * 诊断成功
   */
  SUCCESS = 3,
  /**
   * 诊断失败
   */
  FAILED = 4,
}

export enum E_MarkStatus {
  UNMARK = 0,
  MARKED = 1,
  CANCEL_MARK = 2,
}

interface I_IncidentListParam {
  solutionName: E_SOLUTION_NAME;
  csn?: number; // 事件ID
  name?: string; // 事件名称
  priorities?: E_Priority[]; // 事件级别
  statusList?: E_IncidentStatus[]; // 事件状态
  diagnosisType?: E_DiagnosticType; // 事件诊断类型
  diagnosisStatusList?: E_DiagnosticStatus[]; // 事件诊断状态
  paging?: {
    pageSize: number;
    pageNumber: number;
    sortField?: string;
    sortType?: string;
  };
  incidentType?: E_AggrType;
  /** 故障发生的起止时间，要求要么都传要么都不传 */
  startCreateTime?: number;
  endCreateTime?: number;
}

export interface I_Incident {
  tenantId: null | string; // 租户ID
  solutionName: E_SOLUTION_NAME; // 解决方案
  csn: number; // 事件的流水号，与 solutionName 一起构成事件的唯一ID
  name: string; // 事件的名称
  incidentType: E_AggrType; // 事件聚合类型
  priority: E_Priority; // 优先级。主要基于紧急度和影响度来决定。而对于具有同样优先级的事件,可按照他们需要花费的精力多少来安排顺序。不为空
  status: E_IncidentStatus; // 事件的状态
  ackStatus: E_AckStatus; // 确认状态，0-未确认，1-已确认
  clearStatus: E_ClearStatus; // 清除状态，0-未清除，1-已清除
  createTime: number; // 创建时间。第一条告警达到网管聚合模块,开始聚合的系统时间。不为空
  occurTime: number; // 发生时间。当前Incident中Event的最早发生时间。不为空
  clearTime: number; // 清除时间，如果已经清除，否则为0。故障恢复时间的UTC时间,由故障分析填写或系统强制清除时填写,根据不同策略有不同的清除时间。
  endTime: number; // 事件关闭的时间
  description: null | string; // 事件摘要
  repairAdvice: null | string; // 修复建议
  sourceObjId: null | string; // 故障对象ID。根因告警源对象ID,界面不展示。
  moCount: number; // 涉及的网元数量
  eventSize: number; // 聚合的告警数量
  diagnosisType: E_DiagnosticType; // 诊断类型
  diagnosisStatus: E_DiagnosticStatus; // 诊断状态
  markStatus: E_MarkStatus; // 事件留存状态，清理 Incident 时不会清理留存事件
}

export interface I_IncidentDetail {
  solutionName: E_SOLUTION_NAME;
  csn: number; // 事件的流水号，与 solutionName 一起构成事件的唯一ID
  name: string; // 事件的名称
  status: E_IncidentStatus; // 事件的状态
  diagnosisStatus: E_DiagnosticStatus; // 诊断状态
  macroSteps: E_DIAGNOSE_STEPS;
  /**
   * 感知 —— 呈现事件所聚合告警的全貌和分布
   * - 网元所在层的标识 -- 支撑界面实现分层展示
   * - APP、pod、host三层网元所属的站点实例 -- 支撑界面实现切换查看不同站点实例下的告警APP、pod、虚机 -- siteId
   * - APP层网元类型的业务分组 -- 支撑界面实现APP层网元类型的分组展示
   */
  moList: { // 全量告警网元
    site: I_EventMO[];
    app: I_EventMO[];
    pod: I_EventMO[];
    host: I_EventMO[];
  };
  eventList: I_EventInfo[]; // 全量告警
  /**
   * 分析 —— 进一步给出事件中的关键告警
   */
  keyEventList: I_KeyEventInfo[]; // 关键告警
}

export interface I_EventMO {
  solutionName: E_SOLUTION_NAME; // 解决方案，1：cbs，2：bes，3：MM（首页需要）
  solutionId: number; // 解决方案id（首页需要）
  layer: E_MO_LAYER; // 所属层级，1 - 站点实例，2：业务APP，3：POD，null-虚机
  dn: string; // 网元标识
  moName: string; // 网元名称
  moType: string; // 网元类型标识
  moTypeName: string; // 网元类型名称
  siteId: number; // 网元所属的站点实例ID
  instanceId: number; // 网元的拓扑实例ID。对于站点实例来说，instanceId和siteId是一样的。
  groupId: number; // 业务分组ID（首页需要） -- 组装
  moTypeGroupName: string; // 业务分组名称  -- 与groupId配套
  parentDN?: string; // 父网元DN
  childDNs?: string[]; // 子网元DN，列表，父子关系
  sourceDeployMoDNs?: string[]; // 部署源网元DN，列表，如某个虚机
  targetDeployMoDNs?: string[]; // 部署目标网元DN，列表，如某个pod
  applicationType: number; // 区分业务视图和数据库视图（首页需要），目前Incident只支持1-业务视图
}

export interface I_EventInfo {
  alarmCsn: number;
  alarmId: string;
  name: string;
  severity: E_Severity;
  occurTime: number;
  dn: string; // 告警发生网元
}

export interface I_KeyEventInfo {
  alarmId: string;
  name: string;
  severity: E_Severity; // 级别
  score: number; // 得分
  targetAlarmIdList: string[]; // 有关联的告警ID
}

export enum E_MO_LAYER {
  SITE = 1,
  APP = 2,
  POD = 3,
  HOST = null,
}

interface I_IncidentStatistic {
  diagnosisStatusStatics: {
    waiting: number;
    running: number;
    success: number;
    failed: number;
  };
  incidentStatusStatics: {
    init: number;
    opened: number;
    closed: number;
  };
}

export interface I_AggregateStatics {
  alarmIdDistribute: Array<{
    alarmId: string;
    name: string;
    count: number;
  }>;
  moTypeDistribute: Array<{
    moType: string;
    moTypeName: string;
    count: number;
  }>;
  eventCountTrend: Array<{
    timestamp: number;
    detail: {
      critical: number;
      major: number;
      warning: number;
      info: number;
      total: number;
    };
  }>;
}

interface I_KnowledgeParam {
  solutionName: string;
  incidentCsn: number; // csn
}

interface I_Diagnosisresult {
  diagnosisAbstract: string;
  generalRecommendation: string;
  caseRecommendation: string;
}

/**
 *  查询诊断结果
 * @param param I_KnowledgeParam
 */
export async function queryCaseKnowledgeDetails(param: I_KnowledgeParam): Promise<I_Diagnosisresult | null> {
  try {
    const response = await request.post<I_RestResponse<I_Diagnosisresult>>('/rest/dvtopowebsite/v1/business/topo/incident/diagnosisresult', param);
    if (response.resultCode === 0) {
      return response.data;
    } else {
      notification.error(response.resultMessage ? response.resultMessage : response.resultCode);
    }
  } catch {
    notification.error($t('smart.incident.list.queryFailed'));
  }
  return null;
}

export interface I_Relationresult {
     relationResult: Array<{
       moType: string;
       links: Array<{
         linkDirection: number;
         linkType: number;
         modelName: string;
         protocol: string;
       }>;
     }>;
}

export async function queryAppRelation(solutionName: string): Promise<I_Relationresult | null> {
  try {
    const response = await request.post<I_RestResponse<I_Relationresult>>('/rest/dvtopowebsite/v1/business/topo/incident/getapprelation', {
      solutionName,
    });
    if (response.resultCode === 200) {
      return response.data;
    } else {
      notification.error(response.resultMessage ? response.resultMessage : response.resultCode);
    }
  } catch {
    notification.error($t('smart.incident.list.NE'));
  }
  return null;
}


/**
 *查询告警流水号拼接
 */
export async function queryMoreAlarmData(csnList: string) : Promise<any> {
  try {
    const response = await request.post<I_RestResponse<I_AggregateStatics | null>>(
      '/rest/dvtopowebsite/v1/business/topo/common/queryalarmbycsn',
      { csnList, timestamp:0 },
    );
    if (response.resultCode === 0) {
      return response.data;
    }
  } catch {
    notification.error($t('smart.incident.list.queryAlarm'));
  }
  return {
    data:'',
  };
}