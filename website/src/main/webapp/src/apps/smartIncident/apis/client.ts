import { request, axiosInstance, REQ_MANAGER } from '@digitalview/fe-utils';
import innerConfigMgr from '@util/innerConfig';

// 请求拦截器
const handleBeforeRequest = (config): any => {
  REQ_MANAGER.addReq(config);
  const { csrfToken } = innerConfigMgr.getInnerConfig();
  config.headers.roarand = csrfToken;
  config.headers['X-Requested-With'] = 'XMLHttpRequest';
  return config;
};

// 响应拦截器
const responseOnFullfill = (response): any => {
  REQ_MANAGER.removeReq(response);
  if (response.config?.needRawHeader) {
    return response;
  }

  const result = response.data || response;
  return result;
};

// 处理非200的http响应码
// 对请求抛出的错误进行解析，转换成对应的错误提示抛出
const errorHandler = (error): any => {
  REQ_MANAGER.removeReq(error);
  throw error;
};

axiosInstance.interceptors.request.use(handleBeforeRequest, errorHandler);
axiosInstance.interceptors.response.use(responseOnFullfill, errorHandler);

export default request;

export interface I_RestResponse<T = unknown> {
  resultCode: number;
  resultMessage: string | null;
  data: T;
}
