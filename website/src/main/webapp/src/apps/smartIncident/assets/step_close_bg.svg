<svg viewBox="0 0 1091.47 37" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1091.47485" height="37" fill="none" customFrame="#000000">
	<defs>
		<g id="pixso_custom_effect_0">
			<effect visibility="visible" effectType="backgroundBlur" saturation="0" stdDeviation="30" />
		</g>
		<filter id="filter_0" width="1091.47485" height="37" x="0" y="0" filterUnits="userSpaceOnUse" customEffect="url(#pixso_custom_effect_0)" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feGaussianBlur stdDeviation="10" in="BackgroundImage" />
			<feComposite result="effect_backgroundBlur_1" in2="SourceAlpha" operator="in" />
			<feBlend result="shape" in="SourceGraphic" in2="effect_backgroundBlur_1" mode="normal" />
		</filter>
		<linearGradient id="paint_linear_0" x1="221.569092" x2="221.069763" y1="0.00104904175" y2="44.8635559" gradientUnits="userSpaceOnUse">
			<stop stop-color="rgb(33,33,33)" offset="0" stop-opacity="0.678160906" />
			<stop stop-color="rgb(34,34,34)" offset="1" stop-opacity="1" />
		</linearGradient>
		<radialGradient id="paint_radial_0" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-0.499078,78.5,-717.166,-4.55951,193.063,0.00357819)">
			<stop stop-color="rgb(106,141,242)" offset="0" stop-opacity="1" />
			<stop stop-color="rgb(17,27,71)" offset="1" stop-opacity="0" />
		</radialGradient>
		<radialGradient id="paint_radial_1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-0.499235,18.5,-337.776,-9.11511,193.493,-0.00019455)">
			<stop stop-color="rgb(32,112,243)" offset="0" stop-opacity="0.796078444" />
			<stop stop-color="rgb(32,34,39)" offset="1" stop-opacity="0" />
		</radialGradient>
	</defs>
	<g filter="url(#filter_0)">
		<path id="矩形" d="M0 37L0 22.0618L0 1.06177L1091.47 0L1091.47 0L479.278 0C462.989 0 446.948 3.9788 432.547 11.5904L406.403 25.4096C392.002 33.0212 375.961 37 359.672 37L18.4749 37L0 37Z" fill="url(#paint_linear_0)" fill-opacity="0.400000006" fill-rule="evenodd" transform="matrix(1,0,0,-1,0,37)" />
		<path id="矩形" d="M0 37L0 22.0618L0 1.06177L1091.47 0L1091.47 0L479.278 0C462.989 0 446.948 3.9788 432.547 11.5904L406.403 25.4096C392.002 33.0212 375.961 37 359.672 37L18.4749 37L0 37Z" fill="url(#paint_radial_0)" fill-opacity="0.200000003" fill-rule="evenodd" transform="matrix(1,0,0,-1,0,37)" />
		<path id="矩形" d="M0 37L0 22.0618L0 1.06177L1091.47 0L1091.47 0L479.278 0C462.989 0 446.948 3.9788 432.547 11.5904L406.403 25.4096C392.002 33.0212 375.961 37 359.672 37L18.4749 37L0 37Z" fill="url(#paint_radial_1)" fill-opacity="0.200000003" fill-rule="evenodd" transform="matrix(1,0,0,-1,0,37)" />
		<path id="矩形" d="M0 37L0 22.0618L0 1.06177L1091.47 0L1091.47 0L479.278 0C462.989 0 446.948 3.9788 432.547 11.5904L406.403 25.4096C392.002 33.0212 375.961 37 359.672 37L18.4749 37L0 37Z" fill="rgb(92,162,233)" fill-opacity="0.0799999982" fill-rule="evenodd" transform="matrix(1,0,0,-1,0,37)" />
	</g>
</svg>
