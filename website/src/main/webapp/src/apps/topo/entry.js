import Http from '../../utils/Http';
import i18n from '../../utils/locales';
import LinkComponent from '../../pages/Link/LinkComponent';
import NodeComponent from '../../pages/Node/NodeComponent';
import React from 'react';
import ReactDOM from 'react-dom';
import {Store} from '../../utils/Common';
import './topo.css';
import {ALARM_NAME_KEY} from './const';

import LinkDeletionImpl from '../../pages/Link/LinkDeletionImpl';
import NodeDeletionImpl from '../../pages/Node/NodeDeletionImpl';

function filterFunc2(filter3, filter4, filter8) {
  filter3.menuId = 'mouiPage';
  filter3.checkSupport = function(elements) {
    if (
      window.topoInterface.topoTree.getSelectNode().userData.nativeId === '/'
    ) {
      return false;
    }
    return !isVirtual();
  };
  filter4.menuId = 'alarmPage';
  filter4.checkSupport = function(elements) {
    if (
      window.topoInterface.topoTree.getSelectNode().userData.nativeId === '/'
    ) {
      return false;
    }
    return !isVirtual();
  };
  filter8.menuId = 'containerPage';
  filter8.checkSupport = function(elements) {
    if (
      window.topoInterface.topoTree.getSelectNode().userData.nativeId === '/'
    ) {
      return false;
    }

    if (window.topoInterface.data.getCurrentTopo().objId !== '101') {
      return false;
    }
    const result = Http.get(true, {
      url: '/rest/dvtopowebsite/v1/topo/admin',
    });
    if (result && result.resultMessage && result.resultMessage === '200') {
      return result.data;
    }
    return false;
  };
}

function isVirtual() {
  const result = Http.get(true, {
    url: `/rest/dvtopowebsite/v1/topo/virtual?dn=${encodeURIComponent(
      window.topoInterface.topoTree.getSelectNode().userData.nativeId,
    )}`,
  });
  if (result && !result.result) {
    return result.result;
  }
  if (result && result.resultMessage && result.resultMessage === '200') {
    return result.data;
  }
  return true;
}

function filterFunc() {
  let filter1 = {};
  let filter2 = {};
  let filter3 = {};
  let filter4 = {};
  let filter8 = {};
  filter1.menuId = 'oms.topo.common.delete';
  filter1.checkSupport = function(elements) {
    let isLink = true;
    for (let i = 0; i < elements.length; i++) {
      if (elements[i]._clientMap.category !== 'Link') {
        isLink = false;
      }
    }
    if (!isLink) {
      if (elements.length > 1) {
        return false;
      }
    }
    if (
      elements[0]._clientMap.category === 'Link' ||
      window.topoInterface.data.getCurrentTopo().objId !== '101'
    ) {
      return true;
    } else {
      return isVirtual();
    }
  };
  filter2.menuId = 'cloudsop.topo.menu.properties';
  filter2.checkSupport = function(elements) {
    return false; // 返回false，菜单被过滤
  };
  filterFunc2(filter3, filter4, filter8);
  return {filter1, filter2, filter3, filter4, filter8};
}

function check(topoHasPermission, showCreateMenu) {
  if (
    window.topoInterface.topoTree.getSelectNode().userData.ownerId === 0 &&
    window.topoInterface.topoTree.getSelectNode().userData.nativeId === 'OS=1'
  ) {
    return false;
  }
  if (
    window.topoInterface.topoTree
      .getSelectNode()
      .userData.nativeId.startsWith('ResGroup_')
  ) {
    return false;
  }
  return topoHasPermission() && showCreateMenu();
}

const isNFVResult = Http.get(true, {
  url: '/rest/dvtopowebsite/v1/topo/querynfvconfig',
});
const isNFV = isNFVResult?.data ?? false;

const menuConfigResult = Http.get(true, {
  url: '/rest/dvtopowebsite/v1/topo/querymaskedmenus',
});
const menuConfig = menuConfigResult?.data;

Prel.ready(() => {
  Prel.start(
    'dvTopoPiu',
    '1.0.0',
    ['$dom', 'locale', 'session', 'user'],
    (piu, st) => {
      piu.set('currentApp', 'DV_TopoManagement_Service');
      changeTitle(piu);
      piu.emit(window.topoInterface.constants.event.TOPO_HIDE_TOOLBAR_BUTTONS, [
        'create',
      ]);
      piu.attach(piu, {
        // SPAF切换
        updateRoutingInfo: (menu = {}) => {
          const {path} = menu;
          if (path === '/topowebsite') {
            piu.set('currentApp', 'DV_TopoManagement_Service');
          }
        },

        // 右键菜单
        menuAction: popupMenu => {
          if (popupMenu) {
            menuHandler(popupMenu.data, piu);
          }
        },

        // 左树网元点击事件
        treeClickAction: data => {
          if (data.type === 'click') {
            const nodeId = window.topoInterface.data.getObjectById(
              data.event.data.nodeId,
            ).nativeId;
            let moDn = encodeURIComponent(nodeId);
            Http.post(false, {
              url: '/rest/dvtopowebsite/v1/topo/updatemoconnectstatus',
              data: {dn: moDn},
            });
          }
        },
        setCreateMode: data => data.fromNode && createLink(data),
        topoMapOpened: data => topoAutoLayout(data, st.user.roles),
      });
      if (st.user.ops.indexOf('DVTopo.Basic') < 0) {
        filterAll(piu);
        return;
      }
      let linkDeletionImpl = new LinkDeletionImpl(piu);
      let nodeDeletionImpl = new NodeDeletionImpl(piu);
      piu.emit('registerLinkDeletion', linkDeletionImpl);
      piu.emit('registerNEDeletion', nodeDeletionImpl);

      // 扩展下方面板
      require('./hostBottomPanel').createPanel(piu);
      require('./alarmBottomPanel').createAlramPanel(piu);
      piu.emit('topoRegisterClickAlarmHandler', {
        clickHandler: alarmClickHandler,
      });

      // NFV场景定制tips、右键菜单
      if (isNFV) {
        handleTipsForNFV(piu);
      } else {
        handleLinkTips(piu);
      }

      addMenuFilters(piu);
      // 单击右侧拓扑图网元操作
      singleClickHandler(piu);
      hideAlarmButton();
    },
  );

  const alarmClickHandler = param => {
    let severity = param.alarmLevel;
    let {className} = param.objInfo[0];
    let {nativeId} = param.objInfo[0];
    const result = Http.get(true, {
      url: `/rest/dvtopowebsite/v1/alarmCondition?severity=${severity}&className=${className}&moDn=${nativeId}`,
    });
    let jumpUrl = `/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&conditionId=${result.data}&isShowFilter=true`;
    const windowJump = window.open(encodeURI(jumpUrl));
    windowJump.opener = null;
    windowJump.location = jumpUrl;
  };
  const topoAutoLayout = (data, roles) => {
    let roleIds = '';
    roles.forEach(e => {
      roleIds += `${e.id}and`;
    });
    roleIds = roleIds.substring(0, roleIds.length - 3);
    const result = Http.get(true, {
      url: `/rest/dvtopowebsite/v1/topo/hasRight?ids=${roleIds}`,
    });
    let savePositionFlag = result.data ? true : false;
    if (data.subnetId !== '102') {
      let elements = window.topoInterface.util.getElementPosition();
      let newArray = elements.filter(
        element => element.position.posX === 0 && element.position.posY === 0,
      );
      let nodes = [];
      newArray.forEach(node => nodes.push(node.objId));
      window.topoInterface.util.topoAutoLayout({
        layoutType: 'symmetry',
        nodeIds: nodes,
        autoSavePosition: savePositionFlag,
      });
    }
  };

  const createDomNode = () => {
    let dom = document.getElementById('right-view');
    let myDiv = document.createElement('div');
    let divId = document.createAttribute('id');
    divId.value = 'dvtopo';
    myDiv.setAttributeNode(divId);
    dom.appendChild(myDiv);
  };

  const createLink = data => {
    Store.linkMenuRefresh = true;

    if (document.getElementById('dvtopo') === null) {
      createDomNode();
    }

    ReactDOM.render(
      <LinkComponent data={data} />,
      document.getElementById('dvtopo'),
    );
  };

  const createNode = nodeId => {
    if (document.getElementById('dvtopo') === null) {
      createDomNode();
    }
    Store.nodeMenuRefresh = true;

    ReactDOM.render(
      <NodeComponent data={nodeId} />,
      document.getElementById('dvtopo'),
    );
  };

  function singleClickHandler(piu) {
    piu.emit('cbbRegisterSingleClickHandler', {
      id: 'nodeSingleClick',
      priority: 100,
      handler: {
        isTakeOver(obj) {
          return true;
        },
        handle(obj) {
          // 单击响应动作
          if (obj) {
            const nodeId = window.topoInterface.data.getObjectById(obj.id)
              .nativeId;
            let moDn = encodeURIComponent(nodeId);
            Http.post(false, {
              url: '/rest/dvtopowebsite/v1/topo/updatemoconnectstatus',
              data: {dn: moDn},
            });
          }
        },
      },
    });
  }

  function changeTitle(piu) {
    setInterval(() => {
      if (
        window.location.href.includes(
          '/eviewwebsite/index.html#path=/topowebsite&topoClassId=3001',
        )
      ) {
        piu.emit('updateAppContext', {
          title: i18n.translate('topo.title'),
        });
      }
    }, 100);
  }

  function hideAlarmButton() {
    let css = document.createElement('link');
    css.type = 'text/css';
    css.rel = 'stylesheet';
    css.href = '/dvtopowebsite/topo/topo.css';
    document.querySelector('head').appendChild(css);
  }

  function menuHandler(data, piu) {
    // 菜单项点击处理
    let menuId = data.originalId;
    if (window.topoInterface.topoTree.getSelectNode() === undefined) {
      return;
    }
    let nodeId = window.topoInterface.topoTree.getSelectNode().userData
      .nativeId;
    switch (menuId) {
      case 'mouiPage':
        // 菜单配置管理的点击处理
        const mouiUrl = `/moui/business.action?BMEBusiness=moui&moDn=${nodeId}`;
        const mouiWindowJump = window.open(encodeURI(mouiUrl));
        mouiWindowJump.opener = null;
        mouiWindowJump.location = mouiUrl;
        break;
      case 'alarmPage': {
        // 菜单告警管理的点击处理
        const alarmUrl = `/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&json=true&&nativeMoDn=${encodeURIComponent(
          `value=${nodeId}&operation=in`,
        )}`;
        const alarmWindowJump = window.open(encodeURI(alarmUrl));
        alarmWindowJump.opener = null;
        alarmWindowJump.location = alarmUrl;
        break;
      }
      case 'linkPage':
        piu.emit('cbbChangeNetworkMode', {
          mode: 2,
          id: 3,
        });
        break;
      case 'nodePage':
        createNode(nodeId);
        break;
      case 'infocollectPage': {
        // 故障信息收集
        const infocollectUrl = `/eviewwebsite/index.html?dn=${nodeId}#path=/infocollect&subMenu=taskManagement`;
        const infocollectUrlJump = window.open(encodeURI(infocollectUrl));
        infocollectUrlJump.opener = null;
        infocollectUrlJump.location = infocollectUrl;
        break;
      }
      case 'containerPage': {
        // 网元足迹查询
        const containerUrl = `/eviewwebsite/index.html#path=/resourceLog&moDn=${nodeId}`;
        const containerUrlJump = window.open(encodeURI(containerUrl));
        containerUrlJump.opener = null;
        containerUrlJump.location = containerUrl;
        break;
      }
      case 'iConfigPage': {
        let url = '/servlet/smadapter?url=/iconfig/guide.jsp';
        const urlJump = window.open(encodeURI(url));
        urlJump.opener = null;
        urlJump.location = url;
        break;
      }
      case 'mmlPage': {
        let url = '/dvmaintenancewebsite/dvmaintenance/index.html';
        const urlJump = window.open(encodeURI(url));
        urlJump.opener = null;
        urlJump.location = url;
        break;
      }
      case 'maintenanceTerminalPage': {
        let url = menuConfig.maintenanceLink;
        const urlJump = window.open(encodeURI(url));
        urlJump.opener = null;
        urlJump.location = url;
        break;
      }

      default:
        break;
    }
  }

  function filterAll(piu) {
    let filters = [];
    let filter1 = {};
    let filter2 = {};
    let filter3 = {};
    let filter4 = {};
    let filter5 = {};
    let filter6 = {};
    let filter7 = {};
    let filter8 = {};
    let filter9 = {};
    let filter10 = {};
    let filter11 = {};
    let filter12 = {};
    filter1.menuId = 'oms.topo.common.delete';
    filter1.checkSupport = function(elements) {
      return false;
    };
    filter2.menuId = 'cloudsop.topo.menu.properties';
    filter2.checkSupport = function(elements) {
      return false; // 返回false，菜单被过滤
    };
    filter3.menuId = 'mouiPage';
    filter3.checkSupport = function(elements) {
      return false;
    };
    filter4.menuId = 'alarmPage';
    filter4.checkSupport = function(elements) {
      return false;
    };
    filter5.menuId = 'infocollectPage';
    filter5.checkSupport = function(elements) {
      return false;
    };

    filter6.menuId = 'linkPage';
    filter6.checkSupport = function(elements) {
      return false;
    };

    filter7.menuId = 'nodePage';
    filter7.checkSupport = function(elements) {
      return false;
    };
    filter8.menuId = 'containerPage';
    filter8.checkSupport = function(elements) {
      return false;
    };
    filter9.menuId = 'oms.topo.menu.createButton';
    filter9.checkSupport = function(elements) {
      return false;
    };

    filter10.menuId = 'maintenanceTerminalPage';
    filter10.checkSupport = function(elements) {
      return false;
    };

    filter11.menuId = 'iConfigPage';
    filter11.checkSupport = function(elements) {
      return false;
    };

    filter12.menuId = 'mmlPage';
    filter12.checkSupport = function(elements) {
      return false;
    };
    filters.push(
      filter1,
      filter2,
      filter3,
      filter4,
      filter5,
      filter6,
      filter7,
      filter8,
      filter9,
      filter10,
      filter11,
      filter12,
    );
    piu.emit(
      window.topoInterface.constants.event.REGISTER_MENU_FILTERS,
      filters,
    );
  }

  function addMenuFilters(piu) {
    let filters = [];
    let {filter1, filter2, filter3, filter4, filter8} = filterFunc();
    let filter5 = {};
    let filter9 = {};
    filter5.menuId = 'infocollectPage';
    filter5.checkSupport = function(elements) {
      if (
        window.topoInterface.topoTree.getSelectNode().userData.nativeId === '/'
      ) {
        return false;
      }
      return isAlive() && !isVirtual();
    };

    let filter6 = {};
    filter6.menuId = 'linkPage';
    filter6.checkSupport = function(elements) {
      return topoHasPermissionNoCustom(elements);
    };

    let filter7 = {};
    filter7.menuId = 'nodePage';
    filter7.checkSupport = function(elements) {
      return check(topoHasPermission, showCreateMenu);
    };
    filter9.checkSupport = function(elements) {
      return false;
    };
    filters.push(
      filter1,
      filter2,
      filter3,
      filter4,
      filter5,
      filter6,
      filter7,
      filter8,
      filter9,
    );

    // iconfig右键菜单过滤配置
    let iconfigFilter = {};
    iconfigFilter.menuId = 'iConfigPage';
    iconfigFilter.checkSupport = function(elements) {
      return menuConfig?.maskMenus?.indexOf('iConfigPage') === -1;
    };

    // mml右键菜单过滤配置
    let mmlFilter = {};
    mmlFilter.menuId = 'mmlPage';
    mmlFilter.checkSupport = function(elements) {
      return menuConfig?.maskMenus?.indexOf('mmlPage') === -1;
    };

    // 终端维护右键菜单过滤配置
    let terminalFilter = {};
    terminalFilter.menuId = 'maintenanceTerminalPage';
    terminalFilter.checkSupport = function(elements) {
      return menuConfig?.maskMenus?.indexOf('maintenanceTerminalPage') === -1;
    };

    filters.push(iconfigFilter, mmlFilter, terminalFilter);

    piu.emit(window.topoInterface.constants.event.REGISTER_MENU_FILTERS, filters);
  }

  function isAlive() {
    const result = Http.post(true, {
      url: '/rest/dvinfocollectwebsite/v1/dvsystemservice/getDashboardData',
    });
    return Boolean(result.result);
  }

  function topoHasPermissionNoCustom() {
    const result = Http.getWithText(true, {
      url: '/rest/dvtopowebsite/v1/website',
    });
    return result.result;
  }

  function topoHasPermission() {
    const result = Http.getWithText(true, {
      url: '/rest/dvtopowebsite/v1/website',
    });
    return result.result && window.topoInterface.data.getCurrentTopo().objId === '101';
  }

  function showCreateMenu() {
    const result = Http.get(true, {
      url: `/rest/dvtopowebsite/v1/topo/showcreatemenu?dn=${encodeURIComponent(
        window.topoInterface.topoTree.getSelectNode().userData.nativeId,
      )}`,
    });
    if (result?.resultMessage === '200' && result?.data) {
      return result.data;
    }
    return false;
  }

  /* 以下为自定义拓扑定制方法 */
  const handleLinkTips = piu => {
    piu.emit(window.topoInterface.constants.event.TOPO_REGISTER_TIP_HANDLER, 'DVCarrierSoftwareLink', {
      getToolTip: objInfo => {
        const tipsContainer = document.createElement('div');
        createTipsItem(i18n.translate('topology.custom.link.tooltip.name'), objInfo.name, tipsContainer);
        createTipsItem(
          i18n.translate('topology.custom.link.tooltip.type'),
          i18n.translate('topology.custom.link.tooltip.type.virtual'),
          tipsContainer
        );
        return tipsContainer.innerHTML;
      },
    });
  };

  // NFV场景定制处理tips
  const handleTipsForNFV = piu => {
    piu.emit(window.topoInterface.constants.event.TOPO_REGISTER_TIP_HANDLER, 'Subnet,DVNetworkElement,DVCarrierSoftwareLink,OSS', {
      getToolTip: objInfo => {
        if (objInfo.className === 'DVCarrierSoftwareLink') {
          return {
            asyncUpdate: true,
            updateTipContent: handleLinkTipsForNFV(objInfo).then(tipInfo => ({objectInfo: objInfo, tipInfo})),
          };
        } else {
          return {
            asyncUpdate: true,
            updateTipContent: handleNodeTipsForNFV(objInfo).then(tipInfo => ({objectInfo: objInfo, tipInfo})),
          };
        }
      },
    });
  };

  const isZhCn = () => {
    return i18n.locale === 'zh-cn';
  };

  const createTipsItem = (label, value, parentDom) => {
    const itemDiv = document.createElement('div');
    itemDiv.classList.add('tooltipDiv');
    itemDiv.innerHTML = `<span class="tooltipKey" style="width: ${isZhCn() ? '100px' : '145px'}">${label}</span><span class="tooltipValue">${value}</span>`;
    parentDom.appendChild(itemDiv);
  };

  const handleLinkTipsForNFV = objInfo => {
    return new Promise((resolve, reject) => {
      Http.get(false, {
        url: `/rest/dvtopowebsite/v1/topo/displaylinktips?resId=${objInfo.resid}`,
        success: resp => {
          if (resp.resultCode !== 0) {
            reject(new Error('Get link tips failed!'));
          }

          const tipsContainer = document.createElement('div');

          createTipsItem(i18n.translate('topology.custom.link.tooltip.name'), objInfo.name, tipsContainer);
          createTipsItem(i18n.translate('topology.custom.link.tooltip.type'), i18n.translate('topology.custom.link.tooltip.type.virtual'), tipsContainer);
          createTipsItem(i18n.translate('topology.custom.link.tooltip.left'), resp.data.leftMoName ?? '', tipsContainer);
          createTipsItem(i18n.translate('topology.custom.link.tooltip.right'), resp.data.rightMoName ?? '', tipsContainer);

          resolve(tipsContainer.innerHTML);
        },
        error: () => {
          reject(new Error('Get link tips failed!'));
        },
      });
    });
  };

  const createAlarmTipsItem = (severityNumber, totalNumber, parentDom) => {
    const labelDiv = document.createElement('div');
    labelDiv.classList.add('tooltipDiv');
    labelDiv.innerHTML = `<span class="tooltipKey" style="width: ${isZhCn() ? '100px' : '145px'}">${i18n.translate('topology.custom.tooltip.alarmInfo')}</span>`;
    parentDom.appendChild(labelDiv);

    const severityNumberDiv = document.createElement('div');
    severityNumberDiv.classList.add('tooltipChildDiv');
    severityNumberDiv.innerHTML = `<span class="tooltipValue">${severityNumber}</span>`;
    parentDom.appendChild(severityNumberDiv);

    const totalNumberDiv = document.createElement('div');
    totalNumberDiv.classList.add('tooltipChildDiv');
    totalNumberDiv.innerHTML = `<span class="tooltipValue">${i18n.translate('topology.custom.tooltip.alarmInfo.total')}${totalNumber}</span>`;
    parentDom.appendChild(totalNumberDiv);
  };

  // 处理告警tips展示
  const handleAlarmInfo = alarmArr => {
    let alarmMsg = '';
    let totalCount = 0;
    for (let item of alarmArr) {
      if (item.alarmLeverDisplay) {
        alarmMsg += `${ALARM_NAME_KEY[item.alarmLeverName]}/${item.alarmLeverCount}; `;
        totalCount += item.alarmLeverCount;
      }
    }

    return {alarmMsg, totalCount};
  };

  const handleNodeTipsForNFV = param => {
    return new Promise((resolve, reject) => {
      Http.get(false, {
        url: `/rest/dvtopowebsite/v1/topo/displaymotips?dn=${param.nativeid}`,
        success: resp => {
          if (resp.resultCode !== 0) {
            reject(new Error('Get mo tips failed!'));
          }

          const alarmResult = handleAlarmInfo(resp.data.alarmLever);
          const dnInfo = {
            dnName: resp.data.dnName,
            dnType: resp.data.moType ?? '',
            alarmSeverityNumber: alarmResult.alarmMsg,
            alarmTotalNumber: alarmResult.totalCount,
            physicalLocation: resp.data.location,
            ipAddress: resp.data.ipAddress,
            runStatus: resp.data.runningStatus?.replace(/^"|"$/g, ''),
            managementStatus: resp.data.mangerStatus?.replace(/^"|"$/g, ''),
            isVirtual: resp.data.VirtualMo ? i18n.translate('topology.custom.tooltip.isVirtual.yes') : i18n.translate('topology.custom.tooltip.isVirtual.no'),
          };

          const tipsContainer = document.createElement('div');

          createTipsItem(i18n.translate('topology.custom.tooltip.dnName'), dnInfo.dnName, tipsContainer);
          createTipsItem(i18n.translate('topology.custom.tooltip.dnType'), dnInfo.dnType, tipsContainer);
          createAlarmTipsItem(dnInfo.alarmSeverityNumber, dnInfo.alarmTotalNumber, tipsContainer);
          if (dnInfo.physicalLocation) {
            createTipsItem(i18n.translate('topology.custom.tooltip.physicalLocation'), dnInfo.physicalLocation, tipsContainer);
          }
          if (dnInfo.ipAddress) {
            createTipsItem(i18n.translate('topology.custom.tooltip.ipAddress'), dnInfo.ipAddress, tipsContainer);
          }
          createTipsItem(i18n.translate('topology.custom.tooltip.runStatus'), dnInfo.runStatus, tipsContainer);
          createTipsItem(i18n.translate('topology.custom.tooltip.managementStatus'), dnInfo.managementStatus, tipsContainer);
          createTipsItem(i18n.translate('topology.custom.tooltip.isVirtual'), dnInfo.isVirtual, tipsContainer);

          resolve(tipsContainer.innerHTML);
        },
        error: () => {
          reject(new Error('Get mo tips failed!'));
        },
      });
    });
  };
});
