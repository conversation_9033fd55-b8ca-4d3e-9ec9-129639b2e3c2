#topo_alarm_menu {
  display: none;
}

#oms_topo_menu_createButton {
  display: none;
}

.topo-common-input-cont > .label-cont {
    width: 5.3rem;
}

#dvtoponame .eui_label{
    color: #000;
    width: 4rem;
}

.tooltipDiv {
  display: flex;
  min-height: 1.5rem;
  align-items: flex-start;
  font-size: 0.875rem !important;
}

.tooltipChildDiv {
  display: flex;
  min-height: 1.5rem;
  align-items: flex-start;
  font-size: 0.875rem !important;
  padding-left: 1.75rem;
}

.tooltipKey {
  white-space: nowrap;
  line-height: 1.5rem;
}

.tooltipValue {
  word-break: break-all;
  word-wrap: break-word;
  line-height: 1.5rem;
}
