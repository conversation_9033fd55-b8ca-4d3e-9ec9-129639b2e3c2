import { $t } from '@util';
import DataCheck from './DataCheck';

const dataCheckValidMap = new Map()
  .set('required', (minLength, maxLength, value) => {
    if (!DataCheck.require(value)) {
      return $t('sys_required_val');
    }
    return '';
  })
  .set('cmpValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.cmpValidChar(value)) {
      return $t('sys_valid_audit_char');
    }
    return '';
  })
  .set('indicatorNameValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.indicatorNameValidChar(value)) {
      return $t('sys_valid_indicatorName_char');
    }
    return '';
  })
  .set('cmpPositiveInteger', (minLength, maxLength, value) => {
    if (!DataCheck.cmpPositiveInteger(value)) {
      return $t('sys_valid_positive_integer');
    }
    return '';
  })
  .set('cmpNoZeroPositiveInteger', (minLength, maxLength, value) => {
    if (!DataCheck.cmpNoZeroPositiveInteger(value)) {
      return $t('sys_valid_positive_no_zero_integer');
    }
    return '';
  })
  .set('cmpAllPositiveInteger', (minLength, maxLength, value) => {
    if (!DataCheck.cmpAllPositiveInteger(value)) {
      return $t('sys_valid_positive_all_integer');
    }
    return '';
  })
  .set('cmpDecimals', (minLength, maxLength, value) => {
    if (!DataCheck.cmpDecimals(value)) {
      return $t('sys_valid_positive_decimal');
    }
    return '';
  })
  .set('cmpAllDecimals', (minLength, maxLength, value) => {
    if (!DataCheck.cmpAllDecimals(value)) {
      return $t('sys_valid_positive_all_decimal');
    }
    return '';
  })
  .set('cmpNoZeroDecimals', (minLength, maxLength, value) => {
    if (!DataCheck.cmpNoZeroDecimals(value)) {
      return $t('sys_valid_no_zero_positive_decimal');
    }
    return '';
  })
  .set('validHttpUrl', (minLength, maxLength, value) => {
    if (!DataCheck.validHttpUrl(value)) {
      return $t('sys_valid_http_url');
    }
    return '';
  })
  .set('checkLength', (minLength, maxLength, value) => {
    let checkLengthResult = DataCheck.checkLength(value, minLength, maxLength);
    if (!checkLengthResult.result) {
      return checkLengthResult.parameters;
    }
    return '';
  })
  .set('validChineseWord', (minLength, maxLength, value) => {
    if (!DataCheck.validChineseWord(value)) {
      return $t('sys_valid_chinese');
    }
    return '';
  })
  .set('validLetters', (minLength, maxLength, value) => {
    if (!DataCheck.validLetters(value)) {
      return $t('sys_valid_letter');
    }
    return '';
  })
  .set('validThreshold', (minLength, maxLength, value) => {
    if (!DataCheck.validThreshold(value)) {
      return $t('sys_valid_threshold');
    }
    return '';
  })
  .set('validPerformanceValue', (minLength, maxLength, value) => {
    if (!DataCheck.validPerformanceValue(value)) {
      return $t('sys_valid_performance');
    }
    return '';
  })
  .set('validIPRequired', (minLength, maxLength, value) => {
    if (!DataCheck.validIPRequired(value)) {
      return $t('sys_required_val');
    }
    return '';
  })
  .set('validIPAddress', (minLength, maxLength, value) => {
    if (!DataCheck.validIPAddress(value, minLength)) {
      return $t('sys_resource_valid_ipAddress');
    }
    return '';
  })
  .set('insNameValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.instanceNameValidChar(value)) {
      return $t('sys_resource_valid_instanceName_char');
    }
    return '';
  })
  .set('cmpConditionDecimals', (minLength, maxLength, value) => {
    if (!DataCheck.cmpConditionDecimals(value)) {
      return $t('sys_valid_condition_decimal');
    }
    return '';
  })
  .set('nodeNameValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.nodeNameValidChar(value)) {
      return $t('sys_node_name_valid_audit_char');
    }
    return '';
  })
  .set('cmpValidRegex', (minLength, maxLength, value) => {
    if (!DataCheck.cmpValidRegex(value)) {
      return $t('sys_valid_regex');
    }
    return '';
  })
  .set('dataSourceValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.dataSourceValidChar(value)) {
      return $t('sys_valid_dataSource_char');
    }
    return '';
  })
  .set('range', (minLength, maxLength, value) => {
    if (!DataCheck.range(value, minLength, maxLength)) {
      return $t('sys_format_valid_char_out_range', [minLength, maxLength]);
    }
    return '';
  })
  .set('floatRange', (minLength, maxLength, value) => {
    if (!DataCheck.floatRange(value, minLength, maxLength)) {
      return $t('sys_format_valid_char_out_range', [minLength, maxLength]);
    }
    return '';
  })
  .set('cronValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.cronValidChar(value)) {
      return $t('sys_cron_valid_audit_char');
    }
    return '';
  })
  .set('format', (minLength, maxLength, value) => {
    let checkResult = DataCheck.format(value, minLength);
    if (!checkResult.result) {
      return checkResult.parameters;
    }
    return '';
  })
  .set('resourceValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.resourceValidChar(value)) {
      return $t('sys_resource_valid_audit_char');
    }
    return '';
  })
  .set('siteNameValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.siteNameValidChar(value)) {
      return $t('site_name_valid_audit_char');
    }
    return '';
  })
  .set('sqlValid', (minLength, maxLength, value) => {
    return '';
  })
  .set('validOnlyDigits', (minLength, maxLength, value) => {
    if (!DataCheck.validOnlyDigits(value, minLength)) {
      return $t('sys_resource_only_digits');
    }
    return '';
  })
  .set('validLogPattern', (minLength, maxLength, value) => {
    if (!DataCheck.validLogPattern(value, minLength)) {
      return $t('sys_valid_pattern');
    }
    return '';
  })
  .set('causeInfoValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.causeInfoValidChar(value, minLength)) {
      return $t('sys_valid_causeInfo');
    }
    return '';
  })
  .set('fileNameValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.fileNameValidChar(value, minLength)) {
      return $t('sys_valid_fileName');
    }
    return '';
  })
  .set('numberAndCommaValidChar', (minLength, maxLength, value) => {
    if (!DataCheck.numberAndCommaValidChar(value, minLength)) {
      return $t('sys_valid_numberAndComma');
    }
    return '';
  })
  .set('togglingAlarmMaxNumberValidate', (minLength, maxLength, value) => {
    if (!DataCheck.togglingAlarmMaxNumberValidate(value, minLength)) {
      return $t('sys_valid_alarmId_max', [minLength]);
    }
    return '';
  });

const dataCheckValid = (checkType, minLength, maxLength, value) => {
  let tipMessage = '';
  if (dataCheckValidMap.get(checkType)) {
    tipMessage = dataCheckValidMap.get(checkType)(minLength, maxLength, value);
  }
  return tipMessage;
};

const dataTypeValidCheck = (dataArr, minLength, maxLength, value, id) => {
  let tipMessage;
  for (let i = 0; i < dataArr.length; i++) {
    tipMessage = dataCheckValid(dataArr[i], minLength, maxLength, value, id);
    if (tipMessage) {
      return tipMessage;
    }
  }
  return '';
};

const validate = (dataArr, value, id, minLength, maxLength) => {
  let validationReturnMap = {};
  let message = dataTypeValidCheck(dataArr, minLength, maxLength, value, id);
  let validationResult = message ? false : true;
  validationReturnMap.result = validationResult;
  validationReturnMap.message = message;
  return validationReturnMap;
};

export default validate;

