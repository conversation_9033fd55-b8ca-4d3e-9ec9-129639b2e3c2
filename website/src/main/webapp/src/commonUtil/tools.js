import {formatDate2SysSetting, goToNewPage, sorter} from '@digitalview/fe-utils';
import {modal, isZh} from './index';
import { request } from '@util';
const ALARM_WEBSITE_HISTORY_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmLog&json=true&csn=';
const ALARM_WEBSITE_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&json=true&csn=';
const EVENT_WEBSITE_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/event&json=true&csn=';
const CSN_URL_PARAMS = 'value=[replace]&operation=in';
const VIEW_MORE_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmView&conditionId=[replace]&isShowFilter=true';
const VIEW_MORE_HISTORY_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/fmAlarmLog&conditionId=[replace]&isShowFilter=true';
const VIEW_MORE_EVENT_URL = '/eviewwebsite/index.html#path=/fmAlarmApp/event&conditionId=[replace]&isShowFilter=true';
const ANALYSIS_URL = '/eviewwebsite/index.html#path=/aiOpsService&subMenu=associationAnalysisPage?refr-flags=mH&theme=dark&';

// 根据pattern格式化日期
function formatDate(utcTime, orgPattern) {
  let pattern = orgPattern;
  pattern = pattern || 'yyyy-MM-dd hh:mm:ss';

  // replace将‘-’改为‘/’可兼容IE
  const date = new Date(typeof utcTime === 'string' && !/[tTzZ]/g.test(utcTime) ?
    utcTime.replace(/-/g, '/') : utcTime);
  const year = date.getFullYear();
  const month = _addZero(date.getMonth() + 1);
  const day = _addZero(date.getDate());
  const hour = _addZero(date.getHours());
  const minute = _addZero(date.getMinutes());
  const second = _addZero(date.getSeconds());
  const millisecond = (date.getMilliseconds() + 1000).toString().substring(1);

  return pattern
    .replace(/yyyy/, year)
    .replace(/MM/, month)
    .replace(/dd/, day)
    .replace(/hh/, hour)
    .replace(/mm/, minute)
    .replace(/ss/, second)
    .replace(/fff/, millisecond);
}

function _addZero(number) {
  return (number + 100).toString().substring(1);
}

const debounce = (func, time = 300) => {
  let timeStart;
  let timer;
  let lastArgs;
  let lastThis;
  const _apply = (context, args) => {
    func.apply(context, args);
    timeStart = null;
    timer = null;
    lastThis = null;
    lastArgs = null;
  };
  return (...args) => {
    if (!timeStart) {
      timeStart = Date.now();
    }
    lastArgs = args;
    lastThis = this;
    clearTimeout(timer);
    if (Date.now() - timeStart >= 500) {
      _apply(lastThis, lastArgs);
      return;
    }
    timer = setTimeout(() => {
      _apply(lastThis, lastArgs);
    }, time);
  };
};

const unescape = string => {
  // string list to transform
  const unescapers =
    {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#x27;': '\'',
      '&#x28;': '(',
      '&#x29;': ')',
    };
  const unescaper = /(&amp;|&lt;|&gt;|&quot;|&#x27;|&#x28;|&#x29;)/g;
  const tmpStr = '';

  if (!string) {
    return string;
  }

  return (tmpStr + string).replace(unescaper, match => unescapers[match]);
};

const detachAttributeValue = (arr, findKey, resultKey, findValue) => {
  if (findValue !== undefined && findValue !== null) {
    let result = arr.find(ele => ele[findKey] === findValue);
    return result ? result[resultKey] : null;
  }
  return null;
};

// 13位时间戳转为日期yyyy:MM:dd HH:mm:ss
const timestampToTime = timestamp => {
  let date = new Date(parseInt(timestamp));
  let Y = `${date.getFullYear()}-`;
  let M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
  let D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
  let h = `${date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()}:`;
  let m = `${date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()}:`;
  let s = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();

  return `${Y + M + D} ${h + m + s}`;
};

const DATE_TIME_FORMAT = {
  zh_CN: {
    AM: '上午',
    PM: '下午',
  },
  en_US: {
    AM: 'AM',
    PM: 'PM',
  },
};

// 根据cookieName获取值
const getCookie = name => {
  const reg = new RegExp(`(^| )${name}=([^;]*)(;|$)`);
  let arr = document.cookie.match(reg);
  if (arr) {
    return unescape(arr[2]);
  } else {
    return null;
  }
};

export const formatChartTime = timestamp => {
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};
export const formatChartDate = timestamp => {
  let date = new Date(parseInt(timestamp));

  let M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
  let D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();

  return `${M + D}`;
};

// 根据系统设置的格式对时间戳格式化
export const dateTimeFormat = timestamp => {
  if (!timestamp) {
    return null;
  }
  return formatDate2SysSetting(parseInt(timestamp));
};

// 获取url请求参数
const getUrlParam = name => {
  let reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
  let search = `?${window.location.href.split('?')[1]}`;
  let result = search.substr(1).match(reg);
  if (result !== null) {
    let content = encodeURIComponent(unescape(result[2]).trim());
    if (content && content !== 'null') {
      return content;
    }
  }
  return null;
};

// 13位时间戳转日期 MM-dd
const timestampToDate = timestamp => {
  let date = new Date(parseInt(timestamp));
  let M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
  let D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
  return `${M + D}`;
};

// 计算字符长度,中文占2个
const getByteLength = text => {
  let len = 0;
  for (let i = 0; i < text.length; i++) {
    let char = text.charAt(i);
    if (char.match(/[^\x00-\xff]/ig) !== null) {
      len += 2;
    } else {
      len += 1;
    }
  }
  return len;
};

// 处理超长字符串的显示问题
const interceptString = (text, maxLength) => {
  if (getByteLength(text) > maxLength) {
    let currentLen = 0;
    let result = '';
    for (let i = 0; i < text.length; i++) {
      let char = text.charAt(i);
      if (char.match(/[^\x00-\xff]/ig) !== null) {
        currentLen += 2;
      } else {
        currentLen += 1;
      }
      if (currentLen > maxLength) {
        break;
      }
      result += char;
    }
    return `${result}...`;
  } else {
    return text;
  }
};

// 解决火狐和ie浏览器的title文本过长显示不全问题
const lineBreak = value => {
  if (!value) {
    return value;
  }

  return `${value}`.replace(/(.{50})/g, '$1\n');
};

// 判断IE浏览器
const isIEBrowser = () => Boolean(window.ActiveXObject) || 'ActiveXObject' in window;

const getComponentKey = () => {
  let result = /subMenu=([^&]+)/g.exec(window.location.href);
  return result ? result[1].split('?')[0] : '';
};

export const dateFormatToHour = timestamp => {
  let foramtData = formatDate2SysSetting(timestamp);
  return foramtData.includes('DST') ?
    foramtData.replace(/.*?(\d{2}:\d{2}).*(DST)$/, '$1 $2') :
    foramtData.replace(/.*?(\d{2}:\d{2}).*/, '$1');
};

const timeRange = 60000;
export const jumpAlarmLogPage = (csn, occurTime) => {
  let startTime = parseInt(occurTime) - timeRange;
  let endTime = parseInt(occurTime) + timeRange;
  let csnUrl = encodeURIComponent(CSN_URL_PARAMS).replace('replace', `%22${csn}%22`);
  let win = window.open(`${ALARM_WEBSITE_HISTORY_URL}${csnUrl}&latestEventTime=${startTime}~${endTime}`);
  if (win) {
    win.opener = null;
  }
};
export const jumpAlarmPage = (csn, occurTime) => {
  let startTime = parseInt(occurTime) - timeRange;
  let endTime = parseInt(occurTime) + timeRange;
  let csnUrl = encodeURIComponent(CSN_URL_PARAMS).replace('replace', `%22${csn}%22`);
  let win = window.open(`${ALARM_WEBSITE_URL}${csnUrl}&latestEventTime=${startTime}~${endTime}`);
  if (win) {
    win.opener = null;
  }
};


export const jumpAnalysisPage = analysisParam => {
  let win = window.open(`${ANALYSIS_URL}indicatorId=${
    encodeURIComponent(analysisParam.indicatorId)
  }&selectedAssociationTask=${
    encodeURIComponent(analysisParam.selectedAssociationTask)
  }&executionTime=${
    encodeURIComponent(analysisParam.executionTime)
  }`);
  if (win) {
    win.opener = null;
  }
};

export const jumpEventLogPage = (eventCsn, occurTime) => {
  let startTime = parseInt(occurTime) - 60 * 1000;
  let endTime = parseInt(occurTime) + 60 * 1000;
  let csnUrl = encodeURIComponent(CSN_URL_PARAMS).replace('replace', `%22${eventCsn}%22`);
  let win = window.open(`${EVENT_WEBSITE_URL}${csnUrl}&eventTime=${startTime}~${endTime}`);
  if (win) {
    win.opener = null;
  }
};

export const jumpViewMorePage = id => {
  let newId = encodeURIComponent(id);
  let win = window.open(`${VIEW_MORE_URL.replace('[replace]', newId)}`);
  if (win) {
    win.opener = null;
  }
};

export const jumpViewMoreHistoryPage = id => {
  let newId = encodeURIComponent(id);
  let win = window.open(`${VIEW_MORE_HISTORY_URL.replace('[replace]', newId)}`);
  if (win) {
    win.opener = null;
  }
};

export const jumpViewMoreEventPage = id => {
  let newId = encodeURIComponent(id);
  let win = window.open(`${VIEW_MORE_EVENT_URL.replace('[replace]', newId)}`);
  if (win) {
    win.opener = null;
  }
};

// 跳转到性能历史数据页面
export const jumpToPMHistoryDataPage = (indicatorList, startTime, endTime) => {
  // 查询后台接口判断是否能跳转性能
  request.get('/rest/dvtopowebsite/v1/business/topo/common/querydependentservicestatus', (data)=>{
    if (data.data.jumpToPerformancePage) {
      const filteredIndicatorList = (indicatorList || []).filter(item => item.dn);
      if (filteredIndicatorList.length === 0) {
        if (isZh) {
          modal.info('TopN视图无数据时不支持历史数据查询');
        } else {
          modal.info('Historical data cannot be queried when there is no data in the TopN view.');
        }
        return;
      }
      const sessionData = {
        indexes: filteredIndicatorList.map(item => ({
          moType: item.moType,
          measUnitKey: item.measUnitKey,
          measType: item.measTypeKey,
          originalValue: item.originalValue,
          dn: item.dn,
        })),
        startTime: parseInt(startTime),
        endTime: parseInt(endTime),
      };

      sessionStorage.setItem('topoJumpPMIndicatorData', JSON.stringify(sessionData));
      const pageUrl = `${location.origin}/eviewwebsite/index.html#path=/dvpmApp/historyPm&sessionValueID=topoJumpPMIndicatorData`;
      goToNewPage(pageUrl, true);
    }
  }, ()=>{
    // 空实现
  }, false);
};

/**
 * 版本兼容性实现，兼容低版本浏览器
 *
 */
function findLast(array, callback) {
  if (array === null) {
    throw new TypeError('this is null or not defined');
  }
  const arr = Object(array);
  const len = arr.length >>> 0;
  for (let i = len - 1; i >= 0; i--) {
    if (callback(arr[i], i, arr)) {
      return arr[i];
    }
  }
  return undefined;
}


const getUniqueData = (historyList) => {
  if (!historyList) {
    return historyList;
  }

  historyList.forEach(history => {
    let newComparativeValueMap = {};
    Object.entries(history.comparativeValueMap).forEach(([key, valueList]) => {
      let timeSet = new Set();
      if (!newComparativeValueMap[key]) {
        newComparativeValueMap[key] = [];
      }

      valueList.forEach(value => {
        // 以前面的为准
        if (timeSet.has(value.timestampStr)) {
          return;
        }
        timeSet.add(value.timestampStr);
        newComparativeValueMap[key].push(value);
      });
    });

    Object.values(newComparativeValueMap).forEach(v => {
      v.sort((a, b) => sorter(a?.timestampStr, b?.timestampStr));
    });

    history.comparativeValueMap = newComparativeValueMap;
  });

  return historyList;
};


export const sortHistoryListData = (tempHistoryList) => {
  if (!tempHistoryList) {
    return tempHistoryList;
  }

  // 去重数据
  let historyList = getUniqueData(tempHistoryList);
  if (historyList.length === 1 || historyList.length === 0) {
    return historyList;
  }

  const maxTime = Math.max(
    ...historyList.map(history => {
      const todayData = (history.comparativeValueMap || {}).Today || [];
      return todayData.map(v => Number(v.timestampStr));
    }).flatMap(v => v).filter(v => v),
  );

  historyList.sort((a, b) => {
    const aTodayData = (a.comparativeValueMap || {}).Today || [];
    const bTodayData = (b.comparativeValueMap || {}).Today || [];
    const aLastValue = findLast(aTodayData, (v) => v.timestampStr === String(maxTime));
    const bLastValue = findLast(bTodayData, (v) => v.timestampStr === String(maxTime));
    // 都没有，按名称字典序排
    if (!aLastValue && !bLastValue) {
      return sorter(a.siteName || a.podName, b.siteName || b.podName);
    }
    return sorter(aLastValue?.indexValue, bLastValue?.indexValue, 'descend');
  });
  return historyList;
};

export default {
  debounce,
  getUrlParam,
  formatDate,
  unescape,
  detachAttributeValue,
  timestampToTime,
  dateTimeFormat,
  dateFormatToHour,
  timestampToDate,
  interceptString,
  lineBreak,
  isIEBrowser,
  getComponentKey,
  jumpToPMHistoryDataPage,
  sortHistoryListData,
};
